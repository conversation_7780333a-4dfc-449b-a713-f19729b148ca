# flag 测试环境变量
ENV=test
# 服务器发版
NODE_ENV=production
# java接口地址
VUE_APP_JAVA_API_URL=https://api-test.pinkwallet.xyz/blockchain/
# PHP接口地址
VUE_APP_PHP_API_URL=http://notbug.nftcn.com.cn/api/
# 支付回掉地址
VUE_APP_PAY_BACK=/h5/
# 藏品室地址
VUE_APP_COLLECTION_PATH=/collection/
# 3d 预览地址
VUE_APP_THREE_PREVIEW_PATH=/3dlink/#/preview
# 3d旋转展示
VUE_APP_EXHIBITION_PATH=/3dlink/#/exhibition
# 图片上传
VUE_APP_UPLOAD_URL=http://notbug.nftcn.com.cn/api/v1/extra/upload 
# java图片上传
VUE_APP_JAVA_UPLOADIMAGE=https://api-test.pinkwallet.xyz/blockchain/pinkwallet/appApi/oss/uploadImage
# java  视频上传
VUE_APP_JAVA_UPLOADVIDEO=http://api-test.nftcn.com.cn/nms/dubbo/osscenter/appApi/uploadVideo
# 埋点地址
VUE_APP_TRACK=lingjing-test
VUE_APP_WS_API_URL_WALLET=wss://test-walletws.pinkwallet.xyz/pink-wallet/ws
# VUE_APP_WS_API_URL=ws://api-test.nftcn.com.cn:81/kline-api/ws
VUE_APP_WS_API_URL=wss://test-klinews.pinkwallet.xyz/contract/klineapi/ws
# 测试环境
VUE_APP_URL=https://test-www.pinkwallet.xyz/h5
# 客户端ID
VUE_APP_CLIENT_ID=1018175067878-3amrbvbccaq5mdfaf82hn04v7u22j9jp.apps.googleusercontent.com
# 客户端密钥
VUE_APP_CLIENT_SECRET=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