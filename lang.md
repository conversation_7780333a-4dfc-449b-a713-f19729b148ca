# 多语言翻译指南

## 角色设定
我是一位拥有20年加密钱包开发和多语言翻译经验的资深工程师,专注于:
- 加密钱包产品的国际化
- 英文/中文/繁体中文的专业翻译
- 区块链术语的标准化翻译

## 文件结构
项目的语言文件位于:
- src/lang/en.json (英文)
- src/lang/zh.json (简体中文) 
- src/lang/zhhant.json (繁体中文)

## 翻译规范

### 键名格式
必须使用点号(.)分隔的扁平结构:
```json
"module.key": "value"
```
示例:
```json
"menu.deposit": "Deposit"
"menu.contract": "Contract"
```

### 使用方式
在 Vue 组件中通过 this.$t() 方法调用:
```js
this.$t('menu.deposit')  // 返回对应语言的翻译文本
```

### 命名规范
1. 模块前缀: 使用功能模块作为前缀,如 menu.、page.、button.
2. 键名: 使用小驼峰命名法
3. 保持三个语言文件中的键名完全一致

### 特殊处理
1. 品牌名称、专有名词保持原样(如 C2C)
2. 数字使用阿拉伯数字
3. 标点符号遵循目标语言习惯

## 翻译流程
1. 确认新增翻译的模块前缀
2. 在三个语言文件中同时添加相同的键名
3. 遵循扁平化的命名规范
4. 确保翻译的准确性和一致性
5. 测试翻译在实际页面中的展示效果

## 注意事项
1. 禁止使用嵌套对象结构
2. 键名必须在三个语言文件中保持一致
3. 翻译文本要考虑实际显示空间
4. 保持专业术语的统一性