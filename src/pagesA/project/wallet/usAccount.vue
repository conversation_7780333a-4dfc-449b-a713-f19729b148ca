<template>
	<view class="personal_bg">
		<!-- 顶部资产卡片浮层 -->
		<u-navbar :border-bottom="false" :title="title" :customBack="back">
		</u-navbar>
		<view class="asset_card">
			<view class="asset_card_top">
				<view class="asset_card_left">
					<view clas s="asset_card_label">
						<text class="asset_card_label_text">预计总资产</text>
						<u-icon v-if="eyeoff" @click="eyeoff = !eyeoff"
							name="https://pro-oss.pinkwallet.com/image/1370353025834115072.png" size="36"
							class="asset_card_info_icon" />
						<u-icon v-else @click="eyeoff = !eyeoff"
							name="https://pro-oss.pinkwallet.com/image/1370353145610854400.png" size="36"
							class="asset_card_info_icon" />

					</view>
					<view class="asset_card_amount_row">
						<text class="asset_card_amount" v-if="eyeoff">***</text>
						<text class="asset_card_amount" :style="{ fontSize: $maxFontSize($m(totalNum)) + 'rpx' }" v-else>{{$m(totalNum)||'0.00'}} </text>
						<view>
							<text class="asset_card_unit"
								@click="toggleRotate(),showcurrencyShow = !showcurrencyShow">{{nowsymbol}}</text>
							<u-icon name="arrow-down" size="24" color="#000000"
								:style="{ transform: `rotate(${rotate}deg)` }" class="asset_card_arrow_icon"
								@click="showcurrencyShow = !showcurrencyShow" />
						</view>
						<transition name="expand-slide">
							<view class="helpoption" v-show="showcurrencyShow">
								<view v-for="(item, index) in userCoins" :key="index" class="Roptions"
									@click.stop="SetSymbol(item)">
									<text :style="{ color: nowsymbol == item.name ? '#008E28' : '' }">{{ item.name
										}}</text>
								</view>
							</view>
						</transition>
					</view>
					<view class="asset_card_rate">{{increaseInfo.nowDay}} {{nowsymbol}}
						<text>({{increaseInfo.increaseRate*1000>0?`+${increaseInfo.increaseRate}`:increaseInfo.increaseRate}}%)昨日</text>
					</view>
				</view>
				<view class="asset_card_chart">
					<view class="chart_placeholder" @click="openShowEachart()">
						<image src="https://pro-oss.pinkwallet.com/image/1387826531387662336.png" mode="widthFix">
						</image>
					</view>
				</view>
			</view>
			<view class="echart_view" v-if="showEchart">
				<areaechart ref="areaechart" :type="echartType" :query="echartQuery"> </areaechart>
			</view>
			<view class="asset_view">
				<!-- <view class="title">币种分布</view> -->
				<view class="flex_view">
					<view class="item_view">
						<view class="label">
							浮动盈亏
						</view>
						<view class="val">
							{{accountInfo.yjYkAmount}}
						</view>
					</view>
					<view class="item_view">
						<view class="label">
							持仓市值
						</view>
						<view class="val">
							{{accountInfo.marketValue}}
						</view>
					</view>
					<view class="item_view">
						<view class="label">
							已实现盈亏
						</view>
						<view class="val">
							{{accountInfo.allYkAmount}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 操作按钮区块 -->
		<view class="action_btns_wrap">
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="openBottomPopup()">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386806647358840832.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">交易</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="onPopupOption({title:'转账'})">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807020060499968.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">闪兑</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="nav_to('transfer')">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807124683218944.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">划转</text>
			</view>
		</view>
		<!-- 底部列表块 -->
		<view class="bottom_list_wrap">
			 <positionUs :current="current" ref="position"></positionUs>
			<!-- <view class="tabs">
				<view class="tab_item" :class="{'active':index == current}" v-for="(item,index) in tabsList"
					@click="checkTab(index)">
					<text>{{item.name}}</text>
				</view>

			</view>
			<view class="list_wrap">
				<view class="position_table">
					<view class="position_table_header">
						<text class="position_th">市值</text>
						<text class="position_th">盈亏</text>
						<text class="position_th">持仓/可用</text>
						<text class="position_th right">成本/限价</text>
					</view>
					<view class="position_table_row" v-for="(item,index) in [0,0,0]">
						<view class="position_td position_td_name">
							<text class="position_name">正和生态</text>
							<text class="position_value">94,284.00</text>
						</view>
						<view class="position_td">
							<text class="position_value">-840.43</text>
							<text class="position_value">-0.885%</text>
						</view>
						<view class="position_td">
							<text class="position_value">8100</text>
							<text class="position_value">-0.885%</text>
						</view>
						<view class="position_td right">
							<text class="position_value">8100</text>
							<text class="position_value">-0.885%</text>
						</view>
					</view>
				</view>
			</view> -->
		</view>
	</view>
</template>

<script>
	import areaechart from '../../../components/echartCom/area-echart.vue'
	import positionUs from './components/positionUs.vue'
	export default {
		data() {
			return {
				eyeoff: false,
				userCoins: [],
				showcurrencyShow: false,
				nowsymbol: '',
				rotate: 0,
				totalNum: 0,
				title: '',
				tabsList: [{
						name: "当前持仓"
					},
					{
						name: "历史委托 "
					}
				],
				current: 0,
				increaseInfo: {},
				pageNum: 1,
				accountInfo: {},
				echartType: 'personal',
				echartQuery: {
					convertCoin: '',
					assetType:''
				},
				showEchart: false,
			}
		},
		computed: {

		},
		components: {
			areaechart,
			positionUs
		},
		onLoad(options) {

			if (options.market) {
				this.market = options.market
				this.title = this.market == 'US' ? '美股账户' : '港股账户'
			}
			this.getUserCoinFiat()
			this.getStockAccount()
		},
		methods: {
			onPopupOption(item) {
				// 这里可以根据item.title或其他字段做跳转
				this.showBottomPopup = false
				this.$u.toast('你选择了：' + item.title)
				switch (item.title) {
					case '链上充值':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case '快捷买币':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case 'C2C交易':
						this.nav_to('symbol')
						break
					case '转账':
						this.nav_to('symbol', {
							targetPage: 'withdraw'
						})
						break
					default:
						break
				}
			},
			nav_to(name, params) {
				this.$Router.push({
					name,
					params
				})
			},
			back() {
				this.$Router.back()
			},
			openBottomPopup() {
				this.showBottomPopup = true
			},

			async getInfo() {
				let res = await this.$api.amount({
					assetType: this.market == 'US' ? 3 : 4 ,
					convertCoin: this.nowsymbol
					// assetTypeEnum: "ASSET_COIN",
					// symbol: "USD",
				});
				if (res.code == 200) {
					console.log(res)
					this.totalNum = res.result.amount
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getAllAssetsFiat() {
				let res = await this.$api.userAssetsFiat({
					coin: this.nowsymbol
				})
				if (res.code == 200) {
					this.allassets = res.result.amount
				}
			},
			async getIncreaseRate() {
				let res = await this.$api.increaseRate({
					assetType: this.market == 'US' ? 3 : 4 ,
					convertCoin: this.nowsymbol
				})
				if (res.code == 200) {
					this.increaseInfo = res.result
				}
			},
			SetSymbol(e) {
				console.log(e);
				this.nowsymbol = e.name
				this.showcurrencyShow = false
				if(this.showEchart){
					this.echartQuery.nowsymbol = e.name
					this.$refs.areaechart.fetchChartData()
				}
				this.getInfo()
				this.getIncreaseRate()
			},
			toggleRotate() {
				this.rotate = this.rotate === 0 ? 180 : 0;
			},
			async getUserCoinFiat() {
				let res = await this.$api.userCoinList({
					pageNum: 1,
					pageSize: 100,
					fiat: true, // 法币
					hideZeroAsset: false
				})
				if (res.code == 200) {
					this.nowsymbol = res.result.data[0].symbol
					this.userCoins = res.result.data
					this.getInfo()
					this.getIncreaseRate()
				} else {
					this.$u.toast(res.msg)
				}
			},
			checkTab(index) {
				this.current = index
			},
			// 委托历史订单
			async getstockorder() {
				let res = await this.$api.stockorder({
					market: this.market,
					pageNum: this.pageNum,
					pageSize: 10,
				})
				if (res.code == 200) {
					this.hasNext = res.result.hasNext
					if (res.result.data && res.result.data.length) {
						if (this.pageNum == 1) {
							this.holdList = res.result.data
						} else {
							this.holdList = this.holdList.concat(res.result.data)
						}
					} else {
						this.holdList = []
					}

				}
			},
			async getStockAccount() {
				let res = await this.$api.stockAccount({
					market: this.market
				})
				if (res.code == 200) {
					console.log(res)
					this.accountInfo = res.result
				} else {
					this.$u.toast(res.msg)
				}
			},
			openShowEachart(){
				if(!this.showEchart){
					this.echartQuery.convertCoin = this.nowsymbol
					this.echartQuery.assetType = this.market == 'US' ? 3 : 4 
				}
				this.showEchart = !this.showEchart
			}
		}
	}
</script>

<style lang="scss" scoped>
	.personal_bg {
		min-height: 100vh;
		background: #FEFBFF;
		padding: 0 0 40rpx 0;
		font-family: 'PingFang SC';
		padding-bottom: 100rpx;
	}

	.asset_card {
		margin: 0;
		background: #fff;
		border-radius: 0 0 48rpx 48rpx;
		padding: 48rpx 30rpx 48rpx 30rpx;
		position: relative;
		z-index: 2;
		font-family: 'PingFang SC';
		position: relative;

		.right_icon {
			position: absolute;
			right: 30rpx;
			top: 40rpx;

			image {
				width: 40rpx;
			}
		}

		.asset_card_top {
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			margin: 20rpx 0rpx;

			.asset_card_left {
				.asset_card_labe l {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #BDBDBD;
					font-weight: 400;
					margin-bottom: 12rpx;
				}

				.asset_card_amount_row {
					display: flex;
					align-items: flex-end;
					margin-bottom: 8rpx;
					position: relative;

					.asset_card_amount {
						font-size: 60rpx;
						font-weight: 800;
						color: #121212;
						line-height: 1;
					}

					.asset_card_unit {
						font-size: 28rpx;
						color: #121212;
						font-weight: 500;
						margin-left: 10rpx;
					}

					.asset_card_arrow_icon {
						margin-left: 6rpx;
						font-size: 22rpx;
						color: #BDBDBD;
						margin-bottom: 5rpx;
					}
				}

				.asset_card_rate {
					font-size: 22rpx;
					color: #BDBDBD;
					font-weight: 400;
					/* margin-bottom: 16rpx; */
				}
			}

			.asset_card_chart {
				width: 154rpx;
				height: 74rpx;
				margin-left: 16rpx;

				.chart_placeholder {
					width: 100%;
					height: 100%;

					image {
						width: 100%;
					}
				}
			}
		}

		.asset_view {
			.title {
				font-size: 28rpx;
				margin-bottom: 28rpx;
				font-weight: 600;
				color: #000;
			}

			.flex_view {
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.item_view {
					width: 50%;

					.label {
						font-size: 24rpx;
						color: rgba(0, 0, 0, 0.50);
						margin-bottom: 20rpx;
					}

					.val {
						font-size: 32rpx;
						font-weight: 600;
						color: #000;
					}
				}
			}
		}

	}

	.action_btns_wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 48rpx;
		background: #fff;
		padding: 24rpx 100rpx;
		margin: 20rpx 0rpx;

		.action_btn_item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.action_btn_icon_wrap {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 12rpx;

				.u-icon {
					font-size: 44rpx !important;
				}
			}

			.action_btn_text {
				font-size: 24rpx;
				color: #121212;
				margin-top: 4rpx;
				font-weight: 500;
			}
		}

		/* .action_btn_item+.action_btn_item {
			margin-left: 24rpx;
		} */
	}

	.bottom_list_wrap {
		background-color: #fff;
		border-radius: 48rpx 48rpx 0 0;
		min-height: 50vh;
		padding: 48rpx 32rpx;

		.tabs {
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.tab_item {
				font-size: 28rpx;
				color: rgba(0, 0, 0, 0.50);
				font-weight: 600;
				margin-right: 24rpx;

				&.active {
					color: #000;
				}
			}
		}
	}

	.helpoption {
		width: 80*2rpx;
		transition: transform 0.3s ease, opacity 0.3s ease;
		transform-origin: top;
		/* 设置变换的起点为顶部 */
		z-index: 9999;
		position: absolute;
		top: 80rpx;
		right: -50rpx;
		box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;
		max-height: 400rpx;
		overflow-y: auto;
		background: #fff;
		border-radius: 16*2rpx;
		padding: 16*2rpx;
		opacity: 1;
		display: flex;
		align-items: flex-start;
		flex-direction: column;



		&.collapse {
			transform: scaleY(0) translateY(-100%);
			/* 缩小至0，并向上移动 */
			opacity: 0;
		}

		&.expand {
			transform: scaleY(1) translateY(0%);
			/* 恢复到正常大小，并位置恢复 */
			opacity: 1;

		}

		>view {
			width: 100%;
			padding: 15rpx 0;
			text-align: left;

			image {
				width: 40rpx;
				height: 30rpx;
			}

			text {
				font-family: Gilroy-Bold;
				font-weight: 400;
				font-size: 16*2rpx;
				line-height: 19.2*2rpx;
				color: #000;
			}
		}
	}

	.wallet_popup_content {
		padding: 40rpx 0 32rpx 0;

		.wallet_popup_title_row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 32rpx 32rpx 32rpx;

			.wallet_popup_title {
				font-size: 32rpx;
				font-weight: 600;
				color: #121212;
			}
		}

		.wallet_popup_list {
			.wallet_popup_item {
				display: flex;
				align-items: center;
				background: #fff;
				border-radius: 24rpx;
				margin: 0 32rpx 24rpx 32rpx;
				padding: 32rpx 24rpx;
				border: 1rpx solid rgba(0, 0, 0, 0.05);

				.wallet_popup_icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 24rpx;
				}

				.wallet_popup_info {
					flex: 1;
					display: flex;
					flex-direction: column;

					.wallet_popup_item_title {
						font-size: 28rpx;
						color: #121212;
						font-weight: 600;
						margin-bottom: 8rpx;
					}

					.wallet_popup_item_desc {
						font-size: 20rpx;
						color: #BDBDBD;
					}
				}
			}
		}
	}

	.pie_chart_wrap {
		margin: 24rpx 0 0 0;

		.pie_chart_content {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			margin-top: 40rpx;
		}

		.left {
			display: flex;
			justify-content: space-between;
			flex-direction: column;
			height: 240rpx;

			.pie_legend_tip {
				font-size: 20rpx;
				color: rgba(0, 0, 0, 0.5);
			}
		}

		.pie_chart_img {
			width: 140rpx;
			height: 140rpx;
			display: block;
			margin-right: 24rpx;
			margin-left: 8rpx;
		}

		.pie_legend {
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			min-width: 380rpx;

			.pie_legend_row {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;
			}

			.pie_legend_dot {
				width: 24rpx;
				height: 24rpx;
				border-radius: 8rpx;
				margin-right: 10rpx;
				flex-shrink: 0;
			}

			.pie_legend_name {
				font-size: 22rpx;
				color: #222;
				margin-right: auto;
				font-weight: 400;
			}

			.pie_legend_percent {
				font-size: 22rpx;
				color: #BDBDBD;
				font-weight: 400;
				margin-left: 16rpx;
			}

			.pie_legend_tip {
				font-size: 18rpx;
				color: #BDBDBD;
				margin-top: 8rpx;
				font-weight: 400;
				text-align: left;
			}
		}
	}

	.bottom_list_header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 32rpx;

		.bottom_list_title {
			font-size: 32rpx;
			font-weight: 600;
			color: #121212;
		}

		.bottom_list_tools {
			display: flex;
			align-items: center;

			.bottom_list_icon {
				margin-left: 24rpx;
			}
		}
	}

	.list_wrap {
		margin-top: 32rpx;

		.position_header {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;

			.position_title {
				font-size: 32rpx;
				font-weight: 600;
				color: #121212;
				margin-right: 24rpx;
			}

			.position_action {
				font-size: 28rpx;
				color: #666;
				font-weight: 400;
			}
		}

		.position_table {
			width: 100%;

			.position_table_header {
				display: flex;
				justify-content: flex-start;
				align-items: flex-end;
				margin-bottom: 16rpx;

				.position_th {
					flex: 1;
					font-size: 24rpx;
					color: #999;
					font-weight: 400;
					text-align: left;

					&.right {
						text-align: right;
					}
				}
			}

			.position_table_row {
				display: flex;
				justify-content: flex-start;
				align-items: flex-start;
				margin-bottom: 32rpx;

				.position_td {
					flex: 1;
					display: flex;
					flex-direction: column;
					align-items: flex-start;

					&.right {
						align-items: flex-end;
					}

					.position_name {
						font-size: 28rpx;
						font-weight: 600;
						color: #222;
						margin-bottom: 4rpx;
					}

					.position_value {
						font-size: 24rpx;
						color: #888;
						font-weight: 400;
						margin-bottom: 2rpx;
					}
				}

				.position_td_name {
					min-width: 160rpx;
				}
			}
		}
	}
</style>