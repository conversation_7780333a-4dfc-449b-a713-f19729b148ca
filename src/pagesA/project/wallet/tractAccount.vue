<template>
	<view class="personal_bg">
		<!-- 顶部资产卡片浮层 -->
		<u-navbar :border-bottom="false" :title="title" :customBack="back">
		</u-navbar>
		<view class="asset_card">
			<view class="asset_card_top">
				<view class="asset_card_left">
					<view clas s="asset_card_label">
						<text class="asset_card_label_text">{{$t('personal.estimatedAssets')}}</text>
						<u-icon v-if="eyeoff" @click="eyeoff = !eyeoff"
							name="https://pro-oss.pinkwallet.com/image/1370353025834115072.png"
							size="36" class="asset_card_info_icon" />
						<u-icon v-else @click="eyeoff = !eyeoff"
							name="https://pro-oss.pinkwallet.com/image/1370353145610854400.png"
							size="36" class="asset_card_info_icon" />

					</view>
					<view class="asset_card_amount_row">
						<text class="asset_card_amount" v-if="eyeoff">***</text>
						<text class="asset_card_amount" :style="{ fontSize: fontSize + 'rpx' }" v-else>{{$m(totalNum,isFiat?2:8)}} </text>
						<view>
							<text class="asset_card_unit"
								@click="toggleRotate(),showcurrencyShow = !showcurrencyShow">{{nowsymbol}}</text>
							<u-icon name="arrow-down" size="24" color="#000000"
								:style="{ transform: `rotate(${rotate}deg)` }" class="asset_card_arrow_icon"
								@click="showcurrencyShow = !showcurrencyShow,toggleRotate()" />
						</view> 
						<transition name="expand-slide">
							<view class="helpoption" v-show="showcurrencyShow">
								<view v-for="(item, index) in userCoins" :key="index" class="Roptions"
									@click.stop="SetSymbol(item),toggleRotate()">
									<text :style="{ color: nowsymbol == item.name ? '#008E28' : '' }">{{ item.name
										}}</text>
								</view>
							</view>
						</transition>
					</view>
					<view class="asset_card_rate">{{$m(increaseInfo.incrementAmount,isFiat?2:8)}} {{nowsymbol}}
						<text>({{increaseInfo.increaseRate*100>0?`+${increaseInfo.increaseRate}`:increaseInfo.increaseRate}}%){{$t('personal.yesterday')}}</text>
					</view>
				</view>
				<view class="asset_card_chart">
					<view class="chart_placeholder" @click="openShowEachart()">
						<image src="https://pro-oss.pinkwallet.com/image/1387826531387662336.png" mode="widthFix">
						</image>
					</view>
				</view>
			</view>
			<view class="echart_view" v-if="showEchart">
				<areaechart ref="areaechart" :type="echartType" :query="echartQuery"> </areaechart>
			</view>
			<view class="asset_view">
				<view class="title">{{$t('bDetails.distribution')}}</view>
				<view class="flex_view">
					<view class="item_view">
						<view class="label">
							{{$t('wallet.walletBalance')}}（{{nowsymbol}}）
						</view>
						<view class="val">
							{{$m(totalNum2,isFiat?2:8)}}
						</view>
					</view>
					<view class="item_view">
						<view class="label">
							{{$t('wallet.unrealizedProfit')}}（{{nowsymbol}}）
						</view>
						<view class="val">
							{{$m(totalNum3,isFiat?2:8)}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 操作按钮区块 -->
		<view class="action_btns_wrap">
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="nav_tract()">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386806647358840832.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">{{$t('wallet.trade')}}</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="onPopupOption({title:'wallet.flashSwap'})">
					<u-icon name="https://pro-oss.pinkwallet.com/image/shandui.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">{{$t('wallet.flashSwap')}}</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="nav_to('transfer')">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807124683218944.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">{{$t('personal.swap')}}</text>
			</view>
		</view>
		<!-- 底部列表块 -->
		<view class="bottom_list_wrap">
			<view class="tabs">
				<view class="tab_item" :class="{'active':index == current}" v-for="(item,index) in tabsList"
					@click="checkTab(index)">
					<text>{{item.name}}</text>
				</view>

			</view>
			<view class="list_wrap">
				<view class="view_view" v-if="current==0">
					 <position ref="positions" @cancel="cancel" @close="close" />
				</view>
				<view class="view_view" v-if="current==1">
					<AllOrder ref="AllOrder"/>
				</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	import AllOrder from "./components/All-order.vue" 
	import position from "./components/position.vue" 
	import areaechart from '../../../components/echartCom/area-echart.vue'
	export default {
		data() {
			return {
				eyeoff: false,
				userCoins: [],
				showcurrencyShow: false,
				nowsymbol: '',
				rotate: 0,
				totalNum: 0,
				totalNum2: 0,
				totalNum3:0,
				title: this.$t('wallet.contractAccountCrypto'),
				tabsList: [{
						name: this.$t('wallet.currentPosition')
					},
					{
						name: this.$t('wallet.historyPosition')
					}
				],
				current: 0,
				increaseInfo:{},
				echartType:'personal',
				echartQuery:{
					convertCoin:'',
					assetType:2
				},
				showEchart:false,
				coinLike:'',
				isFiat:false,
			}
		},
		computed: {
			fontSize() {
				let num = this.$maxFontSize(this.$m(this.totalNum, this.isFiat?2:8))
				return num
			}
		},
		components:{
			AllOrder,
			position,
			areaechart
		},
		mounted() {
			this.getUserCoinFiat()
			this.getUserInfos()
		},
		onReachBottom() {
		    console.log(123);
			if(this.current == 0){
				
			}else{
				this.$refs.AllOrder.loadNum()
			}
		},
		methods: {
			onPopupOption(item) {
				// 这里可以根据item.title或其他字段做跳转
				this.showBottomPopup = false
				switch (item.title) {
					case 'wallet.onChainDeposit':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case 'wallet.quickBuy':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case 'wallet.c2c':
						this.nav_to('symbol')
						break
					case 'wallet.flashSwap':
						this.nav_to('swap')
						break
					default:
						break
				}
				this.$u.toast(this.$t('personal.selected') + this.$t(item.title))
			},
			nav_to(name, params) {
				this.$Router.push({
					name,
					params
				})
			},
			back() {
				this.$Router.back()
			},
			openBottomPopup() {
				this.showBottomPopup = true
			},
		
			async getAllAssetsFiat() {
				let res = await this.$api.userAssetsFiat({
					coin: this.nowsymbol
				})
				if (res.code == 200) {
					this.allassets = res.result.amount
				}
			},
			async getIncreaseRate() {
				let res = await this.$api.increaseRate({
					assetType: 2,
					convertCoin: this.nowsymbol
				})
				if (res.code == 200) {
					this.increaseInfo = res.result
				}
			},
			SetSymbol(e) {
				console.log(e);
				this.nowsymbol = e.name
				this.showcurrencyShow = false
				this.isFiat = e.fiat
				if(this.showEchart){
					this.echartQuery.nowsymbol = e.name
					this.$refs.areaechart.fetchChartData()
				}
				this.getInfo()
				this.getInfo2()
				this.getUnrealizedProfit()
				this.getIncreaseRate()
			},
			toggleRotate() {
				this.rotate = this.rotate === 0 ? 180 : 0;
			},
			
			async getUserCoinFiat() {
				let res = await this.$api.getCoinList({})
				if (res.code == 200) {
					// this.nowsymbol = res.result[0].symbol
					this.userCoins = res.result
					
				} else {
					this.$u.toast(res.msg)
				}
			},
			checkTab(index) {
				this.current = index
			},
			async getInfo() {
				let res = await this.$api.amount({
					assetType: 2,
					convertCoin: this.nowsymbol
					// assetTypeEnum: "ASSET_COIN",
					// symbol: "USD",
				});
				if (res.code == 200) {
					console.log(res)
					this.totalNum = res.result.amount
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getInfo2() {
				let res = await this.$api.amount({
					assetType: 6,
					convertCoin: this.nowsymbol
				});
				if (res.code == 200) {
					console.log(res)
					this.totalNum2 = res.result.amount
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getUnrealizedProfit() {
				let res = await this.$api.unrealizedProfit({
					convertCoin: this.nowsymbol
				});
				if (res.code == 200) {
					console.log(res)
					this.totalNum3 = res.result.convertAmount?res.result.convertAmount:0
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			nav_tract(){
				this.$Router.pushTab({
					name:'Home',
					params:{
						tabIndex:2
					}
				})
			},
			openShowEachart(){
				if(!this.showEchart){
					this.echartQuery.convertCoin = this.nowsymbol
				}
				this.showEchart = !this.showEchart
			},
			//获取喜好币种
			async getUserInfos() {
				let res = await this.$api.getUserInfo()
				if (res.code == 200) {
					this.coinLike = res.result.coinLike
					this.isFiat = res.result.fiat?res.result.fiat:false
					this.nowsymbol = this.coinLike?this.coinLike:'USD'
					this.getInfo()
					this.getIncreaseRate()
					this.balanceList()
					this.getInfo2()
					this.getUnrealizedProfit()
				}
			},
			
		}
	}
</script>

<style lang="scss" scoped>
	.personal_bg {
		min-height: 100vh;
		background: #FEFBFF;
		padding: 0 0 40rpx 0;
		font-family: 'Gilroy-Medium';
		padding-bottom: 100rpx;
	}

	.asset_card {
		margin: 0;
		background: #fff;
		border-radius: 0 0 48rpx 48rpx;
		padding: 48rpx 30rpx 48rpx 30rpx;
		position: relative;
		z-index: 2;
		font-family: 'Gilroy-Medium';
		position: relative;

		.right_icon {
			position: absolute;
			right: 30rpx;
			top: 40rpx;

			image {
				width: 40rpx;
			}
		}

		.asset_card_top {
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			margin: 20rpx 0rpx;

			.asset_card_left {
				.asset_card_labe l {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #BDBDBD;
					font-weight: 400;
					margin-bottom: 12rpx;
				}

				.asset_card_amount_row {
					display: flex;
					align-items: flex-end;
					margin-bottom: 24rpx;
					position: relative;
					margin-top: 16rpx;

					.asset_card_amount {
						font-size: 60rpx;
						font-weight: 800;
						color: #121212;
						line-height: 1;
					}

					.asset_card_unit {
						font-size: 28rpx;
						color: #121212;
						font-weight: 500;
						margin-left: 10rpx;
					}

					.asset_card_arrow_icon {
						margin-left: 6rpx;
						font-size: 22rpx;
						color: #BDBDBD;
						margin-bottom: 5rpx;
					}
				}

				.asset_card_rate {
					font-size: 22rpx;
					color: #BDBDBD;
					font-weight: 400;
					/* margin-bottom: 16rpx; */
				}
			}

			.asset_card_chart {
				width: 154rpx;
				height: 74rpx;
				margin-left: 16rpx;

				.chart_placeholder {
					width: 100%;
					height: 100%;

					image {
						width: 100%;
					}
				}
			}
		}

		.asset_view {
			.title {
				font-size: 28rpx;
				margin-bottom: 28rpx;
				font-weight: 600;
				color: #000;
			}

			.flex_view {
				display: flex;
				justify-content: flex-start;
				align-items: center;

				.item_view {
					width: 50%;

					.label {
						font-size: 24rpx;
						color: rgba(0, 0, 0, 0.50);
						margin-bottom: 20rpx;
					}

					.val {
						font-size: 32rpx;
						font-weight: 600;
						color: #000;
					}
				}
			}
		}

	}

	.action_btns_wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 48rpx;
		background: #fff;
		padding: 24rpx 100rpx;
		margin: 20rpx 0rpx;

		.action_btn_item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.action_btn_icon_wrap {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 12rpx;

				.u-icon {
					font-size: 44rpx !important;
				}
			}

			.action_btn_text {
				font-size: 24rpx;
				color: #121212;
				margin-top: 4rpx;
				font-weight: 500;
			}
		}

		/* .action_btn_item+.action_btn_item {
			margin-left: 24rpx;
		} */
	}

	.bottom_list_wrap { 
		background-color: #fff;
		border-radius: 48rpx 48rpx 0 0;
		min-height: 50vh;
		padding: 48rpx 32rpx;

		.tabs {
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.tab_item {
				font-size: 28rpx;
				color: rgba(0, 0, 0, 0.50);
				font-weight: 600;
				margin-right: 24rpx;

				&.active {
					color: #000;
				}
			}
		}
	}

	.helpoption {
		width: 80*2rpx;
		transition: transform 0.3s ease, opacity 0.3s ease;
		transform-origin: top;
		/* 设置变换的起点为顶部 */
		z-index: 9999;
		position: absolute;
		top: 80rpx;
		right: -50rpx;
		box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;
		max-height: 400rpx;
		overflow-y: auto;
		background: #fff;
		border-radius: 16*2rpx;
		padding: 16*2rpx;
		opacity: 1;
		display: flex;
		align-items: flex-start;
		flex-direction: column;



		&.collapse {
			transform: scaleY(0) translateY(-100%);
			/* 缩小至0，并向上移动 */
			opacity: 0;
		}

		&.expand {
			transform: scaleY(1) translateY(0%);
			/* 恢复到正常大小，并位置恢复 */
			opacity: 1;

		}

		>view {
			width: 100%;
			padding: 15rpx 0;
			text-align: left;

			image {
				width: 40rpx;
				height: 30rpx;
			}

			text {
				font-family: Gilroy-Bold;
				font-weight: 400;
				font-size: 16*2rpx;
				line-height: 19.2*2rpx;
				color: #000;
			}
		}
	}

	.wallet_popup_content {
		padding: 40rpx 0 32rpx 0;

		.wallet_popup_title_row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 32rpx 32rpx 32rpx;

			.wallet_popup_title {
				font-size: 32rpx;
				font-weight: 600;
				color: #121212;
			}
		}

		.wallet_popup_list {
			.wallet_popup_item {
				display: flex;
				align-items: center;
				background: #fff;
				border-radius: 24rpx;
				margin: 0 32rpx 24rpx 32rpx;
				padding: 32rpx 24rpx;
				border: 1rpx solid rgba(0, 0, 0, 0.05);

				.wallet_popup_icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 24rpx;
				}

				.wallet_popup_info {
					flex: 1;
					display: flex;
					flex-direction: column;

					.wallet_popup_item_title {
						font-size: 28rpx;
						color: #121212;
						font-weight: 600;
						margin-bottom: 8rpx;
					}

					.wallet_popup_item_desc {
						font-size: 20rpx;
						color: #BDBDBD;
					}
				}
			}
		}
	}

	.pie_chart_wrap {
		margin: 24rpx 0 0 0;

		.pie_chart_content {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			margin-top: 40rpx;
		}

		.left {
			display: flex;
			justify-content: space-between;
			flex-direction: column;
			height: 240rpx;

			.pie_legend_tip {
				font-size: 20rpx;
				color: rgba(0, 0, 0, 0.5);
			}
		}

		.pie_chart_img {
			width: 140rpx;
			height: 140rpx;
			display: block;
			margin-right: 24rpx;
			margin-left: 8rpx;
		}

		.pie_legend {
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			min-width: 380rpx;

			.pie_legend_row {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;
			}

			.pie_legend_dot {
				width: 24rpx;
				height: 24rpx;
				border-radius: 8rpx;
				margin-right: 10rpx;
				flex-shrink: 0;
			}

			.pie_legend_name {
				font-size: 22rpx;
				color: #222;
				margin-right: auto;
				font-weight: 400;
			}

			.pie_legend_percent {
				font-size: 22rpx;
				color: #BDBDBD;
				font-weight: 400;
				margin-left: 16rpx;
			}

			.pie_legend_tip {
				font-size: 18rpx;
				color: #BDBDBD;
				margin-top: 8rpx;
				font-weight: 400;
				text-align: left;
			}
		}
	}
</style>