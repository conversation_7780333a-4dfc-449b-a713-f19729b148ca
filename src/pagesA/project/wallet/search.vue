<template>
	<view class="body">
        <u-navbar :border-bottom="false" :title="title" :customBack="back">
		</u-navbar>
        <view class="search_box_n">
            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385297482358546432.png" />
            <input
              v-model="searchKeyword"
              placeholder="搜索"
              class="search_input"
              @confirm="onSearch"
            />
          </view>
        <view class="search_list">
          <view class="search_item">
            <image class="search_item_logo" src="https://test-oss.pinkwallet.com/image/1357027952415412224.png" />
            <text class="search_item_name">BTC</text>
            <text class="search_item_price">683.37</text>
            <text class="search_item_rate up">+0.15%</text>
          </view>
          <view class="search_item">
            <image class="search_item_logo" src="https://test-oss.pinkwallet.com/image/1357027952415412224.png" />
            <text class="search_item_name">BTC</text>
            <text class="search_item_price">683.37</text>
            <text class="search_item_rate up">+0.15%</text>
          </view>
        </view>
	</view>
</template>

<script>
export default {
    data() {
        return { 
            title: '搜索',
        }
    },
    methods: {
        back(){
            this.$Router.back()
        }
    }
}
</script>
 
<style lang="scss" scoped>
.body {
  background: #fff;
  min-height: 100vh;
  padding-bottom: 120rpx;
  font-family: PingFang SC;
  padding-top: 60rpx;
}
.search_box_n {
    display: flex;
    justify-content: center;
    position: relative;
    width:690rpx;
    margin:0 auto;
    image {
      position: absolute;
      top: 50%;
      left: 14rpx;
      transform: translateY(-50%);
      width: 52rpx;
      height: 52rpx;
    }
    .search_input {
      text-indent: 76rpx;
      border: none;
      outline: none;
      width: 100%;
      height: 76rpx;
      background: rgba(217, 217, 217, .2);
      border-radius: 34rpx;
      font-size: 28rpx;
    }
  }
.search_list {
  margin-top: 32rpx;
  .search_item {
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    height: 72rpx;
    margin-bottom: 60rpx;
    .search_item_logo {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      margin-right: 16rpx;
      background: #fff;
    }
    .search_item_name {
      font-size: 28rpx;
      color: #222;
      font-weight: 600;
      margin-right: 24rpx;
    }
    .search_item_price {
      font-size: 28rpx;
      color: #222;
      margin-right: 80rpx;
      flex: 1;
      text-align: right;
    }
    .search_item_rate {
      font-size: 24rpx;
      color: #fff;
      background: #19b300;
      border-radius: 12rpx;
      padding: 0 18rpx;
      height: 50rpx;
      line-height: 50rpx;
      font-weight: 500;
      &.up {
        background: #19b300;
      }
      &.down {
        background: #ff3b30;
      }
    }
  }
}
</style>
