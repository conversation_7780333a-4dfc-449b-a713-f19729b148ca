<template>
	<view class="personal_bg">
		<!-- 顶部资产卡片浮层 -->
		<u-navbar :border-bottom="false" :title="title" :customBack="back">
		</u-navbar>
		<view class="asset_card">
			<view class="asset_card_top">
				<view class="asset_card_left">
					<view class="asset_card_label">
						<text class="asset_card_label_text">预计总资产</text>
						<u-icon v-if="eyeoff" @click="eyeoff = !eyeoff"
							name="https://pro-oss.pinkwallet.com/image/1370353025834115072.png" size="36"
							class="asset_card_info_icon" />
						<u-icon v-else @click="eyeoff = !eyeoff"
							name="https://pro-oss.pinkwallet.com/image/1370353145610854400.png" size="36"
							class="asset_card_info_icon" />

					</view>
					<view class="asset_card_amount_row">
						<text class="asset_card_amount" v-if="eyeoff">***</text>
						<text class="asset_card_amount" :style="{ fontSize: $maxFontSize($m(totalNum,isFiat?2:8)) + 'rpx' }" v-else>{{$m(totalNum,isFiat?2:8)}} </text>
						<view>
							<text class="asset_card_unit"
								@click="toggleRotate(),showcurrencyShow = !showcurrencyShow">{{nowsymbol}}</text>
							<u-icon name="arrow-down" size="24" color="#000000"
								:style="{ transform: `rotate(${rotate}deg)` }" class="asset_card_arrow_icon"
								@click="showcurrencyShow = !showcurrencyShow" />
						</view>
						<transition name="expand-slide">
							<view class="helpoption" v-show="showcurrencyShow">
								<view v-for="(item, index) in userCoins" :key="index" class="Roptions"
									@click.stop="SetSymbol(item)">
									<text :style="{ color: nowsymbol == item.name ? '#008E28' : '' }">{{ item.name
										}}</text>
								</view>
							</view>
						</transition>
					</view>
					<view class="asset_card_rate">{{$m(increaseInfo.incrementAmount,isFiat?2:8)}} {{nowsymbol}}
						<text>({{increaseInfo.increaseRate*100>0?`+${increaseInfo.increaseRate}`:increaseInfo.increaseRate}}%)昨日</text>
					</view>
				</view>
				<view class="asset_card_chart">
					<view class="chart_placeholder" @click="openShowEachart()">
						<image src="https://pro-oss.pinkwallet.com/image/1387826531387662336.png" mode="widthFix">
						</image>
					</view>
				</view>
			</view>
			<view class="echart_view" v-if="showEchart">
				<areaechart ref="areaechart" :type="echartType" :query="echartQuery"> </areaechart>
			</view>
			<!-- //添加饼图 -->
			<view class="pie_chart_wrap">
				<view class="pie_chart_content">
					<view class="left">
						<view style="height: 148rpx">
							<l-echart ref="chart"></l-echart>
						</view>
						<view class="pie_legend_tip">* 资产饼状图不含负债</view>
					</view>
					<view class="pie_legend">
						<view class="pie_legend_row" v-for="(item, idx) in accountList" :key="idx">
							<view class="pie_legend_dot" :style="{background: colorOptions[item.accountTypeChartEnums]}"></view>
							<text class="pie_legend_name">{{nameOptions[item.accountTypeChartEnums]}}</text>
							<text class="pie_legend_percent">{{item.proportion}}%</text>
						</view>
					</view>
				</view>
			</view>

		</view>

		<!-- 操作按钮区块 -->
		<view class="action_btns_wrap">
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="openBottomPopup()">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386806647358840832.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">添加资金</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="onPopupOption({title:'转账'})">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807020060499968.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">转账</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="nav_to('transfer')">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807124683218944.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">划转</text>
			</view>
		</view>



		<!-- 资产信息区块 -->
		<view class="asset_info_card">
			<view class="asset_info_header">
				<text class="asset_info_title">币种</text>
				<view class="asset_info_search_filter">
					<template v-if="!searchActive">
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386810093399007232.png" size="36"
							color="#121212" @click="searchActive = true" />
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386809693031718912.png" size="36"
							color="#121212" style="margin-left: 24rpx;" />
					</template>
					<template v-else>
						<view class="asset_info_searchbox">
							<u-icon name="https://pro-oss.pinkwallet.com/image/1386810093399007232.png" size="32"
								color="#999" />
							<input class="asset_info_searchinput" v-model="searchText" placeholder="Search" />
						</view>
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386809693031718912.png" size="36"
							color="#121212" style="margin-left: 24rpx;" />
					</template>
				</view>
			</view>
			<view class="asset_info_list">
				<view class="asset_info_item" v-for="(item,index) in symbolBalanceList" v-if="index<showNum"
					@click="nav_to('bDetails',{symbol:item.symbol,symbolIcon:item.icon,fiat:item.fiat})">
					<view class="asset_info_logo_wrap">
						<image class="asset_info_logo" :src="item.icon?item.icon:''" />
					</view>
					<view class="asset_info_main">
						<text class="asset_info_name">{{item.symbol}}</text>
						<text class="asset_info_rate">{{item.percent?`${item.percent}%`:'0%'}}</text>
					</view>
					<view class="asset_info_value">
						<text class="asset_info_num">${{$m(item.balance,isFiat?2:8)}}</text>
						<text class="asset_info_usd">${{$m(item.convertAmount,isFiat?2:8)}}</text>
					</view>
				</view>
			</view>
			<noata v-if="symbolBalanceList.length ==0"></noata>
			<view class="asset_info_more" v-if="showAll">
				<view class="asset_info_more_btn" @click="showAll = false,showNum = 100"
					v-if="symbolBalanceList.length !=0">+ View More</view>
			</view>
			

		</view>
		<!-- 底部弹窗 -->
		<u-popup v-model="showBottomPopup" mode="bottom" border-radius="32" height="auto">
			<view class="bottom-popup-content">
				<view class="popup-title-row">
					<text class="popup-title">选择充值方式</text>
					<u-icon name="close" size="36" color="#999" @click="showBottomPopup = false" />
				</view>
				<view class="popup-option" v-for="item in popupOptions" :key="item.title" @click="onPopupOption(item)">
					<image :src="item.icon" class="popup-option-icon" />
					<view class="popup-option-info">
						<text class="popup-option-title">{{ item.title }}</text>
						<text class="popup-option-desc">{{ item.desc }}</text>
					</view>
					<u-icon name="https://pro-oss.pinkwallet.com/image/1387134736018268160.png" size="12" />
				</view>
			</view>
		</u-popup>
		<u-popup v-model="showWalletPopup" mode="bottom" border-radius="32" height="auto">
			<view class="wallet_popup_content">
				<view class="wallet_popup_title_row">
					<text class="wallet_popup_title">选择钱包</text>
					<u-icon name="close" size="36" color="#999" @click="showWalletPopup = false" />
				</view>
				<view class="wallet_popup_list">
					<view class="wallet_popup_item" v-for="(item, idx) in walletList" :key="idx"
						@click="onSelectWallet(item)">
						<!-- <image :src="item.icon" class="wallet_popup_icon" /> -->
						<u-icon :name="item.icon" size="68" style="margin-right: 20rpx;" />
						<view class="wallet_popup_info">
							<text class="wallet_popup_item_title">{{item.title}}</text>
							<text class="wallet_popup_item_desc">{{item.desc}}</text>
						</view>
						<u-icon name="arrow-right" size="28" color="#BDBDBD" />
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import * as echarts from '@/uni_modules/lime-echart/static/echarts.min'
	import areaechart from '../../../components/echartCom/area-echart.vue'
	import noata from '../../../components/public/noata2.vue'
	export default {
		data() {
			return {
				searchActive: false,
				searchText: '',
				showBottomPopup: false,
				popupOptions: [{
					title: '链上充值',
					desc: '将其他交易平台/钱包的加密货币存入PinkWallet账户',
					icon: 'https://pro-oss.pinkwallet.com/image/1387135206002614272.png'
				},
				{
					title: '快捷买币',
					desc: '法币快速购买数字货币，购买成功后存入PinkWallet账户',
					icon: 'https://pro-oss.pinkwallet.com/image/1387135325519306752.png'
				},
				{
					title: 'C2C交易',
					desc: '点对点交易，价格从优，支持多种本地支付方式',
					icon: 'https://pro-oss.pinkwallet.com/image/1387135386655481856.png'
				}
				],
				eyeoff: false,
				userCoins: [],
				showcurrencyShow: false,
				nowsymbol: '',
				rotate: 0,
				totalNum: 0,
				showWalletPopup: false,
				walletList: [
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187064594948096.png',
						title: '资金账户（加密货币）',
						desc: '≈40,999.12345678 USD（划转到U本位合约账户）',
						tag: '加密货币'
					},
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187124351197184.png',
						title: 'U本位合约账户（加密货币）',
						desc: '≈40,999.12345678 USD（划转到资金账户）',
						tag: '加密货币'
					},
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187172954791936.png',
						title: '资金账户（法币）',
						desc: '≈40,999.12345678 USD（仅限法币账户内转）',
						tag: '法币'
					},
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187222795706368.png',
						title: '美股股票账户（法币）',
						desc: '40,999.12345678 USD（划转到港股资金账户）',
						tag: '法币'
					},
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187284028350464.png',
						title: '港股股票账户（法币）',
						desc: '40,999.12345678 HKD（划转到美股资金账户）',
						tag: '法币'
					}
				],
				title: '钱包账户',
				pieLegendList: [
					{ name: '资金账户(加密)', color: '#F9A8C4', percent: '25.21' },
					{ name: '合约账户(加密)', color: '#F9C97B', percent: '25.21' },
					{ name: '资金账户(法币)', color: '#FFD36E', percent: '25.21' },
					{ name: '美股账户(法币)', color: '#4AC18E', percent: '25.21' },
					{ name: '港股账户(法币)', color: '#7B8DF9', percent: '25.21' },
				],
				increaseInfo: {},
				globalOption: {
					tooltip: {
						trigger: 'item'
					},
					legend: {
						top: '5%',
						left: 'center'
					},
					series: [
						{
							name: 'Access From',
							type: 'pie',
							radius: ['100%', '30%'],
							avoidLabelOverlap: false,
							label: {
								show: false,
								position: 'center'
							},
							emphasis: {
								label: {
									show: true,
									fontSize: 40,
									fontWeight: 'bold'
								}
							},
							labelLine: {
								show: false
							},
							data: []
						}
					]
				},
				accountList:[],
				symbolBalanceList:[],
				echartType:'personal',
				echartQuery:{
					convertCoin:'',
					assetType:0
				},
				showEchart:false,
				coinLike:'',
				isFiat:false,
				showNum: 6,
				showAll:true
			}
		},
		components:{
			noata
		},
		computed: {
			statusOptions() {
				return [
					{ value: 1, label: this.$t("Record.Status.Init") },
					{ value: 2, label: this.$t("Record.Status.InProgress") },
					{ value: 3, label: this.$t("Record.Status.Completed") },
					{ value: 4, label: this.$t("Record.Status.Failed") }
				]
			},
			nameOptions(){
				return {
					'FUND_FAIT_ALL':'资金账户(法币)',
					'FUND_ENCRYPTION_ALL':'资金账户(加密)',
					'STOCK_USD_ALL':'美股账户(法币)',
					'STOCK_HKD_ALL':'港股账户(法币)',
					'CONTRACT_COIN_ALL':'合约账户(加密)'
				}
			},
			colorOptions(){
				return {
					'FUND_FAIT_ALL':'#F3BA2F',
					'FUND_ENCRYPTION_ALL':'#FF82A3',
					'STOCK_USD_ALL':'#26A17B',
					'STOCK_HKD_ALL':'#627EEA',
					'CONTRACT_COIN_ALL':'#F7931A'
				}
			}
			
		},
		components:{
			areaechart
		},
		onLoad() {
			this.getUserInfos()
			this.getUserCoinFiat()
			
		},
		methods: {
			onPopupOption(item) {
				// 这里可以根据item.title或其他字段做跳转
				this.showBottomPopup = false
				this.$u.toast('你选择了：' + item.title)
				switch (item.title) {
					case '链上充值':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case '快捷买币':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case 'C2C交易':
						this.nav_to('symbol')
						break
					case '转账':
						this.nav_to('symbol', {
							targetPage: 'withdraw'
						})
						break
					default:
						break
				}
			},
			nav_to(name, params) {
				this.$Router.push({
					name,
					params
				})
			},
			back() {
				this.$Router.back()
			},
			openBottomPopup() {
				this.showBottomPopup = true
			},

			async getInfo() {
				let res = await this.$api.amount({
					assetType: 0,
					convertCoin: this.nowsymbol
					// assetTypeEnum: "ASSET_COIN",
					// symbol: "USD",
				});
				if (res.code == 200) {
					console.log(res)
					this.totalNum = res.result.amount
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getAllAssetsFiat() {
				let res = await this.$api.userAssetsFiat({
					coin: this.nowsymbol
				})
				if (res.code == 200) {
					this.allassets = res.result.amount
				}
			},
			SetSymbol(e) {
				console.log(e);
				this.nowsymbol = e.name
				this.showcurrencyShow = false
				this.isFiat = e.fiat
				// this.getAllAssetsFiat()
				// this.getLine()
				// this.init()
				if(this.showEchart){
					this.echartQuery.nowsymbol = e.name
					this.$refs.areaechart.fetchChartData()
				}
				this.getIncreaseRate()
				this.getInfo()
				this.balanceList()
				this.getAccountChart()
			},
			toggleRotate() {
				this.rotate = this.rotate === 0 ? 180 : 0;
			},
			async getUserCoinFiat() {
				let res = await this.$api.getCoinList({})
				if (res.code == 200) {
					// this.nowsymbol = res.result[0].symbol
					this.userCoins = res.result
					
				} else {
					this.$u.toast(res.msg)
				}
			},
			async getIncreaseRate() {
				let res = await this.$api.increaseRate({
					assetType: 0,
					convertCoin: this.nowsymbol
				})
				if (res.code == 200) {
					this.increaseInfo = res.result
				}
			},
			async getAccountChart() {
				let res = await this.$api.accountChart({
					convertCoin: this.nowsymbol
				})
				if (res.code == 200) {
					console.log(res.result)
					let data = []
					res.result.forEach((item)=>{
						data.push({
							name:'',
							value:item.amount,
							 itemStyle: { color: this.colorOptions[item.accountTypeChartEnums] }
						})
					})
					res.result.forEach((item)=>{
						if(item.proportion){
							if(item.proportion>0&&item.proportion<0.01){
								
								item.proportion = '<0.01'
							}else{
								console.log(222)
								item.proportion = item.proportion.toFixed(2)
							}
						}
					})
					this.accountList = res.result
					this.init(data)
				}
			},
			async init(data){
				this.$refs.chart.init(echarts, chart => {
					let option = {
						series: [
							{
								name: '访问来源',
								type: 'pie',
								radius: ['95%', '75%'],
								avoidLabelOverlap: false,
								label: {
									show: false,
									position: 'center'
								},
								labelLine: {
									show: false
								},
								emphasis: {
									disabled: true // 禁用高亮
								},
								data: data
							}
						]
					}
					chart.setOption(option);
				});
				const lEchart = await this.$refs.chart.init(echarts);
			},
			openWalletPopup() {
				this.showWalletPopup = true
			},
			onSelectWallet(item) {
				this.showWalletPopup = false
				this.$u.toast('你选择了：' + item.title)
			},
			getStatusLabel(status) {
				const match = this.statusOptions.find(opt => opt.value === status)
				return match ? match.label : '--'
			},
			async balanceList() {
				this.loading = true
				let res = await this.$api.assetCoinBalanceList({
					account: 'FUNDS',
					fiat: 0,
					pageNum: 1,
					pageSize: 100,
					convertCoin: this.nowsymbol,
					symbol:this.searchSymbol,
				})
				if (res.code == 200) {
					res.result.data.forEach((item)=>{
						if(item.percent){
							if(item.percent>0&&item.percent<0.01){
								item.percent = '<0.01'
							}else{
								console.log(222)
								item.percent = item.percent.toFixed(2)
							}
						}
					})
					this.symbolBalanceList = res.result.data
				} else {
					this.loading = false
					this.$u.toast(res.msg)
				}
			},
			openShowEachart(){
				if(!this.showEchart){
					this.echartQuery.convertCoin = this.nowsymbol
				}
				this.showEchart = !this.showEchart
			},
			//获取喜好币种
			async getUserInfos() {
				let res = await this.$api.getUserInfo()
				if (res.code == 200) {
					this.coinLike = res.result.coinLike
					this.isFiat = res.result.fiat?res.result.fiat:false
					this.nowsymbol = this.coinLike?this.coinLike:'USD'
					this.getInfo()
					this.getIncreaseRate()
					this.balanceList()
					this.getAccountChart()
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.personal_bg {
		min-height: 100vh;
		background: #FEFBFF;
		padding: 0 0 40rpx 0;
		font-family: 'PingFang SC';
		padding-bottom: 100rpx;
	}

	.asset_card {
		margin: 0;
		background: #fff;
		border-radius: 0 0 48rpx 48rpx;
		padding: 48rpx 30rpx 48rpx 30rpx;
		position: relative;
		z-index: 2;
		font-family: 'PingFang SC';
		position: relative;

		.right_icon {
			position: absolute;
			right: 30rpx;
			top: 40rpx;

			image {
				width: 40rpx;
			}
		}

		.asset_card_top {
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			margin: 20rpx 0rpx;

			.asset_card_left {
				.asset_card_labe l {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #BDBDBD;
					font-weight: 400;
					margin-bottom: 12rpx;
				}

				.asset_card_amount_row {
					display: flex;
					align-items: flex-end;
					margin-bottom: 8rpx;
					position: relative;

					.asset_card_amount {
						font-size: 60rpx;
						font-weight: 800;
						color: #121212;
						line-height: 1;
					}

					.asset_card_unit {
						font-size: 28rpx;
						color: #121212;
						font-weight: 500;
						margin-left: 10rpx;
					}

					.asset_card_arrow_icon {
						margin-left: 6rpx;
						font-size: 22rpx;
						color: #BDBDBD;
						margin-bottom: 5rpx;
					}
				}

				.asset_card_rate {
					font-size: 22rpx;
					color: #BDBDBD;
					font-weight: 400;
					/* margin-bottom: 16rpx; */
				}
			}

			.asset_card_chart {
				width: 154rpx;
				height: 74rpx;
				margin-left: 16rpx;

				.chart_placeholder {
					width: 100%;
					height: 100%;

					image {
						width: 100%;
					}
				}
			}
		}

		.asset_card_account_row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 40rpx;

			.asset_card_account_left {
				display: flex;
				flex-direction: column;

				.asset_card_account_label {
					font-size: 28rpx;
					color: #121212;
					font-weight: 700;
				}

				.asset_card_account_date {
					font-size: 20rpx;
					color: #BDBDBD;
					font-weight: 400;
					margin-top: 2rpx;
				}
			}

			.asset_card_account_right {
				display: flex;
				align-items: center;

				.asset_card_account_value {
					font-size: 28rpx;
					color: #121212;
					font-weight: 700;
				}

				.asset_card_account_percent {
					font-size: 20rpx;
					color: #BDBDBD;
					margin-left: 8rpx;
				}

				.asset_card_account_arrow {
					margin-left: 8rpx;
					font-size: 22rpx;
					color: #BDBDBD;
				}
			}
		}
	}

	.action_btns_wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 48rpx;
		background: #fff;
		padding: 24rpx 100rpx;
		margin: 20rpx 0rpx;

		.action_btn_item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.action_btn_icon_wrap {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 12rpx;

				.u-icon {
					font-size: 44rpx !important;
				}
			}

			.action_btn_text {
				font-size: 24rpx;
				color: #121212;
				margin-top: 4rpx;
				font-weight: 500;
			}
		}

		/* .action_btn_item+.action_btn_item {
			margin-left: 24rpx;
		} */
	}

	.trade_record_card {
		background: #fff;
		border-radius: 48rpx;
		padding: 32rpx 32rpx 12rpx 32rpx;

		.trade_record_title {
			font-size: 28rpx;
			font-weight: bold;
			color: #121212;
			margin-bottom: 20rpx;
		}

		.trade_record_list {
			.trade_record_item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;

				.trade_record_type {
					font-size: 24rpx;
					color: #121212;
				}

				.trade_record_status {
					font-size: 22rpx;
					color: #BDBDBD;
				}
			}

			.trade_record_item:last-child {
				border-bottom: none;
			}
		}
	}

	.asset_info_card {
		background: #fff;
		border-radius: 48rpx;
		padding: 48rpx 32rpx 48rpx 32rpx;
		margin-top: 20rpx;

		.asset_info_header {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.asset_info_title {
				font-size: 28rpx;
				font-weight: bold;
				color: #121212;
			}

			.asset_info_search_filter {
				display: flex;
				align-items: center;

				.asset_info_searchbox {
					display: flex;
					align-items: center;
					background: #f5f5f5;
					border-radius: 24rpx;
					padding: 0 24rpx;
					height: 56rpx;

					.asset_info_searchinput {
						border: none;
						background: transparent;
						font-size: 26rpx;
						color: #121212;
						margin-left: 12rpx;
						width: 180rpx;
					}
				}
			}
		}

		.asset_info_list {
			.asset_info_item {
				display: flex;
				align-items: center;
				padding: 20rpx 0;

				.asset_info_logo_wrap {
					width: 56rpx;
					height: 56rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;

					.asset_info_logo {
						width: 38rpx;
						height: 38rpx;
					}
				}

				.btc_bg {
					background: #F7931A;
				}

				.eth_bg {
					background: #627EEA;
				}

				.usdt_bg {
					background: #26A17B;
				}

				.bnb_bg {
					background: #F3BA2F;
				}

				.asset_info_main {
					flex: 1;
					display: flex;
					flex-direction: column;

					.asset_info_name {
						font-size: 26rpx;
						color: #121212;
					}

					.asset_info_rate {
						font-size: 22rpx;
						color: #BDBDBD;
					}
				}

				.asset_info_value {
					display: flex;
					flex-direction: column;
					align-items: flex-end;

					.asset_info_num {
						font-size: 26rpx;
						color: #121212;
					}

					.asset_info_usd {
						font-size: 22rpx;
						color: #BDBDBD;
					}
				}
			}

			.asset_info_item:last-child {
				border-bottom: none;
			}
		}

		.asset_info_more {
			display: flex;
			justify-content: center;
			margin: 20rpx 0 0 0;
		
			.asset_info_more_btn {
				width: 92%;
				background: #fff;
				color: #121212;
				border: 1rpx solid rgba(0, 0, 0, 0.05);
				border-radius: 18rpx;
				font-size: 24rpx;
				text-align: center;
				font-weight: 500;
				height: 88rpx;
				line-height: 88rpx;
			}
		}
	}

	.bottom-popup-content {
		padding: 40rpx 0 32rpx 0;

		.popup-title-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 32rpx 32rpx 32rpx;

			.popup-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #121212;
			}
		}

		.popup-option {
			display: flex;
			align-items: center;
			background: #fff;
			border-radius: 24rpx;
			margin: 0 32rpx 24rpx 32rpx;
			padding: 32rpx 24rpx;
			border: 1rpx solid rgba(0, 0, 0, 0.05);

			.popup-option-icon {
				width: 60rpx;
				height: 60rpx;
				margin-right: 24rpx;
			}

			.popup-option-info {
				flex: 1;
				display: flex;
				flex-direction: column;

				.popup-option-title {
					font-size: 28rpx;
					color: #121212;
					font-weight: 600;
					margin-bottom: 8rpx;
				}

				.popup-option-desc {
					font-size: 20rpx;
					color: rgba(0, 0, 0, 0.50);
				}
			}
		}
	}

	.helpoption {
		width: 80*2rpx;
		transition: transform 0.3s ease, opacity 0.3s ease;
		transform-origin: top;
		/* 设置变换的起点为顶部 */
		z-index: 9999;
		position: absolute;
		top: 80rpx;
		right: -50rpx;
		box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;
		max-height: 400rpx;
		overflow-y: auto;
		background: #fff;
		border-radius: 16*2rpx;
		padding: 16*2rpx;
		opacity: 1;
		display: flex;
		align-items: flex-start;
		flex-direction: column;



		&.collapse {
			transform: scaleY(0) translateY(-100%);
			/* 缩小至0，并向上移动 */
			opacity: 0;
		}

		&.expand {
			transform: scaleY(1) translateY(0%);
			/* 恢复到正常大小，并位置恢复 */
			opacity: 1;

		}

		>view {
			width: 100%;
			padding: 15rpx 0;
			text-align: left;

			image {
				width: 40rpx;
				height: 30rpx;
			}

			text {
				font-family: Gilroy-Bold;
				font-weight: 400;
				font-size: 16*2rpx;
				line-height: 19.2*2rpx;
				color: #000;
			}
		}
	}

	.wallet_popup_content {
		padding: 40rpx 0 32rpx 0;

		.wallet_popup_title_row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 32rpx 32rpx 32rpx;

			.wallet_popup_title {
				font-size: 32rpx;
				font-weight: 600;
				color: #121212;
			}
		}

		.wallet_popup_list {
			.wallet_popup_item {
				display: flex;
				align-items: center;
				background: #fff;
				border-radius: 24rpx;
				margin: 0 32rpx 24rpx 32rpx;
				padding: 32rpx 24rpx;
				border: 1rpx solid rgba(0, 0, 0, 0.05);

				.wallet_popup_icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 24rpx;
				}

				.wallet_popup_info {
					flex: 1;
					display: flex;
					flex-direction: column;

					.wallet_popup_item_title {
						font-size: 28rpx;
						color: #121212;
						font-weight: 600;
						margin-bottom: 8rpx;
					}

					.wallet_popup_item_desc {
						font-size: 20rpx;
						color: #BDBDBD;
					}
				}
			}
		}
	}

	.pie_chart_wrap {
		margin: 24rpx 0 0 0;

		.pie_chart_content {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			margin-top: 40rpx;
		}

		.left {
			display: flex;
			justify-content: space-between;
			flex-direction: column;
			height: 240rpx;

			.pie_legend_tip {
				font-size: 20rpx;
				color: rgba(0, 0, 0, 0.5);
			}
		}

		.pie_chart_img {
			width: 140rpx;
			height: 140rpx;
			display: block;
			margin-right: 24rpx;
			margin-left: 8rpx;
		}

		.pie_legend {
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			min-width: 380rpx;

			.pie_legend_row {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;
			}

			.pie_legend_dot {
				width: 24rpx;
				height: 24rpx;
				border-radius: 8rpx;
				margin-right: 10rpx;
				flex-shrink: 0;
			}

			.pie_legend_name {
				font-size: 22rpx;
				color: #222;
				margin-right: auto;
				font-weight: 400;
			}

			.pie_legend_percent {
				font-size: 22rpx;
				color: #BDBDBD;
				font-weight: 400;
				margin-left: 16rpx;
			}

			.pie_legend_tip {
				font-size: 18rpx;
				color: #BDBDBD;
				margin-top: 8rpx;
				font-weight: 400;
				text-align: left;
			}
		}
	}
</style>