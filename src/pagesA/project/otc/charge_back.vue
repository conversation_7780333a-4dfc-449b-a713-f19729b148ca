<template>
    <view class="container">
        <!-- 标题 -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="Deposit" :custom-back="back">
            <view slot="right" class="search-box" @click="nav_to('Record', 'swap')">
                <image
                    src="https://pro-oss.pinkwallet.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
            </view>
        </u-navbar>

        <!-- 充值币种选择 -->
        <view class="form-item flex_between">
            <text class="label">充值币种：</text>
            <view class="label" @click="isShowType = true">
                <!-- "https://pro-oss.pinkwallet.com/image/20250201/90aa643a1d1a8b6ec2729326c55513dc_200x200.png" -->
                {{ currency }}
            </view>
        </view>

        <!-- 充值网络选择 -->
        <view class="form-item flex_between">
            <text class="label">选择充值网络：</text>
            <view class="label" style="color:#ccc" @click="isShownetwork = true">{{ network ||
                '请点击选择network' }}</view>

        </view>

        <!-- 二维码展示 -->
        <view class=" flex">
            <!-- 0x3a4f7b9c2d1e8f56 -->
            <view class="code">
                <uv-qrcode ref="qrcodes" canvas-id="qrcodes" class="qr-code" :value="qrcodeUrl" :size="codeSize"
                    :options="options"></uv-qrcode>
            </view>
        </view>

        <!-- 地址字段 -->
        <view class="form-item flex_between">
            <text class="label">地址：</text>
            <!-- <u-input v-model="address" placeholder="请输入充值地址" /> -->
            <view class="copy_item">
                <text class="address">{{ address }}</text>
                <image @click="copyAddress" src="@/static/imgs/public/copy.png" mode="aspectFill"></image>
            </view>
        </view>

        <!-- Memo字段 -->
        <view class="form-item flex_between">
            <text class="label">Memo：</text>
            <view class="copy_item">
                <u-input input-align="right" class="input" v-model="memo" placeholder="请输入Memo" />
                <image @click="copyMemo" src="@/static/imgs/public/copy.png" mode="aspectFill"></image>
            </view>
        </view>
        <view class="form-item flex_right">
            <text>必须填入，若未填写，则会造成资金损失。</text>
        </view>


        <view class="form-item flex_between text">
            该地址仅用于 USDT 充币，请勿用于发送铭文、NFT等其他资产任何非 USDT 资产一经发送将无法到账或退回。
        </view>

        <!-- 最小充值金额 -->
        <view class="form-item flex_between">
            <text class="label">最小充值金额：</text>
            <text style="font-size: 26rpx;color: #121212;">10.0000 USDT</text>
        </view>

        <!-- 复制按钮 -->
        <!-- <view class="button-group">
            <u-button class="btn" type="primary" @click="copyAddress">复制</u-button>
            <u-button class="btn" type="primary" @click="copyMemo">复制</u-button>
        </view> -->

        <!-- 预计到账时间 -->
        <view class="form-item flex_between">
            <text class="label">预计到账时间：</text>
            <text style="font-size: 26rpx;color: #121212;">10分钟</text>
        </view>

        <view class="form-item flex" style="margin-top: 300rpx;">
            <text class="label" style="color: #1989fa;">在线客服</text>
        </view>

        <u-select v-model="isShowType" mode="single-column" :list="currencyOptions" @confirm="confirm"></u-select>
        <u-select v-model="isShownetwork" mode="single-column" :list="networkOptions"
            @confirm="confirmnetwork"></u-select>

    </view>
</template>

<script>
export default {
    data() {
        return {
            isShowType: false,
            isShownetwork: false,
            qrcodeUrl: "******************************************",
            currency: 'USDT',
            network: '',
            address: '******************************************',
            memo: 'memo demo',
            currencyOptions: [{
                value: 'USDT',
                label: 'USDT'
            }
                // , {
                //     value: 'BTC',
                //     label: 'BTC'
                // }, {
                //     value: 'ETH',
                //     label: 'ETH'
                // }
            ],
            networkOptions: [{
                value: 'ERC-20',
                label: 'ERC-20'
            }, {
                value: 'TRC-20',
                label: 'TRC-20'
            },
            {
                value: 'ETH',
                label: 'ETH'
            }
            ],
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            codeSize: 240,

        };
    },
    onLoad() {
        this.codeSize = 170 * (uni.upx2px(200) / 100)
    },
    methods: {
        confirm(e) {
            this.currency = e[0].value
        },
        confirmnetwork(e) {
            this.network = e[0].value
        },
        nav_charge_record() {
            this.$Router.push({
                name: 'Record',
                params:{
                    type:'deposit'
                }
            })
        },
        copyAddress() {
            uni.setClipboardData({
                data: this.address,
                success() {
                    uni.showToast({
                        title: '复制成功',
                        icon: 'none',
                    });
                },
            });
        },
        copyMemo() {
            uni.setClipboardData({
                data: this.memo,
                success() {
                    uni.showToast({
                        title: '复制成功',
                        icon: 'none',
                    });
                },
            });
        },
        back() {
            this.$Router.back();
        }
    },
};
</script>

<style scoped>
::v-deep .u-input__input {
    color: #121212 !important;
}

::v-deep .u-select__body__picker-view__item {
    color: #e6f0ff !important;
}

::v-deep .u-select__header {
    background-color: #121212;
}

::v-deep .u-select__body__picker-view__item {
    color: var(--message-box-point-color);
}

::v-deep .uni-picker-view-mask {
    height: 0;
}

::v-deep .u-select__body {
    background-color: #121212;
}

.flex {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 80rpx 0;
}

.flex_right {
    margin-top: -40rpx;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 24rpx;
    color: #FF5567;
}

.flex_between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-box {
    font-family: HarmonyOS Sans SC;
    font-weight: 400;
    font-size: 24rpx;
    /* color: #color: #121212; */
    color: #121212;
    margin-right: 42rpx;
}

.container {
    padding: 20px;
    color: #fff;
    /* 文字颜色改为白色 */
}

.header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.title {
    font-size: 24px;
    color: #121212;
    /* 标题文字为白色 */
}

.record {
    font-size: 16px;
    color: #007aff;
}

.text {
    font-size: 24rpx;
    color: rgba(0, 0, 0, .5);
    line-height: 30rpx;
}

.form-item {
    margin-bottom: 20px;

    .address {
        font-size: 24rpx;
        color: #000;
    }

    .copy_item {
        display: flex;
        align-items: center;

        image {
            width: 24rpx;
            height: 26rpx;
            margin-left: 15rpx;
        }
    }
}

.code {
    /* margin: 0 auto; */
    width: fit-content;
    /* border: 10rpx solid #ffffff; */
    padding: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
}

.label {
    font-size: 14px;
    color: #121212;
    /* 标签文字为白色 */
}

.input {
    color: #121212;
}

.qr-code {
    /* height: 200px; */
    /* margin-top: 10px; */
}

.button-group {
    display: flex;
    justify-content: space-between;
}

.btn {
    width: 48%;
}
</style>