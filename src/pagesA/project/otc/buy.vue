<template>
    <!-- <view class="openCoverImg" v-show="isAdvertising"> -->
    <!-- <image :src="coverImg" mode="widthFix"></image>
		<view class="right_view" @click="clickSkip()">
			跳过 <text v-if="count>0">{{count}}</text>
		</view> -->
    <!-- </view> -->

    <view class="onboarding">
        <view class="barHeight"></view>

        <view class="lang">
            <view class="language" @click.stop="toggleRotateLanguage">
                <image class="global"
                    src="https://pro-oss.pinkwallet.com/image/20250410/ca536e196146966e840ad354eb75a102_64x65.png" />
                <text class="lang">{{ lang }}</text>
                <image class="down arrow" :class="{ rotated: isLangRotated }"
                    src="https://pro-oss.pinkwallet.com/image/20250410/e2be0b1ab8b96b6009c5e1cb8459ccb2_56x57.png" />

                <transition name="expand-slide">
                    <view class="helpoption" v-show="showLang">
                        <view v-for="(item, index) in LangList" :key="index" class="Roptions"
                            @click.stop="SetLang(item)">
                            <text>{{ item.name }}</text>
                        </view>
                    </view>
                </transition>
            </view>
        </view>

        <swiper class="swiper-wrapper" :indicator-dots="false" :autoplay="false" circular="false" :current="current"
            @change="onSwiperChange">
            <swiper-item class="swiper-item" v-for="(item, index) in pages" :key="index">
                <view class="text-area">
                    <view class="title">{{ item.title }}</view>
                    <view class="desc">
                        <view class="item">{{ item.description }}</view>
                        <view class="item" v-if="item.desc2">{{ item.desc2 }}</view>
                        <view class="item" v-if="item.desc3">{{ item.desc3 }}</view>
                    </view>
                </view>
                <view class="image-area flex_all">
                    <view class="bgfff"></view>
                    <view class="bgfffdown"></view>
                    <view class="bgfffleft"></view>
                    <view class="bgfffright"></view>

                    <image class="main-image" :style="{ width: item.w + 'rpx' }" :src="item.image" mode="widthFix">
                    </image>
                    <image class="lock" v-if="index == 0"
                        src="https://pro-oss.pinkwallet.com/image/********/a046a066192807b27763a9fbe51ca089_174x214.png" />
                </view>
            </swiper-item>
        </swiper>
        <view class="dot-indicator">
            <view v-for="(dot, index) in pages.length" :key="index" class="dot" :class="{ active: index == current }">
            </view>
        </view>
        <view class="button-group" @click="nav_to('index')">
            <button class="btn-start flex_all">Get Started</button>
            <view class="btn-skip" v-show="pages.length != current + 1">Skip</view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            current: 0,
            pages: [
                {
                    title: 'Fully Compliant',
                    description:
                        'Licensed in Europe (MFSA), USA (MSB), Canada (MSB), New Zealand (FSP), and Hong Kong (MSO).',
                    desc2: "  Backed by a $5 Billion insurance guarantee.",
                    desc3: "  Secure asset custody with Fireblocks",
                    image: "https://pro-oss.pinkwallet.com/image/********/2d9cbee3f9d6c68d440b229bdf9903f0_1243x1161.png",
                    w: "620",
                },
                {
                    title: 'Open an Offshore Bank Account Remotely',
                    description:
                        'Instant setup – Open an account in just 3 minutes  ',
                    desc2: " Minimal documentation required.",
                    desc3: " Completely free – No setup or management fees",
                    image:
                        "https://pro-oss.pinkwallet.com/image/********/df3fcda8f1fe8894cc1daec924ffe9af_1173x1310.png",
                    w: "586",

                },
                {
                    title: 'Best Rates Guaranteed',
                    description:
                        'We compare prices across 30+ liquidity providers to ensure you get the best exchange rates.',
                    image:
                        "https://pro-oss.pinkwallet.com/image/********/1395c8cdc9a44fb39450e24d42ca17ea_1223x1441.png"
                    ,
                    w: "606",

                },
                {
                    title: 'Buy Crypto Instantly',
                    description: 'Purchase crypto with credit/debit cards.',
                    image:
                        "https://pro-oss.pinkwallet.com/image/********/cb31044d51337dbeb58f5b8880b6c7c2_1443x1128.png",
                    w: "720",

                },
            ],
            isAdvertising: true,
            coverImg: "",
            count: 0,
            intervalId: "",
            showLang: false,
            LangList: [
                {
                    name: "English",
                    value: 'en'
                },
                {
                    name: "简体中文",
                    value: 'zh'
                },
                {
                    name: "繁體中文",
                    value: 'zhhant'
                }
            ],
            isLangRotated: false,
            lang: ""
        }
    },
    onLoad() {
        this.lang = uni.getStorageSync('__language__') == 'zh' ? '简体中文' : uni.getStorageSync('__language__') == 'en' ? 'English' : '繁體中文'
        // this.getAppCover()
    },
    methods: {
        SetLang(item) {
			this.$i18n.locale = item.value
			uni.setStorageSync("__language__", item.value)
			this.lang = item.name
			this.showLang = false
		},
        toggleRotateLanguage() {
            this.showLang = !this.showLang;
            this.isLangRotated = !this.isLangRotated; // 
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        },
        onSwiperChange(e) {

            this.current = e.detail.current
            console.log(this.current);

        },
        async getAppCover() {
            let res = await this.$api.dictionaryInfo({
                key: 'advertisement',
            });
            if (res.status.code == 0) {
                this.info = JSON.parse(res.result.value)
                if (this.info.duration == 0) {
                    clearInterval(this.intervalId);
                    console.log("跳过")
                    this.$Router.pushTab({
                        name: 'index'
                    })
                } else {
                    this.coverImg = this.info.advertisement
                    console.log(this.coverImg)
                    this.count = this.info.duration
                    this.startCountdown()
                }
            } else {
                uni.showToast({
                    title: res.status.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        startCountdown() {
            this.intervalId = setInterval(() => {
                this.count--;
                if (this.count <= 0) {
                    clearInterval(this.intervalId);
                    this.$Router.pushTab({
                        name: 'index'
                    })
                }
            }, 1000);
        },
        clickSkip() {
            clearInterval(this.intervalId);
            this.$Router.pushTab({
                name: 'index'
            })
        },
        onNavigationBarBackPress() {
            // 阻止默认的返回行为
            return false;
        },
    }
}
</script>

<style lang="scss">
.onboarding {
    height: 100vh;
    display: flex;
    flex-direction: column;
    // align-items: flex-end;

    .lang {
        // width: 100%;
        display: flex;
        justify-content: flex-end;

        .language {
            margin-right: 32rpx;
            width: 108*2rpx;
            height: 33*2rpx;
            background: #FF82A31A;
            border-radius: 18*2rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            color: #000;
            position: relative;

            .helpoption {
                width: 108*2rpx;

                transition: transform 0.3s ease, opacity 0.3s ease;
                transform-origin: top;
                /* 设置变换的起点为顶部 */
                z-index: 11;
                position: absolute;
                top: 92rpx;
                left: 0;
                box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                // background-color: rgba(0, 0, 0, .5);
                background: #fff;
                border-radius: 16*2rpx;
                padding: 16*2rpx;
                opacity: 1;
                //padding: 100rpx;
                // height: 446rpx;
                display: flex;
                align-items: flex-start;
                flex-direction: column;

                &.collapse {
                    transform: scaleY(0) translateY(-100%);
                    /* 缩小至0，并向上移动 */
                    opacity: 0;
                }

                &.expand {
                    transform: scaleY(1) translateY(0%);
                    /* 恢复到正常大小，并位置恢复 */
                    opacity: 1;

                }

                >view {

                    padding: 15rpx 0;
                    display: flex;
                    align-items: center;

                    image {
                        width: 40rpx;
                        height: 30rpx;
                    }

                    text {
                        margin-left: 20rpx;
                        display: block;
                        font-family: Gilroy-Bold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        color: #000;
                    }
                }
            }

            .lang {
                margin: 0 12rpx;
                // line-height: 17.15*2rpx;
            }

            .global {
                width: 16*2rpx;
                height: 16*2rpx;
            }

            .down {
                width: 14*2rpx;
                height: 14*2rpx;
            }
        }
    }



    .swiper-wrapper {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;

        .swiper-item {
            display: inline-block;
            height: 100%;
            box-sizing: border-box;
            // padding: 40rpx 30rpx;
            text-align: center;

            .text-area {
                margin-top: 42rpx;
                padding: 0 32rpx;

                .title {
                    white-space: wrap;
                    text-align: left;
                    font-family: Gilroy-Bold;
                    font-weight: 400;
                    font-size: 60rpx;
                    line-height: 120%;
                    color: #000;
                    margin-bottom: 32rpx;
                }

                .desc {
                    text-align: left;
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 160%;
                    letter-spacing: 0%;
                    color: #333;

                    .item {
                        white-space: wrap;
                        margin-bottom: 10rpx;
                        display: flex;
                        align-items: flex-start;
                        position: relative;
                        padding-left: 40rpx;

                        &::before {
                            content: '';
                            position: absolute;
                            left: 0;
                            top: 6rpx;
                            width: 32rpx;
                            height: 32rpx;
                            background-image: url('https://pro-oss.pinkwallet.com/image/********/e49a6a80f45432f9e8de4bb4ce3d4d4e_64x64.png');
                            background-size: contain;
                            background-repeat: no-repeat;
                        }
                    }
                }
            }


            .image-area {
                position: relative;
                // background-image: url("https://pro-oss.pinkwallet.com/image/********/77b52fc9313c71fcdde7cea4fe7b3cb8_430x932.png");
                // background-size: cover;
                // background-position: center;
                width: 100vw;
                height: 900rpx;
                overflow: hidden;

                .bgfffleft {
                    position: absolute;
                    width: 100rpx;
                    background: #fff;
                    height: 900rpx;
                    opacity: .01;
                    left: 0;
                    z-index: 2;
                }

                .bgfffright {
                    position: absolute;
                    width: 100rpx;
                    background: #fff;
                    height: 900rpx;
                    opacity: .4;
                    right: 0;
                    z-index: 2;
                }

                .bgfff {
                    position: absolute;
                    width: 100%;
                    background: #fff;
                    opacity: .4;
                    height: 100rpx;
                    top: 0;
                    z-index: 2;
                }

                .bgfffdown {
                    position: absolute;
                    width: 100%;
                    background: #fff;
                    opacity: .4;
                    height: 100rpx;
                    z-index: 2;
                    bottom: 0;
                }


                .main-image {
                    // width: 620rpx;
                    margin: 60rpx auto 0;
                    z-index: 999;
                    opacity: 1 !important;

                }

                .lock {
                    position: absolute;
                    left: 64rpx;
                    top: 220rpx;
                    opacity: 1 !important;
                    z-index: 999;
                    width: 87rpx;
                    height: 106rpx;
                }

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-image: url("https://pro-oss.pinkwallet.com/image/********/77b52fc9313c71fcdde7cea4fe7b3cb8_430x932.png");
                    background-size: cover;
                    background-position: center;
                    opacity: 0.2; // 👈 仅伪元素半透明
                    z-index: 1;
                }



            }





        }
    }

    .dot-indicator {
        display: flex;
        justify-content: center;
        margin: 50rpx 0 30rpx;

        .dot {
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            background-color: #ccc;
            margin: 0 10rpx;

            &.active {
                width: 42rpx;
                border-radius: 116rpx;
                background-color: #008E28;
            }
        }
    }

    .button-group {
        padding: 0 32rpx;


        .btn-start {
            background-color: #FF82A3;
            height: 100rpx;
            border-radius: 128rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            color: #fff;
        }

        .btn-skip {
            text-align: center;
            margin-top: 32rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 160%;
            color: #000;
        }
    }
}

.openCoverImg {
    width: 100%;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 99;
    display: flex;
    justify-content: center;
    align-items: center;



    .right_view {
        font-size: 26rpx;
        width: 150rpx;
        height: 50rpx;
        line-height: 50rpx;
        text-align: center;
        background-color: rgba(0, 0, 0, 0.3);
        color: #fff;
        border-radius: 25rpx;
        position: absolute;
        right: 60rpx;
        top: var(--status-bar-height);

        text {
            color: peru;
            margin-left: 10rpx;
        }
    }

    image {
        width: 100%;
    }
}
</style>