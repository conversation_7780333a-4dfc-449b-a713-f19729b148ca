<template>
    <view class="container">
        <!-- 判断 type 来选择显示不同的表单 -->

        <u-navbar back-icon-color="#121212" :border-bottom="false"
            :title="nowAccount.id ? $t('Send.EditAccount') : $t('Send.AddAccount')">
            <view slot="right" class="search-box" @click="goChat">
                <image src="https://pro-oss.pinkwallet.com/image/********/f657c561ca32cf68bc701b67202e327d_80x80.png" />
            </view>
        </u-navbar>

        <!-- fiat 页面 -->
        <view v-if="pageType == 'legal' | pageType == 1" class="form-container">
            <form @submit="submitForm">
                <view class="form-item">
                    <text>{{ $t("Send.Currency") }}</text>
                    <view class="inputs flex_divide" @click="showcoin = true">
                        <view class="input_view flex_y">
                            <image
                                src="https://pro-oss.pinkwallet.com/image/********/098bd983e0cb24382da5927a853274b9_80x80.png" />
                            <view class="coins">
                                <!-- <image
                                    src="https://pro-oss.pinkwallet.com/image/********/4da55fa247ae6bfcd72fe7555f3c58e9_80x80.png" /> -->
                                <text>BTC</text>
                            </view>
                        </view>
                        <view class="right">
                            <image
                                src="https://pro-oss.pinkwallet.com/image/********/f5ab1cf813f79ba4751f0a4b6c735bff_120x120.png" />
                        </view>
                    </view>
                </view>

                <view class="form-item flex_start">
                    <u-checkbox-group size="38" active-color="#008E28" icon-size="30">
                        <u-checkbox v-model="formData.isAll">
                            <text class="check_label">{{ $t("Send.AllCurrencies") }}</text>
                        </u-checkbox>
                    </u-checkbox-group>
                </view>

                <view class="form-item">
                    <text>{{ $t("Send.AccountHolderName") }}</text>
                    <u-input height="96" v-model="formData.accountName" placeholder=" " />
                </view>

                <view class="form-item flex_start">
                    <u-checkbox-group size="38" active-color="#008E28" icon-size="30">
                        <u-checkbox v-model="formData.SameCount">
                            <text class="check_label">{{ $t("Send.InMyOwnName") }}</text>
                        </u-checkbox>
                    </u-checkbox-group>
                </view>

                <view class="form-item">
                    <text>{{ $t("Send.BankName") }}</text>
                    <u-input height="96" @click="showbank = true" v-model="formData.bankName" placeholder=" " />
                    <u-select @confirm="confirmbank" mode="single-column" v-model="showbank" :list="banks"></u-select>

                </view>
                <view class="form-item">
                    <text>{{ $t("Send.BankName") }}</text>
                    <u-input height="96" v-model="formData.sortCode" placeholder=" " />
                </view>
                <view class="form-item">
                    <text>BIC/SWIFT：</text>
                    <u-input height="96" v-model="formData.bic" placeholder=" " />
                </view>
                <view class="form-item">
                    <text>{{ $t("Send.IbanNumber") }}</text>
                    <u-input height="96" v-model="formData.iban" placeholder=" " />
                </view>
                <view class="form-item">
                    <text>{{ $t("Send.AccountNumber") }}</text>
                    <u-input height="96" v-model="formData.accountNumber" placeholder=" " />
                </view>
                <view class="form-item">
                    <text>{{ $t("Send.BankAddress") }}</text>
                    <u-input height="96" v-model="formData.bankAddress" placeholder=" " />
                </view>
                <view class="form-item">
                    <text>{{ $t("Send.ReferenceOptional") }}</text>
                    <u-input height="96" v-model="formData.bankAddress" placeholder=" " />

                </view>
                <!-- <view class="buttons">
                    <u-button hover-class="none" type="primary" @click="submitForm">提交</u-button>
                </view> -->
                <view class="btn flex-column-all">
                    <!-- <text>Please complate your swap within 69 sectionds.</text> -->
                    <u-button hover-class="none" class="exchange-btn flex_all" @click="convert">{{ $t("title.submit")
                        }}</u-button>
                </view>
                <!-- <view class="form-item flex_x" style="margin-top: 200rpx;">
                    <text class="label" style="color: #1989fa;font-size: 24rpx;">Live support</text>
                </view> -->
            </form>
        </view>

        <!-- BTC 页面 -->
        <view v-if="pageType == 'coin' || pageType == 2" class="form-container">
            <form @submit="submitForm">
                <view class="form-item">
                    <text>{{ $t("Send.Currency") }}</text>
                    <!-- <u-input @click="showcoin = true" input-align="left" border v-model="currency"
                        placeholder="请选择币种" /> -->
                    <view class="inputs flex_divide" @click="showCoin = !showCoin">
                        <view class="input_view flex_y">
                            <image
                                src="https://pro-oss.pinkwallet.com/image/********/098bd983e0cb24382da5927a853274b9_80x80.png" />
                            <view class="coins">
                                <!-- <image
                                    src="https://pro-oss.pinkwallet.com/image/********/4da55fa247ae6bfcd72fe7555f3c58e9_80x80.png" /> -->
                                <text>{{ nowCurrency }}</text>
                            </view>
                        </view>
                        <view class="right" @click.stop="clearNowCurrency" v-show="nowCurrency">
                            <image
                                src="https://pro-oss.pinkwallet.com/image/********/f5ab1cf813f79ba4751f0a4b6c735bff_120x120.png" />
                        </view>

                        <transition name="expand-slide">
                            <view class="helpoption" v-show="showCoin">
                                <view class="popup-top-title">Crypto</view>
                                <view class="crypto flex-column">
                                    <scroll-view :scroll-y="true" style="max-height: 400rpx;"
                                        @scrolltolower="bottomOut()">
                                        <view class="crypto-item" @click.stop="choose_coin(item)"
                                            v-for="item in cryptoList" :key="item.id">
                                            <image :src="item.image" />
                                            <text>{{ item.symbol }}</text>
                                        </view>
                                    </scroll-view>
                                </view>
                                <!-- <view style="height: 40rpx;"></view>
                                <view class="popup-top-title">Fiat</view>
                                <scroll-view :scroll-y="true" @scrolltolower="bottomOut2()" class="crypto flex-column">
                                    <view class="crypto-item " @click.stop="choose_coin(item)" v-for="item in FiatList"
                                        :key="item.id">
                                        <image :src="item.image" />
                                        <text>{{ item.symbol }}</text>
                                    </view>
                                </scroll-view> -->
                            </view>
                        </transition>
                    </view>
                    <!-- <u-select @confirm="confirm" mode="single-column" v-model="showcoin" :list="currencies"></u-select> -->
                </view>

                <view class="set" @click="checked = !checked">
                    <view v-if="!checked" class="check">
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250403/bcf4f8d1a8330712d2575c7591af2645_96x96.png" />
                    </view>
                    <view v-else class="check">
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250403/790b2569f8acbd8f532d18779770b4cc_97x96.png" />
                    </view>
                    {{ $t("Send.SetAsGeneralAddressApplicableToAllCurrencies") }}
                </view>

                <view class="form-item">
                    <text>{{ $t("Send.TokenAddress") }}</text>
                    <image class="right_fix" @click="scanCode"
                        src="https://pro-oss.pinkwallet.com/image/20250323/295e5b271a96c6e5aad0fa9e6a56017f_96x97.png" />
                    <u-input height="96" class="scaninput" v-model="PinkWalletAddress" placeholder=" " />
                </view>

                <view class="form-item">
                    <text>{{ $t("Send.Network") }}</text>
                    <!-- <picker :value="networkIndex" >
                        <view class="picker">
                            <text>{{ networks[networkIndex] }}</text>
                        </view>
                    </picker> -->
                    <view class="inputs flex_divide" @click="ShowNetwork = !ShowNetwork">
                        <view class="input_view">{{ network }}</view>
                        <view class="right">
                            <image
                                src="https://pro-oss.pinkwallet.com/image/********/2ac88d2d91400e2686de9a908f6a8e41_96x96.png" />
                        </view>
                        <transition name="expand-slide">
                            <view class="NetworkPopup" v-show="ShowNetwork">
                                <text class="address" @click="choose_net(item)" v-for="item in networkOptions"
                                    :key="item.id">{{
                                        item.network
                                    }}</text>
                            </view>
                        </transition>
                    </view>


                    <!-- <u-input @click="shownetwork = true" input-align="left" v-model="network" placeholder="请选择网络" /> -->
                    <!-- @change="handleNetworkChange" -->
                    <!-- <u-select @confirm="confirmnetwork" mode="single-column" v-model="shownetwork"
                        :list="networks"></u-select> -->
                </view>
                <view class="form-item">
                    <text>{{ $t("Send.Memo") }}</text>
                    <u-input height="96" input-align="left" v-model="formData.memo" placeholder=" " />

                </view>
                <view class="form-item">
                    <text>{{ $t("Send.TagOptional") }}</text>
                    <u-input height="96" input-align="left" v-model="formData.Tag" placeholder=" " />

                </view>

                <!-- <view class="buttons">
                    <u-button hover-class="none" type="primary" @click="submitForm">提交</u-button>
                    <u-button hover-class="none" @click="cancelForm" type="default">取消</u-button>
                </view> -->
                <view class="btn flex-column-all">
                    <!-- <text>Please complate your swap within 69 sectionds.</text> -->
                    <view class="exchange-btn flex_all" @click="ConvertCrypto">{{ $t("title.submit") }}</view>
                </view>

                <!-- 
                <view class="form-item flex_x" style="margin-top: 60rpx;">
                    <image class="icon_serve"
                        src="https://pro-oss.pinkwallet.com/image/********/6bdf5f1ed5d632b04ba56f8d89b3c81b_81x80.png" />
                    <text class="label">Live support</text>
                </view> -->
            </form>
        </view>

        <!-- pw 页面 -->
        <view v-if="pageType == 'pinkwallet' || pageType == 3" class="form-container">
            <form @submit="submitForm">
                <view class="form-item">
                    <text>{{ $t("Send.PinkWalletAccount") }}</text>
                    <u-input height="96" input-align="left" v-model="PinkWalletAddress"
                        :placeholder="$t('Send.LongPressToPaste')" class="scaninput" />
                    <image @click="scanCode"
                        src="https://pro-oss.pinkwallet.com/image/********/e0558820bd80c05eb6564cdaadf95439_72x73.png"
                        class="scan" />
                </view>
                <view class="tips">
                    <text>
                        {{
                            $t("Send.PleaseEnterYourTokenPayUsersMobileNumberEmailAddressOrScanTheOtherPartysPaymentQRCode")
                        }}
                    </text>
                </view>
                <!-- <view class="buttons">
                    <u-button hover-class="none" @click="submitForm" type="primary">提交</u-button>
                    <u-button hover-class="none" @click="cancelForm" type="default">取消</u-button>
                </view> -->
                <view class="btn flex-column-all">
                    <!-- <text>Please complate your swap within 69 sectionds.</text> -->
                    <view class="exchange-btn flex_all" @click="convertPink">{{ $t("title.submit") }}</view>
                </view>
                <!-- <view class="form-item flex_x" style="margin-top: 60rpx;">
                    <image class="icon_serve"
                        src="https://pro-oss.pinkwallet.com/image/********/6bdf5f1ed5d632b04ba56f8d89b3c81b_81x80.png" />
                    <text class="label">Live support</text>
                </view> -->
            </form>
        </view>


    </view>
</template>

<script>
let Qrcode = require('../../../utils/reqrcode.js')
export default {
    data() {
        return {
            checked: false,
            AddressList: [],
            networkOptions: [],
            ShowNetwork: false,
            Fiat: {
                pageNum: 1,
                pageSize: 10,
            },
            Crypto: {
                pageNum: 1,
                pageSize: 10,
            },
            nowCurrency: "",
            cryptoList: [],
            FiatList: [],
            showCoin: false,
            PinkWalletAddress: "",
            showbank: false,
            banks: [
                {
                    value: "招商银行",
                    label: "招商银行"
                },
                {
                    value: "工商银行",
                    label: "工商银行"
                },
                {
                    value: "建设银行",
                    label: "建设银行"
                },
                {
                    value: "交通银行",
                    label: "交通银行"
                },
                {
                    value: "中国银行",
                    label: "中国银行"
                },


            ],
            showcoin: false,
            shownetwork: false,
            pageType: '', // 页面类型
            formData: {
                SameCount: false,
                isAll: false,
                accountName: '',
                bankName: '',
                sortCode: '',
                bic: '',
                iban: '',
                accountNumber: '',
                bankAddress: '',
                reference: '',
                address: '',
                memo: '',
                payUser: ''
            },
            currencies: [
                {
                    value: 'USD',
                    label: 'USD'
                },
                {
                    value: 'BTC',
                    label: 'BTC'
                }
            ],
            references: ['选填', '必填'],
            networks: [
                {
                    value: 'ERC-20',
                    label: 'ERC-20'
                }, {
                    value: 'TRC-20',
                    label: 'TRC-20'
                },
                {
                    value: 'ETH',
                    label: 'ETH'
                }
            ],
            labels: ['选填', '必填'],
            currencyIndex: 0,
            referenceIndex: 0,
            networkIndex: 0,
            labelIndex: 0,
            currency: "",
            network: "",
            nowAccount: {},
            link: "../../../static/serve.html",
            hasNext: false,
            from: ""

        };
    },
    onLoad(options) {
        uni.setNavigationBarTitle({
            title: this.$t("page.AddCount") // 切换语言后重新设置标题
        })
        if (options.from) {
            this.from = options.from
        }
        if (options.account) {
            let account = JSON.parse(decodeURIComponent(options.account));
            if (account) {
                this.PinkWalletAddress = account.token;
                this.pageType = account.type || 'USD'; // 默认页面类型为 'USD'
                // this.from = account.from;
                if (this.pageType == 2) {
                    this.nowCurrency = account.symbol
                    this.PinkWalletAddress = account.address
                    this.network = account.network
                    this.formData.memo = account?.memo
                    this.formData.tag = account?.tag
                    // this.from = account.from;
                }
                this.nowAccount = account
                // console.log(this.from);
            }
        }
        if (options.type) {
            this.pageType = options.type
        }

        this.getCurrencyFiat()
        this.getCurrencyCrypto()

    },
    watch: {
        nowCurrency: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.getNet()
                }
            },
        }
    },
    methods: {
        bottomOut() {
            console.log(123);

            if (this.hasNext) {
                this.Crypto.pageNum++
                this.getCurrencyCrypto()
            }

        },
        goChat() {
            this.$Router.push({
                name: 'webView',
                params: {
                    url: this.link,

                }
            })
        },
        async convertPink() {
            if (!this.PinkWalletAddress) {
                uni.showToast({
                    icon: 'none',
                    title: this.$t("Please.full"),
                    duration: 2000
                });
                return
            }
            let withdrawInsideAccountDTO = {}
            withdrawInsideAccountDTO.token = this.PinkWalletAddress;
            let res = await this.$api.addOrUpdateUserWithdrawAccount({
                // symbol: this.nowCurrency,
                type: 3,
                infoExtra: JSON.stringify(withdrawInsideAccountDTO),
                // this.nowAccount 有id，那么传id
                id: this.nowAccount.id
            })
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
                console.log(this.from);

                if (this.from == 'sell') {
                    setTimeout(() => {
                        this.$Router.push({
                            name: 'sell'
                        });
                    }, 1000);
                } else {
                    setTimeout(() => {
                        this.$Router.push({
                            name: 'TransferOut'
                        });
                    }, 1000);
                }

            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async ConvertCrypto() {
            if (!this.nowCurrency || !this.network || !this.PinkWalletAddress) {
                uni.showToast({
                    title: this.$t("Please.full"),
                    icon: 'none',
                    duration: 3000
                });
                return
            }
            let withdrawCryptoDTO = {}
            withdrawCryptoDTO.symbol = this.nowCurrency;
            withdrawCryptoDTO.network = this.network;
            withdrawCryptoDTO.address = this.PinkWalletAddress;
            withdrawCryptoDTO.memo = this.formData.memo;
            withdrawCryptoDTO.tag = this.formData.tag;
            withdrawCryptoDTO.general = this.checked

            let res = await this.$api.addOrUpdateUserWithdrawAccount({
                symbol: this.nowCurrency,
                type: 2,
                infoExtra: JSON.stringify(withdrawCryptoDTO),
                id: this.nowAccount.id

            })
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
                if (this.from == 'sell') {
                    setTimeout(() => {
                        this.$Router.push({
                            name: 'sell'
                        });
                    }, 1000);
                } else {
                    setTimeout(() => {
                        this.$Router.push({
                            name: 'TransferOut'
                        });
                    }, 1000);
                }
                // setTimeout(() => {
                //     this.$Router.push({
                //         name: 'TransferOut'
                //     });
                // }, 1000);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async getNet() {
            let res = await this.$api.getNetwork({
                symbol: this.nowCurrency,
            })
            if (res.code == 200) {
                this.networkOptions = res.result
                console.log(this.networkOptions);
            }
        },
        choose_net(item) {
            this.network = item.network
            if (this.nowCurrency && this.network) {
                this.getAdd()
            }
        },
        async getAdd() {
            let res = await this.$api.getWalletAddress({
                symbol: this.nowCurrency,
                network: this.network
            })
            if (res.code == 200) {
                // this.qrcodeUrl = res.result.address
                // this.formData.address
                this.AddressList = res.result
            }
        },
        async getCurrencyFiat() {
            let res = await this.$api.symbolListPaged({
                fiat: true,
                symbol: "",
                pageNum: this.Fiat.pageNum,
                pageSize: this.Fiat.pageSize
            });
            this.FiatList = res.result.data
        },
        async getCurrencyCrypto() {
            let res = await this.$api.symbolListPaged({
                fiat: false,
                symbol: "",
                pageNum: this.Crypto.pageNum,
                pageSize: this.Crypto.pageSize
            });
            if (res.code == 200) {
                this.hasNext = res.result.hasNext
                if (this.Crypto.pageNum == 1) {
                    this.cryptoList = res.result.data
                } else {
                    this.cryptoList = this.cryptoList.concat(res.result.data)
                }
            }
        },
        choose_coin(item) {
            this.nowCurrency = item.symbol
            this.showCoin = false
            console.log(this.showCoin);
        },
        clearNowCurrency() {
            this.nowCurrency = ""
            this.showCoin = false

        },
        scanCode() {
            // #ifdef APP-PLUS
            this.scanQRCode()
            // #endif

            // #ifdef H5
            this.scanCodeH5()
            // #endif
        },
        scanQRCode() {
            let that = this
            uni.scanCode({
                scanType: ['qrCode'],
                success: function (res) {
                    that.PinkWalletAddress = res.result
                }
            })
        },
        // H5通过拉起相机拍照来识别二维码
        scanCodeH5() {
            uni.chooseImage({
                count: 1,
                success: imgRes => {
                    Qrcode.qrcode.decode(this.getObjectURL(imgRes.tempFiles[0]))
                    Qrcode.qrcode.callback = (codeRes) => {
                        if (codeRes.indexOf('error') >= 0) {
                            // 二维码识别失败
                            this.PinkWalletAddress = 'undefined' + codeRes
                        } else {
                            // 二维码识别成功
                            let r = this.decodeStr(codeRes)
                            this.PinkWalletAddress = r
                        }
                    }
                }
            })
        },
        // 获取文件地址函数
        getObjectURL(file) {
            var url = null
            if (window.createObjectURL !== undefined) { // basic
                url = window.createObjectURL(file)
            } else if (window.URL !== undefined) { // mozilla(firefox)
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL !== undefined) { // webkit or chrome
                url = window.webkitURL.createObjectURL(file)
            }
            return url
        },
        // 解码，输出：中文
        decodeStr(str) {
            var out, i, len, c;
            var char2, char3;
            out = "";
            len = str.length;
            i = 0;
            while (i < len) {
                c = str.charCodeAt(i++);
                switch (c >> 4) {
                    case 0:
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                    case 6:
                    case 7:
                        // 0xxxxxxx
                        out += str.charAt(i - 1);
                        break;
                    case 12:
                    case 13:
                        // 110x xxxx 10xx xxxx
                        char2 = str.charCodeAt(i++);
                        out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
                        break;
                    case 14:
                        // 1110 xxxx 10xx xxxx 10xx xxxx
                        char2 = str.charCodeAt(i++);
                        char3 = str.charCodeAt(i++);
                        out += String.fromCharCode(((c & 0x0F) << 12) |
                            ((char2 & 0x3F) << 6) |
                            ((char3 & 0x3F) << 0));
                        break;
                }
            }
            return out;
        },
        confirmbank(e) {
            this.formData.bankName = e[0].value
        },
        confirm(e) {
            this.currency = e[0].value
        },
        confirmnetwork(e) {
            this.network = e[0].value
        },
        // 表单提交
        submitForm() {
            // 处理提交逻辑
        },
        // 表单取消
        cancelForm() {
            // 处理取消逻辑
        },
        handleCurrencyChange(event) {
            this.currencyIndex = event.detail.value;
        },
        handleReferenceChange(event) {
            this.referenceIndex = event.detail.value;
        },
        handleNetworkChange(event) {
            this.networkIndex = event.detail.value;
        },
        handleLabelChange(event) {
            this.labelIndex = event.detail.value;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .u-select__body__picker-view__item {
    color: #e6f0ff !important;
}

::v-deep .u-hairline-border:after {
    border: none !important;
}

::v-deep .u-input {
    margin-top: 20rpx !important;
    height: 48*2rpx !important;
    border-radius: 10*2rpx;
    border-width: 2rpx;
    border: 2rpx solid #999999 !important;
    padding: 0 38rpx !important;

    font-family: Gilroy-Medium;
    font-weight: 400;
    font-size: 14*2rpx;
    line-height: 16.8*2rpx;
    letter-spacing: 0%;

}

// ::v-deep .uni-scroll-view-content {
//     display: flex;
//     align-items: center;
// }

.container {

    padding: 20px;
    min-height: 100vh;

    .form-container {
        margin-top: 64rpx;
        margin-bottom: 20px;

        .set {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 160%;
            // letter-spacing: 0%;
            color: #666;
            display: flex;
            align-items: flex-start;

            image {
                margin-right: 16rpx;
                width: 48rpx;
                height: 48rpx;
            }
        }

        .form-item {
            margin-bottom: 28rpx;
            position: relative;

            .scaninput {
                padding-right: 100rpx;
                padding: 0 100rpx 0rpx 38rpx !important;

                // ::v-deep .u-input {
                //     margin-top: 20rpx !important;
                //     height: 48*2rpx !important;
                //     border-radius: 10*2rpx;
                //     border-width: 2rpx;
                //     border: 2rpx solid #999999 !important;
                //     font-family: Gilroy-Medium;
                //     font-weight: 400;
                //     font-size: 14*2rpx;
                //     line-height: 16.8*2rpx;
                //     letter-spacing: 0%;

                // }
            }

            .scan {
                position: absolute;
                right: 38rpx;
                bottom: 27rpx;
                width: 48rpx;
                height: 48rpx;
                // margin-left: 10rpx;
                // margin-top: -4rpx;
            }

            .check_label {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                color: #666;
            }

            .label {
                margin-left: 20rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 16*2rpx;
                // line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #FF82A3;
            }

            .icon_serve {
                width: 40rpx;
                height: 40rpx;
            }

            .right_fix {
                position: absolute;
                bottom: 28rpx;
                right: 38rpx;
                width: 48rpx;
                height: 48rpx;
            }

            .inputs {
                margin-top: 20rpx;
                height: 48*2rpx !important;
                border-radius: 10*2rpx;
                border-width: 2rpx;
                border: 2rpx solid #999999;
                font-family: Gilroy-Medium;
                color: #333;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                padding: 0 38rpx !important;
                position: relative;

                .NetworkPopup {
                    z-index: 9999;
                    // width: 398*2rpx;
                    width: 100%;
                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 120rpx;
                    left: 0;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    // display: flex;
                    // align-items: flex-start;
                    // flex-direction: column;
                    max-height: 400rpx;
                    overflow-y: auto;
                    width: 100%;

                    .address {
                        width: 300*2rpx;
                        overflow: hidden;
                        // 超出省略号

                        text-overflow: ellipsis;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 100%;
                        display: block;
                        color: #333;
                        margin-bottom: 40rpx;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }


                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;

                    }
                }

                .helpoption {
                    z-index: 9999;
                    // width: 398*2rpx;
                    width: 100%;
                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 120rpx;
                    left: 0;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    .address {
                        width: 300*2rpx;
                        overflow: hidden;
                        // 超出省略号

                        text-overflow: ellipsis;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 100%;
                        display: block;
                        color: #333;
                        margin-bottom: 40rpx;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }

                    .crypto {
                        margin-top: 40rpx;
                        max-height: 400rpx;
                        overflow: auto;
                        width: 100%;

                        .crypto-item {
                            display: flex;
                            align-items: center;
                            margin-bottom: 40rpx;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            image {
                                width: 60rpx;
                                height: 60rpx;
                            }

                            text {
                                margin-left: 20rpx;
                                font-family: Gilroy-SemiBold;
                                font-weight: 400;
                                font-size: 16*2rpx;
                                // line-height: 120%;
                                letter-spacing: 0%;
                                color: #000;
                            }
                        }
                    }

                    .popup-top-title {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 14*2rpx;
                        line-height: 160%;
                        color: #FF82A3;
                    }

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;

                    }

                    // >view {

                    //     padding: 15rpx 0;
                    //     display: flex;
                    //     align-items: center;

                    //     image {
                    //         width: 40rpx;
                    //         height: 30rpx;
                    //     }

                    //     text {
                    //         margin-left: 20rpx;
                    //         display: block;
                    //         font-family: Gilroy-Bold;
                    //         font-weight: 400;
                    //         font-size: 16*2rpx;
                    //         line-height: 19.2*2rpx;
                    //         color: #000;
                    //     }
                    // }
                }

                .right {
                    width: 60rpx;
                    height: 60rpx;

                    image {
                        width: 60rpx;
                        height: 60rpx;
                    }
                }

                .input_view {

                    .coins {
                        display: flex;
                        align-items: center;
                        margin-left: 20rpx;

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }

                        text {
                            line-height: 0;
                            margin-left: 10rpx;
                        }
                    }

                    >image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            >text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #000;
            }
        }

        .picker {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tips {
            width: 100%;
            // height: 76*2rpx;
            border-bottom-right-radius: 10*2rpx;
            border-bottom-left-radius: 10*2rpx;
            padding: 16*2rpx;
            background: #F2F2F2;

            >text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }
        }

        .btn {
            margin-top: 48rpx;
            width: 100%;

            .exchange-btn {
                width: 100%;
                height: 100rpx;
                background: #FF82A3;
                border-radius: 64*2rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                color: #fff;
            }
        }

        .buttons {
            display: flex;
            flex-direction: column;
            margin-top: 70px;
            gap: 26rpx;

            button {
                border: none;
                width: 100%;
                // padding: 10px 20px;
                // color: white;
                border-radius: 5px;
            }
        }
    }
}
</style>