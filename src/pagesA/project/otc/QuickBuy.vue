<template>
    <view class="container">
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="$t('BuyCrypto_Title')" :custom-back="back">
        </u-navbar>
        <view style="height: 60rpx;"></view>
        <!-- 兑换模块 v-if="ClickShow"-->
        <view class="form-wrapper">
            <!-- :style="{ backgroundColor: isSwapped ? '#e6f0ff' : '' }" -->
            <!-- <view class="form-row from"> -->
            <view class="float_box">
                <view class="from-currency">{{$t('BuyCrypto_YouSpend')}}</view>
                <view class="flex_divide padding between">
                    <!--  -->
                    <view>
                        <u-input @input="handleInputFrom" v-model="fromAmount" class="from-input hidden-input"
                            type="digit" :custom-style="inputStyle" :placeholder="fromPlaceholder"></u-input>
                        <!-- 字符动画展示层 -->
                        <view class="animated-text">
                            <view v-for="(char, index) in displayChars" :key="char.id" class="char-wrapper">
                                <text class="char-text" :class="[char.status]" :style="{ fontSize: fontSize + 'rpx' }">
                                    {{ char.value }}
                                </text>
                            </view>
                        </view>
                    </view>


                    <view class="frominfo" @click="Choose()">
                        <transition name="expand-slide">
                            <view class="helpoption" v-show="fromShow">
                                <view v-for="(item, index) in CoinList" :key="index" class="Roptions"
                                    @click="SetCoin(item)">
                                    <text>{{ item.id }}</text>
                                </view>
                            </view>
                        </transition>

                        <!-- <image :src="fromCurrency.icon" /> -->
                        <text>{{ fromCurrency || '--' }}</text>
                        <image :class="{ rotated: isChoose }"
                            src="https://pro-oss.pinkwallet.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png" />
                    </view>
                </view>
            </view>


            <!-- <view class="available">Available : 1,100.00 -->
            <!-- <text v-if="fromAmount" style="margin-left: 14rpx;">最大</text> -->
            <!-- </view> -->
            <!-- </view> -->

            <view style="height: 12rpx"></view>
            <!-- :style="{ backgroundColor: isSwapped ? '' : '#e6f0ff' }" -->
            <view class="float_box">
                <view class="to-currency">{{$t('BuyCrypto_YouGet')}}</view>

                <view class="flex_divide padding between">
                    <u-loading :show="showToAmount"></u-loading>

                    <view style="margin-left: 20rpx;">
                        <u-input @input="handleInputTo" v-model="toAmount" class="to-input hidden-input" type="digit"
                            :placeholder="toPlaceholder"></u-input>

                        <view class="animated-text">
                            <view v-for="(char, index) in displayCharsTo" :key="char.id" class="char-wrapper">
                                <text class="char-text" :class="[char.status]" :style="{ fontSize: fontSize + 'rpx' }">
                                    {{ char.value }}
                                </text>
                            </view>
                        </view>
                    </view>

                    <view class="frominfo" @click="Choosed()">
                        <transition name="expand-slide">
                            <view class="helpoption" v-show="toShow">
                                <view v-for="(item, index) in CoinListCry" :key="index" class="Roptions"
                                    @click="SetCointo(item)">
                                    <text>{{ item.id }}</text>
                                </view>
                            </view>
                        </transition>
                        <!-- <image :src="toCurrency.icon" /> -->
                        <text>{{ toCurrency || '--' }}</text>
                        <image :class="{ rotated: isChoosed }"
                            src="https://pro-oss.pinkwallet.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png" />
                    </view>
                </view>
            </view>
            <!-- <view class="form-row to">

           
                <view class="available">1 BTC = 0.0038</view>
            </view> -->
            <!-- <view class="tips">请在9999秒有效期内完成兑换下单。</view> -->





        </view>
        <view class="fee" v-if="retrieveQuotes.processingFee">
            <text>{{$t('BuyCrypto_ProcessingFee')}}:{{ retrieveQuotes.processingFee || '--' }}</text>
            <text v-if="retrieveQuotes.networkFee">{{$t('BuyCrypto_NetworkFee')}}:{{ retrieveQuotes.networkFee || '--' }}</text>

        </view>
        <view class="form-item" v-if="NowPaymentMethods.length">
            <text>{{$t('BuyCrypto_PayUsing')}}</text>
            <view class="inputs flex_divide" @click="ChoosePaymentMethodPopup = true">
                <view class="input_view flex_y">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250306/96f1115bcb6984e4219d915841cf6af5_76x75.png" />
                    <view class="coins">
                        <!-- <image
                                    src="https://pro-oss.pinkwallet.com/image/20250305/4da55fa247ae6bfcd72fe7555f3c58e9_80x80.png" /> -->
                        <text>{{ currentPaymentName }}</text>
                    </view>
                </view>
                <view class="right">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250306/10776e365e28dc886913bc57dcf9304c_64x65.png" />
                </view>
            </view>
        </view>
        <view class="btn ">
            <!-- <text class="bom">processingFee:1,    networkFee:2</text> -->
            <u-button :disabled="disabled" hover-class="none" class="exchange-btn " @click="StartBuy">{{$t('BuyCrypto_BuyButton')}}</u-button>
        </view>


        <!-- 原生弹窗实现 -->
        <view v-show="ChoosePaymentMethodPopup" class="popup-overlay" @click="closePopup">
            <view class="popup-content" :class="{ show: popupContentShow }" @click.stop>
                <view class="popup-header">
                    <text class="popup-title">{{$t('BuyCrypto_ChoosePaymentMethod')}}</text>
                    <text class="close-btn" @click="closePopup">
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250306/d63ed1f30996328fd0071e62d4f65f9e_69x69.png" />
                    </text>
                </view>
                <view class="payment-options">
                    <view :class="popupContentShow ? 'option-item item-animate' : 'option-item'"
                        :style="getItemStyle(index)" v-for="(option, index) in NowPaymentMethods" :key="index"
                        @click="selectPayment(option)">
                        <!-- <image :src="option.icon" class="option-icon" mode="aspectFit"></image> -->
                        <text class="option-name">{{$t(paymentNameMap[option.name] || option.name)}}</text>
                        <view class="custom-radio flex_all">
                            <image v-if="option.checked"
                                src="https://pro-oss.pinkwallet.com/image/1370356641093476352.png" />
                            <image v-else src="../../../static/imgs/public/mpc.png" />
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <u-popup v-model="Connecting" width="688" mode="center" :mask="true" :mask-close-able="false">
            <view class="transfer-card">
                <view class="transfer-icons">
                    <image class="token-icon" src="../../../static/imgs/public/pink.png" />
                    <view class="arrow-box">
                        <image class="arrow-icon" src="../../../static/imgs/public/arr.png" />
                    </view>
                    <image class="token-icon" src="../../../static/imgs/public/banxa.png" />
                </view>

                <view class="connecting-text">
                    {{$t('BuyCrypto_Connecting')}}
                </view>

                <view class="transfer-amount">
                    <text class="amount-left">{{ fromAmount + ' ' + fromCurrency }}</text>
                    <image mode="widthFix" class="amount-arrow" src="../../../static/imgs/public/arrow.png" />
                    <text class="amount-right">{{ toAmount + ' ' + toCurrency }}</text>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
let uid = 0
export default {
    data() {
        return {
            displayChars: [],
            displayCharsTo: [],

            retrieveQuotes: {},
            toLimit: "",
            leftIcon: 'https://cdn.jsdelivr.net/gh/yourcdn/pink-icon.png',
            rightIcon: 'https://cdn.jsdelivr.net/gh/yourcdn/alchemy-icon.png',
            Connecting: false,
            showToAmount: false,
            showFromAmount: false,
            debounceTimer: null,
            isFirstEnter: true,
            fromShow: false,
            toShow: false,
            isChoose: false,
            CoinList: [],
            CoinListCry: [],
            isChoosed: false,
            toAmount: "",
            fromAmount: "",
            ChoosePaymentMethodPopup: false,
            popupContentShow: false, // 控制弹窗内容动画
            selectedPayment: 'Debit Card', // 默认选中 Debit Card
            paymentMethods: [],
            ClickShow: false,
            fromCurrency: "CAD",
            toCurrency: "BTC",
            isSwapped: false,
            isChoose: false,
            NowPaymentMethods: [],
            fromSymbol: "",
            toSymbol: "",
            nowselectedOption: {},
            inputTimer: null,
            paymentNameMap: {
                'Debit credit card': 'BuyCrypto_DebitCreditCard',
                'Interac bank transfer': 'BuyCrypto_InteracBankTransfer',
                'Debit/Credit Card': 'BuyCrypto_DebitCreditCard',
                'Interac Bank Transfer': 'BuyCrypto_InteracBankTransfer',
                // 其它支付方式可继续补充
            }
        }
    },
    watch: {
        fromAmount(newVal, oldVal) {
            if (this.toCurrency && this.fromCurrency) {
                if (newVal && !this.toAmount) {
                    this.debounceQuery(true, true);
                }
            }
            const newChars = newVal.split('')
            const oldChars = this.displayChars.map((c) => c.value)
            const result = []
            const maxLen = Math.max(newChars.length, oldChars.length)

            for (let i = 0; i < maxLen; i++) {
                const oldChar = oldChars[i]
                const newChar = newChars[i]

                if (oldChar === newChar) {
                    result.push({
                        ...this.displayChars[i],
                        status: 'stay'
                    })
                } else if (newChar !== undefined) {
                    result.push({
                        id: uid++,
                        value: newChar,
                        status: 'enter'
                    })
                }
            }

            if (newChars.length < oldChars.length) {
                for (let i = newChars.length; i < oldChars.length; i++) {
                    result.push({
                        ...this.displayChars[i],
                        status: 'leave'
                    })
                }
            }

            this.displayChars = result

            setTimeout(() => {
                this.displayChars = this.displayChars.filter((c) => c.status !== 'leave')
            }, 300)
        },
        // fromAmount(val) {

        // },
        toAmount(newVal) {
            if (this.toCurrency && this.fromCurrency) {
                if (newVal && !this.fromAmount) {
                    this.debounceQuery(true, true);
                }
            }
            const newChars = newVal.split('')
            const oldChars = this.displayCharsTo.map((c) => c.value)
            const result = []
            const maxLen = Math.max(newChars.length, oldChars.length)

            for (let i = 0; i < maxLen; i++) {
                const oldChar = oldChars[i]
                const newChar = newChars[i]

                if (oldChar === newChar) {
                    result.push({
                        ...this.displayCharsTo[i],
                        status: 'stay'
                    })
                } else if (newChar !== undefined) {
                    result.push({
                        id: uid++,
                        value: newChar,
                        status: 'enter'
                    })
                }
            }

            if (newChars.length < oldChars.length) {
                for (let i = newChars.length; i < oldChars.length; i++) {
                    result.push({
                        ...this.displayCharsTo[i],
                        status: 'leave'
                    })
                }
            }

            this.displayCharsTo = result

            setTimeout(() => {
                this.displayCharsTo = this.displayCharsTo.filter((c) => c.status !== 'leave')
            }, 300)
        },
        fromCurrency: {
            handler(newVal) {
                if (!newVal || !this.toCurrency) return
                if (newVal && this.toCurrency) {
                    if (this.fromAmount || this.toAmount) {
                        this.debounceQuery(true, false);
                    }
                }
            }
        },
        toCurrency: {
            handler(newVal) {
                if (!newVal || !this.fromCurrency) return
                if (newVal && this.fromCurrency) {
                    if (this.fromAmount || this.toAmount) {
                        this.debounceQuery(false, true);

                    }
                }
            },
            immediate: true
        },
        ChoosePaymentMethodPopup(newVal) {
            if (newVal) {
                // 弹窗打开时，延迟触发动画
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.popupContentShow = true;
                    }, 10);
                });
            } else {
                // 弹窗关闭时，立即隐藏动画
                this.popupContentShow = false;
            }
        },
    },
    computed: {
        fontSize() {
            const baseSize = 60
            const minSize = 30
            const len = this.fromAmount.length
            return len === 0 ? baseSize : Math.max(minSize, baseSize - (len - 1) * 2)
        },
        inputStyle() {
            return {
                backgroundColor: 'transparent',
                fontSize: this.fontSize + 'rpx',
                fontWeight: 'bold',
                width: '100%',
                padding: '0',
                textAlign: 'left'
            }
        },
        fromPlaceholder() {
            if (!this.fromSymbol || !this.nowselectedOption) {
                return this.$t('BuyCrypto_Placeholder');
            }
            const min = this.nowselectedOption.minimum ?? 0;
            const max = this.nowselectedOption.maximum ?? 0;
            return this.$t('BuyCrypto_PlaceholderRange', { symbol: this.fromSymbol, min, max });
        },
        toPlaceholder() {
            if (!this.toCurrency) {
                return this.$t('BuyCrypto_Placeholder');
            }
            return this.toLimit;
        },
        disabled() {
            if (!this.fromAmount || !this.toAmount || !this.toCurrency || !this.fromCurrency) {
                return true
            } else {
                return false
            }
        },
        currentPaymentName() {
            const name = this.isFirstEnter ? (this.NowPaymentMethods[0]?.name || '') : (this.nowselectedOption?.name || '');
            return this.paymentNameMap[name] ? this.$t(this.paymentNameMap[name]) : name;
        },
    },
    onLoad() {
        this.GetpaymentMethods()
        this.getfiatsList()
        this.getcryptocurrencies()
    },
    methods: {
        async StartBuy() {
            this.Connecting = true
            let res = await this.$api.createBuyOrder({
                paymentMethodId: this.nowselectedOption.id,
                crypto: this.toCurrency,
                fiat: this.fromCurrency,
                ...(this.fromAmount ? { fiatAmount: this.fromAmount } : {}),
                ...(this.toAmount ? { cryptoAmount: this.toAmount } : {})
            });
            if (res.code == 200) {
                if (res.result.checkoutUrl) {
                    this.$Router.push({
                        name: 'webView',
                        params: {
                            url: res.result.checkoutUrl
                        }
                    })
                }
                this.Connecting = false
            } else {
                // const match = res.msg.match(/"({.*})"/);
                // if (match && match[1]) {
                //     const innerJson = JSON.parse(match[1]);
                // return innerJson.message;
                uni.showToast({
                    icon: 'none',
                    title: res.msg,
                    duration: 2000
                });
                // }
                this.Connecting = false
            }
        },
        handleInputTo(e) {
            console.log(123);
            this.toAmount = e;
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                if (this.toAmount) {
                    if (this.toCurrency && this.fromCurrency) {
                        this.debounceQuery(false, true);
                    }
                }
            }, 300);
        },
        handleInputFrom(e) {
            this.fromAmount = e;
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                if (this.fromAmount) {
                    if (this.toCurrency && this.fromCurrency) {
                        this.debounceQuery(true, false);
                    }
                }
            }, 300);
        },
        debounceQuery(fromMove = true, toMove = true) {
            this.showToAmount = true
            clearTimeout(this.inputTimer); // 清除上一次的定时器
            this.inputTimer = setTimeout(() => {
                // if (this.fromAmount) {
                this.getretrieveQuotes(this.fromCurrency, this.toCurrency, fromMove, toMove)
                // this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency);
                // }
            }, 300); // 300ms 之内有新的输入，就不会请求
        },
        // 获取10s汇率
        async getfineGrainedCoinPrice(a, b) {
            if (a && b) {
                let res = await this.$api.fineGrainedCoinPrice({
                    // coinPair: a + b + '.SPOT'
                    fromCoin: a,
                    toCoin: b
                })
                if (res.code == 200 && res.result) {
                    this.hui = res.result
                } else if (res.code != 200) {
                    uni.showToast({
                        icon: 'none',
                        title: res.msg,
                        duration: 2000
                    });
                }
                this.getpriceGn(this.fromCurrency, this.toCurrency)
            } else {
                // uni.showToast({
                //     icon: 'none',
                //     title: this.$t('Swap.selectpair'),
                //     duration: 2000
                // });
            }
        },
        async getretrieveQuotes(a, b, fromMove, toMove) {

            const params = {
                paymentMethodId: this.nowselectedOption.id,
                crypto: b,
                fiat: a
            }
            console.log(fromMove, toMove);

            if (fromMove && this.fromAmount) {
                params.fiatAmount = this.fromAmount
            }

            if (toMove && this.toAmount) {
                params.cryptoAmount = this.toAmount
            }
            let res = await this.$api.retrieveQuotes(params)

            // let res = await this.$api.retrieveQuotes({
            //     paymentMethodId: this.nowselectedOption.id,
            //     crypto: b,
            //     fiat: a,
            //     ...(this.fromAmount ? { fiatAmount: this.fromAmount } : {}),
            //     ...(this.toAmount ? { cryptoAmount: this.toAmount } : {})
            // })
            if (res.code == 200) {
                this.showToAmount = false
                this.toAmount = res.result.cryptoAmount
                this.fromAmount = res.result.fiatAmount
                this.retrieveQuotes = res.result
            } else {
                this.retrieveQuotes = {}
                this.showToAmount = false
                this.toAmount = ""
                this.fromAmount = ""
                uni.showToast({
                    icon: 'none',
                    title: res.msg,
                    duration: 2000
                });
            }
        },
        SetCointo(e) {
            // this.balanceTo = e.balance
            this.toCurrency = e.id
            // this.toAmount = ""
            this.toLimit = e.blockchains[0].minimum
            // this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency)

        },
        SetCoin(e) {
            // this.fromAmount = ""
            // this.balanceFrom = e.balance
            this.fromCurrency = e.id
            this.fromSymbol = e.symbol
            // this.NowPaymentMethods = e.supportedPaymentMethods
            this.NowPaymentMethods = e.supportedPaymentMethods.map(item => ({
                ...item,
                checked: false
            }));
            if (this.NowPaymentMethods.length > 0) {
                this.NowPaymentMethods[0].checked = true;
            }
            this.nowselectedOption = this.NowPaymentMethods[0]
            console.log(this.NowPaymentMethods, 123);
            // this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency)
        },
        async getfiatsList() {
            let res = await this.$api.getfiats({});
            if (res.code == 200) {
                this.CoinList = res.result
                for (let i = 0; i < res.result.length; i++) {
                    const e = res.result[i];
                    if (e.id == this.fromCurrency) {
                        this.NowPaymentMethods = e.supportedPaymentMethods.map(item => ({
                            ...item,
                            checked: false
                        }));
                        if (this.NowPaymentMethods.length > 0) {
                            this.NowPaymentMethods[0].checked = true;
                        }
                        this.nowselectedOption = this.NowPaymentMethods[0]
                    }
                }
                // this.CoinListCry = res.result.cryptocurrencies
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async getcryptocurrencies() {
            let res = await this.$api.cryptocurrencies({});
            if (res.code == 200) {
                this.CoinListCry = res.result
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        async GetpaymentMethods() {
            let res = await this.$api.paymentMethods({});

        },
        closePopup() {
            this.popupContentShow = false;
            setTimeout(() => {
                this.ChoosePaymentMethodPopup = false;
            }, 300); // 等待动画完成后再隐藏弹窗
        },
        selectPayment(selectedOption) {
            // this.selectedPayment = option.name; // 更新选中支付方式
            this.nowselectedOption = selectedOption;
            this.isFirstEnter = false;
            this.NowPaymentMethods.forEach(option => {
                option.checked = option.name == selectedOption.name;
            });
            this.debounceQuery(true, true);
            this.closePopup();
        },
        back() {
            uni.navigateBack({
                delta: 1
            });
        },
        // Choose() {
        //     this.isChoose = !this.isChoose;
        // },

        Choose() {
            this.fromShow = !this.fromShow
            this.isChoose = !this.isChoose;

        },
        Choosed() {
            this.toShow = !this.toShow
            this.isChoosed = !this.isChoosed;

        },
        inputfocus() {
            this.ClickShow = !this.ClickShow;
        },
        // 获取列表项样式，确保跨平台兼容性
        getItemStyle(index) {
            const delay = index * 0.1;
            return {
                'transition-delay': delay + 's',
                '-webkit-transition-delay': delay + 's', // iOS Safari 兼容
                'animation-delay': delay + 's',
                '-webkit-animation-delay': delay + 's' // iOS Safari 兼容
            };
        },
    }
}
</script>

<style scoped lang="scss">
/* ✅ 强制隐藏 u-input 的字体 */
.hidden-input ::v-deep .u-input__input {
    color: transparent !important;
}


/* 字符动画展示层（与输入层重叠） */
.animated-text {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    height: 80rpx;
    width: 100%;
    z-index: 1;
    pointer-events: none;
}

/* 单字符 */
.char-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.char-text {
    font-family: Gilroy-ExtraBold;
    font-weight: 400;
    font-size: 30*2rpx;
    line-height: 36*2rpx;
    letter-spacing: 0%;
    color: #000;
    display: inline-block;
    transform: translateY(0);
    opacity: 1;
    transition: all 0.3s ease;
}

/* 进入动画 */
.char-text.enter {
    transform: translateY(100%);
    opacity: 0;
    animation: slideUp 0.3s forwards;
}

/* 离开动画 */
.char-text.leave {
    transform: translateY(0%);
    opacity: 1;
    animation: slideDown 0.3s forwards;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }

    to {
        transform: translateY(0%);
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(0%);
        opacity: 1;
    }

    to {
        transform: translateY(100%);
        opacity: 0;
    }
}

::v-deep .u-input__input {
    font-family: Gilroy-ExtraBold;
    font-weight: 400;
    font-size: 30*2rpx;
    line-height: 36*2rpx;
    letter-spacing: 0%;
    color: #000;
}



.container {
    padding: 32rpx;
    width: 100%;

    .transfer-card {
        border: 2rpx solid #D9D6D6;
        background: #fff;
        border-radius: 34rpx;
        // box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;

        .transfer-icons {
            width: 100%;
            margin-top: 40rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-bottom: 40rpx;
            border-bottom: 2rpx dashed #999999;

            .token-icon {
                width: 140rpx;
                height: 140rpx;
                border-radius: 50%;
            }

            .arrow-box {
                margin: 0 41rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                animation: pulse 1.5s infinite ease-in-out;

                .arrow-icon {
                    width: 79rpx;
                    height: 44rpx;
                }
            }
        }

        .connecting-text {
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 36rpx;
            line-height: 120%;
            text-align: center;
            color: #000;
            margin: 40rpx 0 32rpx 0;
        }

        .transfer-amount {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 40rpx;

            .amount-left,
            .amount-right {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 160%;
                text-align: center;
                color: #666;
            }

            .amount-arrow {
                width: 64rpx;
                margin: 0 60rpx 0 56rpx;
            }
        }
    }

    /* 箭头盒子的呼吸动画 */
    @keyframes pulse {
        0% {
            border-color: #ff4d4f;
            transform: scale(1);
        }
        50% {
            border-color: #ff7875;
            transform: scale(1.1);
        }
        100% {
            border-color: #ff4d4f;
            transform: scale(1);
        }
    }


    /* 原生弹窗遮罩层 */
    .popup-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: background-color 0.3s ease-out;
    }

    .popup-content {
        background-color: #fff;
        padding: 44rpx;
        border-radius: 40rpx;
        box-shadow: 7*2rpx 10*2rpx 100.3*2rpx 0 #0000001A;
        width: 688rpx;
        max-width: 90%;
        /* 添加弹窗动画效果 */
        transform: translateY(570px);
        opacity: 0;
        transition: all 0.3s ease-out;

        &.show {
            transform: translateY(235px);
            opacity: 1;
        }

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            // padding: 20rpx;

            .popup-title {
                font-size: 36rpx;
                font-weight: bold;
                color: #333;
            }

            .close-btn {
                image {
                    width: 46rpx;
                    height: 46rpx;
                }
            }
        }

        .payment-options {
            padding: 20rpx 0;
        }

        .option-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: 32rpx;
            /* 初始状态：向下偏移60px，透明度为0 */
            transform: translateY(60px);
            opacity: 0;
            transition: all 0.3s ease-out;

            &:last-child {
                margin-bottom: 0;
            }

            /* 动画状态：回到原位置，透明度为1 */
            &.item-animate {
                transform: translateY(0);
                opacity: 1;
            }

            // &.selected .custom-radio {
            //     background-color: #008E28; // 选中时的绿色填充
            //     border-color: #008E28;
            // }



            .option-icon {
                width: 90rpx;
                height: 90rpx;
                margin-right: 20rpx;
            }

            .option-name {
                flex: 1;
                font-size: 32rpx;
                color: #000;

                font-family: Gilroy-SemiBold;
                font-weight: 400;
                line-height: 19.6*2rpx;

            }

            .custom-radio {
                // width: 58rpx;
                // height: 58rpx;
                // border: 2rpx solid #ccc;
                // border-radius: 50%;
                // background-color: #fff;
                transition: all 0.3s;
                position: relative;

                image {
                    width: 58rpx;
                    height: 58rpx;
                }

                // w
            }
        }
    }



    .form-item {
        margin-top: 60rpx;
        position: relative;

        .scan {
            position: absolute;
            right: 38rpx;
            bottom: 27rpx;
            width: 48rpx;
            height: 48rpx;
            // margin-left: 10rpx;
            // margin-top: -4rpx;
        }

        .check_label {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            color: #666;
        }

        .label {
            margin-left: 20rpx;
            font-family: Gilroy-Medium;
            font-weight: 400;
            font-size: 16*2rpx;
            // line-height: 19.2*2rpx;
            letter-spacing: 0%;
            color: #FF82A3;
        }

        .icon_serve {
            width: 40rpx;
            height: 40rpx;
        }

        .right_fix {
            position: absolute;
            bottom: 28rpx;
            right: 38rpx;
            width: 48rpx;
            height: 48rpx;
        }

        .inputs {
            margin-top: 20rpx;
            height: 48*2rpx !important;
            border-radius: 10*2rpx;
            border-width: 2rpx;
            border: 2rpx solid #999999;
            font-family: Gilroy-Medium;
            color: #333;
            font-weight: 400;
            font-size: 14*2rpx;
            letter-spacing: 0%;
            padding: 0 38rpx !important;

            .right {
                width: 32rpx;
                height: 32rpx;

                image {
                    width: 32rpx;
                    height: 32rpx;
                }
            }

            .input_view {

                .coins {
                    display: flex;
                    align-items: center;
                    margin-left: 20rpx;

                    image {
                        width: 50rpx;
                        height: 50rpx;
                    }

                    text {
                        line-height: 0;
                        margin-left: 10rpx;
                    }
                }

                >image {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }

        >text {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #000;
        }
    }

    .from-currency,
    .to-currency {
        display: block;
        padding: 30rpx 0 0 30rpx;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 22.4*2rpx;
        letter-spacing: 0%;
        color: #666;
    }

    .float_box {
        // position: absolute;
        // top: -2rpx;
        // left: -2rpx;
        border-radius: 17*2rpx;
        background: #FFFFFF;
        height: 94*2rpx;
        width: 101%;
        border: 2rpx solid #D9D6D6;
        position: relative;

        .between {
            position: relative;

            .animated-text {
                position: absolute;

            }
        }

        .padding {
            align-items: center;
            margin: 10rpx 40rpx 0 40rpx;

        }


        .frominfo {
            display: flex;
            transition: transform 0.3s ease-in-out;
            align-items: center;
            justify-content: space-between;
            // padding: 5rpx 8rpx;
            padding: 0 22rpx;
            width: 88*2rpx;
            height: 32*2rpx;
            border-radius: 20*2rpx;
            background: #FF82A333;
            position: relative;

            .helpoption {
                width: 85*2rpx;
                transition: transform 0.3s ease, opacity 0.3s ease;
                transform-origin: top;
                /* 设置变换的起点为顶部 */
                z-index: 9999;
                position: absolute;
                top: 80rpx;
                left: 0;
                box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;
                max-height: 400rpx;
                overflow-y: auto;
                // background-color: rgba(0, 0, 0, .5);
                background: #fff;
                border-radius: 16*2rpx;
                padding: 16*2rpx;
                opacity: 1;
                //padding: 100rpx;
                // height: 446rpx;
                display: flex;
                align-items: flex-start;
                flex-direction: column;

                &.collapse {
                    transform: scaleY(0) translateY(-100%);
                    /* 缩小至0，并向上移动 */
                    opacity: 0;
                }

                &.expand {
                    transform: scaleY(1) translateY(0%);
                    /* 恢复到正常大小，并位置恢复 */
                    opacity: 1;

                }

                >view {

                    padding: 15rpx 0;
                    display: flex;
                    align-items: center;

                    image {
                        width: 40rpx;
                        height: 30rpx;
                    }

                    text {
                        margin-left: 20rpx;
                        display: block;
                        font-family: Gilroy-Bold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 19.2*2rpx;
                        color: #000;
                    }
                }
            }

            image {
                width: 48rpx;
                height: 48rpx;
            }

            text {
                margin-left: 10rpx;
                color: #333333;
                line-height: 38rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                letter-spacing: 0%;

            }
        }
    }

    .bom {
        white-space: nowrap;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 26rpx;
        color: #121212;
        margin-top: 16rpx;
        font-weight: 400;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 14*2rpx;
        line-height: 22.4*2rpx;
        letter-spacing: 0%;

        text {
            display: block;
        }

        width: 100%;
    }

    .btn {
        position: fixed;
        bottom: 60rpx;
        // width: 90%;
        width: 340*2rpx;

        text {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #000;
        }

        .exchange-btn {
            // margin: 0 32rpx;
            height: 100rpx;
            background: #FF82A3;
            border-radius: 64*2rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            color: #fff;
        }

        .exchange-btn[disabled] {
            // background: #FF82A380; // 加透明度效果
            background: #D9D6D6;
            color: #666666;
        }
    }

    .form-wrapper {
        width: 100%;


        .form-row {
            width: 100%;
            // display: flex;
            height: 274rpx;
            // align-items: center;
            // margin: -20rpx 0;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            background: #FF82A326;
            border: 2rpx solid #D9D6D6;
            border-radius: 17*2rpx;
            position: relative;

            .float_box {
                position: absolute;
                top: -2rpx;
                left: -2rpx;
                border-radius: 17*2rpx;
                background: #FFFFFF;
                height: 94*2rpx;
                width: 101%;
                border: 2rpx solid #D9D6D6;

                .padding {
                    align-items: center;
                    margin: 10rpx 40rpx 0 40rpx;

                }


                .frominfo {
                    display: flex;
                    transition: transform 0.3s ease-in-out;
                    align-items: center;
                    justify-content: space-between;
                    // padding: 5rpx 8rpx;
                    padding: 0 22rpx;
                    width: 88*2rpx;
                    height: 32*2rpx;
                    border-radius: 20*2rpx;
                    background: #FF82A333;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }

                    text {
                        margin-left: 10rpx;
                        color: #333333;
                        line-height: 38rpx;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        letter-spacing: 0%;

                    }
                }
            }

            .available {
                font-size: 24rpx;
                color: #121212;
                display: block;
                // padding: 40rpx 0 0 40rpx;
                position: absolute;
                bottom: 24rpx;
                left: 30rpx;

                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #000;
            }



            label {
                flex: 1;
                font-size: 16px;
                color: #333;
            }

            select,
            input {
                flex: 2;
                padding: 10px;
                font-size: 16px;
                border-radius: 6px;
                border: 1px solid #ddd;
                margin-left: 20px;
            }
        }

    }

    .fee {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16rpx;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 28rpx;
        line-height: 160%;
        letter-spacing: 0%;
        color: #000;
    }


}
</style>