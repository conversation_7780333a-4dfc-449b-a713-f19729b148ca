<template>
    <view class="container">
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="$t('Sell.SellCrypto')">
            <view slot="right" class="search-box" @click="nav_to('Record', 'withdraw')">
                <image src="https://pro-oss.pinkwallet.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
            </view>
        </u-navbar>

        <!-- 兑换模块 -->
        <view class="form-wrapper">
            <!-- :style="{ backgroundColor: isSwapped ? '#e6f0ff' : '' }" -->
            <view class="form-row from">
                <view class="float_box">
                    <view class="from-currency">{{ $t("Sell.YouSell") }}</view>
                    <view class="flex_divide padding">
                        <u-input v-model="fromAmount" type="number" @input="onfromAmountInputChange" class="from-input"
                            placeholder="0"></u-input>
                        <view class="frominfo" @click="Choose()">
                            <transition name="expand-slide">
                                <view class="helpoption" v-show="fromShow">
                                    <view v-for="(item, index) in CoinList" :key="index" class="Roptions"
                                        @click="SetCoin(item)">
                                        <text>{{ item.name }}</text>
                                    </view>
                                </view>
                            </transition>

                            <!-- <image :src="fromCurrency.icon" /> -->
                            <text>{{ fromCurrency || '-' }}</text>
                            <image :class="{ rotated: isChoose }"
                                src="https://pro-oss.pinkwallet.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png" />
                        </view>
                    </view>
                </view>


                <view class="available">{{ $t("Sell.Available") }} : {{ balanceFrom || 0.00 }}
                    <!-- <text v-if="fromAmount" style="margin-left: 14rpx;">最大</text> -->
                </view>
            </view>

            <!-- Swap Button -->
            <view class="swap_btn" :class="{ rotated: isSwapped }" @click="swap()">
                <image src="https://pro-oss.pinkwallet.com/image/20250304/4d06d309ed8535459783ea435e356d7e_61x69.png" />
            </view>

            <view class="float_box">
                <view class="to-currency">{{ $t("Sell.YouGet") }}</view>
                <view class="flex_divide padding">
                    <u-loading :show="showToAmount"></u-loading>
                    <u-input :disabled="true" v-model="toAmount" class="to-input" placeholder="0"></u-input>
                    <view class="frominfo" @click="Choosed()">
                        <transition name="expand-slide">
                            <view class="helpoption" v-show="toShow">
                                <view v-for="(item, index) in CoinList" :key="index" class="Roptions"
                                    @click="SetCointo(item)">
                                    <text>{{ item.name }}</text>
                                </view>
                            </view>
                        </transition>
                        <!-- <image :src="toCurrency.icon" /> -->
                        <text>{{ toCurrency || '-' }}</text>
                        <image :class="{ rotated: isChoosed }"
                            src="https://pro-oss.pinkwallet.com/image/20250304/d37b9508f6c39e318e209d1fd0ea6826_96x96.png" />
                    </view>
                </view>
            </view>

            <!-- <view class="bom">
                <text>Fee: {{ nowPair.handFeeRate || 0.00 }} {{ feeSymbol }}</text>
                <text> </text>
            </view> -->



        </view>

        <!-- 联系人 -->
        <view class="contact">
            <view class="titles">{{ $t("Sell.ReceiveFundsIn") }}</view>

            <scroll-view :scroll-y="true" @scrolltolower="bottomOutAccount()" class="dropdown-section flex-column">
                <view class="account-item" v-for="(account, index) in accounts" :key="index"
                    @click="selectAccount(account, index)">
                    <!-- <image :src="account.avatar" class="avatar" mode="aspectFill"></image> -->
                    <!-- {{ account }} -->
                    <view class="account-info flex_divide">
                        <text class="name" v-if="account.tag">{{
                            account.tag || '' }}</text>
                        <text class="bank">{{ account.network || '' }}</text>
                        <text class="account-number">{{
                            account.address || account.token }}</text>
                    </view>
                    <!-- <view @click.stop="checkAccount(account)">
                        <u-radio shape="circle" v-model="account.check" active-color="#008E28"></u-radio>
                    </view> -->
                    <view class="radio" @click.stop="checkAccount(account, index)">
                        <!-- 默认 -->
                        <image v-if="selectedIndex !== index"
                            src="https://pro-oss.pinkwallet.com/image/********/d5c68369f0090c7d2709e143fe4ef322_96x97.png" />

                        <!-- 选中 -->
                        <image v-else
                            src="https://pro-oss.pinkwallet.com/image/********/f9753a20b8dbffd316323879c3a3bb0a_97x96.png" />
                    </view>
                </view>
            </scroll-view>

            <view class="add flex_all" @click="showAddPopup = true">
                <image src="https://pro-oss.pinkwallet.com/image/********/157d7ae37faa21a2007c47b332065f81_80x80.png" />
                {{ $t("Sell.AddNew") }}
            </view>
        </view>

        <!-- btn -->
        <u-button class="btns" hover-class="none" type="primary" @click="convert">{{ $t("Sell.Sell") }} {{ fromCurrency
        }}</u-button>


        <!-- 添加账户选择 -->
        <u-popup v-model="showAddPopup" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="popup-content">
                <view class="popup-header flex_divide">
                    <view class="popup-title">{{ $t("Transfer.selectaccounttype") }}</view>
                    <image @click="showAddPopup = false"
                        src="https://pro-oss.pinkwallet.com/image/********/5b6a43cb2e49dd69bcc90c50372295b0_92x92.png" />
                </view>
                <u-button hover-class="none" @click="nav_to('AddCount', '3', 'sell')" class="popup-button" plain>{{
                    $t("Send.pwcount") }}</u-button>
                <!-- <u-button hover-class="none" @click="nav_to('AddCount', '1', 'sell')" class="popup-button" plain>
                    {{ $t("Transfer.BankAccount") }} </u-button> -->
                <u-button hover-class="none" @click="nav_to('AddCount', '2', 'sell')" class="popup-button" plain>{{
                    $t("Transfer.TokenAddress") }}</u-button>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    data() {
        return {
            showToAmount: false,
            showAddPopup: false,
            isSwapped: false,
            fromAmount: '',
            fromCurrency: "",
            balanceFrom: "",
            toAmount: '',
            toCurrency: "",
            balanceTo: "",
            Account: {
                pageNum: 1,
                pageSize: 10
            },
            page: {
                pageNum: 1,
                pageSize: 10
            },
            accounts: [],
            selectedIndex: null,
            selectedAccount: null,
            CoinList: [],
            toShow: false,
            fromShow: false,
            isChoose: false,
            isChoosed: false,
            inputTimer: null,
            RFQObj: {},
            nowPair: {},
            rfqId: ""
        }
    },
    onLoad(e) {
        if (e.fromCurrency) {
            this.fromCurrency = e.fromCurrency
        }
        uni.setNavigationBarTitle({
            title: this.$t("page.sell") // 切换语言后重新设置标题
        })
        this.getAvailableBalance()
        this.getsearchUserWithdrawAccountPaged()
    },
    watch: {
        fromAmount: {
            // console.log(555);
            handler(newVal) {
                if (newVal && this.fromCurrency && this.toCurrency) {
                    this.debounceQuery()
                }
            },
        },
        toShow(val) {
            if (val) {
                this.fromShow = false
            }
        },
        fromShow(val) {
            if (val) {
                this.toShow = false
            }
        },
    },
    methods: {
        onfromAmountInputChange(event) {
            let value = event; // 取出输入框的值
            let decimalDigits = this.nowPair.priceSignificantDigits || 2; // 小数位限制
            console.log(event, decimalDigits);

            let pattern = new RegExp(`^\\d*(\\.\\d{0,${decimalDigits}})?`);
            let matched = value.match(pattern);
            let formatted = matched ? matched[0] : "";

            this.$nextTick(() => {
                this.fromAmount = formatted;
            });
        },
        async convert() {
            let res = await this.$api.addOrder({
                requestForQuoteId: this.rfqId
            })
            if (res.code == 200) {
                // this.swapType = 'confirmSuccess'
                this.fromAmount = ''
                this.toAmount = ''
                this.balanceFrom = ""
                this.balanceTo = ""

                this.fromCurrency = ""
                this.toCurrency = ""
                this.selectedAccount = ""
                this.getAvailableBalance()
                this.$u.toast(res.msg)
            } else {
                this.$u.toast(res.msg)
            }
        },
        debounceQuery() {
            clearTimeout(this.inputTimer); // 清除上一次的定时器
            this.inputTimer = setTimeout(() => {
                // if (this.fromAmount) {
                this.checkAndFetchPair();
                // }
            }, 300); // 300ms 之内有新的输入，就不会请求
        },
        checkAndFetchPair() {
            if (this.fromCurrency && this.toCurrency) {
                this.getCoinPair();
            }
        },
        // app-查询币对的组合
        async getCoinPair() {
            let res = await this.$api.coinPair({
                coin0: this.fromCurrency,
                coin1: this.toCurrency
            })
            if (res.code == 200) {
                this.nowPair = res.result
                if (res.result.isTradable != 'true') {
                    this.$u.toast('Now CoinPair not Trade')
                    return
                } else {
                    if (this.fromAmount) {
                        this.queryPrice()
                    }
                }
            } else {
                // this.$u.toast(res.msg)
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        // app-闪兑询价
        async queryPrice() {
            if (!this.selectedAccount) {
                uni.showToast({
                    icon: 'none',
                    title: '请先选择账户',
                    duration: 2000
                });
                return
            }
            this.showToAmount = true
            let res = await this.$api.Askrfq({
                coinPair: this.nowPair.coinPair.split('.')[0],
                baseCoin: this.nowPair.baseCoin,
                targetCoin: this.nowPair.targetCoin,
                amount: this.fromAmount,
                side: this.Check(),
                transferOut: 1,
                transferJsonStr: JSON.stringify({
                    amount: this.fromAmount,
                    withdrawType: this.selectedAccount?.type,
                    symbol: this.fromCurrency,
                    token: this.selectedAccount?.token,
                    network: this.selectedAccount?.network,
                    address: this.selectedAccount?.address,
                    // fee: 1,
                    memo: this.selectedAccount?.memo,
                })

                // coinPair: this.nowPair.coinPair.split('.')[0],
                // baseCoin: this.nowPair.baseCoin,
                // targetCoin: this.nowPair.targetCoin,
                // side: this.Check()
            })
            if (res.code == 200) {
                this.RFQObj = res.result
                const now = Date.now();
                const validUntil = new Date(res.result.validUntil).getTime();
                // this.timeDiff = Math.floor((validUntil - now) / 1000); // 计算秒差
                this.showToAmount = false // loading动画
                this.toAmount = res.result.finalAddCoinAmount
                this.rfqId = res.result.rfqId
                // this.startCountdown();

            } else {
                this.showToAmount = false // loading动画
                this.$u.toast(res.msg)
            }
        },
        // 获取买卖方向
        Check() {
            if (this.fromCurrency && this.nowPair.baseCoin && this.nowPair.targetCoin) {
                //    return this.fromCurrency == this.nowPair.baseCoin ? 'sell' : 'buy'
                if (this.fromCurrency == this.nowPair.baseCoin) {
                    return 'sell'
                } else {
                    return 'buy'
                }

                if (this.fromCurrency == this.nowPair.targetCoin) {
                    return 'buy'
                } else {
                    return 'sell'
                }
            }
        },
        SetCointo(e) {
            this.balanceTo = e.balance
            this.toCurrency = e.symbol
            this.toAmount = ''
            if (this.fromCurrency && this.fromAmount) {
                this.debounceQuery()
            }
            // this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency)
            // this.getLine()
            // this.initChartLine()

        },
        SetCoin(e) {
            this.balanceFrom = e.balance
            this.fromCurrency = e.symbol
            // this.getfineGrainedCoinPrice(this.fromCurrency, this.toCurrency)
            // this.getLine()
            // this.initChartLine()
            if (this.fromAmount) {
                this.checkAndFetchPair()
            }

        },
        // app-查询自己币对和可用余额信息
        async getAvailableBalance() {
            let res = await this.$api.userAvailableCoinList({
                // pageNum: this.page.pageNum,
                // pageSize: this.page.pageSize
                pageSize: 100
            })
            if (res.code == 200) {
                if (this.page.pageNum == 1) {
                    this.CoinList = res.result.data
                } else {
                    this.CoinList = this.CoinList.concat(res.result.data)
                }
                // this.fromCurrency = this.CoinList[0].name
                // this.toCurrency = this.CoinList[1].name



                // this.balanceFrom = this.CoinList[0].balance
                // this.balanceTo = this.CoinList[1].balance

            }
        },
        selectAccount(account) {
            console.log(account);

            this.$Router.push({
                name: 'AccountDetail',
                params: {
                    account: encodeURIComponent(JSON.stringify(account))
                }
            })
            // this.selectedAccount = account;
        },
        checkAccount(e, index) {
            console.log(e, 'nowrow');
            this.selectedIndex = index
            this.accounts.forEach((item, i) => {
                item.check = i === index
            })
            this.selectedAccount = this.accounts[index]
            if (this.fromCurrency && this.toCurrency && this.fromAmount) {
                this.queryPrice()
            }
            // this.nowrow = e
            // // this.nownetwork = e?.network
            // this.nowAccount = e.token || e.address
            // this.searchQuery = e.token || e.address
            // this.FetchgetWithdrawFee(e)
        },
        bottomOutAccount() {
            this.Account.pageNum++
            this.getsearchUserWithdrawAccountPaged()
        },
        async getsearchUserWithdrawAccountPaged(arg) {
            let res = await this.$api.searchUserWithdrawAccountPaged({
                pageNum: this.Account.pageNum,
                pageSize: this.Account.pageSize,
                // symbol: arg,
                searchType: 1,
            })
            if (res.code == 200) {
                if (this.Account.pageNum == 1) {
                    this.accounts = res.result.data;

                    this.accounts = (res.result.data || []).map(item => ({
                        type: item.type,
                        ...(item.withdrawCryptoDTO || {}), // 避免 null
                        ...(item.withdrawInsideAccountDTO || {}), // 避免 null
                        check: false
                    }));
                    console.log(this.accounts);

                    // this.addressList = [...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data]
                } else {
                    this.accounts = this.accounts.concat(res.result.data)

                }
            }
        },
        swap() {
            this.isSwapped = !this.isSwapped;
            // 交换 from 和 to 的货币数据
            const tempCurrency = this.fromCurrency;
            this.fromCurrency = this.toCurrency;
            this.toCurrency = tempCurrency;

            const tempBalance = this.balanceFrom;
            this.balanceFrom = this.balanceTo;
            this.balanceTo = tempBalance;

            // 交换金额
            const tempAmount = this.fromAmount;
            this.fromAmount = this.toAmount;
            this.toAmount = tempAmount;

            if (this.fromAmount) {
                // this.checkAndFetchPair()
            }
        },
        Choose() {
            this.fromShow = !this.fromShow
            this.isChoose = !this.isChoose;

        },
        Choosed() {
            this.toShow = !this.toShow
            this.isChoosed = !this.isChoosed;

        },
        nav_to(name, type, from) {
            this.$Router.push({
                name: name,
                params: {
                    type: type,
                    from
                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-input {
    height: 60rpx !important;
    width: 400rpx !important;
}

::v-deep .u-input__input {
    font-family: Gilroy-ExtraBold;
    font-weight: 400;
    font-size: 30*2rpx;
    line-height: 36*2r12px;
    letter-spacing: 0%;
    color: #000;
}

.rotated {
    transform: rotate(180deg);
}

.container {
    padding: 70rpx 32rpx 32rpx 32rpx;

    .btns {
        margin-top: 266rpx;
        width: 100%;
        height: 50*2rpx;
        border-radius: 64*2rpx;
        background: #FF82A3;
        color: #fff;
        font-family: Gilroy-Bold;
        font-weight: 400;
        font-size: 32rpx;
        letter-spacing: 0%;
        text-align: center;

    }

    .contact {
        margin-top: 32rpx;


        .add {
            margin-top: 32rpx;
            height: 100rpx;
            border-radius: 128rpx;
            border: 2rpx solid #999999;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 32rpx;
            line-height: 120%;
            letter-spacing: 0%;
            text-align: center;
            vertical-align: middle;
            color: #D72D4A;

            image {
                width: 40rpx;
                height: 40rpx;
                margin-right: 12rpx;
            }
        }

        .titles {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 160%;
            letter-spacing: 0%;
            color: #666666;
        }

        .dropdown-section {
            // padding: 16rpx;
            max-height: 500rpx;
            overflow-y: auto;

            .account-item {
                width: 100%;
                display: flex;
                align-items: center;
                padding: 20rpx 0 20rpx 20rpx;
                border-bottom: 2rpx solid #999999;
                cursor: pointer;

                // &:hover {
                //     background-color: #f5f5f5;
                // }

                &:last-child {
                    border-bottom: none;
                }

                .avatar {
                    width: 60rpx;
                    height: 60rpx;
                    border-radius: 50%;
                    margin-right: 20rpx;
                }

                .account-info {
                    flex: 1;
                    color: #000;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    line-height: 19.2*2rpx;

                    .name {}

                    .bank {}

                    .account-number {
                        width: 400rpx;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }

                .radio {
                    margin-left: 16rpx;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }

                }
            }
        }
    }

    .form-wrapper {
        width: 100%;
        border-radius: 6px;
        position: relative;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;

        .swap_btn {
            // position: absolute;
            // left: 50%;
            cursor: pointer;
            transition: transform 0.3s ease-in-out;
            height: 40rpx;
            margin: -20rpx 0;
            // top: 220rpx;
            z-index: 999;
            // margin: 0 auto;
            border-radius: 50%;
            background-color: #FF82A3;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 68rpx;
            height: 68rpx;


            image {
                width: 34rpx;
                height: 30rpx;
            }
        }

        .float_box {
            // position: absolute;
            // top: -2rpx;
            // left: -2rpx;
            border-radius: 17*2rpx;
            background: #FFFFFF;
            height: 94*2rpx;
            width: 101%;
            border: 2rpx solid #D9D6D6;

            .padding {
                align-items: center;
                margin: 10rpx 40rpx 0 40rpx;
            }

            .to-currency {
                display: block;
                padding: 30rpx 0 0 30rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }

            .frominfo {
                display: flex;
                transition: transform 0.3s ease-in-out;
                align-items: center;
                justify-content: space-between;
                // padding: 5rpx 8rpx;
                padding: 0 22rpx;
                width: 88*2rpx;
                height: 32*2rpx;
                border-radius: 20*2rpx;
                background: #FF82A333;
                position: relative;

                .helpoption {
                    max-height: 400rpx;
                    overflow-y: auto;
                    width: 85*2rpx;
                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 9999;
                    position: absolute;
                    top: 80rpx;
                    left: 0;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;

                    }

                    >view {

                        padding: 15rpx 0;
                        display: flex;
                        align-items: center;

                        image {
                            width: 40rpx;
                            height: 30rpx;
                        }

                        text {
                            margin-left: 20rpx;
                            display: block;
                            font-family: Gilroy-Bold;
                            font-weight: 400;
                            font-size: 16*2rpx;
                            line-height: 19.2*2rpx;
                            color: #000;
                        }
                    }
                }

                image {
                    width: 48rpx;
                    height: 48rpx;
                }

                text {
                    margin-left: 10rpx;
                    color: #333333;
                    line-height: 38rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    letter-spacing: 0%;

                }
            }
        }

        .form-row {
            width: 100%;
            // display: flex;
            height: 274rpx;
            // align-items: center;
            // margin: -20rpx 0;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            background: #FF82A326;
            border: 2rpx solid #D9D6D6;
            border-radius: 17*2rpx;
            position: relative;

            .float_box {
                position: absolute;
                top: -2rpx;
                left: -2rpx;
                border-radius: 17*2rpx;
                background: #FFFFFF;
                height: 94*2rpx;
                width: 101%;
                border: 2rpx solid #D9D6D6;



                .frominfo {
                    display: flex;
                    transition: transform 0.3s ease-in-out;
                    align-items: center;
                    justify-content: space-between;
                    // padding: 5rpx 8rpx;
                    padding: 0 22rpx;
                    width: 88*2rpx;
                    height: 32*2rpx;
                    border-radius: 20*2rpx;
                    background: #FF82A333;
                    position: relative;

                    .helpoption {
                        width: 85*2rpx;
                        transition: transform 0.3s ease, opacity 0.3s ease;
                        transform-origin: top;
                        /* 设置变换的起点为顶部 */
                        z-index: 11;
                        position: absolute;
                        top: 80rpx;
                        left: 0;
                        box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                        // background-color: rgba(0, 0, 0, .5);
                        background: #fff;
                        border-radius: 16*2rpx;
                        padding: 16*2rpx;
                        opacity: 1;
                        //padding: 100rpx;
                        // height: 446rpx;
                        display: flex;
                        align-items: flex-start;
                        flex-direction: column;

                        &.collapse {
                            transform: scaleY(0) translateY(-100%);
                            /* 缩小至0，并向上移动 */
                            opacity: 0;
                        }

                        &.expand {
                            transform: scaleY(1) translateY(0%);
                            /* 恢复到正常大小，并位置恢复 */
                            opacity: 1;

                        }

                        >view {

                            padding: 15rpx 0;
                            display: flex;
                            align-items: center;

                            image {
                                width: 40rpx;
                                height: 30rpx;
                            }

                            text {
                                margin-left: 20rpx;
                                display: block;
                                font-family: Gilroy-Bold;
                                font-weight: 400;
                                font-size: 16*2rpx;
                                line-height: 19.2*2rpx;
                                color: #000;
                            }
                        }
                    }

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }

                    text {
                        margin-left: 10rpx;
                        color: #333333;
                        line-height: 38rpx;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        letter-spacing: 0%;

                    }
                }
            }

            .available {
                font-size: 24rpx;
                color: #121212;
                display: block;
                // padding: 40rpx 0 0 40rpx;
                position: absolute;
                bottom: 24rpx;
                left: 30rpx;

                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #000;
            }

            .from-currency,
            .to-currency {
                display: block;
                padding: 30rpx 0 0 30rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }

            label {
                flex: 1;
                font-size: 16px;
                color: #333;
            }

            select,
            input {
                flex: 2;
                padding: 10px;
                font-size: 16px;
                border-radius: 6px;
                border: 1px solid #ddd;
                margin-left: 20px;
            }
        }

        .bom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 26rpx;
            color: #121212;
            margin-top: 16rpx;
            font-weight: 400;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;

            text {
                display: block;
            }

            width: 100%;
        }
    }

    .popup-content {
        // padding: 20px;
        width: 694rpx;
        margin: 0 32rpx;
        // height: 190*2rpx;
        padding-bottom: 32rpx;
        border-radius: 20*2rpx;
        background: #FFFFFF;
        box-shadow: 14rpx 20rpx 100.3*2rpx 0px #0000001A;

        .popup-header {
            padding: 44rpx 44rpx 32rpx 44rpx;

            image {
                width: 46rpx;
                height: 46rpx;
            }

            .popup-title {
                // margin-bottom: 20px;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 20*2rpx;
                line-height: 24*2rpx;
                letter-spacing: 0%;
                color: #000;
            }
        }



        .popup-button {
            width: 100%;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 16*2rpx;
            color: #333;
            display: flex;
            justify-content: flex-start !important;
            background: transparent !important;

            &:hover {
                background-color: transparent;
                /* 取消 hover 背景颜色 */
            }

            &:active {
                background-color: transparent;
                /* 取消 active 背景颜色 */
            }
        }
    }

}
</style>