<template>
    <view class="container">
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="$t('withdraw.SendCrypto')">
            <view slot="right">
                <view class="flex_x">
                    <view class="search-box2" @click="nav_to('Record', 'swap')">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250304/2f4bf831f143d194ca7375413d478b94_20x20.png" />
                    </view>
                    <view class="search-box" @click="showManagePopup = true">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1. .com/image/********/08531f1fd9d0df3287e5b480e2be3418_80x80.png" />
                    </view>
                </view>

            </view> 
        </u-navbar>

        <view class="form-container">
            <view class="form-item">
                <text>{{ $t("withdraw.WithdrawalCurrency") }}</text>
                <view class="inputs flex_divide" @click.prevent="showCoin = !showCoin">
                    <view class="input_view flex_y">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/098bd983e0cb24382da5927a853274b9_80x80.png" />
                        <view class="coins">
                            <!-- <image
                                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250306/72e5d75d2638d2439b2d1037ca19c985_80x80.png" /> -->
                            <text>{{ nowCurrency }}</text>
                        </view>
                    </view>
                    <view class="right" @click.stop="clearNowCurrency" v-show="nowCurrency">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/f5ab1cf813f79ba4751f0a4b6c735bff_120x120.png" />
                    </view>


                    <transition name="expand-slide">
                        <view class="helpoption" v-show="showCoin">
                            <view class="popup-top-title">Crypto</view>
                            <scroll-view :scroll-y="true" @scrolltolower="bottomOut()" class="crypto flex-column">
                                <view class="crypto-item " @click.stop="choose_coin(item)" v-for="item in cryptoList"
                                    :key="item.id">
                                    <image :src="item.image" />
                                    <text>{{ item.symbol }}</text>
                                </view>
                            </scroll-view>
                            <!-- <view style="height: 40rpx;"></view>
                            <view class="popup-top-title">Fiat</view>
                            <scroll-view :scroll-y="true" @scrolltolower="bottomOut2()" class="crypto flex-column">
                                <view class="crypto-item " @click.stop="choose_coin(item)" v-for="item in FiatList"
                                    :key="item.id">
                                    <image :src="item.image" />
                                    <text>{{ item.symbol }}</text>
                                </view>
                            </scroll-view> -->
                        </view>
                    </transition>
                </view>
            </view>
            <view class="form-item">
                <text>{{ $t("Transfer.Network") }}</text>
                <view class="inputs flex_divide" @click="ShowNetwork = !ShowNetwork">
                    <view class="input_view">{{ network }}</view>
                    <view class="right">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/2ac88d2d91400e2686de9a908f6a8e41_96x96.png" />
                    </view>

                    <transition name="expand-slide">
                        <view class="NetworkPopup" v-show="ShowNetwork">
                            <text class="address" @click="choose_net(item)" v-for="item in networkOptions"
                                :key="item.id">{{
                                    item.network
                                }}</text>
                        </view>
                    </transition>
                </view>
                <!-- <u-input height="96" v-model="formData.accountName" placeholder="请输入账户名称" /> -->
            </view>

            <view class="form-item">
                <text>{{ $t("Transfer.Address") }}</text>
                <view class="inputs flex_divide" @click="showAddressPopup = !showAddressPopup">
                    <view class="input_view">{{ formData.address }}</view>
                    <view class="right">
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/2ac88d2d91400e2686de9a908f6a8e41_96x96.png" />
                    </view>
                    <!--   <transition name="expand-slide">
                        <view class="helpoption" v-show="Showaddress">
                           <text class="address" @click="choose_add(item)" v-for="item in AddressList">{{ item.address
                                }}</text> 
                               <text class="address" @click="choose_add()">{{ AddressList.address }}</text>  
                        </view>
                    </transition>-->
                </view>

            </view>
            <view class="form-item">
                <text>Memo</text>
                <u-input height="96" v-model="formData.memo" placeholder="  " />
            </view>
            <view class="form-item">
                <text>{{ $t("withdraw.amounts") }}</text>
                <view class="all" @click="fetchAvaliable(nowCurrency)">{{ $t("withdraw.ALL") }}</view>
                <!-- <view class="dao">$</view> -->
                <u-input height="96" @input="onfromAmountInputChange" :clearable="false" class="amount"
                    v-model="formData.amount" placeholder="" />
            </view>
            <view class="form-item">
                <!-- <u-input height="96" v-model="formData.iban" placeholder="请输入IBAN号" /> -->
                <view class="inputs_bom flex_divide" @click="showcoin = true">
                    <view class="input_view">{{ $t("withdraw.Fee") }}</view>
                    <text class="rights">{{ fee + ' ' + nowCurrency }}</text>
                </view>
            </view>
            <view class="form-item" v-if="formData.amount && nowCurrency">
                <!-- <u-input height="96" v-model="formData.iban" placeholder="请输入IBAN号" /> -->
                <view class="inputs_bom flex_divide" @click="showcoin = true">
                    <view class="input_view">{{ $t("withdraw.amount") }}</view>
                    <view class="rights">
                        <text> {{ formData.amount + ' ' + nowCurrency }} </text>
                        <image @click="AmountDetailsPopup = true"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/d5c19c2f1ebb83160393324856666ab6_96x97.png" />
                    </view>
                </view>
            </view>
            <view class="tips">
                <view class="line1">{{ $t("withdraw.Reminders") }}:</view>
                <view class="content">{{ $t("withdraw.tips") }}</view>
            </view>


            <view class="buttons">
                <u-button class="btns" hover-class="none" type="primary" @click="openPwd">{{ $t("title.submit")
                    }}</u-button>
                <u-button class="btns_chat" hover-class="none" type="primary" @click="goChat">
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370105797093711872.png" />
                    {{ $t("title.LiveSupport")
                    }}</u-button>
                <!-- <text class="tips_text">{{ $t("Transfer.arrivaltime") }} : -- Minutes</text> -->
            </view>

        </view>

        <!-- <view class="bomx flex_all" @click="goChat" style="margin-top: 60rpx;">
            <image class="icon_serve"
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250403/30069ed52b5ee6cce4a080a30f9cde35_81x80.png" />
            <text class="label">{{ $t("title.LiveSupport") }}</text>
        </view> -->

        <!-- 地址弹窗 -->
        <u-popup v-model="showAddressPopup" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="popup-content">
                <!-- 标题 & 关闭按钮 -->
                <view class="popup-header">
                    <text class="title">{{ $t("Transfer.Address") }}</text>
                    <image @click="showAddressPopup = false"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/139c8567b797f5c2c107df3a2fb94062_104x104.png" />
                    <!-- <u-icon name="close" size="24" color="#000" @click="showAddressPopup = false"></u-icon> -->
                </view>
                <scroll-view class="addressContainer" :scroll-y="true" @scrolltolower="bottomOutAddress()">
                    <!-- 账户信息 -->
                    <view class="account-info" v-if="addressList.length > 0 && item.withdrawCryptoDTO"
                        v-for="(item, index) in addressList" @click="choose_add(item)" :key="item.id">
                        <!-- .withdrawCryptoDTO.tag || 'notag' -->
                        <text class="label" v-if="item.withdrawCryptoDTO.tag">{{
                            item.withdrawCryptoDTO.tag }}:</text>
                        <text class="address">{{ item.withdrawCryptoDTO.address || 'no-address' }}</text>
                    </view>
                </scroll-view>


                <!-- 按钮 -->
                <view class="popup-footer">
                    <button class="add-btn" @click="nav_to('AddCount', 'coin')">
                        <text class="icon">＋</text> {{ $t("withdraw.add") }}
                    </button>
                    <button class="manage-btn" @click="openManage">{{ $t("withdraw.Manage") }}</button>
                </view>
            </view>
        </u-popup>

        <!-- 地址管理弹窗 -->
        <u-popup v-model="showManagePopup" mode="center" border-radius="16" bg-color="#fff">
            <view class="popup-container">
                <!-- 标题 & 关闭按钮 -->
                <view class="popup-header">
                    <text class="popup-title">{{ $t("withdraw.sendaddress") }}</text>
                    <image @click="showManagePopup = false"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/139c8567b797f5c2c107df3a2fb94062_104x104.png" />
                </view>
                <!-- 地址列表 -->
                <scroll-view :scroll-y="true" @scrolltolower="bottomOutAddress()" class="popup-list">
                    <view class="popup-item" @click="selectAccount(item)" v-if="addressList.length > 0"
                        v-for="(item, index) in addressList" :key="index">
                        <!-- <image class="popup-item-icon" :src="item.icon"></image> -->
                        <!-- <text class="popup-item-sub" v-if="item.sub">{{ item.sub }}</text> -->

                        <view class="popup-item-info">
                            <text class="popup-item-name" v-if="item.withdrawCryptoDTO">{{ item.withdrawCryptoDTO.tag
                                }}</text>
                        </view>
                        <text class="popup-item-address" v-if="item.withdrawCryptoDTO.address">{{
                            item.withdrawCryptoDTO.address || '--' }}</text>
                        <image class="details"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/********/46200807531a3b9f19f3aa3fe0d3eabf_80x80.png" />
                        <!-- <u-icon name="file-copy" size="20" color="#ff6699" @click="copyAddress(item.address)"></u-icon> -->
                    </view>
                </scroll-view>

                <!-- 底部按钮 -->
                <view class="popup-footer" @click="nav_to('AddCount', 'coin')">
                    <button class="btn-add">
                        <text class="btn-icon">＋</text> {{ $t("withdraw.add") }}
                    </button>
                </view>
            </view>
        </u-popup>

        <!-- 金额详情 -->
        <u-popup v-model="AmountDetailsPopup" mode="center" border-radius="16" bg-color="#fff">
            <view class="amount-popup">
                <!-- 标题 -->
                <view class="popup-header">
                    <text class="popup-title">{{ $t("withdraw.edit") }}</text>
                </view>

                <!-- 说明文本 -->
                <view class="popup-message">
                    {{ $t("withdraw.tip") }}{{ formData.amount }} {{ nowCurrency }}{{ $t("withdraw.tip1") }}
                </view>

                <!-- 金额详情 -->
                <view class="amount-summary">
                    <view class="amount-item">
                        <text class="amount-label">{{ $t("withdraw.Withdrawalamount") }}</text>
                        <text class="amount-value">{{ formData.amount + nowCurrency }} </text>
                    </view>
                    <view class="amount-item">
                        <text class="amount-label">{{ $t("withdraw.amount") }}</text>
                        <text class="amount-value">{{ (formData.amount - fee).toFixed(2) + nowCurrency }}</text>
                    </view>
                </view>

                <!-- 操作按钮 -->
                <view class="popup-actions" @click="AmountDetailsPopup = false">
                    <button class="confirm-btn">{{ $t("title.confirm") }}</button>
                    <button class="cancel-btn">{{ $t("title.cancel") }}</button>
                </view>
            </view>
        </u-popup>

        <!-- 去设置密码 -->
        <u-popup v-model="goSetPopup" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="set-password-modal">
                <view class="set-password-title">{{ $t("Send.SetPassword") }}</view>
                <view class="set-password-description">
                    {{ $t("Send.YouHaventSetALoginPasswordAndAPaymentPasswordYet") }}
                </view>

                <view class="set-password-actions">
                    <view class="set-password-btn set-now-btn" @click="onSetNow">{{ $t("Send.SetNow") }}</view>
                    <view class="set-password-btn later-btn" @click="goSetPopup = false">{{ $t("Send.Later") }}</view>
                </view>
            </view>
        </u-popup>

        <!-- 输入密码 -->
        <u-popup v-model="ConfirmTransfer" mode="center" :mask="true" :close-on-click-mask="true">
            <view class="confirm-transfer-modal">
                <view class="confirm-title">{{ $t("Send.ConfirmTransfer") }}</view>
                <view class="confirm-subtitle">
                    {{ $t("Send.YouAreCurrentlyMakingAPaymentOf") }} {{ formData.amount + ' ' + nowCurrency }}
                </view>

                <view class="confirm-password-label">
                    <text class="plz">{{ $t("Send.PleaseEnterYourPaymentPassword") }}</text>
                    <view class="show-toggle" @click="togglePassword">
                        <image v-if="!showPassword"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370353025834115072.png" />
                        <image v-else
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370353145610854400.png" />
                        <!-- <text class="toggle-text">{{ $t("Send.Show") }}</text> -->
                    </view>
                </view>

                <view class="password-box">
                    <view v-for="(digit, index) in 6" :key="index" class="password-digit">
                        {{ showPassword ? password[index] || '' : password[index] ? '•' : '' }}
                    </view>
                    <!-- 输入框透明覆盖 -->
                    <input type="number" maxlength="6" class="hidden-input" v-model="password" @input="handleInput" />
                </view>


                <view class="set-password-actions">
                    <view class="set-password-btn set-now-btn" @click="submitForm">{{ $t("title.submit") }}</view>
                    <view class="set-password-btn later-btn" @click="ConfirmTransfer = false">{{ $t("title.cancel") }}
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import store from "@/store/index.js"
export default {
    data() {
        return {
            precision: 2,
            userInfo: {},
            showPassword: false,
            password: '',
            goSetPopup: false,
            ConfirmTransfer: false,
            link: "../../../static/serve.html",
            fee: "",
            AmountDetailsPopup: false,
            nowItem: {},
            addressList: [],
            showManagePopup: false,
            showAddressPopup: false,
            ShowNetwork: false,
            AddressList: {},
            Showaddress: false,
            nowCurrency: "",
            cryptoList: [],
            FiatList: [],
            showCoin: false,
            showbank: false,
            showcoin: false,
            shownetwork: false,
            pageType: '', // 页面类型
            formData: {
                SameCount: false,
                isAll: false,
                accountName: '',
                bankName: '',
                sortCode: '',
                amount: '',
                iban: '',
                accountNumber: '',
                bankAddress: '',
                reference: '',
                address: '',
                memo: '',
                payUser: ''
            },
            currencies: [
                {
                    value: 'USD',
                    label: 'USD'
                },
                {
                    value: 'BTC',
                    label: 'BTC'
                }
            ],
            references: ['选填', '必填'],
            networkOptions: [

            ],
            labels: ['选填', '必填'],
            currencyIndex: 0,
            referenceIndex: 0,
            networkIndex: 0,
            labelIndex: 0,
            currency: "",
            network: "",
            Fiat: {
                pageNum: 1,
                pageSize: 10,
            },
            Crypto: {
                pageNum: 1,
                pageSize: 10,
            },
            Account: {
                pageNum: 1,
                pageSize: 10
            },
            inputTimer: null,
            CoinList: []
        };
    },
    watch: {
        ShowNetwork: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.showCoin = false
                }
            }
        },
        showCoin: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.ShowNetwork = false
                }
            }
        },
        nowCurrency: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.getNet()
                }
            },
        },
        network: {
            handler(newVal, oldVal) {
                console.log(newVal, this.nowCurrency);

                if (newVal && this.nowCurrency) {
                    this.getsearchUserWithdrawAccountPaged()
                }
            },
        },
        "formData.amount": {
            handler(newVal, oldVal) {
                if (newVal) {
                    clearTimeout(this.inputTimer); // 清除上一次的定时器
                    this.inputTimer = setTimeout(() => {
                        this.FetchgetWithdrawFee(this.nowItem)
                    }, 300);

                }
            },
        }
    },
    onLoad(options) {
        uni.setNavigationBarTitle({
            title: this.$t("page.withdraw") // 切换语言后重新设置标题
        })
        this.pageType = options.type || 'USD'; // 默认页面类型为 'USD'
        this.getCurrencyFiat()
        this.getCurrencyCrypto()
        this.getUserInfos()
        this.getAvailableBalance()
    },
    methods: {
        async getAvailableBalance() {
            let res = await this.$api.userAvailableCoinList({
                // pageNum: this.Crypto.pageNum,
                pageSize: 100,
                zeroAsset: false,
                fiat: false,
            })
            if (res.code == 200) {
                // if (this.Crypto.pageNum == 1) {
                this.CoinList = res.result.data
                // } else {
                //     this.CoinList = this.CoinList.concat(res.result.data)
                // }
            }
        },
        onfromAmountInputChange(event) {
            console.log(event);
            let decimalDigits = this.precision || 2;
            let pattern = new RegExp(`^\\d*(\\.\\d{0,${decimalDigits}})?`);
            event = (event.match(pattern)?.[0]) || "";
            this.$nextTick(() => {
                this.formData.amount = event
            })
        },
        handleInput(e) {
            this.password = e.target.value.slice(0, 6);
        },
        togglePassword() {
            this.showPassword = !this.showPassword;
        },
        async fetchAvaliable(e) {
            if (e) {
                let res = await this.$api.userAvailableCoinList({
                    symbol: e,
                    zeroAsset: false,
                    fiat: false
                })
                if (res.code == 200) {
                    this.formData.amount = res.result.data[0].balance || 0
                }
            }
            // this.CoinList
        },
        goChat() {
            this.$Router.push({
                name: 'webView',
                params: {
                    url: this.link,

                }
            })
        },
        bottomOut() {
            console.log(123);

        },
        bottomOutAddress() {
            console.log(123);

            this.Account.pageNum++
            this.getsearchUserWithdrawAccountPaged()
        },
        async getsearchUserWithdrawAccountPaged() {
            let res = await this.$api.searchUserWithdrawAccountPaged({
                pageNum: this.Account.pageNum,
                pageSize: this.Account.pageSize,
                symbol: this.nowCurrency,
                network: this.network,
                searchType: 3,
            })
            if (res.code == 200) {
                if (this.Account.pageNum == 1) {
                    this.addressList = res.result.data
                    // this.addressList = [...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data]

                } else {
                    this.addressList = this.addressList.concat(res.result.data)
                }
            }
        },
        selectAccount(account) {
            console.log(account);
            account = {
                type: account.type,
                id: account.id,
                ...(account.withdrawCryptoDTO || {}), // 避免 null
                ...(account.withdrawInsideAccountDTO || {}), // 避免 null
            };
            this.$Router.push({
                name: 'AccountDetail',
                params: {
                    account: encodeURIComponent(JSON.stringify(account))
                }
            })
            // this.selectedAccount = account;
        },
        openManage() {
            this.showAddressPopup = false
            this.showManagePopup = true
        },
        async getAdd() {
            let res = await this.$api.getWalletAddress({
                symbol: this.nowCurrency,
                network: this.network
            })
            if (res.code == 200) {
                // this.qrcodeUrl = res.result.address
                // this.formData.address
                this.AddressList = res.result
            }
        },
        async getCurrencyFiat() {
            let res = await this.$api.symbolListPaged({
                fiat: true,
                symbol: "",
                pageNum: this.Fiat.pageNum,
                pageSize: this.Fiat.pageSize
            });
            this.FiatList = res.result.data
        },
        async getCurrencyCrypto() {
            let res = await this.$api.symbolListPaged({
                fiat: false,
                symbol: "",
                pageNum: this.Crypto.pageNum,
                pageSize: this.Crypto.pageSize
            });
            this.cryptoList = res.result.data
        },
        async getNet() {
            let res = await this.$api.getNetwork({
                symbol: this.nowCurrency,
            })
            if (res.code == 200) {
                this.networkOptions = res.result
                console.log(this.networkOptions);
            }
        },
        choose_net(item) {
            this.network = item.network
            if (this.nowCurrency && this.network) {
                // this.getAdd()

            }

        },
        choose_add(item) {
            this.formData.address = item.withdrawCryptoDTO.address
            this.showAddressPopup = false
            this.nowItem = item

            // this.formData.address = this.AddressList.address
        },
        async FetchgetWithdrawFee(e) {
            console.log(e);

            let res = await this.$api.getWithdrawFee({
                withdrawType: e.type,
                symbol: this.nowCurrency,
                token: e?.token,
                network: this.network,
                address: this.formData.address,
                amount: this.formData.amount,
            })
            this.getWithDrawPrecisions(e)

            if (res.code == 200) {
                this.fee = res.result
            } else {
                this.$u.toast(res.msg)
            }
        },
        async getWithDrawPrecisions(e) {
            this.precision = 2
            let res = await this.$api.getWithDrawPrecision({
                withdrawType: e.type,
                symbol: this.nowCurrency,
                token: e?.token,
                network: this.network,
                address: this.formData.address,
                amount: this.formData.amount,
            })
            if (res.code == 200) {
                this.precision = res.result
            }
        },
        clearNowCurrency() {
            this.nowCurrency = ""
            this.showCoin = false

        },
        choose_coin(item) {
            this.nowCurrency = item.symbol
            this.showCoin = false
            console.log(this.showCoin);
        },
        nav_to(e, name) {
            this.$Router.push({
                name: e,
                params: {
                    type: name
                }
            })
        },
        confirmbank(e) {
            this.formData.bankName = e[0].value
        },
        confirm(e) {
            this.currency = e[0].value
        },
        confirmnetwork(e) { 
            this.network = e[0].value
        },
        async getUserInfos() {
            let res = await this.$api.getUserInfo()
            if (res.code == 200) {
                this.userInfo = res.result
            }
        },
        openPwd() {
            // symbol: this.nowCurrency,
            //     network: this.network,
            //     amount: this.formData.amount,
            //     fee: this.fee,
            //     address: this.formData.address,
            //     memo: this.formData.memo,
            const { amount, address } = this.formData;
            const { fee } = this;
            const symbol = this.nowCurrency;
            const network = this.network;

            if (!symbol || !network || !amount || (!fee && fee != 0) || !address) {
                uni.showToast({
                    icon: "none",
                    title: this.$t("Please.full"),
                    duration: 2000
                });
                return;
            }

            this.showPassword = false
            // this.ConfirmTransfer = true
            if (store.state.shouldVibrate) {
                uni.vibrateShort()
            }
            if (!this.userInfo.tradePassSet) {
                this.goSetPopup = true
                return
            } else {
                this.password = ''
                this.ConfirmTransfer = true
                return
            }
        },
        // 表单提交
        async submitForm() {
            this.ConfirmTransfer = false

            // nowCurrency
            let res = await this.$api.withdraw({
                symbol: this.nowCurrency,
                network: this.network,
                amount: this.formData.amount,
                fee: this.fee,
                address: this.formData.address,
                memo: this.formData.memo,
                withdrawType: 2,
                tradePass: this.password

            })
            console.log(res);

            if (res.code == 200) {
                this.$u.toast(res.msg)
                this.nowCurrency = ''
                this.network = ""
                this.formData.amount = ""
                this.formData.address = ""
                this.formData.memo = ""
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        // 表单取消
        cancelForm() {
            console.log('取消操作');
            // 处理取消逻辑
        },
        handleCurrencyChange(event) {
            this.currencyIndex = event.detail.value;
        },
        handleReferenceChange(event) {
            this.referenceIndex = event.detail.value;
        },
        handleNetworkChange(event) {
            this.networkIndex = event.detail.value;
        },
        handleLabelChange(event) {
            this.labelIndex = event.detail.value;
        }
    }
};
</script>

<style lang="scss" scoped>
::v-deep .u-select__body__picker-view__item {
    color: #e6f0ff !important;
}

::v-deep .u-hairline-border:after {
    border: none !important;
}

::v-deep .u-input {
    margin-top: 20rpx !important;
    height: 48*2rpx !important;
    border-radius: 10*2rpx;
    border-width: 2rpx;
    border: 2rpx solid #999999 !important;
    padding: 0 38rpx !important;

    font-family: Gilroy-Medium;
    font-weight: 400;
    font-size: 14*2rpx;
    line-height: 16.8*2rpx;
    letter-spacing: 0%;
}

// ::v-deep .uni-scroll-view-content {
//     display: flex;
//     align-items: center;
// }

.container {
    min-height: 100vh;

    .set-password-modal {
        background: #fff;
        padding: 42rpx 44rpx;
        width: 90vw;
        border-radius: 40rpx;
        text-align: center;
        box-shadow: 7px 10px 100.3px 0px #0000001A;

        .set-password-title {
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 40rpx;
            line-height: 120%;
            letter-spacing: 0%;
            color: #000;
            margin-bottom: 16rpx;
        }

        .set-password-description {
            font-family: Gilroy-Medium;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 120%;
            color: #666666;
            margin-bottom: 48rpx;
        }

        .set-password-actions {
            display: flex;
            flex-direction: column;
            gap: 24rpx;

            .set-password-btn {
                height: 100rpx;
                border-radius: 128rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                letter-spacing: 0%;
                text-align: center;
                vertical-align: middle;

            }

            .set-now-btn {
                background: #FF82A3;
                color: #fff;
                line-height: 100rpx;

            }

            .later-btn {
                border: 2rpx solid #999;
                color: #000;
                line-height: 100rpx;

                // background: #fff;
            }

        }

    }


    .confirm-transfer-modal {
        background: #fff;
        padding: 42rpx 44rpx;
        width: 94vw;
        border-radius: 40rpx;
        text-align: center;
        box-shadow: 7px 10px 100.3px 0px #0000001A;

        .confirm-title {
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 40rpx;
            line-height: 120%;
            letter-spacing: 0%;
            color: #000;
            margin-bottom: 16rpx;
        }

        .confirm-subtitle {
            font-family: Gilroy-Medium;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 120%;
            color: #666666;
            margin-bottom: 32rpx;
        }

        .confirm-password-label {

            display: flex;
            align-items: center;
            // justify-content: space-between;
            gap: 20rpx;
            margin-bottom: 20rpx;

            .plz {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 160%;
                color: #666666;
                white-space: wrap;
                white-space: nowrap;
            }

            .show-toggle {
                display: flex;
                // border: 2rpx solid #D9D7D7;
                align-items: center;
                // gap: 8rpx;
                // border-radius: 134rpx;
                // padding: 12rpx 16rpx;

                image {
                    margin-right: 16rpx;
                    width: 36rpx;
                    height: 36rpx;
                }

                .toggle-text {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 100%;
                    text-align: center;
                    color: #000;
                }
            }
        }

        .password-box {
            display: flex;
            justify-content: space-between;
            gap: 12rpx;
            margin-bottom: 40rpx;
            position: relative;

            .password-digit {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 108rpx;
                height: 90rpx;
                border-radius: 20rpx;
                gap: 10px;
                // padding: 14px;
                border: 2rpx solid #999999;

                text-align: center;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 36rpx;

            }

            .hidden-input {
                position: absolute;
                opacity: 0;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10;
                caret-color: transparent;
            }
        }

        .set-password-actions {
            display: flex;
            flex-direction: column;
            gap: 24rpx;

            .set-password-btn {
                height: 100rpx;
                border-radius: 128rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                letter-spacing: 0%;
                text-align: center;
                vertical-align: middle;

            }

            .set-now-btn {
                background: #FF82A3;
                color: #fff;
                line-height: 100rpx;

            }

            .later-btn {
                border: 2rpx solid #999;
                color: #000;
                line-height: 100rpx;

                // background: #fff;
            }

        }
    }


    /* 弹窗整体样式 */
    .amount-popup {
        width: 90vw;
        background: #fff;
        border-radius: 40rpx;
        padding: 42rpx 44rpx;

        /* 标题 */
        .popup-header {
            text-align: center;

            .popup-title {
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 36rpx;
                line-height: 100%;
                letter-spacing: 0%;
                color: #000;
            }
        }

        /* 说明文本 */
        .popup-message {
            margin: 16rpx 0 0 0;
            font-family: Gilroy-Medium;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 120%;
            letter-spacing: 0%;
            text-align: center;
            color: #666;
            text-align: center;
        }

        /* 金额详情 */
        .amount-summary {
            margin-top: 32rpx;
            display: flex;
            flex-direction: column;
            // gap: 10px;

            .amount-item {
                display: flex;
                justify-content: space-between;
                border-radius: 8px;
                align-items: center;
                height: 102rpx;
                border-radius: 20rpx;
                padding: 18rpx 18rpx 18rpx 38rpx;
                border: 2rpx solid #999999;


                .amount-label {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    color: #000;
                }

                .amount-value {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    text-align: right;
                    color: #000;
                }
            }

        }

        /* 底部按钮 */
        .popup-actions {
            margin-top: 48rpx;
            display: flex;
            flex-direction: column;
            gap: 24rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;

            .confirm-btn {
                background: #FF82A3;
                height: 100rpx;
                width: 100%;
                color: #fff;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                letter-spacing: 0%;
                line-height: 100rpx;
                text-align: center;
                vertical-align: middle;
                border-radius: 128rpx;
            }

            .cancel-btn {
                width: 100%;
                height: 100rpx;
                background: #fff;
                border: 2rpx solid #999999;
                line-height: 100rpx;
                color: #000;
                border-radius: 128rpx;

            }
        }




    }

    /* 弹窗容器 */
    .popup-container {
        width: 90vw;
        border-radius: 40rpx;
        padding: 42rpx 44rpx;
        background: #FFFFFF;
        box-shadow: 7px 10px 100.3px 0px #0000001A;

        /* 标题 + 关闭按钮 */
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 40rpx;
            line-height: 120%;
            letter-spacing: 0%;

            image {
                width: 52rpx;
                height: 52rpx;
            }
        }


        /* 地址列表 */
        .popup-list {
            margin-top: 32rpx;
            display: flex;
            flex-direction: column;
            gap: 24rpx;
            max-height: 246*2rpx;
            overflow-y: auto;

            /* 单个地址项 */
            .popup-item {
                display: flex;
                align-items: center;
                gap: 20rpx;
                padding: 20rpx 0;
                border-bottom: 1px solid #ddd;


                .popup-item-icon {
                    width: 60rpx;
                    height: 60rpx;
                    border-radius: 50%;
                }

                .popup-item-info {
                    flex: 1;
                    display: flex;
                    flex-direction: column;

                    .popup-item-name {
                        font-family: Gilroy-Medium;
                        font-weight: 400;
                        font-size: 28rpx;
                        line-height: 120%;
                        letter-spacing: 0%;
                        color: #000;
                    }

                    .popup-item-sub {
                        font-size: 12px;
                        color: #888;
                    }

                }


                .popup-item-address {
                    width: 350rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 120%;
                    letter-spacing: 0%;

                    color: #000;
                    white-space: nowrap;
                }

                .details {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }

        /* 底部按钮 */
        .popup-footer {
            margin-top: 32rpx;

            .btn-add {
                width: 100%;
                background: #FF82A3;
                color: #fff;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                letter-spacing: 0%;
                text-align: center;
                vertical-align: middle;
                height: 100rpx;
                border-radius: 128rpx;
                // padding: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .btn-icon {
                font-size: 40rpx;
                margin-right: 20rpx;
            }

        }


    }

    /* 弹窗内容 */
    .popup-content {
        // height: 197;
        width: 90vw;
        border-radius: 40rpx;
        padding: 42rpx 44rpx;
        background: #FFFFFF;
        box-shadow: 7px 10px 100.3px 0px #0000001A;


        /* 标题 + 关闭按钮 */
        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 40rpx;
            line-height: 120%;
            letter-spacing: 0%;


            image {
                width: 52rpx;
                height: 52rpx;
            }
        }

        .addressContainer {
            max-height: 400rpx;
            overflow-y: auto;

            .account-info {
                display: flex;
                flex-direction: column;
                gap: 6px;
                margin-top: 32rpx;

                .label {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 100%;
                    letter-spacing: 0%;

                    color: #666;
                }

                .address {
                    width: 500rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    color: #000;
                }
            }

        }



        /* 账户信息
         */

        /* 按钮区域 */
        .popup-footer {
            display: flex;
            justify-content: space-between;
            margin-top: 32rpx;
            gap: 26rpx;

            .add-btn {
                flex: 1;
                background: #FF82A3;
                width: 168.5*2rpx;
                height: 100rpx;
                border-radius: 128rpx;

                color: #fff;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                letter-spacing: 0%;
                text-align: center;
                vertical-align: middle;

                display: flex;
                align-items: center;
                justify-content: center;

                .icon {
                    font-size: 40rpx;
                    margin-right: 20rpx;
                }
            }

            .manage-btn {
                flex: 1;
                width: 168.5*2rpx;
                height: 100rpx;
                border-radius: 128rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 2rpx solid #FF82A3;
                color: #FF82A3;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                letter-spacing: 0%;

                background: transparent;
            }
        }
    }

    .form-container {
        margin-top: 64rpx;
        margin-bottom: 20px;
        padding: 32rpx;

        .form-item {
            margin-bottom: 28rpx;
            position: relative;

            .amount {
                // text-indent: 16rpx;
            }

            .dao {
                position: absolute;
                bottom: 30rpx;
                left: 38rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 16.8*2rpx;
                letter-spacing: 0%;
                color: #666;
            }

            .all {
                position: absolute;
                bottom: 28rpx;
                right: 38rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 16.8*2rpx;
                letter-spacing: 0%;
                color: #FF82A3;
                z-index: 999;
            }


            .right_fix {
                position: absolute;
                bottom: 28rpx;
                right: 38rpx;
                width: 48rpx;
                height: 48rpx;
            }

            .inputs_bom {
                margin-top: 20rpx;
                height: 48*2rpx !important;
                border-radius: 10*2rpx;
                border-width: 2rpx;
                border: 2rpx solid #999999;
                font-family: Gilroy-Medium;
                color: #000000;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                padding: 0 38rpx !important;

                text {
                    line-height: 120%;

                }

                .rights {
                    // width: 60rpx;
                    // height: 60rpx;
                    display: flex;
                    align-items: center;

                    image {
                        margin-left: 10rpx;
                        width: 60rpx;
                        height: 60rpx;
                    }
                }

                .input_view {



                    .coins {
                        display: flex;
                        align-items: center;
                        margin-left: 20rpx;

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }

                        text {
                            line-height: 0;
                            margin-left: 10rpx;
                        }
                    }

                    >image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            .inputs {
                margin-top: 20rpx;
                height: 48*2rpx !important;
                border-radius: 10*2rpx;
                border-width: 2rpx;
                border: 2rpx solid #999999;
                font-family: Gilroy-Medium;
                color: #333;
                font-weight: 400;
                font-size: 14*2rpx;
                letter-spacing: 0%;
                padding: 0 38rpx !important;
                position: relative;

                .NetworkPopup {
                    z-index: 9999;
                    // width: 398*2rpx;
                    width: 100%;
                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 120rpx;
                    left: 0;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    // display: flex;
                    // align-items: flex-start;
                    // flex-direction: column;
                    max-height: 400rpx;
                    overflow-y: auto;
                    width: 100%;

                    .address {
                        width: 300*2rpx;
                        overflow: hidden;
                        // 超出省略号

                        text-overflow: ellipsis;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 100%;
                        display: block;
                        color: #333;
                        margin-bottom: 40rpx;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }


                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;

                    }
                }

                .helpoption {
                    z-index: 9999;
                    // width: 398*2rpx;
                    width: 100%;
                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 120rpx;
                    left: 0;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    .address {
                        width: 300*2rpx;
                        overflow: hidden;
                        // 超出省略号

                        text-overflow: ellipsis;
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 16*2rpx;
                        line-height: 100%;
                        display: block;
                        color: #333;
                        margin-bottom: 40rpx;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }

                    .crypto {
                        margin-top: 40rpx;
                        max-height: 400rpx;
                        overflow-y: auto;
                        width: 100%;

                        .crypto-item {
                            display: flex;
                            align-items: center;
                            margin-bottom: 40rpx;

                            &:last-child {
                                margin-bottom: 0;
                            }

                            image {
                                width: 60rpx;
                                height: 60rpx;
                            }

                            text {
                                margin-left: 20rpx;
                                font-family: Gilroy-SemiBold;
                                font-weight: 400;
                                font-size: 16*2rpx;
                                // line-height: 120%;
                                letter-spacing: 0%;
                                color: #000;
                            }
                        }
                    }

                    .popup-top-title {
                        font-family: Gilroy-SemiBold;
                        font-weight: 400;
                        font-size: 14*2rpx;
                        line-height: 160%;
                        color: #FF82A3;
                    }

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;

                    }

                    // >view {

                    //     padding: 15rpx 0;
                    //     display: flex;
                    //     align-items: center;

                    //     image {
                    //         width: 40rpx;
                    //         height: 30rpx;
                    //     }

                    //     text {
                    //         margin-left: 20rpx;
                    //         display: block;
                    //         font-family: Gilroy-Bold;
                    //         font-weight: 400;
                    //         font-size: 16*2rpx;
                    //         line-height: 19.2*2rpx;
                    //         color: #000;
                    //     }
                    // }
                }


                .right {
                    width: 60rpx;
                    height: 60rpx;

                    image {
                        width: 60rpx;
                        height: 60rpx;
                    }
                }

                .input_view {
                    width: 326*2rpx;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    .coins {
                        display: flex;
                        align-items: center;
                        margin-left: 20rpx;

                        image {
                            width: 40rpx;
                            height: 40rpx;
                        }

                        text {
                            line-height: 0;
                            margin-left: 10rpx;
                        }
                    }

                    >image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }
            }

            >text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }
        }

        .picker {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .tips {
            margin-top: 20rp;
            // height: 121*2rpx;
            // height: 260rpx;
            border-bottom-right-radius: 10*2rpx;
            border-bottom-left-radius: 10*2rpx;
            padding: 16*2rpx;
            background: #F2F2F2;

            .line1 {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                letter-spacing: 0%;
                color: #333;
            }

            .content {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #666;
            }
        }

        .btn {
            margin-top: 48rpx;
            width: 100%;

            .exchange-btn {
                width: 100%;
                height: 100rpx;
                background: #FF82A3;
                border-radius: 64*2rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                color: #fff;
            }
        }

        .buttons {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 47rpx;
            gap: 32rpx;
            width: 100%;

            .tips_text {
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 16.8*2rpx;
                color: #333;
            }

            .btns_chat {
                width: 100%;
                background: #fff;
                border: 1px solid #999999;
                height: 50*2rpx;
                border-radius: 64*2rpx;
                color: #000;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                letter-spacing: 0%;
                text-align: center;
                display: flex;
                align-items: center;
                image {
                    width: 52rpx;
                    height: 52rpx;
                    margin-right: 12rpx;
                }
            }

            .btns {
                width: 100%;

                height: 50*2rpx;
                border-radius: 64*2rpx;
                background: #FF82A3;
                color: #fff;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                letter-spacing: 0%;
                text-align: center;

            }

        }
    }

    .bomx {
        height: 73*2rpx;
        border-top-left-radius: 60rpx;
        border-top-right-radius: 60rpx;
        background: #EFEFEF;

        .label {
            margin-left: 10rpx;
            font-size: 16*2rpx;
            // line-height: 19.2*2rpx;
            color: #000;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            line-height: 120%;

        }

        .icon_serve {
            width: 40rpx;
            height: 40rpx;
        }
    }
}
</style>
