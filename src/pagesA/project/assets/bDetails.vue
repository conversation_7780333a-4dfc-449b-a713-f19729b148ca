<template>
    <view class="b_details_page">
        <!-- 导航栏 -->
        <u-navbar :is-back="true" :immersive="true" back-icon-color="#121212" :border-bottom="false">
            <view class="navbar_title">
                <image class="coin_flag" :src="symbolIcon" />
                <text>{{symbol}}</text>
            </view>
        </u-navbar>
        <view class="content_wrap">
            <view class="asset_header">
                <view class="asset_card_top">
                    <view class="asset_card_left">
                        <view class="asset_card_label">
                            <text class="asset_card_label_text">预计总资产</text>
                            <u-icon v-if="eyeoff" @click="eyeoff = !eyeoff"
                                name="https://pro-oss.pinkwallet.com/image/1370353025834115072.png"
                                size="36" class="asset_card_info_icon" />
                            <u-icon v-else @click="eyeoff = !eyeoff"
                                name="https://pro-oss.pinkwallet.com/image/1370353145610854400.png"
                                size="36" class="asset_card_info_icon" />

                        </view>
                        <view class="asset_card_amount_row">
                            <text class="asset_card_amount" v-if="eyeoff">***</text>
                            <text class="asset_card_amount"
                                v-else>${{Number(totalNum).toFixed(2).toLocaleString('en-US') ||'0.00'}} </text>
                            <view>
                                <text class="asset_card_unit"
                                    @click="toggleRotate(),showcurrencyShow = !showcurrencyShow">{{nowsymbol}}</text>
                                <u-icon name="arrow-down" size="24" color="#000000"
                                    :style="{ transform: `rotate(${rotate}deg)` }" class="asset_card_arrow_icon"
                                    @click="showcurrencyShow = !showcurrencyShow" />
                            </view>
                            <transition name="expand-slide">
                                <view class="helpoption" v-show="showcurrencyShow">
                                    <view v-for="(item, index) in userCoins" :key="index" class="Roptions"
                                        @click.stop="SetSymbol(item)">
                                        <text :style="{ color: nowsymbol == item.name ? '#008E28' : '' }">{{ item.name
                                            }}</text>
                                    </view>
                                </view>
                            </transition>
                        </view>
                        <view class="asset_card_rate">{{increaseInfo.nowDay}} {{nowsymbol}}
                            <text>({{increaseInfo.increaseRate*1000>0?`+${increaseInfo.increaseRate}`:increaseInfo.increaseRate}}%)昨日</text>
                        </view>
                    </view>
                    <view class="asset_card_chart">
                        <view class="chart_placeholder" @click="openShowEachart()">
                            <image src="https://pro-oss.pinkwallet.com/image/1387826531387662336.png" mode="widthFix">
                            </image>
                        </view>
                    </view>
                </view>
				<view class="echart_view" v-if="showEchart">
					<areaechart ref="areaechart" :type="echartType" :query="echartQuery"> </areaechart>
				</view>
                <!-- 币种分布 -->
                <view class="distribution_card">
                    <view class="card_title">币种分布</view>
                    <view class="account_list">
                        <view class="account_item" v-for="(item, index) in DistributionList" :key="index">
                            <view class="account_left">
                                <view class="progress_wrap">
                                    <u-circle-progress :percent="item.percent*100" :width="30" :border-width="4"
                                        active-color="#F76B8A" bg-color="#FFF" />
                                </view>
                                <text class="account_name">{{ nameOptions[item.account] }}</text>
                            </view>
                            <text class="account_value">{{ item.totalBalance }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 合约交易 -->
            <view class="contracts_card">
                <view class="card_header">
                    <text class="card_title">合约交易</text>
                    <u-icon name="arrow-right" color="#999" size="28" />
                </view>
                <scroll-view class="contracts_scroll" :scroll-x="true" :show-scrollbar="false">
                    <view class="contract_item" v-for="(item, index) in contracts" :key="index">
                        <view class="pair_name">{{ item.pair }}</view>
                        <view class="price_row">
                            <text class="pair_price">{{ item.price }}</text>
                            <text class="pair_change" :class="item.change >= 0 ? 'up' : 'down'">
                                {{ item.change >= 0 ? '+' : '' }}{{ item.change.toFixed(2) }}%
                            </text>
                        </view>
                        <view class="pair_chart_wrap">
                            <view class="pair_chart" :class="item.change >= 0 ? 'up_bg' : 'down_bg'"></view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <!-- 历史记录 -->
            <view class="history_card">
                <view class="card_title">历史记录</view>
                <view class="history_list">
                    <view class="history_item" v-for="(item, index) in transactions" :key="index">
                        <view class="history_left">
                            <view class="history_type">
                                <text class="history_desc" v-if="item.type == 'deposit'">充值</text>
                                <text class="history_desc" v-if="item.type == 'withdraw'">提现</text>
                                <text class="history_desc" v-if="item.type == 'swap'">闪兑</text>
                            </view>
                            <view class="history_desc_row">
                                <text class="history_desc">{{item.symbol}}</text>
                                <view class="history_date">{{ formatTimestamp(item.createAt) }}</view>
                            </view>
                        </view>
                        <view class="history_right" :class="item.amount > 0 ? 'up' : 'down'">
                            {{ item.amount > 0 ? '+' : '' }}{{ item.amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,
                            ',') }}
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- 操作按钮区块 -->
        <view class="action_btns_wrap">
            <view class="action_btn_item">
                <view class="action_btn_icon_wrap" @click="nav_to('addCapital',navParams)">
                    <u-icon name="https://pro-oss.pinkwallet.com/image/1386806647358840832.png" size="80"
                        color="#EC5CF4" />
                </view>
                <text class="action_btn_text">添加资金</text>
            </view>
            <view class="action_btn_item">
                <view class="action_btn_icon_wrap" @click="nav_to('withdraw',navParams)">
                    <u-icon name="https://pro-oss.pinkwallet.com/image/1386807020060499968.png" size="80"
                        color="#EC5CF4" />
                </view>
                <text class="action_btn_text">转账</text>
            </view>
            <view class="action_btn_item">
                <view class="action_btn_icon_wrap" @click="nav_to('transfer')">
                    <u-icon name="https://pro-oss.pinkwallet.com/image/1386807124683218944.png" size="80"
                        color="#EC5CF4" />
                </view>
                <text class="action_btn_text">划转</text>
            </view>
        </view>
		

    </view>
</template>
<script>
	import areaechart from '../../../components/echartCom/area-echart.vue'
    export default {
        data() {
            return {
                accounts: [
                    { name: '资金账户 (法币)', value: '40,123.00', percent: 100 },
                    { name: '美股账户 (法币)', value: '40,123.00', percent: 75 },
                    { name: '港股账户 (法币)', value: '40,123.00', percent: 50 },
                ],
                contracts: [
                    { pair: 'BTC/USDT', price: '41,859.09', change: 0.00 },
                    { pair: 'ETH/USDT', price: '18,925.73', change: 0.28 },
                    { pair: 'SOL/USDT', price: '41,859.09', change: -0.01 },
                ],
                history: [
                    { type: '提现', desc: '数字币', date: '2025-05-12 15:43:14', amount: -5212.62 },
                    { type: '充值', desc: '数字币', date: '2025-05-12 15:43:14', amount: 5212.62 },
                    { type: '提现', desc: '数字币', date: '2025-05-12 15:43:14', amount: -5212.62 },
                    { type: '提现', desc: '数字币', date: '2025-05-12 15:43:14', amount: -5212.62 },
                    { type: '提现', desc: '数字币', date: '2025-05-12 15:43:14', amount: -5212.62 },
                ],
                eyeoff: false,
                userCoins: [],
                showcurrencyShow: false,
                nowsymbol: '',
                rotate: 0,
                totalNum: 0,
                transactions: [],
                symbol: '',//查询对应的币种
                increaseInfo: {},
                navParams: {},
                fiat:false,
                DistributionList:[],
				echartType:'personal',
				echartQuery:{
					convertCoin:'',
					assetType: 15,
					symbol:''
				},
				showEchart:false,
            }
        },
        onLoad(options) {
            this.symbol = options.symbol
            this.symbolIcon = options.symbolIcon ? options.symbolIcon : 'https://pro-oss.pinkwallet.com/image/1385363807210659840.png'
            this.fiat = options.fiat
            this.getUserCoinFiat()
            this.getList()
            this.navParams = { 
                symbol:  this.symbol,
                name:  this.symbol,
                image: this.symbolIcon
            }
        },
        computed:{
            nameOptions(){
				return {
					'FUNDS':'资金账户',
					'STOCK_US':'美股账户',
					'STOCK_HK':'港股账户',
					'CONTRACT':'合约账户'
				}
			},
        },
		components:{
			areaechart
		},
        methods: {
            async getUserCoinFiat() {
                let res = await this.$api.userCoinList({
                    pageNum: 1,
                    pageSize: 100,
                    fiat: true, // 法币
                    hideZeroAsset: false
                })
                if (res.code == 200) {
                    this.nowsymbol = res.result.data[0].symbol
                    this.userCoins = res.result.data
                    this.getInfo()
                    this.getIncreaseRate()
                    this.getBalanceDistributio()
                } else {
                    this.$u.toast(res.msg)
                }
            },
            async getInfo() {
                let res = await this.$api.amount({
                    assetType: 15,
                    convertCoin: this.nowsymbol,
                    symbol: this.symbol,
                });
                if (res.code == 200) {
                    console.log(res)
                    this.totalNum = res.result.amount
                } else {
                    uni.showToast({
                        title: res.status.msg,
                        icon: 'none',
                        duration: 3000
                    });
                }
            },
            async getIncreaseRate() {
                let res = await this.$api.increaseRate({
                    assetType: 15,
                    convertCoin: this.nowsymbol
                })
                if (res.code == 200) {
                    this.increaseInfo = res.result
                }
            },
            SetSymbol(e) {
                console.log(e);
                this.nowsymbol = e.name
                this.showcurrencyShow = false
                // this.getAllAssetsFiat()
                // this.getLine()
                // this.init()
				if(this.showEchart){
					this.echartQuery.nowsymbol = e.name
					this.$refs.areaechart.fetchChartData()
				}
                this.getInfo()
                this.getIncreaseRate()
                this.getBalanceDistributio()
            },
            async getBalanceDistributio() {
                let res = await this.$api.symbolBalanceDistribution({
                    symbol: this.symbol,
                    fiat:this.fiat
                });
                if (res.code == 200) {
                    console.log(res)
                    this.DistributionList = res.result.symbolBalanceVOList
                } else {
                    uni.showToast({
                        title: res.status.msg,
                        icon: 'none',
                        duration: 3000
                    });
                }
            },
            async getList() {
                this.loading = true
                let res = await this.$api.getTransactionPaged({
                    pageNum: 1,
                    pageSize: 20,
                    symbol: this.symbol,
                    type: 'all'
                })
                if (res.code == 200) {
                    this.transactions = res.result.data
                } else {
                    this.loading = false
                    this.$u.toast(res.msg)
                }
            },
            toggleRotate() {
                this.rotate = this.rotate === 0 ? 180 : 0;
            },
            nav_to(name, params) {
                this.$Router.push({
                    name,
                    params
                })
            },
            formatTimestamp(timestamp) {
                if (!timestamp) return '--';

                const date = new Date(timestamp * 1000);

                // 检查日期是否有效
                if (isNaN(date.getTime())) {
                    return '--';
                }

                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            },

            // 简化版本，只显示年月日
            formatDate(timestamp) {
                if (!timestamp) return '--';

                const date = new Date(timestamp);

                if (isNaN(date.getTime())) {
                    return '--';
                }

                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');

                return `${year}-${month}-${day}`;
            },
			openShowEachart(){
				if(!this.showEchart){
					this.echartQuery.convertCoin = this.nowsymbol
					this.echartQuery.symbol = this.symbol
				}
				this.showEchart = !this.showEchart
			}
        }
    }
</script>
<style lang="scss" scoped>
    .b_details_page {
        background: #F5F7FA;
        min-height: 100vh;
        padding-bottom: 40rpx;
        font-family: 'PingFang SC';
    }

    .navbar_title {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 83%;

        .coin_flag {
            width: 40rpx;
            height: 40rpx;
            margin-right: 12rpx;
            border-radius: 50%;
        }

        text {
            font-size: 32rpx;
            font-weight: 600;
            color: #121212;
        }
    }

    .card_title {
        font-size: 28rpx;
        font-weight: 600;
        color: #121212;
    }

    .asset_header {
        border-radius: 0 0 32rpx 32rpx;
        padding: 32rpx;
        padding-top: 100rpx;
        background-color: #FFF;
    }

    .asset_card_top {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        margin: 20rpx 0rpx;

        .asset_card_left {
            .asset_card_labe l {
                display: flex;
                align-items: center;
                font-size: 28rpx;
                color: #BDBDBD;
                font-weight: 400;
                margin-bottom: 12rpx;
            }

            .asset_card_amount_row {
                display: flex;
                align-items: flex-end;
                margin-bottom: 8rpx;
                position: relative;

                .asset_card_amount {
                    font-size: 60rpx;
                    font-weight: 800;
                    color: #121212;
                    line-height: 1;
                }

                .asset_card_unit {
                    font-size: 28rpx;
                    color: #121212;
                    font-weight: 500;
                    margin-left: 10rpx;
                }

                .asset_card_arrow_icon {
                    margin-left: 6rpx;
                    font-size: 22rpx;
                    color: #BDBDBD;
                    margin-bottom: 5rpx;
                }
            }

            .asset_card_rate {
                font-size: 22rpx;
                color: #BDBDBD;
                font-weight: 400;
                /* margin-bottom: 16rpx; */
            }
        }

        .asset_card_chart {
            width: 154rpx;
            height: 74rpx;
            margin-left: 16rpx;

            .chart_placeholder {
                width: 100%;
                height: 100%;

                image {
                    width: 100%;
                }
            }
        }
    }

    .content_wrap {}

    .asset_summary_card {
        background: #fff;
        border-radius: 32rpx 32rpx 0 0;
        padding: 32rpx 0rpx;
        margin-top: 12rpx;

        .top_row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            .left_col {
                .label_row {
                    display: flex;
                    align-items: center;
                    color: #999;
                    font-size: 24rpx;
                    font-weight: 400;
                }

                .amount_row {
                    display: flex;
                    align-items: baseline;
                    margin: 12rpx 0;

                    .amount {
                        font-size: 52rpx;
                        font-weight: 700;
                        color: #121212;
                        font-family: 'DIN Alternate';
                    }

                    .unit {
                        font-size: 28rpx;
                        font-weight: 500;
                        margin: 0 8rpx;
                        color: #121212;
                    }
                }

                .sub_text {
                    font-size: 22rpx;
                    color: #999;
                    font-weight: 400;
                }
            }

            .right_col .chart_img {
                width: 180rpx;
                height: 60rpx;
                margin-top: 12rpx;
            }
        }
    }

    .distribution_card {
        background: #fff;
        border-radius: 0 0 32rpx 32rpx;
        padding: 32rpx 0rpx;

        .account_list {
            margin-top: 24rpx;

            .account_item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 32rpx;

                &:last-child {
                    margin-bottom: 0;
                }

                .account_left {
                    display: flex;
                    align-items: center;

                    .progress_wrap {
                        width: 40rpx;
                        height: 40rpx;
                        margin-right: 16rpx;
                        transform: rotate(-90deg);
                    }

                    .account_name {
                        font-size: 26rpx;
                        color: #121212;
                    }
                }

                .account_value {
                    font-size: 26rpx;
                    color: #121212;
                    font-weight: 500;
                }
            }
        }
    }

    .contracts_card {
        background: #fff;
        border-radius: 32rpx;
        padding: 32rpx;
        margin-top: 12rpx;

        .card_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .contracts_scroll {
            width: 100%;
            margin-top: 24rpx;
            white-space: nowrap;

            .contract_item {
                display: inline-flex;
                flex-direction: column;
                width: 280rpx;
                height: auto;
                border-radius: 16rpx;
                border: 1rpx solid #f0f0f0;
                margin-right: 24rpx;
                padding: 24rpx;

                &:last-child {
                    margin-right: 0;
                }

                .pair_name {
                    font-size: 26rpx;
                    font-weight: 600;
                    color: #121212;
                }

                .price_row {
                    display: flex;
                    align-items: baseline;
                    margin-top: 8rpx;

                    .pair_price {
                        font-size: 24rpx;
                        color: #121212;
                    }

                    .pair_change {
                        font-size: 22rpx;
                        margin-left: 12rpx;
                    }
                }

                .pair_chart_wrap {
                    flex: 1;
                    margin-top: 16rpx;

                    .pair_chart {
                        height: 40rpx;
                        border-radius: 8rpx;
                    }
                }
            }
        }
    }

    .history_card {
        background: #fff;
        border-radius: 32rpx;
        padding: 32rpx;
        margin-top: 12rpx;

        .history_list {
            margin-top: 16rpx;

            .history_item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 24rpx 0;
                border-bottom: 1rpx solid #f5f5f5;

                &:last-child {
                    border-bottom: none;
                    padding-bottom: 0;
                }

                .history_left {
                    .history_type {
                        font-size: 26rpx;
                        color: #121212;
                        font-weight: 500;
                        margin-bottom: 8rpx;
                    }

                    .history_desc_row {
                        font-size: 22rpx;
                        color: #999;

                        .history_date {}
                    }
                }

                .history_right {
                    font-size: 28rpx;
                    font-weight: 500;
                    font-family: 'DIN Alternate';
                }
            }
        }
    }

    .action_btns_wrap {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 48rpx;
        background: #fff;
        padding: 24rpx 100rpx;
        margin: 12rpx 0rpx;

        .action_btn_item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .action_btn_icon_wrap {
                width: 80rpx;
                height: 80rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 12rpx;

                .u-icon {
                    font-size: 44rpx !important;
                }
            }

            .action_btn_text {
                font-size: 24rpx;
                color: #121212;
                margin-top: 4rpx;
                font-weight: 500;
            }
        }

        .action_btn_item+.action_btn_item {
            margin-left: 24rpx;
        }
    }

    .up {
        color: #26A17B;
    }

    .down {
        color: #F76B8A;
    }

    .up_bg {
        background: linear-gradient(to top, rgba(38, 161, 123, 0.05), rgba(38, 161, 123, 0.2));
    }

    .down_bg {
        background: linear-gradient(to top, rgba(247, 107, 138, 0.05), rgba(247, 107, 138, 0.2));
    }

    .helpoption {
        width: 80*2rpx;
        transition: transform 0.3s ease, opacity 0.3s ease;
        transform-origin: top;
        /* 设置变换的起点为顶部 */
        z-index: 9999;
        position: absolute;
        top: 80rpx;
        right: -50rpx;
        box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;
        max-height: 400rpx;
        overflow-y: auto;
        background: #fff;
        border-radius: 16*2rpx;
        padding: 16*2rpx;
        opacity: 1;
        display: flex;
        align-items: flex-start;
        flex-direction: column;



        &.collapse {
            transform: scaleY(0) translateY(-100%);
            /* 缩小至0，并向上移动 */
            opacity: 0;
        }

        &.expand {
            transform: scaleY(1) translateY(0%);
            /* 恢复到正常大小，并位置恢复 */
            opacity: 1;

        }

        >view {
            width: 100%;
            padding: 15rpx 0;
            text-align: left;

            image {
                width: 40rpx;
                height: 30rpx;
            }

            text {
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                color: #000;
            }
        }
    }
</style>