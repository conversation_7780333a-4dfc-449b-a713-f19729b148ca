<template>
    <view class="b_details_page">
        <!-- 导航栏 -->
        <u-navbar :is-back="true" :immersive="true" back-icon-color="#121212" :border-bottom="false" >
            <view class="navbar_title">
                <image class="coin_flag" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385363807210659840.png" />
                <text>USD</text>
            </view>
        </u-navbar>
        <view class="content_wrap">
             <view class="asset_header">
                  <!-- 资产总览 -->
                <view class="asset_summary_card">
                    <view class="top_row">
                        <view class="left_col">
                            <view class="label_row">
                                <text>预估总资产</text>
                                <u-icon name="eye" size="32" color="#999" style="margin-left: 8rpx;" />
                            </view>
                            <view class="amount_row">
                                <text class="amount">$ 3,211.59</text>
                                <text class="unit">USD</text>
                                <u-icon name="arrow-down" size="16" color="#999" style="margin-left: 4rpx;"/>
                            </view>
                            <view class="sub_text">99999 USD(100%) 昨日</view>
                        </view>
                        <view class="right_col">
                            <image class="chart_img" src="https://pro-oss.pinkwallet.com/image/1387485710561927168.png" />
                        </view>
                    </view>
                </view>
                <!-- 币种分布 -->
                <view class="distribution_card">
                    <view class="card_title">币种分布</view>
                    <view class="account_list">
                        <view class="account_item" v-for="(item, index) in accounts" :key="index">
                            <view class="account_left">
                               <view class="progress_wrap">
                                    <u-circle-progress :percent="item.percent" :width="40" :border-width="4" active-color="#F76B8A" bg-color="#f0f0f0" />
                               </view>
                                <text class="account_name">{{ item.name }}</text>
                            </view>
                            <text class="account_value">{{ item.value }}</text>
                        </view>
                    </view>
                </view>
             </view>
           
            <!-- 合约交易 -->
            <view class="contracts_card">
                <view class="card_header">
                    <text class="card_title">合约交易</text>
                    <u-icon name="arrow-right" color="#999" size="28" />
                </view>
                <scroll-view class="contracts_scroll" :scroll-x="true" :show-scrollbar="false">
                    <view class="contract_item" v-for="(item, index) in contracts" :key="index">
                        <view class="pair_name">{{ item.pair }}</view>
                        <view class="price_row">
                            <text class="pair_price">{{ item.price }}</text>
                            <text class="pair_change" :class="item.change >= 0 ? 'up' : 'down'">
                                {{ item.change >= 0 ? '+' : '' }}{{ item.change.toFixed(2) }}%
                            </text>
                        </view>
                        <view class="pair_chart_wrap">
                            <view class="pair_chart" :class="item.change >= 0 ? 'up_bg' : 'down_bg'"></view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <!-- 历史记录 -->
            <view class="history_card">
                <view class="card_title">历史记录</view>
                <view class="history_list">
                    <view class="history_item" v-for="(item, index) in history" :key="index">
                        <view class="history_left">
                            <view class="history_type">{{ item.type }}</view>
                            <view class="history_desc_row">
                                <text class="history_desc">{{ item.desc }}</text>
                                <text class="history_date">{{ item.date }}</text>
                            </view>
                        </view>
                        <view class="history_right" :class="item.amount > 0 ? 'up' : 'down'">
                            {{ item.amount > 0 ? '+' : '' }}{{ item.amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',') }}
                        </view>
                    </view>
                </view>
            </view>
        </view>
       <!-- 操作按钮区块 -->
    <view class="action_btns_wrap">
        <view class="action_btn_item">
          <view class="action_btn_icon_wrap" @click="nav_to('addCapital')">
            <u-icon name="https://pro-oss.pinkwallet.com/image/1386806647358840832.png" size="80" color="#EC5CF4" />
          </view>
          <text class="action_btn_text">添加资金</text>
        </view>
        <view class="action_btn_item">
          <view class="action_btn_icon_wrap" @click="nav_to('withdraw')">
            <u-icon name="https://pro-oss.pinkwallet.com/image/1386807020060499968.png" size="80" color="#EC5CF4" />
          </view>
          <text class="action_btn_text">转账</text>
        </view>
        <view class="action_btn_item">
          <view class="action_btn_icon_wrap" @click="nav_to('transfer')">
            <u-icon name="https://pro-oss.pinkwallet.com/image/1386807124683218944.png" size="80" color="#EC5CF4" />
          </view>
          <text class="action_btn_text">划转</text>
        </view>
      </view>
       
    </view>
</template>
<script>
export default {
    data() {
        return {
            accounts: [
                { name: '资金账户 (法币)', value: '40,123.00', percent: 100 },
                { name: '美股账户 (法币)', value: '40,123.00', percent: 75 },
                { name: '港股账户 (法币)', value: '40,123.00', percent: 50 },
            ],
            contracts: [
                { pair: 'BTC/USDT', price: '41,859.09', change: 0.00 },
                { pair: 'ETH/USDT', price: '18,925.73', change: 0.28 },
                { pair: 'SOL/USDT', price: '41,859.09', change: -0.01 },
            ],
            history: [
                { type: '提现', desc: '数字币', date: '2025-05-12 15:43:14', amount: -5212.62 },
                { type: '充值', desc: '数字币', date: '2025-05-12 15:43:14', amount: 5212.62 },
                { type: '提现', desc: '数字币', date: '2025-05-12 15:43:14', amount: -5212.62 },
                { type: '提现', desc: '数字币', date: '2025-05-12 15:43:14', amount: -5212.62 },
                { type: '提现', desc: '数字币', date: '2025-05-12 15:43:14', amount: -5212.62 },
            ]
        }
    }
}
</script>
<style lang="scss" scoped>
.b_details_page {
    background: #F5F7FA;
    min-height: 100vh;
    padding-bottom: 40rpx;
    font-family: 'PingFang SC';
}
.navbar_title {
    display: flex;
    align-items: center;
    justify-content: center;
    width:83%;
    .coin_flag {
        width: 40rpx;
        height: 40rpx;
        margin-right: 12rpx;
        border-radius: 50%;
    }
    text {
        font-size: 32rpx;
        font-weight: 600;
        color: #121212;
    }
}

.card_title {
    font-size: 28rpx;
    font-weight: 600;
    color: #121212;
}
.asset_header{ 
    border-radius:0 0 32rpx 32rpx;
    padding: 32rpx;
    padding-top: 100rpx;
}
.content_wrap{
}
.asset_summary_card {
    background: #fff;
    border-radius: 32rpx 32rpx 0 0;
    padding: 32rpx 0rpx;
    margin-top: 12rpx;
    .top_row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        .left_col {
            .label_row {
                display: flex;
                align-items: center;
                color: #999;
                font-size: 24rpx;
                font-weight: 400;
            }
            .amount_row {
                display: flex;
                align-items: baseline;
                margin: 12rpx 0;
                .amount {
                    font-size: 52rpx;
                    font-weight: 700;
                    color: #121212;
                    font-family: 'DIN Alternate';
                }
                .unit {
                    font-size: 28rpx;
                    font-weight: 500;
                    margin: 0 8rpx;
                    color: #121212;
                }
            }
            .sub_text {
                font-size: 22rpx;
                color: #999;
                font-weight: 400;
            }
        }
        .right_col .chart_img {
            width: 180rpx;
            height: 60rpx;
            margin-top: 12rpx;
        }
    }
}
.distribution_card {
    background: #fff;
    border-radius:0 0 32rpx 32rpx;
    padding: 32rpx 0rpx;
    .account_list {
        margin-top: 24rpx;
        .account_item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 32rpx;
            &:last-child { margin-bottom: 0; }
            .account_left {
                display: flex;
                align-items: center;
                .progress_wrap {
                    width: 40rpx;
                    height: 40rpx;
                    margin-right: 16rpx;
                    transform: rotate(-90deg);
                }
                .account_name {
                    font-size: 26rpx;
                    color: #121212;
                }
            }
            .account_value {
                font-size: 26rpx;
                color: #121212;
                font-weight: 500;
            }
        }
    }
}
.contracts_card {
    background: #fff;
    border-radius: 32rpx;
    padding: 32rpx;
    margin-top: 12rpx;
    .card_header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .contracts_scroll {
        width: 100%;
        margin-top: 24rpx;
        white-space: nowrap;
        .contract_item {
            display: inline-flex;
            flex-direction: column;
            width: 280rpx;
            height: auto;
            border-radius: 16rpx;
            border: 1rpx solid #f0f0f0;
            margin-right: 24rpx;
            padding: 24rpx;
            &:last-child { margin-right: 0; }
            .pair_name { 
                font-size: 26rpx; 
                font-weight: 600; 
                color: #121212;
            }
            .price_row {
                display: flex;
                align-items: baseline;
                margin-top: 8rpx;
                .pair_price { 
                    font-size: 24rpx; 
                    color: #121212;
                }
                .pair_change { 
                    font-size: 22rpx; 
                    margin-left: 12rpx;
                }
            }
            .pair_chart_wrap {
                flex: 1;
                margin-top: 16rpx;
                .pair_chart {
                    height: 40rpx;
                    border-radius: 8rpx;
                }
            }
        }
    }
}
.history_card {
    background: #fff;
    border-radius: 32rpx;
    padding: 32rpx;
    margin-top: 12rpx;
    .history_list {
        margin-top: 16rpx;
        .history_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24rpx 0;
            border-bottom: 1rpx solid #f5f5f5;
            &:last-child { border-bottom: none; padding-bottom: 0; }
            .history_left {
                .history_type { 
                    font-size: 26rpx; 
                    color: #121212;
                    font-weight: 500;
                    margin-bottom: 8rpx;
                }
                .history_desc_row {
                    display: flex;
                    align-items: center;
                    font-size: 22rpx; 
                    color: #999;
                    .history_date {
                        margin-left: 12rpx;
                    }
                }
            }
            .history_right { 
                font-size: 28rpx; 
                font-weight: 500; 
                font-family: 'DIN Alternate';
            }
        }
    }
} 
.action_btns_wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 48rpx;
    background: #fff;
    padding: 24rpx 100rpx;
    margin: 12rpx 0rpx;

    .action_btn_item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .action_btn_icon_wrap {
        width: 80rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12rpx;

        .u-icon {
          font-size: 44rpx !important;
        }
      }

      .action_btn_text {
        font-size: 24rpx;
        color: #121212;
        margin-top: 4rpx;
        font-weight: 500;
      }
    }

    .action_btn_item+.action_btn_item {
      margin-left: 24rpx;
    }
  }
.up { color: #26A17B; }
.down { color: #F76B8A; }
.up_bg { background: linear-gradient(to top, rgba(38, 161, 123, 0.05), rgba(38, 161, 123, 0.2)); }
.down_bg { background: linear-gradient(to top, rgba(247, 107, 138, 0.05), rgba(247, 107, 138, 0.2)); }
</style>


