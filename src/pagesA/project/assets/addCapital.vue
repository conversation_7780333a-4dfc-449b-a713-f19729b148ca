<template>
    <view class="transfer-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="充值">
            <view slot="right" class="search-box-right" @click="nav_to('Record', 'deposit')">
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385362296246198272.png"
                    class="search-icon" />
            </view>
        </u-navbar>
        <!-- 主要内容 -->
        <view class="content">
            <!-- 二维码区域 -->
            <view v-if="showAddress" class="qrcode-section">
                <view class="qrcode-wrapper">
                    <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="320rpx"
                        :options="options"></uv-qrcode>
                    <view class="qrcode-coin-icon">
                        <image :src="selectedCoin.image" class="coin-center-icon" />
                    </view>
                </view>
            </view>
            <!-- 币种选择 -->
            <!-- <view class="select-section">
                <view class="select-label">币种</view>
                <view class="select-box" @click="showCoinPopup = true">
                    <view class="select-value">
                        <image :src="selectedCoin.image" class="coin-icon" />
                        <text class="coin-symbol">{{ selectedCoin.symbol }}</text>
                    </view>
                    <u-icon name="https://pro-oss.pinkwallet.com/image/1387107155650895872.png" size="40"
                        color="#C0C4CC" />
                </view>
            </view> -->
            <!-- 网络选择 -->
            <view class="select-section">
                <view class="select-label">网络</view>
                <view class="select-box" @click="showNetworkPopup = true">
                    <view class="select-value">
                        <text class="network-name">{{ network || '请选择网络' }}</text>
                    </view>
                    <u-icon name="https://pro-oss.pinkwallet.com/image/1387107155650895872.png" size="40"
                        color="#C0C4CC" />
                </view>
                <!-- 网络弹窗 -->
                <u-popup v-model="showNetworkPopup" mode="bottom" border-radius="14" height="900rpx">
                    <view>
                        <view class="popup_title">
                            <text>选择网络</text>
                            <u-icon @click="showNetworkPopup = false"
                                name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385342962270560256.png"
                                size="48" />
                        </view>
                        <view class="coin-list">
                            <view class="coin-item" v-for="item in networkOptions" :key="item.network"
                                @click="selectNetwork(item)">
                                <text class="coin-name">{{ item.network }}</text>
                            </view>
                        </view>
                    </view>
                </u-popup>
            </view>
            <!-- 充值地址和memo -->
            <view v-if="showAddress" class="address-section">
                <view class="address-label">充值地址</view>
                <view class="address-box">
                    <text class="address-text">{{ qrcodeUrl }}</text>
                    <view class="copy-btn" @click="onCopyAddress">
                        <u-icon name="https://pro-oss.pinkwallet.com/image/1387106965997051904.png" size="40"
                            color="#C0C4CC" />
                    </view>
                </view>
                <view class="info-list">
                    <view class="info-row">
                        <text class="info-label">充值至</text>
                        <text class="info-value">现货钱包</text>
                    </view>
                    <view class="info-row">
                        <text class="info-label">最小充值数量</text>
                        <text class="info-value">{{ minAmount ? '>' + minAmount + ' ' + selectedCoin.symbol : ''
                            }}</text>
                    </view>
                    <view class="info-row">
                        <text class="info-label">到账(可交易)</text>
                        <text class="info-value">1 次确认</text>
                    </view>
                </view>
                <view class="info-tips">
                    <text class="info-tip">*请勿与受制裁实体进行交易。<text class="info-link">了解详情</text></text>
                    <text class="info-tip">*请不要往该地址充值NFT。</text>
                </view>
            </view>
            <!-- 更多信息 -->
            <view class="more-info" v-if="showAddress">
                <text>更多信息</text>
                <u-icon name="arrow-down" size="28" color="#C0C4CC" />
            </view>
        </view>
        <!-- 底部按钮 -->
        <view class="footer">
            <u-button hover-class="none" class="confirm-btn" @click="showSavePopup = true">保存并分享地址</u-button>
        </view>
        <!-- 币种弹窗 -->
        <u-popup v-model="showCoinPopup" mode="bottom" border-radius="14" height="1400rpx">
            <view>
                <view class="popup_title">
                    <text> 选择币种</text>
                    <u-icon @click="showCoinPopup = false"
                        name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385342962270560256.png"
                        size="48" />
                </view>
                <view class="search-box-n">
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385297482358546432.png" />
                    <input v-model="searchKeyword" placeholder="搜索" class="search-input" @input="onSearch" />
                </view>
                <view class="coin-list">
                    <view class="coin-item" v-for="coin in filteredCoins" :key="coin.name" @click="selectCoin(coin)">
                        <image :src="coin.image" class="coin-icon" />
                        <text class="coin-name">{{ coin.symbol }}</text>
                        <image class="arrow"
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374825043983949824.png" />
                    </view>
                    <nodata v-if="filteredCoins.length === 0" />
                </view>
            </view>
        </u-popup>
        <!-- 保存图片弹窗 -->
        <u-popup v-model="showSavePopup" mode="center" width="750" border-radius="0" :mask-close-able="false">
            <view class="save_poster_bg">
                <view class="save-poster" id="savePoster">
                    <view class="save-title">充值{{ selectedCoin.symbol }}</view>
                    <view class="save-qrcode">
                        <uv-qrcode :value="qrcodeUrl" size="220rpx" :options="options"></uv-qrcode>
                        <!-- <view class="qrcode-coin-icon">
                            <image :src="selectedCoin.image" class="coin-center-icon" />
                        </view> -->
                    </view>
                    <view class="save-info">
                        <view class="save-row"><text class="save-label">网络</text><text class="save-value">{{ network
                                }}</text></view>
                        <view class="save-row"><text class="save-label">地址</text>
                            <text class="save-value save-address">{{ qrcodeUrl }}</text>
                        </view>
                        <view class="save-tip">*请不要往该地址充值NFT</view>
                    </view>
                </view>
                <view class="save_btns">
                    <view class="save-btn" @click="savePosterImage">保存</view>
                    <view class="line"></view>
                    <view class="save-btn" @click="showSavePopup = false">取消</view>
                </view>
            </view>

        </u-popup>
        <zero-loading type="sword" v-if="loading"></zero-loading>
    </view>
</template>

<script>
    import uvQrcode from '@/uni_modules/uv-qrcode/components/uv-qrcode/uv-qrcode.vue'
    import zeroLoading from '@/uni_modules/zero-loading/components/zero-loading/zero-loading.vue'
    export default {
        name: 'addCapital',
        components: {
            uvQrcode,
            zeroLoading
        },
        data() {
            return {
                coins: [],
                searchKeyword: '',
                transferAmount: '',
                showCoinPopup: false,
                showNetworkPopup: false,
                // 网络相关
                network: '',
                networkOptions: [],
                // 当前选中的币种
                selectedCoin: {
                    symbol: 'USDT',
                    name: 'USDT',
                    image: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385363807210659840.png'
                },
                // 充值相关
                qrcodeUrl: '',
                memo: '',
                minAmount: '',
                showAddress: false,
                loading: false,
                options: {
                    useDynamicSize: false,
                    errorCorrectLevel: 'Q',
                    areaColor: "#fff"
                },
                showSavePopup: false
            }
        },
        watch: {
            "$store.state.indexRate"(val) {
                console.log(val.address, '地址ws');
                console.log(val, '地址ws');

                if (val.address) {
                    this.cdObj = val
                    this.showAddress = true
                    this.minAmount = val.minAmount
                    this.memo = val.memo
                    this.qrcodeUrl = val.address
                    this.loading = false
                }
            },
        },
        onLoad(options) {
            // this.getCoin()
            this.selectedCoin = {
                symbol: options.symbol,
                name: options.name || options.symbol,
                image: options.image
            }
            this.getNet()
        },
        computed: {
            filteredCoins() {
                if (!this.searchKeyword) {
                    return this.coins
                }
                const keyword = this.searchKeyword.trim().toLowerCase()
                return this.coins.filter(coin =>
                    coin.symbol.toLowerCase().includes(keyword)
                )
            }
        },
        methods: {
            // 币种弹窗
            opencoin() {
                this.showCoinPopup = true
                this.getCoin()
            },
            selectCoin(coin) {
                this.selectedCoin = {
                    symbol: coin.symbol,
                    name: coin.name || coin.symbol,
                    image: coin.image
                }
                this.showCoinPopup = false
                this.network = ''
                this.qrcodeUrl = ''
                this.showAddress = false
                this.getNet()
            },
            async getCoin() {
                let res = await this.$api.symbolListPaged({
                    pageNum: 1,
                    pageSize: 100
                })
                if (res.code == 200) {
                    this.coins = res.result.data
                }
            },
            onSearch() {
                // 搜索币种
            },
            // 网络选择
            async getNet() {
                let res = await this.$api.getNetwork({
                    symbol: this.selectedCoin.symbol,
                })
                if (res.code == 200) {
                    this.networkOptions = res.result
                } else {
                    this.$u.toast(res.msg)
                }
            },
            selectNetwork(item) {
                this.network = item.network
                this.showNetworkPopup = false
                this.getAdd()
            },
            // 获取充值地址
            async getAdd() {
                this.showAddress = false
                this.loading = true
                let res = await this.$api.getWalletAddress({
                    symbol: this.selectedCoin.symbol,
                    network: this.network
                })
                if (res.code == 200) {
                    this.qrcodeUrl = res.result.address
                    this.memo = res.result.memo
                    this.minAmount = res.result.minAmount
                    this.showAddress = true
                    this.loading = false
                } else {
                    this.showAddress = false
                    this.loading = false
                    uni.showToast({
                        title: res.msg,
                        icon: 'none',
                        duration: 2000
                    });
                }
            },
            onCopyAddress() {
                let that = this
                uni.setClipboardData({
                    data: this.qrcodeUrl,
                    success() {
                        uni.showToast({
                            title: '已复制',
                            icon: 'none',
                        });
                    },
                });
            },
            onCopyMemo() {
                let that = this
                uni.setClipboardData({
                    data: this.memo,
                    success() {
                        uni.showToast({
                            title: '已复制',
                            icon: 'none',
                        });
                    },
                });
            },
            nav_to(name, type) {
                this.$Router.push({
                    name: name,
                    params: {
                        type: type
                    }
                });
            },
            async savePosterImage() {
                // #ifdef H5
                const html2canvas = (await import('html2canvas')).default
                html2canvas(document.getElementById('savePoster')).then(canvas => {
                    let url = canvas.toDataURL('image/png')
                    console.log(url)
                    let a = document.createElement('a')
                    a.href = url
                    a.download = '充值二维码.png'
                    a.click()
                    this.$u.toast('图片已保存')
                    this.showSavePopup = false
                })
                // #endif
                // #ifdef MP-WEIXIN || APP-PLUS
                uni.showLoading({ title: '保存中...' })
                uni.createSelectorQuery().select('#savePoster').boundingClientRect(rect => {
                    uni.canvasToTempFilePath({
                        canvasId: 'savePosterCanvas',
                        success: res => {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success: () => {
                                    uni.showToast({ title: '保存成功', icon: 'success' })
                                    this.showSavePopup = false
                                },
                                fail: () => {
                                    uni.showToast({ title: '保存失败', icon: 'none' })
                                }
                            })
                        },
                        fail: () => {
                            uni.showToast({ title: '生成图片失败', icon: 'none' })
                        },
                        complete: () => {
                            uni.hideLoading()
                        }
                    }, this)
                }).exec()
                // #endif
            }
        }
    }
</script>

<style lang="scss" scoped>
    ::v-deep .u-input__input {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24rpx !important;
        line-height: 40rpx;
    }

    .transfer-page {
        min-height: 100vh;

        .search-icon {
            width: 28rpx;
            height: 30rpx;
            margin-right: 38rpx
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20rpx 32rpx;
            background: #fff;

            .back-btn,
            .history-btn {
                width: 48rpx;
                height: 48rpx;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .back-icon,
            .history-icon {
                width: 24rpx;
                height: 24rpx;
            }

            .title {
                font-size: 32rpx;
                font-weight: 600;
                color: #333;
            }
        }

        .content {
            padding: 0 32rpx;
            margin-top: 32rpx;
            font-family: PingFang SC;
            padding-top: 50rpx;

            .qrcode-section {
                display: flex;
                justify-content: center;
                margin-top: 24rpx;
                margin-bottom: 32rpx;

                .qrcode-wrapper {
                    position: relative;
                    width: 320rpx;
                    height: 320rpx;

                    .qrcode-img {
                        width: 100%;
                        height: 100%;
                        border-radius: 24rpx;
                        background: #fff;
                    }
                }
            }

            .qrcode-coin-icon {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 72rpx;
                height: 72rpx;
                background: #fff;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
            }

            .coin-center-icon {
                width: 56rpx;
                height: 56rpx;
                border-radius: 50%;
            }

            .select-section {
                margin-bottom: 24rpx;

                .select-label {
                    font-size: 24rpx;
                    color: #999;
                    margin-bottom: 12rpx;
                }

                .select-box {
                    background: #F7F7F7;
                    border-radius: 12rpx;
                    height: 72rpx;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0 24rpx;
                    cursor: pointer;
                }

                .select-value {
                    display: flex;
                    align-items: center;
                }

                .coin-icon {
                    width: 36rpx;
                    height: 36rpx;
                    border-radius: 50%;
                    margin-right: 12rpx;
                }

                .coin-symbol {
                    font-size: 28rpx;
                    color: #333;
                    font-weight: 500;
                }

                .network-name {
                    font-size: 28rpx;
                    color: #333;
                    font-weight: 500;
                }
            }

            .address-section {
                margin-bottom: 24rpx;

                .address-label {
                    font-size: 24rpx;
                    color: #999;
                    margin-bottom: 12rpx;
                }

                .address-box {
                    background: #F7F7F7;
                    border-radius: 12rpx;
                    height: 72rpx;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0 24rpx;
                }

                .address-text {
                    font-size: 26rpx;
                    color: #333;
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            .info-section {
                margin: 32rpx 0 16rpx 0;

                .info-row {
                    display: flex;
                    justify-content: space-between;
                    font-size: 24rpx;
                    color: #999;
                    margin-bottom: 8rpx;

                    .info-value {
                        color: #333;
                    }
                }

                .info-tips {
                    font-size: 22rpx;
                    color: #FF82A3;
                    margin-bottom: 2rpx;

                    .info-link {
                        color: #FF82A3;
                        text-decoration: underline;
                        margin-left: 8rpx;
                    }
                }
            }

            .more-info {
                display: flex;
                align-items: center;
                justify-content: center;
                color: #999;
                font-size: 24rpx;
                margin: 16rpx 0 0 0;

                .u-icon {
                    margin-left: 8rpx;
                }
            }
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 32rpx;

            .confirm-btn {
                width: 100%;
                height: 88rpx;
                background: #FF82A3;
                border-radius: 112rpx;
                border: none;
                color: #fff;
                font-weight: 600;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 28rpx;

            }
        }


        .popup_title {
            display: flex;
            justify-content: space-between;
            padding: 32rpx;
            font-size: 28rpx;
            font-weight: 600;
            color: #000;
        }

        .search-box-n {
            display: flex;
            justify-content: center;
            position: relative;

            image {
                position: absolute;
                top: 50%;
                left: 44rpx;
                transform: translateY(-50%);
                width: 52rpx;
                height: 52rpx;
            }
        }

        .search-input {
            text-indent: 76rpx;
            border: none;
            outline: none;
            width: 100%;
            margin: 0 32rpx;
            height: 38*2rpx;
            background: rgba(217, 217, 217, .2);
            border-radius: 34rpx;

        }

        .coin-list {
            padding: 0 32rpx;
            display: flex;
            flex-direction: column;
        }

        .coin-item {
            display: flex;
            align-items: center;
            padding: 30rpx 0;
        }

        .coin-icon {
            width: 64rpx;
            height: 64rpx;
            margin-right: 18rpx;
            border-radius: 50%;
        }

        .coin-name {
            flex: 1;
            font-weight: 500;
            font-size: 28rpx;
            line-height: 40rpx;
            color: #000;
        }

        .arrow {

            width: 36rpx;
            height: 36rpx;
        }

        .info-list {
            margin-top: 32rpx;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12rpx;
        }

        .info-label {
            color: #999;
            font-size: 26rpx;
        }

        .info-value {
            color: #121212;
            font-size: 26rpx;
        }

        .info-tips {
            margin-top: 24rpx;
        }

        .info-tip {
            display: block;
            color: #FF82A3;
            font-size: 24rpx;
            margin-bottom: 4rpx;
        }

        .info-link {
            color: #FF82A3;
            text-decoration: underline;
            margin-left: 8rpx;
        }

        .copy-btn {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-left: 12rpx;
        }

        .copy-text {
            color: #FF82A3;
            font-size: 26rpx;
            margin-left: 4rpx;
            font-weight: 500;
        }
    }

    .save-poster {
        background: #fff;
        border-radius: 32rpx;
        padding: 64rpx 32rpx 110rpx 32rpx;
        margin: 0 auto;
        width: 690rpx;
        box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.10);
        display: flex;
        flex-direction: column;
        align-items: center;
        font-family: PingFang SC;
    }

    .save-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #121212;
        text-align: center;
        margin-bottom: 32rpx;
    }

    .save-qrcode {
        position: relative;
        width: 220rpx;
        height: 220rpx;
        margin: 0 auto 32rpx auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .save-qrcode .qrcode-coin-icon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 56rpx;
        height: 56rpx;
        background: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    }

    .save-qrcode .coin-center-icon {
        width: 44rpx;
        height: 44rpx;
        border-radius: 50%;
    }

    .save-info {
        width: 100%;
        margin-top: 8rpx;
    }

    .save-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        font-size: 26rpx;
        color: #333;
        margin-bottom: 40rpx;

    }

    .save-label {
        color: #999;
        min-width: 80rpx;
        font-size: 26rpx;
    }

    .save-value {
        color: #121212;
        word-break: break-all;
        font-size: 26rpx;
        margin-left: 12rpx;
        max-width: 400rpx;
        text-align: right;
    }

    .save-address {
        font-size: 24rpx;
        word-break: break-all;
        line-height: 1.5;
        max-width: 400rpx;
    }

    .save-tip {
        color: #bbb;
        font-size: 22rpx;
        margin-top: 12rpx;
        text-align: left;
    }

    .save_poster_bg {
        height: 100vh;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .save_btns {
            display: flex;
            flex-direction: column;
            gap: 16rpx;
            margin: 32rpx 0 0 0;
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            background-color: #fff;
            border-radius: 24rpx 24rpx 0 0;
            padding: 12rpx 0rpx;

            .line {
                width: 100%;
                height: 12rpx;
                background-color: rgba(217, 217, 217, 0.2);
            }

            .save-btn {
                width: 100%;
                height: 100rpx;
                border: none;
                color: #000;
                font-size: 28rpx;
                font-weight: 600;
                font-family: PingFang SC;
                font-weight: 500;
                text-align: center;
                line-height: 100rpx;
            }
        }
    }
</style>