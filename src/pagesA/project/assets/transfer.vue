<template>
    <view class="transfer-page">
        <!-- 头部 -->
        <!-- <view class="header">
        <view class="back-btn" @click="goBack">
          <image src="/static/icons/back.png" class="back-icon" />
        </view>
        <text class="title">划转</text>
        <view class="history-btn" @click="goToHistory">
          <image src="/static/icons/history.png" class="history-icon" />
        </view>
      </view> -->
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="划转">
            <view slot="right" class="search-box-right" @click="nav_to('transfer_history')">
                <image src="https://pro-oss.pinkwallet.com/image/1385362296246198272.png" class="search-icon" />
            </view>
        </u-navbar>


        <!-- 主要内容 -->
        <view class="content">
            <!-- 币种选择 -->
            <view class="coin-section">
                <view class="coin-item" @click="opencoin">
                    <view class="coin-info">
                        <image :src="selectedCoin.image" class="coin-icon" />
                        <text class="coin-name">{{ selectedCoin.symbol }}</text>
                    </view>
                    <image src="https://pro-oss.pinkwallet.com/image/1385364156545851392.png" class="arrow-icon" />
                </view>
            </view>

            <!-- 账户选择区域 -->
            <view class="account-section">
                <!-- 从账户 -->
                <view class="account-item" @click="showFromAccountPopup = true">
                    <view class="account-label">
                        <view class="left">从</view>
                        <view class="right">
                            <text class="label-text">{{ fromAccount.name }}</text>
                            <text class="account-name">{{ fromAccount.type }}</text>
                        </view>
                    </view>
                    <image src="https://pro-oss.pinkwallet.com/image/1385364156545851392.png" class="arrow-icon" />
                </view>

                <!-- 转换箭头 -->
                <view class="swap-section">
                    <view class="swap-btn flex_all" @click="swapAccounts">
                        <image src="https://pro-oss.pinkwallet.com/image/1385366662764126208.png" class="swap-icon" />
                    </view>
                </view>
                <view style="height: 2rpx;"></view>
                <!-- 到账户 -->
                <view class="account-item" @click="showToAccountPopup = true">


                    <view class="account-label">
                        <view class="left">到</view>
                        <view class="right">
                            <text class="label-text">{{ toAccount.name }}</text>
                            <text class="account-name">{{ toAccount.type }}</text>
                        </view>
                    </view>
                    <image src="https://pro-oss.pinkwallet.com/image/1385364156545851392.png" class="arrow-icon" />

                </view>
            </view>

            <!-- 金额输入 -->
            <view class="amount-section">
                <view class="input-wrapper">
                    <u-input :clearable="false" :height="80" class="amount-input" type="digit" v-model="transferAmount"
                        placeholder="请输入划转数量" />
                    <view class="currency-info">
                        <text class="currency">{{ selectedCoin.symbol }}</text>
                        <text class="all-btn" @click="setMaxAmount">全部</text>
                    </view>
                </view>

                <!-- 余额信息 -->
                <view class="balance-info">
                    <text class="balance-label"> </text>
                    <text class="available">可用：{{ availableBalance }}{{ selectedCoin.symbol }}</text>
                </view>
            </view>
        </view>

        <!-- 确认按钮 -->
        <view class="footer">
            <u-button hover-class="none" class="confirm-btn" @click="confirmTransfer">确认划转</u-button>
        </view>
        <!-- 账户选择弹窗 -->
        <account-popup :show="showFromAccountPopup || showToAccountPopup" :accounts="accountList"
            :currentAccountId="getCurrentAccountId" @close="closeAccountPopup" @select="selectAccount" />

        <u-popup v-model="showCoinPopup" mode="bottom" border-radius="14" height="1400rpx">
            <view>
                <view class="popup_title">
                    <text> 选择币种</text>
                    <u-icon @click="showCoinPopup = false"
                        name="https://pro-oss.pinkwallet.com/image/1385342962270560256.png" size="48" />
                </view>
                <view class="search-box-n">
                    <image src="https://pro-oss.pinkwallet.com/image/1385297482358546432.png" />
                    <input v-model="searchKeyword" placeholder="搜索" class="search-input" @input="onSearch" />
                </view>

                <view class="coin-list">
                    <view class="coin-item" v-for="coin in filteredCoins" :key="coin.name" @click="selectCoin(coin)">
                        <image :src="coin.image" class="coin-icon" />
                        <text class="coin-name">{{ coin.symbol }}</text>
                        <!-- <text class="arrow">></text> -->
                        <image class="arrow" src="https://pro-oss.pinkwallet.com/image/1374825************.png" />
                    </view>
                    <nodata v-if="filteredCoins.length === 0" />
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import nodata from "../trade/components/nodata"
export default {
    name: 'TransferPage',
    components: {
        nodata
    },
    data() {
        return {
            coins: [],
            searchKeyword: '',
            transferAmount: '',
            showCoinPopup: false,
            showFromAccountPopup: false,
            showToAccountPopup: false,
            // 当前选中的币种
            selectedCoin: {
                symbol: 'USDT',
                name: 'USDT',
                image: 'https://pro-oss.pinkwallet.com/image/1385363807210659840.png'
            },
            fromAccount: {
                id: 1,
                name: '资金账户',
                type: 'USDT',
                balance: '0.000000'
            },
            toAccount: {
                id: 3,
                name: '合约账户',
                type: 'USDT',
                balance: '0.000000'
            },
            accountList: [
                { id: 1, name: '资金账户', balance: '', selected: false },
                { id: 2, name: '美股账户', balance: '', selected: false },
                { id: 3, name: '合约账户', balance: '', selected: false },
                { id: 4, name: '港股账户', balance: '', selected: false }
            ],
            // 账户ID到枚举的映射
            accountTypeMap: {
                1: 'FUNDS',      // 资金账户
                2: 'STOCK_US',   // 美股账户
                3: 'CONTRACT',   // 合约账户
                4: 'STOCK_HK'    // 港股账户
            }
        }
    },
    onLoad() {
        this.symbolAvailableBalances()
        this.getCoin()
    },
    watch: {
        'fromAccount.id': {
            handler(newId, oldId) {
                if (newId && newId !== oldId) {
                    console.log(`fromAccount.id 变化: ${oldId} -> ${newId}`);
                    this.symbolAvailableBalances();
                }
            },
            immediate: false
        },
        'selectedCoin.symbol': {
            handler(newSymbol, oldSymbol) {
                if (newSymbol && newSymbol !== oldSymbol) {
                    console.log(`选中币种变化: ${oldSymbol} -> ${newSymbol}`);
                    // 更新账户类型显示
                    this.fromAccount.type = newSymbol;
                    this.toAccount.type = newSymbol;
                    // 重新查询余额
                    this.symbolAvailableBalances();
                }
            },
            immediate: false
        }
    },
    computed: {

        filteredCoins() {
            if (!this.searchKeyword) {
                return this.coins
            }
            const keyword = this.searchKeyword.trim().toLowerCase()
            return this.coins.filter(coin =>
                coin.symbol.toLowerCase().includes(keyword)
            )
        },
        availableBalance() {
            return this.fromAccount.balance
        },
        getCurrentAccountId() {
            if (this.showFromAccountPopup) {
                return this.fromAccount.id
            } else if (this.showToAccountPopup) {
                return this.toAccount.id
            }
            return null
        }
    },
    methods: {
        opencoin() {
            this.showCoinPopup = true
            this.getCoin()
        },
        selectCoin(coin) {
            console.log('Selected coin:', coin);

            // 更新选中的币种
            this.selectedCoin = {
                symbol: coin.symbol,
                name: coin.name || coin.symbol,
                image: coin.image
            };

            // 更新账户类型显示
            this.fromAccount.type = coin.symbol;
            this.toAccount.type = coin.symbol;

            // 关闭弹窗
            this.showCoinPopup = false;

            // 清空输入金额
            this.transferAmount = '';

            // 重新查询余额
            // this.symbolAvailableBalances();

            // uni.showToast({
            //     title: `已选择 ${coin.symbol}`,
            //     icon: 'none',
            //     duration: 1500
            // });
        },
        async getCoin() {
            let res = await this.$api.symbolListPaged({
                pageNum: 1,
                pageSize: 100
            })
            if (res.code == 200) {
                this.coins = res.result.data
            }
        },
        onSearch() {
            const keyword = this.searchKeyword.trim().toLowerCase()
            this.filteredCoins = this.coins.filter(coin =>
                coin.symbol.toLowerCase().includes(keyword)
            )
        },
        async symbolAvailableBalances() {
            // 根据 fromAccount.id 获取对应的账户类型
            const accountType = this.getAccountType(this.fromAccount.id);

            if (!accountType) {
                console.error('无法获取账户类型，无法查询余额');
                return;
            }

            // 将枚举值转换为API需要的账户参数
            const accountParam = this.getAccountParam(accountType);

            try {
                let res = await this.$api.symbolAvailableBalance({
                    account: accountParam,
                    symbol: this.selectedCoin.symbol
                })

                if (res.code == 200) {
                    // 更新当前 fromAccount 的余额
                    this.fromAccount.balance = res.result.balance.toFixed(4);

                    // 同时更新 accountList 中对应账户的余额
                    const accountIndex = this.accountList.findIndex(acc => acc.id === this.fromAccount.id);
                    if (accountIndex !== -1) {
                        this.accountList[accountIndex].balance = res.result.balance.toFixed(4);
                    }

                    console.log(`账户 ${this.fromAccount.name} 余额更新: ${this.fromAccount.balance} ${this.selectedCoin.symbol}`);
                } else {
                    uni.showToast({
                        title: res.msg || '查询余额失败',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('查询余额出错:', error);
                uni.showToast({
                    title: '查询余额失败',
                    icon: 'none'
                })
            }
        },
        goBack() {
            uni.navigateBack()
        },
        nav_to(e) {
            this.$Router.push({ name: e })
        },
        swapAccounts() {
            const temp = { ...this.fromAccount }
            this.fromAccount = { ...this.toAccount }
            this.toAccount = temp

            // 清空输入金额
            this.transferAmount = ''

            // 注意：这里不需要手动调用 symbolAvailableBalances，因为 watch 会自动触发
            // uni.showToast({
            //     title: '账户已切换',
            //     icon: 'none',
            //     duration: 1500
            // })
        },
        closeAccountPopup() {
            this.showFromAccountPopup = false
            this.showToAccountPopup = false

            // 重置选中状态
            this.accountList.forEach(account => {
                account.selected = false
            })
        },
        selectAccount(account) {
            const selectedAccount = this.accountList.find(item => item.id === account.id)

            if (selectedAccount.id === this.fromAccount.id || selectedAccount.id === this.toAccount.id) {
                uni.showToast({
                    title: '不能选择相同的账户',
                    icon: 'none'
                })
                // this.showToAccountPopup = false
                return
            }

            if (this.showFromAccountPopup) {
                this.fromAccount = {
                    id: selectedAccount.id,
                    name: selectedAccount.name,
                    type: this.selectedCoin.symbol,
                    balance: selectedAccount.balance || '0.0000'
                }
                // 注意：这里不需要手动调用 symbolAvailableBalances，因为 watch 会自动触发
            } else if (this.showToAccountPopup) {
                this.toAccount = {
                    id: selectedAccount.id,
                    name: selectedAccount.name,
                    type: this.selectedCoin.symbol,
                    balance: selectedAccount.balance || '0.0000'
                }
            }

            this.closeAccountPopup()
            this.transferAmount = '' // 切换账户后清空金额
        },
        setMaxAmount() {
            this.transferAmount = this.fromAccount.balance
        },

        // 根据账户ID获取账户类型枚举
        getAccountType(accountId) {
            const accountType = this.accountTypeMap[accountId];
            if (!accountType) {
                console.error(`未找到账户ID ${accountId} 对应的账户类型`);
                return null;
            }
            return accountType;
        },

        // 将账户类型枚举转换为API参数
        getAccountParam(accountType) {
            const accountParamMap = {
                'FUNDS': 'FUNDS',        // 资金账户
                'STOCK_US': 'STOCK_US',  // 美股账户
                'CONTRACT': 'CONTRACT',  // 合约账户
                'STOCK_HK': 'STOCK_HK'   // 港股账户
            };

            return accountParamMap[accountType] || 'CONTRACT'; // 默认返回 contract
        },
        async confirmTransfer() {
            if (!this.transferAmount) {
                uni.showToast({
                    title: '请输入划转数量',
                    icon: 'none'
                })
                return
            }

            // 获取账户类型枚举
            const fromAccountType = this.getAccountType(this.fromAccount.id);
            const toAccountType = this.getAccountType(this.toAccount.id);

            // 验证账户类型是否有效
            if (!fromAccountType || !toAccountType) {
                uni.showToast({
                    title: '账户类型错误',
                    icon: 'none'
                });
                return;
            }

            let res = await this.$api.assettransfer({
                fromAccount: fromAccountType,
                toAccount: toAccountType,
                amount: this.transferAmount,
                symbol: this.selectedCoin.symbol
            })
            if (res.code == 200) {
                this.symbolAvailableBalances()
                this.$Router.push({
                    name: "transferSucceed",
                    params: {
                        amount: this.transferAmount,
                        symbol: this.selectedCoin.symbol
                    }
                })
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })

            }

        }
    },
    components: {
        'account-popup': () => import('./components/account-popup.vue')
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-input__input {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 24rpx !important;
    line-height: 40rpx;
}

.transfer-page {
    min-height: 100vh;

    .search-icon {
        width: 28rpx;
        height: 30rpx;
        margin-right: 38rpx
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 32rpx;
        background: #fff;

        .back-btn,
        .history-btn {
            width: 48rpx;
            height: 48rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-icon,
        .history-icon {
            width: 24rpx;
            height: 24rpx;
        }

        .title {
            font-size: 32rpx;
            font-weight: 600;
            color: #333;
        }
    }

    .content {
        padding: 0 32rpx;
        margin-top: 68rpx;

        .coin-section {
            background: #F7F7F7;
            border-radius: 8rpx;
            height: 100rpx;
            margin-bottom: 16rpx;

            .coin-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                // padding: 32rpx;
                height: 100%;
                padding: 0 28rpx 0 20rpx;

                .coin-info {
                    display: flex;
                    align-items: center;

                    .coin-icon {
                        width: 56rpx;
                        height: 56rpx;
                        margin-right: 16rpx;
                    }

                    .coin-name {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        line-height: 32rpx;
                        color: #000;
                    }
                }

                .arrow-icon {
                    width: 36rpx;
                    height: 36rpx;
                }
            }
        }

        .account-section {
            background: #fff;
            border-radius: 16rpx;
            position: relative;

            .account-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 20rpx 0 28rpx;
                background: #F7F7F7;
                border-radius: 8rpx;
                height: 124rpx;



                .account-label {
                    display: flex;
                    align-items: center;
                    // min-width: 120rpx;
                    gap: 20rpx;

                    .left {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        line-height: 32rpx;
                        color: #000;
                    }

                    .right {
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;

                        .account-name {
                            margin-top: 4rpx;
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 20rpx;
                            line-height: 32rpx;
                            opacity: .5;
                        }

                        .label-text {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 24rpx;
                            line-height: 32rpx;
                            color: #000;
                        }
                    }



                }


                .account-info {
                    flex: 1;
                    text-align: center;

                    .account-name {
                        font-size: 28rpx;
                        color: #333;
                        display: block;
                    }

                    .account-type {
                        font-size: 24rpx;
                        color: #999;
                        margin-top: 8rpx;
                        display: block;
                    }
                }

                .arrow-icon {
                    width: 36rpx;
                    height: 36rpx;
                }
            }

            .swap-section {
                position: absolute;
                right: 50%;
                top: 50%;
                transform: translate(50%, -50%);
                z-index: 10;

                .swap-btn {
                    width: 64rpx;
                    height: 64rpx;
                    background: #f8f9fa;
                    border: 2rpx solid #e9ecef;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

                    .swap-icon {
                        width: 14rpx;
                        height: 24rpx;
                    }
                }
            }
        }

        .amount-section {
            margin-top: 16rpx;
            // background: #fff;
            // border-radius: 16rpx;

            .input-wrapper {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 24rpx;
                background: #F7F7F7;
                padding: 0 28rpx;

                .uni-input-placeholder {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                }


                .amount-input {
                    flex: 1;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx !important;
                    line-height: 40rpx;
                    color: red !important;
                    border: none;
                    outline: none;
                }

                .currency-info {
                    display: flex;
                    align-items: center;

                    .currency {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 28rpx;
                        line-height: 32rpx;
                        margin-right: 16rpx;
                    }

                    .all-btn {
                        color: #FF82A3;
                        font-family: PingFang SC;
                        font-weight: 500;
                        font-size: 28rpx;
                        line-height: 32rpx;
                    }
                }
            }

            .balance-info {
                display: flex;
                align-items: center;
                justify-content: space-between;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;

            }
        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 32rpx;

        .confirm-btn {
            width: 100%;
            height: 88rpx;
            background: #FF82A3;
            border-radius: 112rpx;
            border: none;
            color: #fff;
            font-weight: 600;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;

        }
    }


    .popup_title {
        display: flex;
        justify-content: space-between;
        padding: 32rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #000;
    }

    .search-box-n {
        display: flex;
        justify-content: center;
        position: relative;

        image {
            position: absolute;
            top: 50%;
            left: 44rpx;
            transform: translateY(-50%);
            width: 52rpx;
            height: 52rpx;
        }
    }

    .search-input {
        text-indent: 76rpx;
        border: none;
        outline: none;
        width: 100%;
        margin: 0 32rpx;
        height: 38*2rpx;
        background: rgba(217, 217, 217, .2);
        border-radius: 34rpx;

    }

    .coin-list {
        padding: 0 32rpx;
        display: flex;
        flex-direction: column;
    }

    .coin-item {
        display: flex;
        align-items: center;
        padding: 30rpx 0;
    }

    .coin-icon {
        width: 64rpx;
        height: 64rpx;
        margin-right: 18rpx;
        border-radius: 50%;
    }

    .coin-name {
        flex: 1;
        font-family: PingFang HK;
        font-weight: 500;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #000;
    }

    .arrow {

        width: 36rpx;
        height: 36rpx;
    }

}
</style>