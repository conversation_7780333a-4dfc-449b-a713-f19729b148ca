<template>
    <view class="transfer-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="title" :customBack="back">
            <view slot="right" class="search-box-right" @click="nav_to('Record', 'withdraw')">
                <image src="https://pro-oss.pinkwallet.com/image/1385362296246198272.png" class="search-icon" />
            </view>
        </u-navbar>
        <view class="content">
            <!-- 地址输入框 -->
            <view class="form-label">地址</view>
            <view class="input-box">
                <input class="input" v-model="address" placeholder="长按粘贴" />
                <u-icon name="https://pro-oss.pinkwallet.com/image/1387173194321453056.png" size="40" color="#C0C4CC"
                    @click="openAddressPopup" />
                <u-icon name="https://pro-oss.pinkwallet.com/image/1387173311648718848.png" size="40" color="#C0C4CC"
                    style="margin-left:12rpx;" @click="scanCode" />
            </view>
            <!-- 币种选择 -->
            <!-- <view class="select-section">
                <view class="select-label">币种</view>
                <view class="select-box" @click="showCoinPopup = true">
                    <view class="select-value">
                        <image :src="selectedCoin.image" class="coin-icon" />
                        <text class="coin-symbol">{{ selectedCoin.symbol }}</text>
                    </view>
                    <u-icon name="https://pro-oss.pinkwallet.com/image/1387107155650895872.png" size="40"
                        color="#C0C4CC" />
                </view>
            </view> -->
            <!-- 币种弹窗 -->
            <u-popup v-model="showCoinPopup" mode="bottom" border-radius="14" height="1400rpx">
                <view>
                    <view class="popup_title">
                        <text>选择币种</text>
                        <u-icon @click="showCoinPopup = false"
                            name="https://pro-oss.pinkwallet.com/image/1385342962270560256.png" size="48" />
                    </view>
                    <view class="search-box-n">
                        <image src="https://pro-oss.pinkwallet.com/image/1385297482358546432.png" />
                        <input v-model="searchKeyword" placeholder="搜索" class="search-input" @input="onSearch" />
                    </view>
                    <view class="coin-list">
                        <view class="coin-item" v-for="coin in filteredCoins" :key="coin.name"
                            @click="selectCoin(coin)">
                            <image :src="coin.image" class="coin-icon" />
                            <text class="coin-name">{{ coin.symbol }}</text>
                            <image class="arrow" src="https://pro-oss.pinkwallet.com/image/1374825043983949824.png" />
                        </view>
                        <nodata v-if="filteredCoins.length === 0" />
                    </view>
                </view>
            </u-popup>
            <!-- 网络选择 -->
            <view class="select-section">
                <view class="select-label">网络</view>
                <view class="select-box" :class="{ disabled: !selectedCoin.symbol }" @click="onNetworkBoxClick">
                    <view class="select-value">
                        <text class="network-name">{{ network || '请选择网络' }}</text>
                    </view>
                    <u-icon name="https://pro-oss.pinkwallet.com/image/1387107155650895872.png" size="40"
                        color="#C0C4CC" />
                </view>
                <!-- 网络弹窗 -->
                <u-popup v-model="showNetworkPopup" mode="bottom" border-radius="14" height="900rpx">
                    <view>
                        <view class="popup_title">
                            <text>选择网络</text>
                            <u-icon @click="showNetworkPopup = false"
                                name="https://pro-oss.pinkwallet.com/image/1385342962270560256.png" size="48" />
                        </view>
                        <view class="coin-list">
                            <view class="coin-item" v-for="item in networkOptions" :key="item.network"
                                @click="selectNetwork(item)">
                                <text class="coin-name">{{ item.network }}</text>
                            </view>
                        </view>
                    </view>
                </u-popup>
            </view>

            <!-- 提现数量 -->
            <view class="form-label">提现数量</view>
            <view class="input-box">
                <input class="input" v-model="amount" type="number" placeholder="最少0" />
                <text class="unit">{{selectedCoin.symbol}}</text>
                <text class="all-btn" @click="setMaxAmount">全部提现</text>
            </view>
            <view class="yujiNum">
                ≈{{ (amount - fee) > 0 ? (amount - fee).toFixed(2) : '0.00' }} {{selectedCoin.symbol}}
            </view>
            <!-- 可用余额 -->
            <view class="available-row">
                <text>可用</text>
                <text class="available-num">{{ available }} {{selectedCoin.symbol}}</text>
            </view>
            <!-- 温馨提示 -->
            <view class="tips">
                <text class="tip">*请勿直接提现至众筹或ICO地址，否则将无法收到众筹或ICO发放的代币</text>
                <text class="tip">*请勿与受制裁或实体进行交易 <text class="tip-link">了解详情</text></text>
            </view>
            <!-- 到账数量和手续费 -->
            <view class="bottom-info-row">
                <view class="bottom-info-left">
                    <view class="bottom-amount-label">到账数量</view>
                    <view class="bottom-amount-value">
                        <text class="amount-num">{{ (amount - fee) > 0 ? (amount - fee).toFixed(2) : '0.00' }}</text>
                        <text class="amount-unit">{{ selectedCoin.symbol }}</text>
                    </view>
                    <view class="bottom-fee">网络手续费 {{ fee }} {{ selectedCoin.symbol }}</view>
                </view>
                <view class="bottom-info-right">
                    <u-button hover-class="none" class="submit-btn-new" @click="onWithdraw">提现</u-button>
                </view>
            </view>
        </view>
        <!-- 地址选择弹窗 -->
        <u-popup v-model="showAddressPopup" mode="bottom" border-radius="14" height="900rpx">
            <view class="address_popup">
                <view class="address_popup_title_row">
                    <text class="address_popup_title">选择地址</text>
                    <u-icon name="https://pro-oss.pinkwallet.com/image/1385342962270560256.png" size="44"
                        @click="closeAddressPopup" />
                </view>
                <view class="address_popup_header">
                    <view class="address_popup_tab">
                        <text :class="['tab_btn', addressTab === 2 ? 'active' : '']"
                            @click="switchAddressTab(2)">地址簿</text>
                        <text :class="['tab_btn', addressTab === 1 ? 'active' : '']"
                            @click="switchAddressTab(1)">最近</text>
                    </view>
                </view>
                <view class="address_popup_list">
                    <view v-if="addressTab === 2">
                        <view class="address_item" v-for="item in addressList" :key="item.address"
                            @click="selectAddress(item)">
                            <view class="address_row">
                                <text class="address_text">{{ item.withdrawCryptoDTO.address }}</text>
                            </view>
                            <view class="address_tags">
                                <text class="tag tag_pink" v-if="item.withdrawCryptoDTO.general !=1">{{
                                    item.withdrawCryptoDTO.symbol }}</text>
                                <text class="remark">{{ item.withdrawCryptoDTO.general? '通用' : '' }}</text>
                            </view>
                        </view>
                    </view>
                    <view v-else>
                        <view class="address_item" v-for="item in addressList" :key="item.address"
                            @click="selectAddress(item)">
                            <view class="address_row">
                                <text class="address_text">{{ item.withdrawCryptoDTO.address }}</text>
                            </view>
                            <view class="address_tags">
                                <text class="tag tag_pink" v-if="item.withdrawCryptoDTO.general !=1">{{
                                    item.withdrawCryptoDTO.symbol }}</text>
                                <text class="remark">{{ item.withdrawCryptoDTO.general? '通用' : '' }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="address_popup_footer">
                    <u-button class="add_btn" @click="addNewAddress">添加新地址</u-button>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
    let Qrcode = require('@/utils/reqrcode.js')
    export default {
        name: 'withdraw',
        data() {
            return {
                title: '提现',
                address: '',
                network: '',
                networkOptions: [],
                showNetworkPopup: false,
                amount: '',
                available: 0,
                fee: 0.00,
                coins: [],
                searchKeyword: '',
                showCoinPopup: false,
                selectedCoin: {
                    symbol: 'USDT',
                    name: 'USDT',
                    image: 'https://pro-oss.pinkwallet.com/image/1385363807210659840.png'
                },
                showAddressPopup: false,
                addressTab: 2, // 3: 地址簿, 1: 最近
                addressList: [],
                recentList: [
                    // 最近使用的地址
                ],
                precision: "",

            }
        },
        onLoad(options) {
            this.selectedCoin = {
                symbol: options.symbol,
                name: options.name || options.symbol,
                image: options.image
            }
            this.title = '提现' + options.symbol
            this.getNet()
            this.getBalance()

        },
        computed: {
            filteredCoins() {
                if (!this.searchKeyword) {
                    return this.coins
                }
                const keyword = this.searchKeyword.trim().toLowerCase()
                return this.coins.filter(coin =>
                    coin.symbol.toLowerCase().includes(keyword)
                )
            }
        },
        watch: {
            amount: {
                handler(newVal, oldVal) {
                    if (newVal) {
                        clearTimeout(this.inputTimer); // 清除上一次的定时器
                        this.inputTimer = setTimeout(() => {
                            this.FetchgetWithdrawFee()
                        }, 300);

                    }
                },
            }
        },
        methods: {
            back() {
                this.$Router.pushTab({
                    name: "Home",
                    params: {
                        tabIndex: 4
                    }
                })
            },
            nav_to(name, type) {
                this.$Router.push({ name, params: { type } })
            },
            onPasteAddress() {
                // 粘贴剪贴板内容
                uni.getClipboardData({
                    success: res => {
                        this.address = res.data
                    }
                })
            },
            onCopyAddress() {
                uni.setClipboardData({
                    data: this.address,
                    success: () => {
                        uni.showToast({ title: '已复制', icon: 'none' })
                    }
                })
            },
            async getNet() {
                // 复用addCapital.vue逻辑
                let res = await this.$api.getNetwork({ symbol: this.selectedCoin.symbol })
                if (res.code == 200) {
                    this.networkOptions = res.result
                } else {
                    this.$u.toast(res.msg)
                }
            },
            selectNetwork(item) {
                this.network = item.network
                this.showNetworkPopup = false
            },
            setMaxAmount() {
                this.amount = this.available
            },
            async onWithdraw() {
                let res = await this.$api.withdrawCheck({

                })
                if (res.code == 200) {
                    // 提现逻辑
                    if (!this.address) return this.$u.toast('请输入地址')
                    if (!this.network) return this.$u.toast('请选择网络')
                    if (!this.amount || Number(this.amount) <= 0) return this.$u.toast('请输入正确的提现数量')
                    let params = {
                        withdrawType: 2,
                        symbol: this.selectedCoin.symbol,
                        network: this.network,
                        address: this.address,
                        amount: this.amount,
                        fee: this.fee
                    }
                    uni.setStorageSync('withdraw', params)
                    this.$Router.push({
                        name: "checkwithdraw"
                    })
                } else {
                    this.$u.toast(res.msg)
                }
            },
            opencoin() {
                this.showCoinPopup = true
                this.getCoin()
            },
            selectCoin(coin) {
                this.selectedCoin = {
                    symbol: coin.symbol,
                    name: coin.name || coin.symbol,
                    image: coin.image
                }
                this.showCoinPopup = false
                this.network = ''
                this.address = ''
                this.amount = ''
                this.networkOptions = []
                this.getNet()
            },
            async getCoin() {
                let res = await this.$api.symbolListPaged({
                    pageNum: 1,
                    pageSize: 100
                })
                if (res.code == 200) {
                    this.coins = res.result.data
                }
            },
            onSearch() { },
            onNetworkBoxClick() {
                if (!this.selectedCoin.symbol) {
                    this.$u.toast('请先选择币种')
                    return
                }
                if (this.networkOptions.length === 0) {
                    this.$u.toast('暂无可用网络')
                    return
                }
                this.showNetworkPopup = true
            },
            scanCode() {
                // #ifdef APP-PLUS
                this.scanQRCode()
                // #endif

                // #ifdef H5
                this.scanCodeH5()
                // #endif
            },
            scanQRCode() {
                let that = this
                uni.scanCode({
                    scanType: ['qrCode'],
                    success: function (res) {
                        that.address = res.result
                    }
                })
            },
            // H5通过拉起相机拍照来识别二维码
            scanCodeH5() {
                uni.chooseImage({
                    count: 1,
                    success: imgRes => {
                        Qrcode.qrcode.decode(this.getObjectURL(imgRes.tempFiles[0]))
                        Qrcode.qrcode.callback = (codeRes) => {
                            if (codeRes.indexOf('error') >= 0) {
                                // 二维码识别失败
                                this.address = 'undefined' + codeRes
                            } else {
                                // 二维码识别成功
                                let r = this.decodeStr(codeRes)
                                this.address = r
                            }
                        }
                    }
                })
            },
            // 获取文件地址函数
            getObjectURL(file) {
                var url = null
                if (window.createObjectURL !== undefined) { // basic
                    url = window.createObjectURL(file)
                } else if (window.URL !== undefined) { // mozilla(firefox)
                    url = window.URL.createObjectURL(file)
                } else if (window.webkitURL !== undefined) { // webkit or chrome
                    url = window.webkitURL.createObjectURL(file)
                }
                return url
            },
            // 解码，输出：中文
            decodeStr(str) {
                var out, i, len, c;
                var char2, char3;
                out = "";
                len = str.length;
                i = 0;
                while (i < len) {
                    c = str.charCodeAt(i++);
                    switch (c >> 4) {
                        case 0:
                        case 1:
                        case 2:
                        case 3:
                        case 4:
                        case 5:
                        case 6:
                        case 7:
                            // 0xxxxxxx
                            out += str.charAt(i - 1);
                            break;
                        case 12:
                        case 13:
                            // 110x xxxx 10xx xxxx
                            char2 = str.charCodeAt(i++);
                            out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F));
                            break;
                        case 14:
                            // 1110 xxxx 10xx xxxx 10xx xxxx
                            char2 = str.charCodeAt(i++);
                            char3 = str.charCodeAt(i++);
                            out += String.fromCharCode(((c & 0x0F) << 12) |
                                ((char2 & 0x3F) << 6) |
                                ((char3 & 0x3F) << 0));
                            break;
                    }
                }
                return out;
            },
            openAddressPopup() {
                this.showAddressPopup = true
                this.getsearchUserWithdrawAccountPaged()
            },
            closeAddressPopup() {
                this.showAddressPopup = false
            },
            switchAddressTab(tab) {
                this.addressTab = tab
                this.getsearchUserWithdrawAccountPaged()
            },
            addNewAddress() {
                // 跳转到添加地址页面
                this.$Router.push({ name: 'addAddress' })
            },
            async getsearchUserWithdrawAccountPaged() {
                this.addressList = []
                let res = await this.$api.searchUserWithdrawAccountPaged({
                    pageNum: 1,
                    pageSize: 100,
                    symbol: this.selectedCoin.symbol,
                    // network: this.network,
                    searchType: this.addressTab
                })
                if (res.code == 200) {
                    res.result.data.forEach(item => {
                        if (item.withdrawCryptoDTO) {
                            this.addressList.push(item)
                        }
                    })
                    console.log(this.addressList)
                    // this.addressList = res.result.data
                    // if (this.Account.pageNum == 1) {


                    //     // this.addressList = [...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data, ...res.result.data]

                    // } else {
                    //     this.addressList = this.addressList.concat(res.result.data)
                    // }this.addressList
                }
            },
            selectAddress(item) {
                this.address = item.withdrawCryptoDTO.address
                this.network = item.withdrawCryptoDTO.network
                if (item.withdrawCryptoDTO.general == 0) {
                    this.selectedCoin = {
                        symbol: item.withdrawCryptoDTO.symbol,
                        name: item.withdrawCryptoDTO.symbol,
                    }
                    this.title = '提现' + item.withdrawCryptoDTO.symbol
                }
                this.showAddressPopup = false
            },
            async getBalance() {
                let res = await this.$api.symbolAvailableBalance({
                    account: 'FUNDS',
                    symbol: this.selectedCoin.symbol
                })
                if (res.code == 200) {
                    console.log(res)
                    this.available = res.result.balance
                }
            },
            onfromAmountInputChange(event) {
                console.log(event);
                let decimalDigits = this.precision || 2;
                let pattern = new RegExp(`^\\d*(\\.\\d{0,${decimalDigits}})?`);
                event = (event.match(pattern)?.[0]) || "";
                this.$nextTick(() => {
                    this.amount = event
                })
            },
            async FetchgetWithdrawFee(e) {
                let res = await this.$api.getWithdrawFee({
                    withdrawType: 2,
                    symbol: this.selectedCoin.symbol,
                    network: this.network,
                    address: this.address,
                    amount: this.amount,
                })
                this.getWithDrawPrecisions(e)

                if (res.code == 200) {
                    this.fee = res.result
                } else {
                    this.$u.toast(res.msg)
                }
            },
            async getWithDrawPrecisions(e) {
                let res = await this.$api.getWithDrawPrecision({
                    withdrawType: 2,
                    symbol: this.selectedCoin.symbol,
                    network: this.network,
                    address: this.address,
                    amount: this.amount,
                })
                if (res.code == 200) {
                    this.precision = res.result
                }
            },



        }

    }
</script>

<style lang="scss" scoped>
    .transfer-page {
        background: #fff;
        min-height: 100vh;
        padding-bottom: 120rpx;
        font-family: PingFang SC;

        .search-icon {
            width: 28rpx;
            height: 30rpx;
            margin-right: 38rpx
        }
    }

    .content {
        padding: 50rpx 32rpx 0 32rpx;

        .select-section {
            margin-bottom: 24rpx;

            .select-label {
                font-size: 28rpx;
                color: #121212;
                font-weight: 600;
                margin-bottom: 12rpx;
            }

            .select-box {
                background: #F7F7F7;
                border-radius: 12rpx;
                height: 80rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 0 24rpx;
                cursor: pointer;
            }

            .select-value {
                display: flex;
                align-items: center;
            }

            .coin-icon {
                width: 36rpx;
                height: 36rpx;
                border-radius: 50%;
                margin-right: 12rpx;
            }

            .coin-symbol {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
            }

            .network-name {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
            }
        }

        .search-box-n {
            display: flex;
            justify-content: center;
            position: relative;

            image {
                position: absolute;
                top: 50%;
                left: 44rpx;
                transform: translateY(-50%);
                width: 52rpx;
                height: 52rpx;
            }
        }

        .search-input {
            text-indent: 76rpx;
            border: none;
            outline: none;
            width: 100%;
            margin: 0 32rpx;
            height: 38*2rpx;
            background: rgba(217, 217, 217, .2);
            border-radius: 34rpx;
        }

        .coin-list {
            padding: 0 32rpx;
            display: flex;
            flex-direction: column;
        }

        .coin-item {
            display: flex;
            align-items: center;
            padding: 30rpx 0;
        }

        .coin-icon {
            width: 64rpx;
            height: 64rpx;
            margin-right: 18rpx;
            border-radius: 50%;
        }

        .coin-name {
            flex: 1;
            font-weight: 500;
            font-size: 28rpx;
            line-height: 40rpx;
            color: #000;
        }

        .arrow {
            width: 36rpx;
            height: 36rpx;
        }

        .form-label {
            font-size: 28rpx;
            color: #121212;
            font-weight: 600;
            margin: 32rpx 0 12rpx 0;
        }

        .yujiNum {
            font-size: 24rpx;
            color: rgba(0, 0, 0, .5);
        }

        .input-box {
            background: #F7F7F7;
            border-radius: 12rpx;
            height: 80rpx;
            display: flex;
            align-items: center;
            padding: 0 24rpx;
            margin-bottom: 30rpx;

            &.select {
                cursor: pointer;
            }

            .input {
                flex: 1;
                font-size: 28rpx;
                color: #121212;
                background: transparent;
                border: none;
                outline: none;
            }

            .unit {
                font-size: 26rpx;
                color: #121212;
                margin-left: 12rpx;
            }

            .all-btn {
                color: #FF82A3;
                font-size: 26rpx;
                margin-left: 24rpx;
            }
        }

        .available-row {
            display: flex;
            justify-content: flex-end;
            font-size: 24rpx;
            color: #999;
            margin-bottom: 24rpx;

            .available-num {
                color: #121212;
                margin-left: 12rpx;
            }
        }

        .tips {
            margin: 24rpx 0 0 0;

            .tip {
                display: block;
                color: #FF82A3;
                font-size: 22rpx;
                margin-bottom: 4rpx;
            }

            .tip-link {
                color: #FF82A3;
                text-decoration: underline;
                margin-left: 8rpx;
            }
        }

        .bottom-info-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            padding: 0 20rpx calc(16rpx + env(safe-area-inset-bottom)) 20rpx;
            background: #fff;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 10;
            min-height: 120rpx;
        }

        .bottom-info-left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-end;
            padding-bottom: 8rpx;
        }

        .bottom-amount-label {
            font-size: 22rpx;
            color: #999;
            margin-bottom: 2rpx;
        }

        .bottom-amount-value {
            display: flex;
            align-items: baseline;
            font-size: 36rpx;
            font-weight: bold;
            color: #121212;
            margin-bottom: 0;
            line-height: 1.1;
        }

        .amount-num {
            font-size: 36rpx;
            font-weight: bold;
            color: #121212;
        }

        .amount-unit {
            font-size: 24rpx;
            color: #121212;
            margin-left: 4rpx;
            font-weight: 600;
        }

        .bottom-fee {
            font-size: 24rpx;
            color: #999;
            margin-top: 2rpx;
            line-height: 1.1;
        }

        .bottom-info-right {
            display: flex;
            align-items: flex-end;
            height: 100%;
            margin-left: 32rpx;
        }

        .submit-btn-new {
            width: 220rpx;
            height: 72rpx;
            background: #FF82A3;
            border-radius: 72rpx;
            border: none;
            color: #fff;
            font-weight: 600;
            font-size: 28rpx;
            text-align: center;
            justify-content: center;
            align-items: center;
            display: flex;
            box-shadow: none;
        }
    }

    .footer {
        display: none;
    }

    .popup_title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #000;
    }

    .select-box.disabled {
        background: #f0f0f0;
        color: #bbb;
        pointer-events: none;
    }

    .address_popup {
        padding: 0 0 40rpx 0;
        background: #fff;
        min-height: 700rpx;
        display: flex;
        flex-direction: column;
        height: 100%;

        .address_popup_title_row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 32rpx 32rpx 0 32rpx;
        }

        .address_popup_title {
            font-size: 32rpx;
            font-weight: bold;
            color: #121212;
        }

        .address_popup_header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 32rpx 32rpx 0 32rpx;
        }

        .address_popup_tab {
            display: flex;

            .tab_btn {
                font-size: 28rpx;
                color: #999;
                padding: 0 24rpx;
                font-weight: 600;

                &.active {
                    color: #121212;
                    background: #F7F7F7;
                    border-radius: 32rpx;
                }
            }
        }

        .address_popup_list {
            flex: 1;
            padding: 32rpx;
            overflow-y: auto;

            .address_item {
                background: #F7F7F7;
                border-radius: 16rpx;
                margin-bottom: 24rpx;
                padding: 24rpx;

                .address_row {
                    font-size: 26rpx;
                    color: #121212;
                    font-weight: 500;
                    margin-bottom: 12rpx;
                    word-break: break-all;
                }

                .address_tags {
                    display: flex;
                    align-items: center;

                    .tag {
                        font-size: 22rpx;
                        padding: 4rpx 18rpx;
                        border-radius: 24rpx;
                        margin-right: 12rpx;

                        &.tag_pink {
                            background: #FFD6E3;
                            color: #FF82A3;
                        }
                    }

                    .remark {
                        font-size: 22rpx;
                        color: #999;
                    }
                }
            }
        }

        .address_popup_footer {
            padding: 0 32rpx;

            .add_btn {
                width: 100%;
                height: 88rpx;
                background: #FF82A3;
                border-radius: 44rpx;
                color: #fff;
                font-size: 28rpx;
                font-weight: 600;
            }
        }
    }
</style>