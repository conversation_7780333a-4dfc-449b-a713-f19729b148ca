<template>
	<view class="body">
        <u-navbar :border-bottom="false" :title="title" :customBack="back">
		</u-navbar>
		<view class="container">
			<view class="top_desc">为确保账户安全，需要选择其中一种验证方式进行身份验证</view>
			
			<view class="list_container">
				<u-cell-group :border="false">
					<u-cell-item 
						v-for="(item, index) in verifyList" 
						:key="index"
						:title="item.title"
						:arrow="true"
						hover-class="cell_hover"
						@click="selectVerify(item)"
						:class="{'selected': selectedVerifyType === item.type}"
					>
						<u-icon slot="icon" :name="item.icon" size="36"></u-icon>
					</u-cell-item>
				</u-cell-group>
			</view>
		</view>
		<!-- <view class="bottom_button">
			<u-button @click="confirm" :custom-style="customStyle" :disabled="!selectedVerifyType">确认</u-button>
		</view> -->
	</view>
</template>

<script>
export default {
    data() {
        return {
            title: '密码',
			verifyList: [
				{ icon: 'email', title: '邮箱验证', type: 'email',name:'assetsEmailCode' },
				// { icon: 'star', title: '谷歌身份验证', type: 'google',path:'/pagesA/project/user/security/passwordVerify' },
			],
			selectedVerifyType: '',
			customStyle: {
				backgroundColor: '#FF82A3',
				color: '#FFFFFF',
				borderRadius: '48rpx',
				height: '96rpx',
				fontSize: '30rpx'
			}
        }
    },
	onLoad(options) {
		if (options.targetPage) {
			this.targetPage = options.targetPage
		}
	},
    methods: {
        // 返回上一页
		back() {
			uni.navigateBack()
		},
		selectVerify(item) {
			this.$Router.push({
				name: item.name,
				params: {
					targetPage: this.targetPage
				}
			})
		},
		confirm() {
			if (!this.selectedVerifyType) {
				uni.showToast({
					title: '请选择一种验证方式',
					icon: 'none'
				});
				return;
			}
			
			const routes = {
				email: '/pagesA/project/user/security/emailVerify',
				google: '/pagesA/project/user/security/googleVerify' // 假设的谷歌验证页面
			};
			
			if (routes[this.selectedVerifyType]) {
				uni.navigateTo({
					url: routes[this.selectedVerifyType]
				});
			}
		}
    }
}
</script>
 
<style lang="scss" scoped>
.body {
  min-height: 100vh;
  font-family: 'Gilroy-Medium';
  display: flex;
  flex-direction: column;
}

.container {
	padding: 30rpx;
	flex: 1;

	.top_desc {
		font-size: 26rpx;
		color: #999;
		line-height: 1.6;
		margin-top: 20rpx;
		margin-bottom: 40rpx;
	}

	.list_container {
		background-color: #fff;
		border-radius: 16rpx;
		overflow: hidden;

		::v-deep .u-cell {
			border:1px solid rgba(0,0,0,0.1);
		border-radius:16rpx;
		margin-bottom: 20rpx;
		height:120rpx;
		line-height: 120rpx;
			&::after {
				border-color: #f4f4f4;
				left: 100rpx;
			}
		}
		
		::v-deep .u-cell_title {
			margin-left: 20rpx;
			color: #333;
			font-size: 28rpx;
		}

		::v-deep .u-cell.selected {
			box-shadow: 0 0 0 1px #FF82A3 inset;
		}
		
		.cell_hover {
			background-color: #f8f8f8;
		}
	}
}

.bottom_button {
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	background-color: #fff;

	.u-button {
		&[disabled] {
			background-color: #fab3c5 !important;
			color: #fff !important;
		}
		&::after {
			border: none;
		}
	}
}
</style>
