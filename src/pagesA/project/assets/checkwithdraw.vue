<template>
    <view class="checkwithdraw_page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="确认订单" />
        <view class="content">
            <view class="receive_tip">您将收到</view>
            <view class="receive_amount">{{info.amount}} <text class="unit">{{info.symbol}}</text></view>
            <view class="receive_usd">≈{{info.fee}}{{info.symbol}}</view>
            <view class="info_list">
                <view class="info_item">
                    <text class="label">转账网络</text>
                    <text class="value">{{info.network}}</text>
                </view>
                <view class="info_item">
                    <text class="label">地址</text>
                    <text class="value address">{{info.address}}</text>
                </view>
                <view class="info_item">
                    <text class="label">提现数量</text>
                    <text class="value">{{info.amount}}</text>
                </view>
                <view class="info_item">
                    <text class="label">网络手续费</text>
                    <text class="value">{{info.fee}}{{info.symbol}}</text>
                </view>
                <view class="info_item">
                    <text class="label">钱包</text>
                    <text class="value">现货钱包</text>
                </view>
            </view>
            <view class="warn_box">
                <u-icon name="https://pro-oss.pinkwallet.com/image/1387457734214508544.png"
                    size="32" />
                <text class="warn_text">请确保您输入了正确的提币地址并且您选择的转账网络与地址相匹配。提币订单创建后不可取消。</text>
            </view>
        </view>
        <view class="footer">
            <u-button class="confirm_btn" @click="submit()">确认</u-button>
        </view>
    </view>
</template>

<script>
    export default {
        name: 'checkwithdraw',
        data() {
            return {
                info: {}
            }
        },
        onLoad() {
            this.info = uni.getStorageSync('withdraw')
        },
        methods:{
            submit(){
                uni.setStorageSync('withdrawInfo',  this.info)
                this.$Router.push({
                    name: 'assetsEmail',
                    params:{
                        targetPage: 'succeedWithdraw',
                    }
                })
            }
        }
    }
</script>

<style lang="scss" scoped>
    .checkwithdraw_page {
        background: #fff;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        font-family: 'Gilroy-Medium';

        .content {
            flex: 1;
            padding: 0 32rpx;
            display: flex;
            flex-direction: column;
            align-items: center;

            .receive_tip {
                margin-top: 48rpx;
                font-size: 26rpx;
                color: #999;
                text-align: center;
            }

            .receive_amount {
                margin-top: 16rpx;
                font-size: 48rpx;
                font-weight: bold;
                color: #121212;
                text-align: center;

                .unit {
                    font-size: 32rpx;
                    font-weight: 600;
                    margin-left: 8rpx;
                }
            }

            .receive_usd {
                margin-top: 8rpx;
                font-size: 26rpx;
                color: #999;
                text-align: center;
            }

            .info_list {
                width: 100%;
                margin-top: 48rpx;
                background: #fff;
                border-radius: 16rpx;

                .info_item {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    padding: 0 0 32rpx 0;

                    &:last-child {
                        padding-bottom: 0;
                    }

                    .label {
                        font-size: 26rpx;
                        color: #999;
                        min-width: 140rpx;
                    }

                    .value {
                        font-size: 26rpx;
                        color: #121212;
                        text-align: right;
                        word-break: break-all;
                    }

                    .address {
                        line-height: 36rpx;
                        word-break: break-all;
                    }
                }
            }

            .warn_box {
                margin-top: 48rpx;
                width: 100%;
                background: #F7F7F7;
                border-radius: 12rpx;
                display: flex;
                align-items: flex-start;
                padding: 24rpx;

                .warn_icon {
                    width: 32rpx;
                    height: 32rpx;
                    margin-right: 16rpx;
                    margin-top: 2rpx;
                }

                .warn_text {
                    font-size: 22rpx;
                    color: #999;
                    line-height: 1.6;
                    margin-left: 20rpx;
                }
            }
        }

        .footer {
            padding: 0 32rpx 48rpx 32rpx;

            .confirm_btn {
                width: 100%;
                height: 88rpx;
                background: #FF82A3;
                border-radius: 44rpx;
                color: #fff;
                font-size: 28rpx;
                font-weight: 600;
            }
        }
    }
</style>