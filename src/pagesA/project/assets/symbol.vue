<template>
  <view class="symbol_page">
    <!-- 顶部导航 -->
    <u-navbar :is-back="true" back-icon-color="#121212" :background="{'background-color':'#FFF'}" :border-bottom="false" title="选择币种" />

    <!-- 搜索框 -->
    <view class="search_wrap">
      <view class="search-box-n">
        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385297482358546432.png" />
        <input v-model="searchKeyword" placeholder="搜索" class="search-input" @input="onSearch" />
      </view>
    </view>

    <!-- 索引列表 -->
    <u-index-list :scrollTop="scrollTop" :index-list="indexList" :sticky="true" :offset-top="100" activeColor="#000000">
      <view v-for="(group, idx) in filteredGroups" :key="group.letter"> 
        <u-index-anchor :index="group.letter" />
        <view class="coin-list">
          <view class="coin-item" v-for="coin in group.list" :key="coin.symbol" @click="onSelect(coin)">
            <image :src="coin.image" class="coin-icon" />
            <text class="coin-name">{{ coin.symbol }}</text> 
            <!-- <image class="arrow" src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1374825043983949824.png" /> -->
          </view>
        </view>
      </view>
    </u-index-list>
    <nodata v-if="filteredGroups.length === 0" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      coins: [],
      searchKeyword: '',
      targetPage: '',
      scrollTop: 0,
      indexList: [
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
        'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
      ]
    }
  },
  computed: {
    filteredGroups() {
      // 先过滤币种
      let filtered = this.searchKeyword
        ? this.coins.filter(coin => coin.symbol.toLowerCase().includes(this.searchKeyword.trim().toLowerCase()))
        : this.coins
      // 分组
      let groups = {}
      filtered.forEach(coin => {
        let letter = coin.symbol.charAt(0).toUpperCase()
        if (!groups[letter]) groups[letter] = []
        groups[letter].push(coin)
      })
      // 组装为数组并按字母顺序
      return this.indexList.map(letter => ({
        letter,
        list: groups[letter] || []
      })).filter(group => group.list.length > 0)
    }
  },
  onLoad(options) {
    this.targetPage = options && options.targetPage ? options.targetPage : '';
    this.getCoin();
  },
  methods: {
    async getCoin() {
      let res = await this.$api.symbolListPaged({
        pageNum: 1,
        pageSize: 100
      })
      if (res.code == 200) {
        this.coins = res.result.data
      }
    },
    onSearch() {},
    onSelect(coin) {
      if (this.targetPage) {
        this.$Router.push({
          name: this.targetPage,
          params: { symbol: coin.symbol, name: coin.name }
        });
      } else {
        this.$u.toast('未指定跳转页面');
      }
    }
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  }
}
</script>

<style lang="scss" scoped>
.symbol_page {
  background: #fff;
  min-height: 100vh;
  font-family: 'PingFang SC';
}
.search_wrap {
  padding: 52rpx 32rpx 30rpx 32rpx;
  .search-box-n {
    display: flex;
    justify-content: center;
    position: relative;
    image {
      position: absolute;
      top: 50%;
      left: 44rpx;
      transform: translateY(-50%);
      width: 52rpx;
      height: 52rpx;
    }
  }
  .search-input {
    text-indent: 76rpx;
    border: none;
    outline: none;
    width: 100%;
    margin: 0 32rpx;
    height: 76rpx;
    background: rgba(217, 217, 217, .2);
    border-radius: 34rpx;
    font-size: 28rpx;
  }
}
.coin-list {
  padding: 0 32rpx;
  display: flex;
  flex-direction: column;
}
.coin-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F7F7F7;
  transition: background 0.2s;
  border-radius: 12rpx;
  &:active {
    background: #f5f5f5;
  }
  .coin-icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 18rpx;
    border-radius: 50%;
    background: #f7f7f7;
  }
  .coin-name {
    flex: 1;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #121212;
  }
  .arrow {
    width: 36rpx;
    height: 36rpx;
  }
}
.u-index-anchor {
  font-size: 26rpx;
  color: #999;
  font-weight: 600;
  background: #fff !important;
  padding-left: 32rpx;
}
</style>
