<template>
  <view class="symbol_page">
    <!-- 顶部导航 -->
    <u-navbar :is-back="true" back-icon-color="#121212" :background="{'background-color':'#FFF'}" :border-bottom="false" title="选择币种" />

    <!-- 搜索框 -->
    <view class="search_wrap">
      <view class="search_box_n">
        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385297482358546432.png" />
        <input
          v-model="searchKeyword"
          placeholder="搜索"
          class="search_input"
          @confirm="onSearch"
        />
      </view>
      <!-- 历史记录 -->
      <view v-if="searchHistory.length" class="history_wrap">
        <text class="history_title">历史记录</text>
        <view class="history_list">
          <view
            class="history_item"
            v-for="(item, idx) in searchHistory"
            :key="idx"
            @click="onHistoryClick(item)"
          >{{ item }}</view>
        </view>
      </view>
    </view>

    <!-- 索引列表 -->
    <u-index-list :scrollTop="scrollTop" :index-list="indexList" :sticky="true" :offset-top="100" activeColor="#000000">
      <view v-for="(group, idx) in filteredGroups" :key="group.letter"> 
        <u-index-anchor :index="group.letter" />
        <view class="coin_list">
          <view class="coin_item" v-for="coin in group.list" :key="coin.symbol" @click="onSelect(coin)">
            <image :src="coin.image" class="coin_icon" />
            <text class="coin_name">{{ coin.symbol }}</text> 
          </view>
        </view>
      </view>
    </u-index-list>
    <nodata v-if="filteredGroups.length === 0" />
  </view>
</template>

<script>
export default {
  data() {
    return {
      coins: [],
      searchKeyword: '',
      searchResult: [],
      searchHistory: [],
      targetPage: '',
      scrollTop: 0,
      indexList: [
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
        'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
      ]
    }
  },
  computed: {
    filteredGroups() {
      let filtered = this.searchResult
      let groups = {}
      filtered.forEach(coin => {
        let letter = coin.symbol.charAt(0).toUpperCase()
        if (!groups[letter]) groups[letter] = []
        groups[letter].push(coin)
      })
      return this.indexList.map(letter => ({
        letter,
        list: groups[letter] || []
      })).filter(group => group.list.length > 0)
    }
  },
  onLoad(options) {
    this.targetPage = options && options.targetPage ? options.targetPage : '';
    this.getCoin();
    this.loadHistory();
  },
  methods: {
    async getCoin() {
      let res = await this.$api.symbolListPaged({
        pageNum: 1,
        pageSize: 100
      })
      if (res.code == 200) {
        this.coins = res.result.data
        this.searchResult = this.coins // 默认显示全部
      }
    },
    onSearch() {
      console.log(this.searchKeyword)
      const keyword = this.searchKeyword.trim()
      if (!keyword) {
        this.searchResult = this.coins
        return
      }
      this.searchResult = this.coins.filter(coin =>
        coin.symbol.toLowerCase().includes(keyword.toLowerCase())
      )
      // 存历史到本地
      this.saveHistory(keyword)
    },
    onHistoryClick(keyword) {
      this.searchKeyword = keyword
      this.onSearch()
    },
    saveHistory(keyword) {
      let history = this.searchHistory.slice()
      const idx = history.indexOf(keyword)
      if (idx !== -1) history.splice(idx, 1)
      history.unshift(keyword)
      if (history.length > 10) history.pop()
      this.searchHistory = history
      uni.setStorageSync('symbol_search_history', history)
    },
    loadHistory() {
      const history = uni.getStorageSync('symbol_search_history')
      if (Array.isArray(history)) {
        this.searchHistory = history
      }
    },
    onSelect(coin) {
      if (this.targetPage) {
        this.$Router.push({
          name: this.targetPage,
          params: { symbol: coin.symbol, name: coin.name, image: coin.image }
        });
      } else {
        this.$u.toast('未指定跳转页面');
      }
    }
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  }
}
</script>

<style lang="scss" scoped>
.symbol_page {
  background: #fff;
  min-height: 100vh;
  font-family: 'PingFang SC';
}
.search_wrap {
  padding: 52rpx 32rpx 30rpx 32rpx;
  .search_box_n {
    display: flex;
    justify-content: center;
    position: relative;
    image {
      position: absolute;
      top: 50%;
      left: 14rpx;
      transform: translateY(-50%);
      width: 52rpx;
      height: 52rpx;
    }
    .search_input {
      text-indent: 76rpx;
      border: none;
      outline: none;
      width: 100%;
      height: 76rpx;
      background: rgba(217, 217, 217, .2);
      border-radius: 34rpx;
      font-size: 28rpx;
    }
  }
  .history_wrap {
    margin-top: 20rpx;
    .history_title {
      font-size: 30rpx;
      font-weight: bold;
      margin-bottom: 40rpx;
      display: block;
    }
    .history_list {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;
      .history_item {
        background: #f5f5f5;
        border-radius: 16rpx;
        padding: 12rpx 28rpx;
        font-size: 22rpx;
        color: #222;
        cursor: pointer;
      }
    }
  }
}
.coin_list {
  padding: 0 32rpx;
  display: flex;
  flex-direction: column;
}
.coin_item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #F7F7F7;
  transition: background 0.2s;
  border-radius: 12rpx;
  &:active {
    background: #f5f5f5;
  }
  .coin_icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 18rpx;
    border-radius: 50%;
    background: #f7f7f7;
  }
  .coin_name {
    flex: 1;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #121212;
  }
}
.u-index-anchor {
  font-size: 26rpx;
  color: #999;
  font-weight: 600;
  background: #fff !important;
  padding-left: 32rpx;
}
</style>
