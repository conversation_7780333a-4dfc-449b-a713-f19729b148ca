<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="title" ></u-navbar>
		<view class="success_content">
			<view class="success_icon">
				<u-icon name="checkmark-circle-fill" color="#08B819" size="110"></u-icon>
			</view>
			<view class="success_text">划转成功</view>
			<view class="success_amount">{{$m(info.amount)}} {{info.symbol}}</view>
		</view>
		<view class="product_tip">您可能想使用下面的产品</view>
		<view class="product_list">
			<view class="product_item" @click="nav_tract()">
				<u-icon name="file-text" size="28"></u-icon>
				<text class="product_name">合约交易</text>
				<u-icon name="arrow-right" size="22" color="#999"></u-icon>
			</view>
			<view class="product_item" @click="nav_to('C2C')">
				<u-icon name="account" size="28"></u-icon>
				<text class="product_name">C2C出售</text>
				<u-icon name="arrow-right" size="22" color="#999"></u-icon>
			</view>
			<view class="product_item" @click="nav_to('swap')">
				<u-icon name="reload" size="28"></u-icon>
				<text class="product_name">闪兑</text>
				<u-icon name="arrow-right" size="22" color="#999"></u-icon>
			</view>
		</view>
		<view class="btn_area">
			<u-button type="primary" class="confirm_btn" shape="circle" @click="onConfirm">好的</u-button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			title: '',
            info:{}
		}
	},
    onLoad(options){
        this.info = options
    },
	methods: {
		back() {
			uni.navigateBack();
		},
		nav_to(name, params) {
				this.$Router.push({
					name,
					params
				})
			},
		nav_tract(){
			this.$Router.pushTab({
				name:'Home',
				params:{
					tabIndex:2
				}
			})
		},
		onConfirm() {
			uni.navigateBack();
		}
	}
}
</script>

<style lang="scss" scoped>
.body {
	background: #fff;
	min-height: 100vh;
	padding-bottom: 120rpx;
	font-family: 'Gilroy-Medium';
}
.success_content {
	display: flex;
	flex-direction: column;
	align-items: center;
    justify-content: center;
    height:600rpx;
}
.success_icon {
	margin-bottom: 24rpx;
}
.success_text {
	font-size: 32rpx;
	color: #000;
	margin-bottom: 12rpx;
}
.success_amount {
	font-size: 40rpx;
	font-weight: bold;
	color: #000;
	margin-bottom: 60rpx;
}
.product_tip {
	text-align: center;
	color: #999;
	font-size: 24rpx;
	margin-bottom: 24rpx;
}
.product_list {
	margin: 0 32rpx 0 32rpx;
}
.product_item {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 24rpx;
	padding: 34rpx 32rpx;
	margin-bottom: 20rpx;
    border:1px solid rgb(0, 0,0, 0.05);
    height:124rpx;

	.product_name {
		flex: 1;
		margin-left: 20rpx;
		font-size: 28rpx;
		color: #222;
        font-weight:600;
	}
}
.btn_area {
	position: fixed;
	bottom: 40rpx;
	left: 0;
	width: 100%;
	display: flex;
	justify-content: center;
}
.confirm_btn {
	width: 90%;
	background: #FF7CA9;
	color: #fff;
	font-size: 32rpx;
	border-radius: 60rpx;
}
</style>
