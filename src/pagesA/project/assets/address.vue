<template>
    <view class="address_page">
        <u-navbar back-icon-color="#121212" bg-color="#fff" :immersive="false" :border-bottom="false" title="添加新地址">
            <view slot="right" class="search_box_right">
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385362296246198272.png" class="search_icon" />
            </view>
        </u-navbar>
        <view class="content">
            <!-- 币种选择 -->
            <view class="form_label">币种</view>
            <view class="select_box" @click="showCoinPopup = true">
                <image :src="selectedCoin.image" class="coin_icon" />
                <text class="coin_symbol">{{ selectedCoin.symbol }}</text>
                <view class="select_arrow">
                    <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1387107155650895872.png" size="40" color="#C0C4CC" />
                </view>
            </view>
            <view class="general_row">
                <u-checkbox v-model="isGeneral"  active-color="#FF82A3" size="32" />
                <text class="general_text">设为通用地址，适用于全部币种</text>
            </view>
            <!-- 币种弹窗 -->
            <u-popup v-model="showCoinPopup" mode="bottom" border-radius="14" height="1200rpx">
                <view class="coin_popup">
                    <view class="popup_title">
                        <text>选择币种</text>
                        <u-icon @click="showCoinPopup = false" name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385342962270560256.png" size="48" />
                    </view>
                    <view class="coin_list">
                        <view class="coin_item" v-for="coin in coins" :key="coin.symbol" @click="selectCoin(coin)">
                            <image :src="coin.image" class="coin_icon" />
                            <text class="coin_symbol">{{ coin.symbol }}</text>
                        </view>
                    </view >
                </view>
            </u-popup>
            <!-- 地址输入 -->
            <view class="form_label">地址</view>
            <view class="input_box">
                <input class="input" v-model="address" placeholder="请输入或粘贴地址" />
                <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1387173311648718848.png" size="40" color="#C0C4CC" @click="scanCode" />
            </view>
            <!-- 主网选择 -->
            <view class="form_label">转账网络</view>
            <view class="select_box" @click="showNetworkPopup = true">
                <text class="select_placeholder">{{ network || '选择主网' }}</text>
                <view class="select_arrow">
                    <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1387107155650895872.png" size="40" color="#C0C4CC" />
                </view>
            </view>
            <u-popup v-model="showNetworkPopup" mode="bottom" border-radius="14" height="900rpx">
                <view class="network_popup">
                    <view class="popup_title">
                        <text>选择主网</text>
                        <u-icon @click="showNetworkPopup = false" name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385342962270560256.png" size="48" />
                    </view>
                    <view class="network_list">
                        <view class="network_item" v-for="item in networkOptions" :key="item.network" @click="selectNetwork(item)">
                            <text class="network_name">{{ item.network }}</text>
                        </view>
                    </view>
                </view>
            </u-popup>
            <!-- 钱包标签 -->
            <view class="form_label">钱包标签(选填)</view>
            <view class="input_box">
                <input class="input" v-model="tag" placeholder="输入钱包标签" />
            </view>
        </view>
        <view class="footer">
            <u-button class="save_btn" @click="onSave">保存</u-button>
        </view>
    </view>
</template>

<script>
let Qrcode = require('@/utils/reqrcode.js')
export default {
    name: 'address',
    data() {
        return {
            coins: [],
            selectedCoin: {
                symbol: 'USDT',
                image: 'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385363807210659840.png'
            },
            showCoinPopup: false,
            isGeneral: false,
            address: '',
            network: '',
            networkOptions: [],
            showNetworkPopup: false,
            tag: '',
        }
    },
    methods: {
        async getCoin() {
            let res = await this.$api.symbolListPaged({ pageNum: 1, pageSize: 100 })
            if (res.code == 200) {
                this.coins = res.result.data
            }
        },
        selectCoin(coin) {
            this.selectedCoin = coin
            this.showCoinPopup = false
            this.network = ''
            this.getNet()
        },
        async getNet() {
            let res = await this.$api.getNetwork({ symbol: this.selectedCoin.symbol })
            if (res.code == 200) {
                this.networkOptions = res.result
            }
        },
        selectNetwork(item) {
            this.network = item.network
            this.showNetworkPopup = false
        },
        scanCode() {
            // #ifdef APP-PLUS
            this.scanQRCode()
            // #endif
            // #ifdef H5
            this.scanCodeH5()
            // #endif
        },
        scanQRCode() {
            let that = this
            uni.scanCode({
                scanType: ['qrCode'],
                success: function (res) {
                    that.address = res.result
                }
            })
        },
        scanCodeH5() {
            uni.chooseImage({
                count: 1,
                success: imgRes => {
                    Qrcode.qrcode.decode(this.getObjectURL(imgRes.tempFiles[0]))
                    Qrcode.qrcode.callback = (codeRes) => {
                        if (codeRes.indexOf('error') >= 0) {
                            this.address = 'undefined' + codeRes
                        } else {
                            let r = this.decodeStr(codeRes)
                            this.address = r
                        }
                    }
                }
            })
        },
        getObjectURL(file) {
            var url = null
            if (window.createObjectURL !== undefined) {
                url = window.createObjectURL(file)
            } else if (window.URL !== undefined) {
                url = window.URL.createObjectURL(file)
            } else if (window.webkitURL !== undefined) {
                url = window.webkitURL.createObjectURL(file)
            }
            return url
        },
        decodeStr(str) {
            var out = '', i = 0, len = str.length, c, char2, char3
            while (i < len) {
                c = str.charCodeAt(i++)
                switch (c >> 4) {
                    case 0: case 1: case 2: case 3: case 4: case 5: case 6: case 7:
                        out += str.charAt(i - 1); break;
                    case 12: case 13:
                        char2 = str.charCodeAt(i++);
                        out += String.fromCharCode(((c & 0x1F) << 6) | (char2 & 0x3F)); break;
                    case 14:
                        char2 = str.charCodeAt(i++);
                        char3 = str.charCodeAt(i++);
                        out += String.fromCharCode(((c & 0x0F) << 12) | ((char2 & 0x3F) << 6) | ((char3 & 0x3F) << 0)); break;
                }
            }
            return out
        },
        async onSave() {
            if (!this.address) return this.$u.toast('请输入地址')
            if (!this.network) return this.$u.toast('请选择主网')
            let withdrawCryptoDTO = {
                symbol: this.selectedCoin.symbol,
                network: this.network,
                address: this.address,
                tag: this.tag,
                general: this.isGeneral
            }
            let res = await this.$api.addOrUpdateUserWithdrawAccount({
                symbol: this.selectedCoin.symbol,
                type: 2,
                infoExtra: JSON.stringify(withdrawCryptoDTO)
            })
            if (res.code == 200) {
                uni.showToast({ title: res.msg, icon: 'none', duration: 2000 })
                setTimeout(() => {
                    this.$Router.back()
                }, 1000)
            } else {
                uni.showToast({ title: res.msg, icon: 'none', duration: 2000 })
            }
        }
    },
    mounted() {
        this.getCoin()
        this.getNet()
    }
}
</script>

<style lang="scss" scoped>
.address_page {
    background: #fff;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    .search_box_right {
        margin-right: 38rpx;
        .search_icon {
            width: 28rpx;
            height: 30rpx;
        }
    }
    .content {
        flex: 1;
        padding: 40rpx 32rpx 0 32rpx;
        font-family: PingFang SC;
        .form_label {
            font-size: 28rpx;
            color: #121212;
            font-weight: 600;
            margin: 32rpx 0 12rpx 0;
        }
        .select_box {
            background: #F7F7F7;
            border-radius: 12rpx;
            height: 80rpx;
            display: flex;
            align-items: center;
            padding: 0 24rpx;
            margin-bottom: 12rpx;
            cursor: pointer;
            position: relative;
            .coin_icon {
                width: 40rpx;
                height: 40rpx;
                border-radius: 50%;
                margin-right: 12rpx;
            }
            .coin_symbol {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
            }
            .select_placeholder {
                font-size: 28rpx;
                color: #bbb;
            }
            .select_arrow {
                position: absolute;
                right: 0;
                top: 0;
                height: 100%;
                display: flex;
                align-items: center;
                padding-right: 24rpx;
            }
        }
        .general_row {
            display: flex;
            align-items: center;
            margin-bottom: 12rpx;
            .general_text {
                font-size: 24rpx;
                color: #bbb;
                margin-left: -10rpx;
            }
        }
        .input_box {
            background: #F7F7F7;
            border-radius: 12rpx;
            height: 80rpx;
            display: flex;
            align-items: center;
            padding: 0 24rpx;
            margin-bottom: 30rpx;
            .input {
                flex: 1;
                font-size: 28rpx;
                color: #121212;
                background: transparent;
                border: none;
                outline: none;
            }
        }
    }
    .footer {
        padding: 0 32rpx 48rpx 32rpx;
        .save_btn {
            width: 100%;
            height: 88rpx;
            background: #FF82A3;
            border-radius: 44rpx;
            color: #fff;
            font-size: 28rpx;
            font-weight: 600;
        }
    }
    .coin_popup, .network_popup {
        padding: 0 0 40rpx 0;
        background: #fff;
        min-height: 700rpx;
        display: flex;
        flex-direction: column;
        height: 100%;
        .popup_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 32rpx 32rpx 0 32rpx;
            font-size: 28rpx;
            font-weight: 600;
            color: #000;
        }
        .coin_list, .network_list {
            flex: 1;
            padding: 32rpx;
            overflow-y: auto;
        }
        .coin_item, .network_item {
            background: #F7F7F7;
            border-radius: 16rpx;
            margin-bottom: 24rpx;
            padding: 24rpx;
            display: flex;
            align-items: center;
            .coin_icon {
                width: 48rpx;
                height: 48rpx;
                border-radius: 50%;
                margin-right: 18rpx;
            }
            .coin_symbol, .network_name {
                font-size: 28rpx;
                color: #000;
                font-weight: 500;
            }
        }
    }
}
</style>