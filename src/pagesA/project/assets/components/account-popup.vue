<template>
  <view class="popup-overlay" v-if="show" @click="close">
    <view class="popup-content" @click.stop>
      <!-- 头部 -->
      <view class="popup-header">
        <text class="popup-title">选择账号</text>
        <view class="close-btn" @click="close">
          <image src="https://pro-oss.pinkwallet.com/image/1377388318231715840.png" class="close-icon" />
        </view>
      </view>

      <!-- 账户列表 -->
      <view class="account-list">
        <view class="account-option" :class="{ selected: account.id === currentAccountId }" v-for="account in accounts"
          :key="account.id" @click="selectAccount(account)">
          <text class="account-name">{{ account.name }}</text>
          <view class="check-icon" v-if="account.id === currentAccountId">
            <image src="https://pro-oss.pinkwallet.com/image/1385374494213365760.png" class="check-img" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AccountPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    accounts: {
      type: Array,
      default: () => []
    },
    currentAccountId: {
      type: [Number, String],
      default: null
    }
  },
  mounted() {
    console.log(this.currentAccountId);

    // this.close()
  },
  methods: {
    close() {
      this.$emit('close')
    },
    selectAccount(account) {
      this.$emit('select', account)
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;

  .popup-content {
    width: 100%;
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;
    padding: 50rpx 32rpx 100rpx 32rpx;
    animation: slideUp 0.3s ease-out;

    .popup-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32rpx;
      padding-bottom: 24rpx;

      .popup-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .close-icon {
          width: 48rpx;
          height: 48rpx;
        }
      }
    }

    .account-list {
      .account-option {
        height: 106rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 32rpx;
        margin-bottom: 8rpx;
        border-radius: 24rpx;
        // border: 2rpx solid transparent;
        transition: all 0.3s ease;
        // background: #DFDFDF;
        border: 1.5px solid rgba(0, 0, 0, .1);
        background: rgba(223, 223, 223, .1);

        &:last-child {
          margin-bottom: 0;
        }

        .account-name {
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 28rpx;
          line-height: 40rpx;

          color: #000;
        }

        .check-icon {
          width: 32rpx;
          height: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .check-img {
            width: 41rpx;
            height: 41rpx;
          }
        }

        // 选中状态
        &.selected {
          border-color: #FF82A3;
          // background: rgba(255, 130, 163, 0.05);
          background: #FCFCFC;
        }
      }
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}
</style>
