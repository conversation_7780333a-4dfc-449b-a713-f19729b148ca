<template>
    <view class="page">
        <!-- 顶部导航 -->
        <view class="nav">
            <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1381638686293712896.png"
                size="88" @click="back"></u-icon>
            <view class="nav_title">
                <view class="stock_name">阿里巴巴</view>
                <view class="stock_code">300321</view>
            </view>
            <!-- <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1381638642836529152.png" size="88"></u-icon> -->
            <!-- info.selfSelect info.selfSelect-->
            <view class="star">
                <image @click="starClick()" v-if="!selfSelect"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384985216803233792.png" />
                <image @click="starClick()" v-else
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384985542650322944.png" />
            </view>
        </view>

        <!-- 股票主信息 -->
        <view class="main_info">
            <view class="price">250.12</view>
            <view class="change">
                <view class="change_amount up">+636.74</view>
                <view class="change_percent up">+0.71%</view>
            </view>
        </view>

        <!-- 详细数据 -->
        <view class="detail_info">
            <view class="detail_row">
                <view class="label_box">
                    <view class="label left ">高</view>
                    <view class="value left ">221.67</view>
                </view>
                <view class="label_box">
                    <view class="label right">昨收</view>
                    <view class="value right">221.67</view>
                </view>
            </view>
            <view class="detail_row">
                <view class="label_box">
                    <view class="label left ">低</view>
                    <view class="value left ">221.67</view>
                </view>
                <view class="label_box">
                    <view class="label right">换</view>
                    <view class="value right">8.75B</view>
                </view>
            </view>
            <view class="detail_row">
                <view class="label_box">
                    <view class="label left ">开</view>
                    <view class="value left ">3.273T</view>
                </view>
                <view class="label_box">
                    <view class="label right">额</view>
                    <view class="value right">221.67</view>
                </view>
            </view>
        </view>

        <!-- 盘后信息 -->
        <view class="after_market">
            <view>
                盘后:115.820 <text class=" ">+2231.07</text><text class="after_up">+3.17%</text>
            </view>
            <view>20:00 美东时间</view>
        </view>

        <!-- 时间选择器 -->
        <view class="tab_select">
            <view class="left_view">

                <view class='tab_item' @click="tabClickPan()">{{ getCurrentPanLabel() }}
                    <image style="margin-left: 10rpx;"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1381650876635635712.png">
                    </image>
                </view>

                <view v-for="(item, index) in tabList" :key="item.id || index"
                    :class="['tab_item', { tab_active: currentTabType == item.value }]" @click="tabClick(item)">{{
                        item.name }}
                </view>
            </view>
            <!-- <u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1381650747664982016.png"
                size="74"></u-icon> -->
        </view>

        <view class="tab_select" style="margin-bottom: 0;" v-if="showPanList">
            <view class="left_view">
                <view v-for="(item, index) in panList" :key="index"
                    :class="['tab_item', { tab_active: currentPanSession == item.value && !currentTabType }]"
                    @click="SubtabClick(item)">{{
                        item.label }}
                </view>
            </view>
        </view>

        <view class="databox">
            <!-- 左侧盘口数据 -->
            <view class="depth_panel">
                <!-- 盘前最新 -->
                <view class="market_header">
                    <text class="market_title">盘前最新</text>
                    <text class="market_price">115.410</text>
                    <!-- FF82A3 08B819 -->
                    <text class="market_change">+636.74 +0.71%</text>
                </view>

                <!-- 价格数据 -->
                <view class="price_data">
                    <view class="price_row">
                        <text class="label">最高</text>
                        <text class="value">250.54</text>
                        <!-- <text class="percent">6.47%</text> -->
                    </view>
                    <view class="price_row">
                        <text class="label">最低</text>
                        <text class="value">250.54</text>
                    </view>
                    <view class="price_row">
                        <text class="label">成交量</text>
                        <text class="value">62.84W</text>
                    </view>
                    <view class="price_row">
                        <text class="label">成交额</text>
                        <text class="value">8062.84W</text>
                    </view>
                </view>

                <!-- 盘口数据 -->
                <view class="depth_data">
                    <view class="depth_row" v-for="(item, index) in depthList" :key="index">
                        <view class="left">
                            <!-- 1750766400127 -->
                            <text class="time">{{ formatTimeToHM(item.time) }}</text>
                            <text class="price" :class="item.type">{{ item.price }}</text>
                        </view>

                        <view class="volume">{{ item.volume }}</view>
                    </view>
                </view>
            </view>

            <!-- 右侧图表区域 -->
            <view class="chart_panel">
                <!-- 图表头部信息 -->
                <view class="chart_header">
                    <text class="chart_price">250.30</text>
                    <text class="chart_percent">0.00%</text>
                </view>

                <!-- K线图区域 -->
                <view class="kline_chart">
                    <!-- 这里放置K线图组件 -->
                    <view class="chart_placeholder">
                        <!-- 临时占位，实际应该是图表组件 -->
                        <text>K线图表区域</text>
                    </view>
                </view>

                <!-- 成交量图表 -->
                <view class="volume_chart">
                    <!-- 成交量柱状图 -->
                    <view class="volume_bars">
                        <view class="volume_bar" v-for="n in 50" :key="n"
                            :style="{ height: Math.random() * 100 + 'rpx', backgroundColor: Math.random() > 0.5 ? '#4ecb73' : '#f56c6c' }">
                        </view>
                    </view>
                    <view class="volume_info">
                        <text class="volume_label">量: 3727</text>
                        <text class="volume_value">240000</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
// import { startSocket, onSocketOpen } from "./websockets.js"
export default {
    components: {},
    data() {
        return {
            selfSelect: false,
            showPanList: false, // 控制盘前盘中盘后选择器的显示
            currentPanSession: 'PreMarket', // 当前选中的交易时段
            currentTabType: 'day', // 当前选中的K线类型
            panList: [
                {
                    value: "PreMarket",
                    label: "盘前",
                },
                {
                    value: "Regular",
                    label: "盘中",
                },
                {
                    value: "AfterHours",
                    label: "盘后",
                }
            ],
            tabList: [
                // {
                //     name: '盘前',
                //     active: false
                // },
                // {
                //     name: '盘后',
                //     active: false
                // },
                {
                    name: '时K',
                    value: 'hour',
                    active: false
                },
                {
                    name: '日K',
                    value: 'day',
                    active: true // 默认选中日K
                },
                {
                    name: '周K',
                    value: 'week',
                    active: false
                },
                {
                    name: '月K',
                    value: 'month',
                    active: false
                },
                {
                    name: '年K',
                    value: 'year',
                    active: false
                }
            ],
            // 盘口数据
            depthList: [
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' },
                // { time: '15:59', price: '250.54', volume: '1,192', type: 'up' }
            ]

        }
    },
    onLoad() {
        // startSocket()
        this.initDefaultTab();
        this.getTradeTicker()
        this.getrealtimeQuote()
    },
    methods: {
        // 格式化时间戳为时分格式 (HH:MM)
        formatTimeToHM(timestamp) {
            if (!timestamp) {
                return '--:--';
            }
            // 确保时间戳是毫秒格式
            let time = timestamp;
            if (timestamp.toString().length === 10) {
                time = timestamp * 1000;
            }
            const date = new Date(time);
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${hours}:${minutes}`;
        },
        // 获取顶部数据
        async getrealtimeQuote() {
            let res = await this.$api.realtimeQuote({
                symbol: this.$store.state.stockname
            })
            if (res.code == 200) {
                console.log(res, 'res');
            }
        },
        // 初始化默认选中的tab
        initDefaultTab() {
            const defaultTab = this.tabList.find(item => item.active);
            if (defaultTab) {
                this.currentTabType = defaultTab.value;
            }
        },
        // 获取盘口数据
        async getTradeTicker() {
            let res = await this.$api.tradeTick({
                tradeSession: this.currentPanSession,
                symbol: this.$store.state.stockname
                // symbol: 'TSLA'
            })
            if (res.code == 200) {
                if (res.result[0].items) {
                    this.depthList = res.result[0].items.slice(0, 100);
                }
            }
        },
        // tradeTick
        async starClick(e) {
            // if (e) {
            //     let res = await this.$api.deleteContractSelf({
            //         id: uni.getStorageSync("currentContract").id
            //     })
            //     if (res.code == 200) {
            //         this.getContractSymbols()
            //         uni.showToast({
            //             title: "取消自选成功",
            //             icon: 'none'
            //         })
            //     }
            // } else {
            //     let res = await this.$api.addContractSelf({
            //         id: uni.getStorageSync("currentContract").id
            //     })
            //     if (res.code == 200) {
            //         this.getContractSymbols()
            //         uni.showToast({
            //             title: "添加自选成功",
            //             icon: 'none'
            //         })
            //     }
            // }
        },
        tabClick(item) {
            // 重置所有tab的active状态
            this.tabList.forEach(tabItem => {
                tabItem.active = false;
            });
            // 设置当前选中项为active
            item.active = true;
            // 赋值动作：更新当前选中的K线类型
            this.currentTabType = item.value;

            // 互斥逻辑：选中周期时，重置盘前盘中盘后为默认状态
            this.resetPanListToDefault();
            // this.currentPanSession = 
            // 这里可以添加切换K线类型后的业务逻辑
            console.log('切换到K线类型:', item.name);
            // 例如：重新获取对应类型的K线数据
            // this.getKlineData(item.name);
        },
        // 获取当前选中的交易时段标签
        getCurrentPanLabel() {
            const currentPan = this.panList.find(item => item.value == this.currentPanSession);
            return currentPan ? currentPan.label : '--';
        },
        // 重置盘前盘中盘后为默认状态（盘中）
        resetPanListToDefault() {
            this.panList.forEach(panItem => {
                panItem.active = false;
            });
            // 设置盘中为默认选中
            const defaultPan = this.panList.find(item => item.value === 'Regular');
            if (defaultPan) {
                defaultPan.active = true;
                // this.currentPanSession = 'Regular';
            }
            console.log('重置交易时段为默认状态：盘中');
        },
        // 重置周期选择为默认状态（日K）
        resetTabListToDefault() {
            // this.tabList.forEach(tabItem => {
            //     tabItem.active = false;
            // });
            // 设置日K为默认选中
            // const defaultTab = this.tabList.find(item => item.name === '日K');
            // if (defaultTab) {
            //     defaultTab.active = true;
            //     this.currentTabType = '日K';
            // }
            console.log('重置K线类型为默认状态：日K');
        },
        // 点击盘前按钮，显示/隐藏盘前盘中盘后选择器
        tabClickPan() {
            this.showPanList = !this.showPanList;
        },
        // 选择盘前盘中盘后
        SubtabClick(item) {
            // 重置所有选项的active状态
            this.panList.forEach(panItem => {
                panItem.active = false;
            });
            // 设置当前选中项为active
            item.active = true;
            // 更新当前交易时段
            this.currentPanSession = item.value;
            // 隐藏选择器
            this.showPanList = false;

            // 互斥逻辑：选中盘前盘中盘后时，重置周期选择为默认状态
            // this.resetTabListToDefault();
            this.currentTabType = ""

            // 这里可以添加切换交易时段后的业务逻辑
            console.log('切换到交易时段:', item.label, item.value);
            // 例如：重新获取对应时段的数据
            // this.getTradeTicker();
        },
        back() {
            this.$Router.back();
        }
    }
} 
</script>

<style scoped lang="scss">
.page {
    background: #fff;
    min-height: 100vh;
    padding: 0 20rpx;
    font-family: Gilroy;
}

.nav {
    display: flex;
    align-items: center;
    padding: 30rpx 0 20rpx 0;

    .nav_title {
        flex: 1;
        margin: 0 20rpx;

        .stock_name {
            font-size: 32rpx;
            font-weight: bold;
        }

        .stock_code {
            font-size: 22rpx;
            color: #888;
        }
    }

    .star {
        image {
            width: 48rpx;
            height: 48rpx;
        }
    }
}

.main_info {
    display: flex;
    margin-top: 20rpx;

    .price {
        font-size: 48rpx;
        font-weight: bold;
        font-family: PingFang SC;
    }

    .change {
        margin-left: 20rpx;
        display: flex;
        align-items: center;
        font-family: Gilroy;

        .change_amount,
        .change_percent {
            font-size: 26rpx;
            border-radius: 10rpx;
            height: 44rpx;
            line-height: 44rpx;
            text-align: center;
            padding: 0 10rpx;
            margin-left: 10rpx;
            font-weight: 500;
        }

        .up {
            background: #eaffea;
            color: #30C147;
        }

        .down {
            background: #ffeaea;
            color: #f56c6c;
        }
    }
}

.detail_info {
    margin: 20rpx 0 0;
    border-bottom: 1rpx solid #E1E1E1;
    padding-bottom: 30rpx;

    .detail_row {
        display: flex;
        justify-content: space-between;
        font-size: 22rpx;
        color: #888;
        margin-bottom: 6rpx;

        .label_box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 284rpx;
        }

        .label,
        .value {
            width: 25%;
            text-align: right;
        }

        .left {
            font-weight: 500;
        }

        .right {
            color: #888;
        }

        .label {
            color: rgba(0, 0, 0, 0.8);
            text-align: left;
        }

        .up {
            color: #4ecb73;
        }
    }
}

.after_market {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    margin-bottom: 20rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-bottom: 0.5rpx solid #E1E1E1;
    color: #000;
    font-family: Gilroy;

    .after_up {
        color: #4ecb73;
        margin-left: 20rpx;
    }
}

.tab_select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
    width: 100%;
    font-family: Gilroy;

    .left_view {
        display: flex;
        align-items: center;
        gap: 24rpx;

        .tab_item {
            white-space: nowrap;
            background: #f6f6f6;
            border-radius: 34rpx;
            padding: 11rpx 15rpx;
            font-size: 22rpx;
            color: #222;
            min-width: 70rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            image {
                width: 10rpx;
                height: 8rpx;
            }
        }

        .tab_active {
            background: #FF82A3;
            color: #fff;
        }
    }

}

.granularity {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20rpx;

    .gran_item {
        background: #f6f6f6;
        border-radius: 20rpx;
        padding: 8rpx 24rpx;
        margin-right: 12rpx;
        margin-bottom: 12rpx;
        font-size: 22rpx;
        color: #222;
    }

    .gran_active {
        background: #ffdbe1;
        color: #ff4d6a;
    }
}

.databox {
    display: flex;
    gap: 12rpx;
    margin-top: 20rpx;
    // height: 800rpx;

    .depth_panel {
        width: 184rpx;
        flex-shrink: 0;
        overflow-y: auto;

        .market_header {
            margin-bottom: 20rpx;

            .market_title {
                font-family: Gilroy;
                font-weight: 500;
                font-size: 20rpx;
                line-height: 34rpx;
                letter-spacing: 0%;
                color: rgba(0, 0, 0, .5);
                margin-bottom: 8rpx;
            }

            .market_price {
                display: block;
                font-family: Gilroy;
                font-weight: 800;
                font-size: 28rpx;
                line-height: 100%;
                color: #FF82A3;
                margin-bottom: 4rpx;
            }

            .market_change {
                display: block;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16rpx;
                line-height: 22rpx;
                color: #FF82A3;

            }
        }

        .price_data {
            .price_row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                // margin-bottom: 12rpx;
                font-size: 22rpx;
                margin-bottom: 17rpx;

                // &:last-child {
                //     margin-bottom: 0;
                // }
                .label {
                    font-family: Gilroy;
                    font-weight: 500;
                    font-size: 18rpx;
                    line-height: 32rpx;
                    color: #868686;
                    width: 80rpx;
                }

                .value {
                    font-family: Gilroy;
                    font-weight: 500;
                    font-size: 18rpx;
                    line-height: 32rpx;
                    color: #000;
                    flex: 1;
                    text-align: right;
                }

                .percent {
                    color: #4ecb73;
                    margin-left: 10rpx;
                }
            }
        }

        .depth_data {
            .depth_row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8rpx;
                font-size: 20rpx;

                .time {
                    font-family: Gilroy;
                    font-weight: 500;
                    font-size: 18rpx;
                    line-height: 32rpx;
                    color: #000;
                }

                .price {
                    color: #333;
                    margin-left: 8rpx;
                    flex: 1;
                    text-align: center;
                    font-family: Gilroy;
                    font-weight: 500;
                    font-size: 18rpx;
                    line-height: 34rpx;



                    &.up {
                        color: #4ecb73;
                    }

                    &.down {
                        color: #f56c6c;
                    }
                }

                .volume {
                    font-family: Gilroy;
                    font-weight: 500;
                    font-size: 18rpx;
                    line-height: 32rpx;
                    color: #000;
                }
            }
        }
    }

    .chart_panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        // background: #fff;
        // border-radius: 12rpx;
        // padding: 20rpx;

        .chart_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;

            .chart_price {
                font-size: 28rpx;
                font-weight: bold;
                color: #333;
            }

            .chart_percent {
                font-size: 24rpx;
                color: #666;
            }
        }

        .kline_chart {
            flex: 1;
            background: #fafafa;
            border-radius: 8rpx;
            margin-bottom: 20rpx;
            position: relative;

            .chart_placeholder {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: #999;
                font-size: 24rpx;
            }
        }

        .volume_chart {
            height: 200rpx;

            .volume_bars {
                display: flex;
                align-items: flex-end;
                height: 160rpx;
                gap: 2rpx;
                margin-bottom: 10rpx;

                .volume_bar {
                    flex: 1;
                    min-height: 4rpx;
                    border-radius: 1rpx;
                }
            }

            .volume_info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 20rpx;

                .volume_label {
                    color: #4ecb73;
                }

                .volume_value {
                    color: #333;
                }
            }
        }
    }
}
</style>