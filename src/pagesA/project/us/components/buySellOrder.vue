<template>
  <view class="buySellOrder">
    <!-- 盘口 -->
    <depths ref="depths" />
    <!-- 下单面板 -->
    <view class="orderPanel">
      <view class="orderPanel_tabs">
        <view :class="['orderPanel_tab_slider', { 'activeSell': activeTab == 'sell' }]"
          :style="{ 'transform': activeTab === 'buy' ? 'translateX(0%)' : 'translateX(100%)' }"></view>
        <view :class="['orderPanel_tab', activeTab === 'buy' ? 'orderPanel_tab_active' : '']"
          @click="checkActive('buy')">Buy</view>
        <view :class="['orderPanel_tab', activeTab === 'sell' ? 'orderPanel_tab_active' : '']"
          @click="checkActive('sell')">Sell</view>
      </view>
      <!-- 输入框价格 -->
      <view class="orderPanel_input_price">
        <numberBox :value.sync="inputPrice" :limit="1" :placeholder="activeTab == 'buy' ? '买入价格' : '卖出价格'"></numberBox>
        <!-- activeTab === 'buy' ?  -->
        <!--  -->
        <view class="orderPanel_input_msg">
          <!-- <text>下限 {{ l }} </text> -->
          <!-- <text>可买 {{ stockinfomation.limitHoldMaxNum - stockinfomation.currentHoldCount }} 股</text> -->
          <text>可买 {{ availableGU }} 股</text>
        </view>
      </view>
      <!-- 输入框数量 -->
      <view class="orderPanel_input_qty" v-if="stockLimit.minTick">
        <numberBox :value.sync="inputQty" :limit="stockLimit.minTick" :spreadScale="stockLimit.spreadScale"
          :placeholder="activeTab == 'buy' ? '买入数量' : '卖出数量'"></numberBox>
        <view class="orderPanel_input_msg">
          <text>下限 {{ activeTab === 'buy' ? stockinfomation.limitBuyPlaceOrderMinNum :
            stockinfomation.limitSellPlaceOrderMinNum }}</text>
          <text>上限 {{ activeTab === 'buy' ? stockinfomation.limitBuyPlaceOrderMaxNum :
            stockinfomation.limitSellPlaceOrderMaxNum }}</text>
        </view>

      </view>

      <!-- <numberBox :value.sync="inputPrice" placeholder="买入价格"></numberBox>
      <numberBox :value.sync="inputQty" placeholder="买入数量"></numberBox> -->
      <view class="orderPanel_slider">
        <u-slider max="100" @end="endMove" v-model="sliderValue" inactive-color="rgba(231, 231, 231, .6)"
          :levelList="levelList"
          :active-color="activeTab == 'buy' ? 'rgba(48, 193, 71, .6)' : 'rgba(255, 130, 163, .6)'" step="25"
          :tradeType="activeTab" :use-slot="true">
        </u-slider>
        <view class="orderPanel_slider_labels">
          <text v-for="p in [0, 25, 50, 75, 100]" :key="p">{{ p }}%</text>
        </view>
      </view>
      <view class="orderPanel_btn">
        <u-button type="primary" @click="configOrder(activeTab == 'buy' ? 0 : 1)" hover-class="none"
          :class="{ 'activeBuy': activeTab == 'buy' }" shape="circle">{{ activeTab
            == 'buy' ? 'Buy' : 'Sell' }}</u-button>
      </view>
      <view class="orderPanel_btn_msg" @click="startProgressBarTimer">
        <text>预估: {{ calcAmount }} </text>
      </view>
    </view>

    <u-popup v-model="showConfirmPopup" mode="bottom" border-radius="24" :mask-close-able="true"
      @close="closeConfirmPopup" class="order_confirm">
      <view class="order_confirm_content">
        <view class="order_confirm_header">
          <text class="order_confirm_title">委托{{ butStatus == 0 ? '买入' : '卖出' }}确认</text>
          <u-icon name="close" size="24" color="#929292" class="order_confirm_close" @click="closeConfirmPopup" />
        </view>
        <view class="order_confirm_row">
          <text class="order_confirm_label">名称</text>
          <text class="order_confirm_value"> {{ $store.state.stockname }}</text>
        </view>
        <view class="order_confirm_row">
          <text class="order_confirm_label">代码</text>
          <text class="order_confirm_value">{{ $store.state.stockcode }}</text>
        </view>
        <view class="order_confirm_row">
          <text class="order_confirm_label">价格</text>
          <text class="order_confirm_value">{{ inputPrice }}</text>
        </view>
        <view class="order_confirm_row">
          <text class="order_confirm_label">金额</text>
          <text class="order_confirm_value">{{ inputQty * inputPrice }}</text>
        </view>
        <view class="order_confirm_row order_confirm_row_confirm">
          <text>是否确认以上委托</text>
        </view>
        <view class="order_confirm_btns">
          <view class="btn_cancel" @click="closeConfirmPopup">取消订单</view>
          <view class="btn_confirm" @click="confirmBuy">确认{{ butStatus == 0 ? '买入' : '卖出' }}</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import depths from "./depth"
import numberBox from "./numberBox"
export default {
  components: {
    depths,
    numberBox
  },
  props: ['current'],
  data() {
    return {
      stockinfomation: {},
      levelList: ["0%", "25%", "50%", "75%", "100%"],
      sellList: [
        { price: '62,503.1', qty: '0.003', progressBar: 89 },
        { price: '62,503.1', qty: '0.003', progressBar: 60 },
        { price: '62,503.1', qty: '0.003', progressBar: 40 },
        { price: '62,503.1', qty: '0.003', progressBar: 20 },
        { price: '62,503.1', qty: '0.003', progressBar: 10 },
      ],
      buyList: [
        { price: '62,503.1', qty: '0.003', progressBar: 89 },
        { price: '62,503.1', qty: '0.003', progressBar: 60 },
        { price: '62,503.1', qty: '0.003', progressBar: 40 },
        { price: '62,503.1', qty: '0.003', progressBar: 20 },
        { price: '62,503.1', qty: '0.003', progressBar: 10 },
      ],
      currentPrice: '62,500.00',
      currentQty: '',
      activeTab: 'buy',
      orderType: 'Limit Order',
      // inputPrice: '61,000.00',
      inputQty: '20000',
      sliderValue: 0,
      available: '',
      validity: 'gtc',
      footerInput: '0.1',
      tradeType: 'buy',
      currentfff: 0,
      validityList: [
        { name: 'Day', value: 'day' },
        { name: 'Good Till Cancelled', value: 'gtc' }

      ],
      inputPrice: "",
      inputQty: "",
      showConfirmPopup: false,
      confirmOrderInfo: {
        name: '--',
        code: '300033',
        price: 244.92,
        amount: 2492.92
      },
      butStatus: 0,
      stockLimit: {
        lotSize: "",
        minTick: "",
        spreadScale: ""
      }
    }
  },
  computed: {
    availableGU() {
      // return Number(this.available / this.$store.state.uslatestPrice).toFixed(0)
      return (this.stockinfomation.limitHoldMaxNum - this.stockinfomation.currentHoldCount).toFixed(0)
    },
    calcAmount() {
      // 这里只是示例，实际应根据 inputPrice 和 inputQty 计算
      let allprice = this.inputPrice * this.inputQty
      let isGeBl = this.stockinfomation.isGeBl
      let platformFee
      if (isGeBl == 0) {
        platformFee = this.stockinfomation.fixServiceFee
      } else {
        platformFee = this.stockinfomation.proportionFee * allprice
      }

      // 🔵 2. 佣金计算
      const feeConf = this.stockinfomation.tigerConfigFee
      const t1 = Number(feeConf.t1) || 0 // 每股收费
      const t2 = Number(feeConf.t2) || 0 // 最低佣金
      const t3 = Number(feeConf.t3) || 0 // 最多交易值百分比（0.005 = 0.5%）

      const feePerShare = this.inputQty * t1           // 每股计算
      const feeByAmount = allprice * t3           // 按成交额百分比计算
      const tradeFee = Math.max(t2, Math.min(feePerShare, feeByAmount)) // 实际佣金

      return allprice + platformFee + tradeFee
    }
  },
  mounted() {
    this.getIndexData()
    this.startProgressBarTimer()
    this.getbalance()
    this.getstockInfo()
  },
  methods: {
    // 获取股票详情
    async getstockInfo() {
      let res = await this.$api.stockInfo({
        symbol: this.$store.state.stockcode
      })
      if (res.code == 200) {
        console.log(res, 'res');
        this.stockinfomation = res.result
      }
    },
    async getbalance() {
      let res = await this.$api.symbolAvailableBalance({
        account: this.current == 0 ? 'STOCK_US' : 'STOCK_HK',
        symbol: "USDT"
      })
      if (res.code == 200) {
        this.available = res.result.balance.toFixed(4)
      }
    },
    getdepth() {
      console.log('请求了');
      setTimeout(() => {
        this.$refs.depths.getusdepthItems(this.current)
      }, 100);
    },
    async getIndexData() {
      let res = await this.$api.quoteStockTrade({
        symbol: this.$store.state.stockcode
      })
      if (res.code == 200) {
        this.stockLimit = res.result[0]
      } else {
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 2000
        });
      }
    },
    async markstockOrderCreate() {
      // 校验限价输入框
      if (!this.inputPrice || this.inputPrice <= 0) {
        uni.showToast({
          title: '请输入有效的限价',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 校验数量输入框
      if (!this.inputQty || this.inputQty <= 0) {
        uni.showToast({
          title: '请输入有效的数量',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      let res = await this.$api.stockOrderCreate({
        market: this.current == 0 ? 'US' : 'HK',
        symbol: this.$store.state.stockcode,
        timeInForce: "DAY",
        limitPrice: this.inputPrice,
        quantity: this.inputQty,
        actionType: this.activeTab.toUpperCase(),
      })
      if (res.code == 200) {
        uni.showToast({
          title: '下单成功',
          icon: 'none',
          duration: 2000
        });
        this.inputPrice = ""
        this.inputQty = ""
        this.showConfirmPopup = false
      } else {
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 2000
        });
      }
    },
    onSliderChange(val) {
      this.sliderValue = val
    },
    checkActive(tab) {
      this.activeTab = tab
      console.log(this.activeTab)
    },
    // 随机更新 sellList 的 progressBar 值
    updateProgressBar() {
      this.sellList = this.sellList.map(item => {
        return {
          ...item,
          progressBar: Math.floor(Math.random() * 100) // 生成0-100的随机数
        }
      })
      this.buyList = this.buyList.map(item => {
        return {
          ...item,
          progressBar: Math.floor(Math.random() * 100) // 生成0-100的随机数
        }
      })
    },

    // 启动定时器
    startProgressBarTimer() {
      // 每3秒更新一次进度条
      this.timer = setInterval(() => {
        this.updateProgressBar()
      }, 800)
    },

    // 清除定时器
    clearProgressBarTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    endMove(e) {
      if (e == 0) {
        this.currentfff = 0
      } else if (e == 25) {
        this.currentfff = 1
      } else if (e == 50) {
        this.currentfff = 2
      } else if (e == 75) {
        this.currentfff = 3
      } else {
        this.currentfff = 4
      }
      this.inputQty = this.availableGU * (e / 100)
    },
    changeValidity(item, index) {
      this.validity = index
    },
    configOrder(butStatus) {
      this.butStatus = butStatus
      // 校验限价输入框
      if (!this.inputPrice || this.inputPrice <= 0) {
        uni.showToast({
          title: '请输入有效的限价',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 校验数量输入框
      if (!this.inputQty || this.inputQty <= 0) {
        uni.showToast({
          title: '请输入有效的数量',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      this.showConfirmPopup = true
    },
    openConfirmPopup(orderInfo) {
      this.confirmOrderInfo = orderInfo
      this.showConfirmPopup = true
    },
    closeConfirmPopup() {
      this.showConfirmPopup = false
    },
    confirmBuy() {
      this.markstockOrderCreate()
      // 这里写下单逻辑
      // 其他逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .u-slider__button {
  display: none !important;
}

::v-deep .uni-input-placeholder {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  text-align: center;
  color: #000;
  opacity: .5;
}

.buySellOrder {
  display: flex;
  flex-direction: row;
  background: #fff;
  border-radius: 16rpx;
  min-width: 600rpx;
  margin-top: 20rpx;

  .orderBook {
    display: flex;
    flex-direction: column;

    .orderBook_header {
      display: flex;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.5);
      font-size: 24rpx;
      margin-bottom: 14rpx;
    }

    .orderBook_list {
      flex: 1;

      .orderBook_row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        line-height: 42rpx;
        font-size: 20rpx;
        position: relative;

        .orderBook_bg {
          position: absolute;
          right: 0;
          top: 0;
          width: 89%;
          height: 42rpx;
          transition: width 0.8s ease-in-out;

          &.orderBook_bg_sell {
            background-color: rgba(255, 130, 163, 0.2);
          }

          &.orderBook_bg_buy {
            background-color: rgba(0, 192, 135, 0.2);
          }
        }
      }

      .orderBook_row_sell .orderBook_price {
        color: #FF82A3;
      }

      .orderBook_row_buy .orderBook_price {
        color: #00c087;
      }

      .orderBook_row_current {
        display: flex;
        justify-content: space-between;
        margin: 14rpx 0rpx;

        .price_current {
          font-weight: bold;
          color: #FF82A3;
          font-size: 28rpx;
          line-height: 40rpx;
        }

        .price_min {
          font-size: 24rpx;
          color: rgba(0, 0, 0, 0.5);
          line-height: 40rpx;
        }

        .icon {
          font-size: 24rpx;
          background-color: #F5F5F5;
          width: 92rpx;
          height: 48rpx;
          border-radius: 48rpx;
          text-align: center;
          line-height: 48rpx;
          color: rgba(0, 0, 0, 1);
        }
      }
    }

    .orderPanel_footer {
      display: flex;
      align-items: center;
      margin-top: 16rpx;

      .orderPanel_ratio {
        display: flex;
        align-items: center;
        margin-right: 20rpx;

        .orderPanel_buy {
          background: #e6f9f0;
          color: #00c087;
          border-radius: 8rpx;
          padding: 4rpx 8rpx;
          margin-right: 8rpx;
          font-size: 22rpx;
        }

        .orderPanel_sell {
          background: #ffeaea;
          color: #FF82A3;
          border-radius: 8rpx;
          padding: 4rpx 8rpx;
          font-size: 22rpx;
        }
      }

      .orderPanel_maxBuy {
        margin-right: 20rpx;
        color: #999;
        font-size: 22rpx;
      }

      .orderPanel_input_short {
        width: 80rpx;
        margin-right: 20rpx;
      }
    }
  }

  .orderPanel {
    width: 478rpx;
    padding-left: 22rpx;
    display: flex;
    flex-direction: column;

    .orderPanel_tabs {
      position: relative;
      display: flex;
      background: #f6f6f6;
      border-radius: 40rpx;
      height: 52rpx;
      overflow: hidden;
      width: 100%;

      .orderPanel_tab_slider {
        position: absolute;
        top: 0;
        left: 0;
        width: 50%;
        height: 100%;
        background: #19c15b;
        border-radius: 40rpx;
        z-index: 1;
        transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: transform;

        &.activeSell {
          background: #FF82A3;
        }
      }

      .orderPanel_tab {
        flex: 1;
        text-align: center;
        line-height: 52rpx;
        font-size: 24rpx;
        color: rgba(0, 0, 0, 0.4);
        z-index: 2;
        position: relative;
        cursor: pointer;
        transition: color 0.2s;
        user-select: none;
      }

      .orderPanel_tab_active {
        color: #fff;
        font-weight: 600;
      }
    }

    .orderPanel_input_price {
      margin-top: 28rpx;

      .orderPanel_input_msg {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: #000;
        margin-top: 10rpx;
        font-family: 'PingFang SC';
      }
    }

    .orderPanel_input_qty {
      margin-top: 28rpx;

      .orderPanel_input_msg {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: #000;
        margin-top: 10rpx;
        font-family: 'PingFang SC';
      }
    }

    .orderPanel_amount {
      font-size: 24rpx;
      color: #000;
      margin-bottom: 16rpx;
      font-family: 'Gilroy';
    }

    .orderPanel_slider {
      margin-bottom: 12rpx;
      margin-top: 28rpx;

      .orderPanel_slider_labels {
        display: flex;
        justify-content: space-between;
        font-size: 20rpx;
        color: #999;
        font-family: 'Gilroy';
        margin-top: 16rpx;

        text {
          min-width: 50rpx;
          text-align: center;
        }
      }
    }

    .orderPanel_available {
      display: flex;
      justify-content: space-between;
      color: #999;
      margin-bottom: 12rpx;
      font-size: 20rpx;
      font-family: 'Gilroy';
    }

    .orderPanel_validity {
      display: flex;
      justify-content: space-between;
      color: #999;
      margin-bottom: 12rpx;
      font-size: 20rpx;

      .orderPanel_radio {
        .li {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          image {
            width: 36rpx;
            height: 36rpx;
            margin-right: 8rpx;
          }

          text {
            font-size: 20rpx;
            color: #999;
          }

        }

      }
    }

    .orderPanel_btn {
      margin-top: 100rpx;

      button {
        width: 100%;
        height: 72rpx;
        border-radius: 40rpx;
        font-size: 28rpx;
        background: #FF82A3;
        color: #fff;

        &.activeBuy {
          background: #19c15b;
        }
      }
    }

    .orderPanel_btn_msg {
      font-size: 24rpx;
      color: #000;
      margin-top: 10rpx;
      font-family: 'PingFang SC';
    }
  }
}

.order_confirm {
  font-family: 'PingFang SC';

  .order_confirm_content {
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 20rpx 32rpx 32rpx 32rpx;
    position: relative;

    .order_confirm_header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32rpx;
      height: 100rpx;
      line-height: 100rpx;

      .order_confirm_title {
        font-size: 32rpx;
        font-weight: bold;
        text-align: left;
      }

      .order_confirm_close {
        cursor: pointer;
      }
    }

    .order_confirm_row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      margin-bottom: 24rpx;

      .order_confirm_label {
        color: #999;
      }

      .order_confirm_value {
        color: #333;
      }

      &.order_confirm_row_confirm {
        justify-content: flex-start;
        color: #333;
        font-size: 28rpx;
        margin: 32rpx 0 48rpx 0;
      }
    }

    .order_confirm_btns {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 280rpx;

      view {
        height: 88rpx;
        border-radius: 44rpx;
        font-size: 28rpx;
        margin: 0 12rpx;
        font-family: 'PingFang SC';
        line-height: 88rpx;
        border: none;
        text-align: center;
        gap: 16rpx;

        &.btn_cancel {
          background: #fff0f3;
          color: #FF82A3;
          border: none;
          width: 222rpx;
        }

        &.btn_confirm {
          background: #FF82A3;
          color: #fff;
          border: none;
          width: 452rpx;
        }
      }
    }
  }
}
</style>