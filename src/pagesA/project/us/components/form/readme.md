Req 请求历史k线
{

    "event":"req",
    "params" {
        "channel": "market_e_aaveusdt_kline_1min", "cb_id":"e_btcusdt", "since":"1506602880"
    }
}


实时推送 订阅 
market_e_aaveusdt_kline_5min
market_e_aaveusdt_kline_30min 1day、4h、60min
{
	"event":"sub",
	"params":{
        "channel":"market_e_aaveusdt_kline_1min","cb_id":"自定义"
    }
}


返回参数 解读
"event_rep":"rep",
"channel":"market_$base$quote_kline_[5min/30min/60min/4h/1day]",
"cb_id":"原路返回",
"since":"1506602880",//since缺省时返回最新300条，有值时返回大于since的最多1小时数据，since有强校验，不能早于当前1小时
"ts":1506584998239,//请求时间
"amount":123.1221,//交易额
"vol":1212.12211,//交易量
"open":2233.22,//开盘价
"close":1221.11,//收盘价
"high":22322.22,//最高价
"low":2321.22//最低价




返回具体的 json
{
    "channel":"market_e_aaveusdt_kline_1min",
    "data":[ {
        "amount": 51031956.861, 
        "close":80.03, 
        "ds":"2023-01-20 06:00:00",
        "high":80.124, 
        "id":1674194400, 
        "idx":1674194400, 
        "low":80.03,
        "open":80.1,
        "tradeId":0,
        "vol":637267
    }

    ],
    "event_rep":"rep",
    "status":"ok",
    "tick":null,
    "ts":1722360401000
}


深度请求  盘口
{

    "event":"sub",
    "params": {
        "channel": "market_e_aaveusdt_depth_step[0-2]", "cb_id":"自定义", "asks":150, "bids":150
    }
}

响应参数解读
{
    "channel":"market_$base$quote_depth_step[0-2]",//$base$quote表示aaveusdt等,深度有3个维度，0、1、2
    "ts":1506584998239,//请求时间
    "tick":{
        "asks":[   //卖盘
            [22112.22,0.9332], // 价格
            [22112.21,0.2]     // 数量
        ],
        "buys":[   //买盘
            [22111.22,0.9332],
            [22111.21,0.2]
        ]
    }
}

实时成交信息
{
    "event": "sub",
    "params": {
        "channel": "market_e_aaveusdt_trade_ticker",
        "cb_id": "1"
    }
}

响应数据
{
    "channel":"market_$base$quote_trade_ticker",//订阅的交易对行情$base$quote表示btckrw等
    "ts":1506584998239,//请求时间
    "tick":{
        "id":12121,//data中最大交易ID
        "ts":1506584998239,//data中最大时间
        "data":[
            {
                "id":12121,//交易ID
                "side":"buy",//买卖方向buy,sell
                "price":32.233,//单价
                "vol":232,//数量
                "amount":323,//总额
                "ts":1506584998239,//数据产生时间
                "ds":'2017-09-10 23:12:21'
            },
            {
                "id":12120,//交易ID
                "side":"buy",//买卖方向buy,sell
                "price":32.233,//单价
                "vol":232,//数量
                "amount":323,//总额
                "ts":1506584998239,//数据产生时间
                "ds":'2017-09-10 23:12:21'
            }
        ]
    }
}


indexYs live_data history_position_data
                <view class="live_data"
                                            :style="{ borderRadius: (item.trailType && item.epProfit != null) ? '20rpx' : '' }">
                                            <view class="sy red">
                                                <text class="text">收益</text>
                                                <view
                                                    style="display: flex;flex-direction: column;font-size: 18rpx;margin-left: 13rpx;">
                                                    <text class="text" v-if="item.status == 0"
                                                        style="margin-left: 4rpx;"
                                                        :class="[item.trailType == 2 ? 'textline' : '']"
                                                        :style="{ color: (item.profit) >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                            (item.profit) >= 0 ? '+' : '' }} {{ item.profit.toFixed(2) || 0
                                                        }}
                                                    </text>
                                                    <!-- :style="{ color: (item.epProfit) >= 0 ? '#EC4068' : '#6CFF8A' }" -->
                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfit != null && item.status == 0 && item.trailType == 2">{{
                                                            (item.epProfit) >= 0 ? '+' : '' }} {{ item.epProfit.toFixed(2)
                                                        }}</text>
                                                    <!-- :style="{ color: (item.epProfit) >= 0 ? '#EC4068' : '#6CFF8A' }" -->

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfit != null && item.status == 0 && item.trailType == 1">{{
                                                            (item.epProfit) >= 0 ? '+' : '' }} {{ item.epProfit.toFixed(2)
                                                        }}</text>
                                                </view>
                                                <text style="margin-left: 10rpx;"
                                                    :style="{ color: isred(item) ? '#EC4068' : '#6CFF8A' }"
                                                    v-if="item.status == 1">
                                                    {{ calculateEarnings(item) }}
                                                </text>

                                            </view>
                                            <view class="syl red">
                                                <!-- :class="{ 'red': item.red2 }" -->
                                                <text>收益率</text>
                                                <!-- <text v-if="item.status == 0"
                                                    :style="{ color: item.profit >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                        item.profit >= 0 ? '+'
                                                            : '' }}{{ (accMul(item.profitRate, 100)).toFixed(2) + '%' }}
                                                </text> -->
                                                <view v-if="item.status == 0"
                                                    style="display: flex;flex-direction: column;font-size: 18rpx;margin-left: 13rpx;">

                                                    <text v-if="item.status == 0"
                                                        :class="[item.trailType == 2 ? 'textline' : '']"
                                                        :style="{ color: item.profitRate >= 0 ? '#EC4068' : '#6CFF8A' }">{{
                                                            item.profitRate >= 0 ? '+'
                                                                : '' }}{{ (accMul(item.profitRate, 100)).toFixed(2) + '%' }}
                                                    </text>
                                                    <!-- :style="{ color: (item.epProfitRate) >= 0 ? '#EC4068' : '#6CFF8A' }" -->

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfitRate != null && item.status == 0 && item.trailType == 2">{{
                                                            (item.epProfitRate) >= 0 ? '+' : '' }} {{
                                                            (accMul(item.epProfitRate,
                                                                100)).toFixed(2) + '%'
                                                        }}</text>
                                                    <!-- :style="{ color: (item.epProfitRate) >= 0 ? '#EC4068' : '#6CFF8A' }" -->

                                                    <text style="color: #fff;" class="text"
                                                        v-if="item.epProfitRate != null && item.status == 0 && item.trailType == 1">{{
                                                            (item.epProfitRate) >= 0 ? '+' : '' }} {{
                                                            (accMul(item.epProfitRate,
                                                                100)).toFixed(2) + '%'
                                                        }}</text>
                                                </view>
                                                <text :style="{ color: item.red ? '#EC4068' : '#6CFF8A' }"
                                                    v-if="item.status == 1">
                                                    {{ calculateYield(item) }}
                                                </text>
                                            </view>
                                        </view>