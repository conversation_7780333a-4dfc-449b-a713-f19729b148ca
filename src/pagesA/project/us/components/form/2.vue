<template>
	<view style="background-color: #000000;">
		<!--  #ifndef  H5 -->
		<view>
			<canvas id="kline" canvas-id='kline' class='kline' v-bind:style="{width: ChartWidth+'px', height: ChartHeight+'px'}" 
			  @touchstart="KLineTouchStart" @touchmove='KLineTouchMove' @touchend='KLineTouchEnd' ></canvas>
		</view>
		
		<div class="button-sp-area">
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(1)">日线</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(2)">周线</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(4)">1分种</button>
			<button class="mini-btn" type="default" size="mini" @click="ChangePeriod(5)">5分钟</button>
		</div>
		
		<view id='customtooltip'  class='uni-flex uni-column customtooltip' v-show='Tooltip.IsShow'>
			<view class='tooltip_name'>{{Tooltip.Name}}</view>
			<view class='tooltip_label'>日期</view>
			<view class='tooltip_time'>{{Tooltip.Date}}</view>
			<view class='tooltip_label' v-show='Tooltip.IsShowTime'>时间</view>
			<view class='tooltip_time' >{{Tooltip.Time}}</view>
			<view class='tooltip_label'>价格</view>
			<view class='tooltip_price' :style="{color:Tooltip.ColorPrice}">{{Tooltip.Price}}</view>
			<view class='tooltip_label'>成交量</view>
			<view class='tooltip_vol'>{{Tooltip.Vol}}</view>
		</view>
		<!--  #endif -->
	</view>
</template>

<script>
// #ifdef H5
import HQChart from '../../umychart_uniapp_h5/umychart.uniapp.h5.js';
// #endif

// #ifndef H5
import { JSCommon } from '../../umychart.uniapp/umychart.wechat.3.0.js';
import {JSCommonHQStyle} from '../../umychart.uniapp/umychart.style.wechat.js'
// #endif

function DefaultData()
{
	
}

DefaultData.GetKLineOption = function () 
{
    let data = 
    {
        Type: '历史K线图', 
        
        Windows: //窗口指标
        [
            {Index:"MA",Modify: false, Change: false}, 
			{Index:"VOL",Modify: false, Change: false}
        ], 
 
        IsCorssOnlyDrawKLine:true,
        CorssCursorTouchEnd:true,
		IsClickShowCorssCursor:true,
		//IsFullDraw:true,
		
		CorssCursorInfo:{ Left:2, Right:2, Bottom:1, IsShowCorss:true },  //十字光标刻度设置
 
        Border: //边框
        {
            Left:   1,
            Right:  1, //右边间距
            Top:    1,
            Bottom: 25,
        },
 
        KLine:
        {
			DragMode:1, 
            Right:1,                            //复权 0 不复权 1 前复权 2 后复权
            Period:0,                           //周期: 0 日线 1 周线 2 月线 3 年线 
            PageSize:30,
            IsShowTooltip:false,
			DrawType:0, 
        },
		
		ExtendChart:
		[
			//{Name:'KLineTooltip' },	//开启手机端tooltip
		], 
		
		Frame:  //子框架设置
		[
		    {SplitCount:3,Custom: [{ Type: 0, Position: 'right'}]},
		    {SplitCount:2},
		    {SplitCount:3},
		],
        
    };
 
    return data;
}
	
var g_HQChart = { JSChart: null };	//图形实例

export default 
{
	data() 
	{
		var data=
		{
			Symbol:'600000.sh',
			ChartWidth:300,
			ChartHeight:600,
			
			Tooltip:
			{
				Name:"",
				Time:"",
				Date:"",
				Price:"",
				Vol:"",
				IsShow:false,
				IsShowTime:false,
				ColorPrice:'rgb(0,255,0)',
			}
		};
		
		return data;
	},
	
	onReady()
	{	
		console.log("[KLineChart::onReady]");
		this.$nextTick(()=>
		{
			// #ifndef H5
			this.CreateKLineChart(); 
			// #endif
		});
	},
	
	onShow()
	{
		uni.getSystemInfo({
		    success:  (res) =>
			{
				var width=res.windowWidth;
				var height=res.windowHeight;
		        this.ChartWidth=width;
				this.ChartHeight=height-40;
				this.$nextTick(()=>
				{
					// #ifndef H5
					this.CreateKLineChart(); 
					// #endif
				})
		    }
		});
	},
	
	onHide()
	{
		if (g_HQChart.JSChart)
		{
			g_HQChart.JSChart.StopAutoUpdate();
			g_HQChart.JSChart=null;
		}
	},
	
	onUnload()
	{
		if (g_HQChart.JSChart)
		{
			g_HQChart.JSChart.StopAutoUpdate();
			g_HQChart.JSChart=null;
		}
	},
	
	methods: 
	{
		ChangePeriod(period)
		{
			if(g_HQChart.JSChart) g_HQChart.JSChart.ChangePeriod(period);
		},
		
		CreateKLineChart_app()
		{
			if (g_HQChart.JSChart) return;
			
			var element = new JSCommon.JSCanvasElement();
			// #ifdef APP-PLUS
			element.IsUniApp=true;	//canvas需要指定下 是uniapp的app
			// #endif
			element.ID = 'kline';
			element.Height = this.ChartHeight;  //高度宽度需要手动绑定!!
			element.Width = this.ChartWidth;
				
			//用黑色风格
			var blackStyle=JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);	
			JSCommon.JSChart.SetStyle(blackStyle);
					
			g_HQChart.JSChart = JSCommon.JSChart.Init(element);
			var option=DefaultData.GetKLineOption();
			option.Symbol=this.Symbol;
			option.IsFullDraw=true; 				//每次手势移动全屏重绘
			g_HQChart.JSChart.SetOption(option);
			
			g_HQChart.JSChart.AddEventCallback({ 
				event: JSCommon.JSCHART_EVENT_ID.ON_TITLE_DRAW, 
				callback:(event, data, obj)=>{ this.DrawTooltip(event, data, obj); } 
			});
		},
			
		CreateKLineChart()
		{
			// #ifndef H5
			this.CreateKLineChart_app();
			// #endif
		},
			
		DrawTooltip(event, data, obj)
		{
			var tooltip=this.$refs.customtooltip;
			var item=data.Draw;	//分钟数据
			if (item) //有数据显示
			{
				//console.log(item);
				this.Tooltip.Name=obj.Name;
				//是否是分钟周期, 分钟周期有时间字段
				if (JSCommon.ChartData.IsMinutePeriod(g_HQChart.JSChart.JSChartContainer.Period,true)) //多日显示日期
				{
					this.Tooltip.Time=JSCommon.IFrameSplitOperator.FormatTimeString(item.Time,"HH:MM"); //格式化时间
					this.Tooltip.IsShowTime=true;
				}
				else
				{
					this.Tooltip.Time="";
					this.Tooltip.IsShowTime=false;
				}
				
				this.Tooltip.Date=JSCommon.IFrameSplitOperator.FormatDateString(item.Date,"MM-DD"); //格式化时间
				this.Tooltip.Price=item.Close.toFixed(2);
				if (item.Close>item.YClose) this.Tooltip.ColorPrice='#FF0000';       //上涨
				else if (item.Close<item.YClose) this.Tooltip.ColorPrice='#008B45';  //下跌
				else this.Tooltip.ColorPrice='#000000';                             //平盘
								
				this.Tooltip.Vol=item.Vol;
				this.Tooltip.IsShow=true;
			}
			else
			{
				this.Tooltip.IsShow=false;
			}
		},
			
		///
		//手势事件 app/小程序才有
		//KLine事件
		KLineTouchStart: function (event) 
		{
		  if (g_HQChart.JSChart) g_HQChart.JSChart.OnTouchStart(event);
		},
		
		KLineTouchMove: function (event) 
		{
		  if (g_HQChart.JSChart) g_HQChart.JSChart.OnTouchMove(event);
		},
		
		KLineTouchEnd: function (event) 
		{
		  if (g_HQChart.JSChart) g_HQChart.JSChart.OnTouchEnd(event);
		},
	}
}
</script>

<style>
	
	.customtooltip
	{
	    position:absolute;
	    border:1px solid #000000;
	    background-color:#FFFAFA;
	    padding:7px 7px 7px 7px;
	    top:20px;
	    left:2px;
	    width: 80px;
	    Z-index:99;
		font-size:12px;
	}
	
	.tooltip_label
	{
	    text-align:left;
	    display: block;
	    line-height: 15px;
	}
	
	.tooltip_name
	{
	    text-align:center;
	    display: block;
	    line-height: 15px;
	}
	
	.tooltip_time
	{
	    text-align:right;
	    display: block;
	    line-height: 15px;
	}
	
	.tooltip_price
	{
	    text-align:right;
	    display: block;
	    line-height: 15px;
	}
	
	.tooltip_vol
	{
	    text-align:right;
	    display: block;
	    line-height: 15px;
	}

</style>

var resource = JSCommon.JSChart.GetResource();
if (this.$store.state.themeColor.name == "primaryNight") {
  //黑暗模式
  var blackStyle = JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.BLACK_ID);
  JSCommon.JSChart.SetStyle(blackStyle);
} else if (this.$store.state.themeColor.name == "primaryDay") {
  this.isDay = true;
  var whiteStyle = JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.WHITE_ID);
  JSCommon.JSChart.SetStyle(whiteStyle);
} else {
  this.isDay = true;
  var whiteStyle = JSCommonHQStyle.GetStyleConfig(JSCommonHQStyle.STYLE_TYPE_ID.WHITE_ID);
  JSCommon.JSChart.SetStyle(whiteStyle);
}
//修改分时图颜色
console.log('================================',this.Fluctuation)
if (this.Fluctuation.includes('+')) {
  if (this.$store.state.themeColor.name == "primaryNight") {
	resource.Minute.PriceColor = 'rgb(255,0,0)';
	resource.Minute.AreaPriceColor = ['rgba(255,0,0,0.35)', 'rgba(255,0,0,0.1)'];
	resource.Minute.AvPriceColor = 'rgb(255,0,0)';
  } else {
	resource.Minute.PriceColor = 'rgb(255,0,0)';
	resource.Minute.AreaPriceColor = ['rgba(255,0,0,0.25)', 'rgba(255,0,0,0.02)'];
	resource.Minute.AvPriceColor = 'rgb(255,0,0)';
  }

} else if (this.Fluctuation.includes('-')) {
  resource.Minute.PriceColor = 'rgb(0,178,0)';
  resource.Minute.AreaPriceColor = ['rgba(0,178,0,0.6)', 'rgba(0,178,0,0.15)'];
  resource.Minute.AvPriceColor = 'rgb(0,178,0)';
}