<template>
    <view class="top-tabs">
        <view class="bottom-tabs-container">
            <!-- tab 滑动栏 -->
            <scroll-view scroll-x class="bottom-tabs-scroll">
                <view class="tab-wrap">
                    <view v-for="(tab, index) in bottomTabs" :key="index" class="tab-item"
                        :class="{ active: currentTab === index }" @click="onTabClick(index)">
                        {{ tab }}
                    </view>
                </view>
            </scroll-view>
            <!-- {{ holdList }} -->

            <!-- 对应内容滑块 -->
            <!-- <swiper class="tab-swiper" :style="{ height: swiperHeight + 'px' }" :current="currentTab"
                @change="onSwiperChange" circular>
                <swiper-item v-for="(tab, index) in bottomTabs" :key="index"> -->
            <!-- 持仓 -->
            <view class="tab-swiper">
                <view class="position-detail" v-if="currentTab == 0">
                    <view class="position-info">
                        <view class="title-row">
                            <text class="symbol">300023 阿里巴巴</text>
                            <!-- <text class="rate">收益率</text> -->
                        </view>

                        <view class="unrealized">
                            <view class="item left">
                                <view class="label">未实现盈亏(USDT)</view>
                                <view class="value">{{ position.unrealized }}</view>
                            </view>

                            <view class="item right">
                                <view class="label" style="opacity: 0.4;">收益率</view>
                                <view class="value">{{ position.percent }}</view>
                            </view>
                        </view>

                        <view class="meta">
                            <view class="meta-item" v-for="(item, index) in metaList" :key="index" style="width: auto;">
                                <text class="label">{{ item.label }}</text>
                                <text class="value">{{ item.value }}</text>
                            </view>
                        </view>
                    </view>
                    <nodata v-if="!holdList.length" />
                </view>
                <!-- 当前委托 -->
                <view class="position-detail" v-if="currentTab == 1">
                    <view class="order-detail">
                        <view class="order-card" v-for="(item, index) in holdList" :key="index">
                            <view class="header">
                                <view class="left">
                                    <text class="symbol">{{ item.symbol + ' ' + item.symbolNameUs }}</text>
                                    <text class="direction" :class="item.directionClass">{{ item.status
                                    }}</text>
                                </view>
                                <view class="right">
                                    <u-button hover-class="none" class="cancel-btn flex_all"
                                        @click="onCancel(item)">撤单</u-button>
                                </view>
                            </view>
                            <view class="info-list">
                                <view class="info-item">
                                    <text class="label">委托/均价</text>
                                    <text class="value">{{ item.wtPrice + '/' + item.avgPrice }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">委托/成交</text>
                                    <text class="value">{{ item.wtTotal + '/' + item.tradeTotal }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">委托时间</text>
                                    <text class="value">{{ formatTimestampms(item.ct) }}</text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <nodata v-if="!orders.length" />

                </view>
                <!-- 成交 -->
                 <!-- {{ holdList }} -->
                <view class="position-detail" v-if="currentTab == 2">
                    <view class="order-detail">
                        <view class="order-card" v-for="(item, index) in holdList" :key="index">
                            <view class="header">
                                <view class="left">
                                    <text class="symbol">{{ item.symbol }}</text>
                                    <text class="direction">{{ item.direction
                                    }}</text>
                                </view>
                            </view>

                            <view class="info-list">
                                <view class="info-item">
                                    <text class="label">{{ '成交价' }}</text>
                                    <text class="value">{{ item.value }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">{{ '成交量' }}</text>
                                    <text class="value">{{ item.value }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">{{ '成交价' }}</text>
                                    <text class="value">{{ item.value }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">{{ '成交价' }}</text>
                                    <text class="value">{{ item.value }}</text>
                                </view>

                                <!-- info: [
                        { label: '成交价', value: '1' },
                        { label: '成交量', value: '31588047' },
                        { label: '成交额', value: '31588047' },
                        { label: '成交时间', value: '2025–05–14 15:05:41' }
                    ] -->
                            </view>
                        </view>
                    </view>
                    <nodata v-if="!Planorders.length" />
                </view>
                <!-- <view class="position-detail" v-if="index == 3">
                    <nodata />
                </view> -->
            </view>

            <!-- </swiper-item> -->
            <!-- </swiper> -->
        </view>
    </view>
</template>

<script>
import nodata from "./nodata"
import { formatTimestampms } from "@/utils/utils"
export default {
    components: {
        nodata
    },
    props: ["current"],
    data() {
        return {
            orders: [
                {
                    symbol: '300023 阿里巴巴',
                    direction: '委托中',
                    directionClass: 'long',
                    info: [
                        { label: '委托/均价', value: '100.00 / 123.23' },
                        { label: '委托/成交', value: '8100/100' },
                        { label: '委托时间', value: '2025-05-14 15:05:41' },
                    ]
                },
            ],
            Planorders: [
                {
                    symbol: '300023 阿里巴巴',
                    directionClass: 'long',
                    info: [
                        { label: '成交价', value: '1' },
                        { label: '成交量', value: '31588047' },
                        { label: '成交额', value: '31588047' },
                        { label: '成交时间', value: '2025–05–14 15:05:41' }
                    ]
                },
                // 可继续添加更多订单...
            ],
            bottomTabs: ['持仓', '委托 ', '成交'],
            activeBottomTab: '持仓',
            currentTab: 0,
            swiperHeight: 0,
            position: {
                unrealized: '-0.6438',
                percent: '-3.12%'
            },
            holdList: [
                { label: '持仓量(张)', value: '333' },
            ],
            metaList: [
                { label: '市值', value: '31,588,047.00' },
                { label: '持仓/可用', value: '8100/0' },
                { label: '成本/现价', value: '8100/100' },
                { label: '当日盈亏', value: '8100' },
                { label: '个股仓位', value: '8100' }
            ],
            actionButtons: ['一键反向', '平仓', '止盈止损'],
            pageNum: 1,
            pageSize: 10,
            hasNext: true,
        }
    },
    mounted() {
        // this.$nextTick(() => {
        this.getHold()
        // settimeout(() => {
        //     this.updateSwiperHeight()
        // }, 0)
        // this.updateSwiperHeight()
        // })
        // this.currentTab = 0;
    },
    methods: {
        formatTimestampms,
        // 委托历史订单
        async getstockorder() {
            let res = await this.$api.stockorder({
                market: this.current == 0 ? 'US' : 'HK',
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            })
            if (res.code == 200) {
                this.hasNext = res.result.hasNext
                if (res.result.data && res.result.data.length) {
                    if (this.pageNum == 1) {
                        this.holdList = res.result.data
                    } else {
                        this.holdList = this.holdList.concat(res.result.data)
                    }
                } else {
                    this.holdList = []
                }

            }
        },
        // 成交记录
        async gettradeLog() {
            let res = await this.$api.tradeLog({
                market: this.current == 0 ? 'US' : 'HK',
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            })
            if (res.code == 200) {
                this.hasNext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.holdList = res.result
                } else {
                    this.holdList = this.holdList.concat(res.result)
                }
            }
        },
        // 持仓
        async getHold() {
            let res = await this.$api.stockhold({
                market: this.current == 0 ? 'US' : 'HK',
                pageNum: this.pageNum,
                pageSize: this.pageSize,
            })
            if (res.code == 200) {
                this.hasNext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.holdList = res.result.data
                } else {
                    this.holdList = this.holdList.concat(res.result.data)
                }
            }
        },
        // 撤单
        async onCancel(e) {
            if (e?.canCalcel == 0) {
                return
            }
            let res = await this.$api.stockcancel({
                orderId: e.id,
            })
            if (res.code == 200) {
                uni.showToast({
                    title: '撤单成功',
                    icon: 'none',
                    duration: 3000
                })
                this.getstockorder()
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
        },
        // onCancel(index) {
        //     uni.showToast({
        //         title: `撤销第 ${index + 1} 条订单`,
        //         icon: 'none'
        //     })
        // },
        onTabClick(index) {
            this.currentTab = index;
            this.holdList = []
            this.pageNum = 1
            if (index == 0) {
                this.getHold()
            } else if (index == 1) {
                this.getstockorder()
            } else {
                this.gettradeLog()
            }
        },
        onSwiperChange(e) {
            this.currentTab = e.detail.current;
        },
        updateSwiperHeight() {
            const id = `content-${this.currentTab}`
            uni.createSelectorQuery()
                .in(this)
                .select(`#${id}`)
                .boundingClientRect((res) => {
                    console.log(res);
                    if (res) this.swiperHeight = res.height
                })
                .exec()
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.top-tabs {
    margin-top: 34rpx;

}

.bottom-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24rpx;
    width: 100%;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 34rpx;

    .tab {
        color: rgba(0, 0, 0, .4);

        &.active {
            background: #F6F6F6;
            border-radius: 8rpx;
            padding: 11rpx 19rpx;
            color: #000;
            font-weight: 500;
        }
    }
}

.bottom-tabs-container {
    .bottom-tabs-scroll {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        background: #fff;

        .tab-wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            width: 50%;

            .tab-item {
                flex: 1 0 auto;
                padding: 11rpx 19rpx;
                font-size: 24rpx;
                color: rgba(0, 0, 0, 0.4);
                text-align: center;
                z-index: 1;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;

                image {
                    margin-right: 12rpx;
                    width: 28rpx;
                    height: 30rpx;
                }
            }

            .tab-item.active {
                background: #F6F6F6;
                padding: 11rpx 19rpx;
                border-radius: 8rpx;
                color: #000;
                font-weight: 500;
            }
        }

    }

    .tab-swiper {
        margin-top: 24rpx;
        height: fit-content;

        .order-detail {
            .order-card {
                margin-bottom: 56rpx;

                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 24rpx;

                    .left {
                        display: flex;
                        flex-direction: column;

                        .symbol {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 24rpx;
                            line-height: 34rpx;
                            color: #000;
                        }

                        .direction {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 24rpx;
                            line-height: 34rpx;

                            &.long {
                                color: #FF82A3;
                            }

                            &.short {
                                color: #30C147;
                            }
                        }
                    }

                    .right {
                        .cancel-btn {
                            width: 122rpx;
                            height: 60rpx;
                            background-color: #F6F6F6;
                            font-family: PingFang SC;
                            font-weight: 500;
                            font-size: 24rpx;
                            color: #000;
                            border-radius: 40rpx;
                            margin: 0;
                        }
                    }
                }

                .info-list {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: flex-start;

                    .info-item {
                        width: 33.33%;
                        margin-bottom: 20rpx;
                        display: flex;
                        flex-direction: column;

                        &:nth-child(3n + 1) {
                            align-items: flex-start;
                        }

                        &:nth-child(3n + 2) {
                            align-items: center;
                        }

                        &:nth-child(3n) {
                            align-items: flex-end;
                        }

                        .label {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 20rpx;
                            line-height: 28rpx;
                            color: rgba(0, 0, 0, 0.4);
                        }

                        .value {
                            font-family: PingFang SC;
                            font-weight: 500;
                            font-size: 20rpx;
                            line-height: 28rpx;
                            margin-top: 4rpx;
                            color: #000;
                        }
                    }
                }
            }
        }

        .position-detail {
            margin-bottom: 56rpx;

            .platform {
                margin-bottom: 24rpx;

                .platform-button {
                    margin: 0;
                    width: 182rpx;
                    height: 60rpx;
                    border-radius: 40rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 24rpx;
                    background-color: #f6f6f6;
                    color: #000;
                }
            }

            .position-info {

                .title-row {
                    display: flex;
                    align-items: center;
                    margin-bottom: 18rpx;

                    .side {
                        width: 30rpx;
                        height: 30rpx;
                        border-radius: 7rpx;
                        background: #30C147;
                        margin-right: 16rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        font-size: 14rpx;
                        color: #FFFFFF;
                    }

                    .symbol {
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 24rpx;
                        line-height: 34rpx;
                        color: #000;
                    }
                }

                .unrealized {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 14rpx;

                    .left {
                        align-items: flex-start;
                    }

                    .right {
                        align-items: flex-end;
                    }

                    .item {
                        display: flex;
                        flex-direction: column;


                        .label {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 20rpx;
                            line-height: 28rpx;
                            color: #000000;
                        }

                        .value {
                            margin-top: 8rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            font-size: 20rpx;
                            line-height: 28rpx;
                            color: #FF82A3;
                        }
                    }

                }

                .meta {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;

                    .meta-item {
                        display: flex;
                        flex-direction: column;
                        // align-items: flex-start;
                        width: 33%;
                        margin-bottom: 18rpx;

                        .label {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 20rpx;
                            line-height: 28rpx;
                            color: rgba(0, 0, 0, .4);
                        }

                        .value {
                            margin-top: 4rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            font-size: 20rpx;
                            line-height: 28rpx;
                            color: #000;
                        }

                        // 第1列
                        &:nth-child(3n + 1) {
                            align-items: flex-start;
                        }

                        // 第2列
                        &:nth-child(3n + 2) {
                            align-items: center;
                        }

                        // 第3列
                        &:nth-child(3n) {
                            align-items: flex-end;
                        }
                    }
                }
            }

            .actions {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 28rpx;

                .action-button {
                    // flex: 1;
                    margin: 0;
                    width: 220rpx;
                    height: 60rpx;
                    border-radius: 40rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 24rpx;
                    background-color: #f6f6f6;
                    color: #000;


                }
            }
        }

    }
}
</style>