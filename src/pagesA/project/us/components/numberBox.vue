<template>
    <view class="numberBox">
        <view class="del" @click="del">
            <image src="https://pro-oss.pinkwallet.com/image/1380587907743047680.png"
                mode="widthFix"></image>
        </view>
        <view class="input">
            <input maxlength="10" type="number" v-model="localValue" :placeholder="placeholder" />
        </view>
        <view class="add" @click="add">
            <image src="https://pro-oss.pinkwallet.com/image/1380587949447012352.png"
                mode="widthFix"></image>
        </view>
    </view>
</template>
<script>
export default {
    name: 'numberBox',
    props: {
        placeholder: {
            type: String,
            default: '0.00'
        },
        value: {
            type: Number,
            default: 0
        },
        limit: {
            type: Number
        },
        spreadScale: {
            type: Number,
            default: 2
        }
    },
    data() {
        return {
            localValue: this.value
        }
    },
    watch: {
        value: {
            handler(newVal) {
                this.localValue = newVal;
            },
            immediate: true
        },
        localValue(newVal) {
            this.$emit('update:value', Number(newVal));
        }
    },
    methods: {
        del() {
            this.localValue = Number(this.localValue) - this.limit;
            // this.localValue = this.localValue.toFixed(this.spreadScale);
            this.localValue = this.localValue.toFixed(4);
            
        },
        add() {
            this.localValue = Number(this.localValue) + this.limit;
            // this.localValue = this.localValue.toFixed(this.spreadScale);
            this.localValue = this.localValue.toFixed(4);
        }
    }
}
</script>

<style scoped>
.numberBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 80rpx;
    background-color: #F6F6F6;
    border-radius: 16rpx;
    padding: 22rpx 20rpx;

    .del {
        width: 80rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20rpx;

        image {
            width: 28rpx;
            height: 28rpx;
        }
    }

    .input {
        width: 100%;
        height: 100%;
        border-right: 1px solid rgba(0, 0, 0, 0.1);
        border-left: 1px solid rgba(0, 0, 0, 0.1);

        input {
            width: 100%;
            height: 100%;
            font-size: 28rpx;
            color: #000;
            text-align: center;
            font-family: 'PingFang SC';
            font-weight: 600;
        }

        input::-webkit-input-placeholder {
            font-family: 'PingFang SC';
            font-weight: 400 !important;
            color: rgba(0, 0, 0, 0.5);
        }
    }

    .add {
        width: 80rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 20rpx;

        image {
            width: 28rpx;
            height: 28rpx;
        }
    }

}
</style>
