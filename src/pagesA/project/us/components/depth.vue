<template>
    <view class="order-book">
        <!-- 卖盘 -->
        <view class="order-side">
            <view class="order-title">
                <view class="priceamount">
                    <text>Price</text>
                </view>
                <view class="priceamount">
                    <text>Quantity</text>
                </view>

            </view>
            <view class="order-list">
                <view v-for="(item, index) in sellOrders.slice(0, 6)" :key="'sell-' + index" class="order-row">
                    <view class="bg" :style="{ width: `${item.percent}%` }"></view>
                    <view class="text">
                        <text :class="['price', { highlight: item.isHighlighted }]">{{ formatThousand(item.price)
                        }}</text>
                        <text :class="['amount', { highlight: item.isHighlighted }]">{{ formatThousand(item.volume)
                        }}</text>
                    </view>
                </view>

            </view>
        </view>

        <view class="middle-price">
            <view class="">
                <view class="markPrice"> {{ formatThousand($store.state.uslatestPrice) }} </view>
                <!-- <view class="newlatest">{{ formatThousand(62500.00) }} </view> -->
            </view>
        </view>
        <!-- {{ orders }} -->
        <!-- 买盘 -->
        <view class="order-side">
            <view class="order-list">
                <view v-for="(item, index) in buyOrders.slice(0, 6)" :key="'sell-' + index" class="order-row-buy">
                    <view class="bg" :style="{ width: `${item.percent}%` }"></view>
                    <view class="text">
                        <text :class="['price', { highlight: item.isHighlighted }]">{{ formatThousand(item.price)
                        }}</text>
                        <text :class="['amount', { highlight: item.isHighlighted }]">{{ formatThousand(item.volume)
                        }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 盘口只显示买票或者卖盘 -->
        <view class="segmented-progress-bar-container" style="margin:10rpx 0rpx;">
            <view class="segmented-progress-bar">
                <view class="segment green-segment" :style="greenSegmentStyle">
                    <view class="char-box">B</view>
                    <text class="percentage">{{ greenPercentageText }}</text>
                </view>
                <view class="segment red-segment-bg" :style="redSegmentStyle"></view>
                <view class="red-content-fixed">
                    <text class="percentage red-percentage-fixed">{{ redPercentageText }}</text>
                    <view class="char-box red-char-box-fixed">S</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import store from "@/store"
export default {
    data() {
        return {
            isOpen: false,
            animationTimer: null, // 动画定时器
            orders: [],
            // { price: '62503.1', amount: '0.003', type: 'sell', percent: 60 },
            // { price: '62503.0', amount: '0.003', type: 'sell', percent: 45 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 30 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 20 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 10 },
            // { price: '62500.0', amount: '0.003', type: 'buy', percent: 15 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 25 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 35 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 45 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 55 },
            intervalId: null,
            currentGreenPercentage: 99, // 初始值
            intervalId2: null,
        }
    },

    mounted() {
        this.getrealtimeQuote()
        // this.intervalId = setInterval(this.updateBuyPercents, 1000)
        // this.intervalId2 = setInterval(this.updateProgressBar, 500);
        // this.getusdepthItems()
    },
    watch: {
        "$store.state.usDepth"(val) {
            // console.log(val, 'sahq');
            // 判断val对象是否为空
            // if (Object.keys(val).length === 0 || !val) {
            //     return [];
            // }
            // console.log(this.transformOrderBookData(val), 'sa');
            this.orders = []
            this.orders = this.transformToOrderList(val)
            // this.orders = this.processDepthData(this.transformOrderBookData(this.transformOrderBook(val)))
            // console.log(this.orders, 'sahq');
        },
    },
    computed: {
        sellOrders() {
            if (!this.orders.length) {
                return [];
            }
            return this.orders.filter(o => o.type == 'sell')
        },
        buyOrders() {
            if (!this.orders.length) {
                return [];
            }
            return this.orders.filter(o => o.type == 'buy')
        },
        currentRedPercentage() {
            return 100 - this.currentGreenPercentage;
        },
        displayMetrics() {
            const gP = this.currentGreenPercentage;
            const rP = 100 - gP;
            const minVisual = 20; // 最小视觉宽度百分比调整为20%

            let greenFlex = gP;
            let redFlex = rP;

            if (gP === 0) {
                greenFlex = 0;
                redFlex = 100;
            } else if (rP === 0) { // gP === 100
                redFlex = 0;
                greenFlex = 100;
            } else {
                // Both gP and rP are > 0
                if (gP < minVisual) { // Green is active but too small
                    greenFlex = minVisual;
                    redFlex = 100 - minVisual; // Red must be able to cover this (i.e., rP was originally >= 100 - minVisual)
                } else if (rP < minVisual) { // Red is active but too small
                    redFlex = minVisual;
                    greenFlex = 100 - minVisual; // Green must be able to cover this
                }
                // If neither of the above, it means both gP and rP (if > 0) are >= minVisual.
                // So, their initial values (gP, rP) are correct for flex-basis.
            }
            return {
                greenFlexBasis: greenFlex,
                redFlexBasis: redFlex
            };
        },
        redSegmentStyle() {
            // 计算this.orders的里面的volume的一共的值，用sellOrders里面的volume的总和除以这个值，得到一个百分比
            const totalVolume = this.orders.reduce((sum, order) => sum + (order.volume || 0), 0);
            const sellVolume = this.sellOrders.reduce((sum, order) => sum + (order.volume || 0), 0);
            const sellPercentage = totalVolume > 0 ? (sellVolume / totalVolume) * 100 : 0;

            return {
                flexBasis: `${sellPercentage.toFixed(2)}%`,
            };
        },
        greenSegmentStyle() {
            // 下面red用buyOrders的数据
            const totalVolume = this.orders.reduce((sum, order) => sum + (order.volume || 0), 0);
            const buyVolume = this.buyOrders.reduce((sum, order) => sum + (order.volume || 0), 0);
            const buyPercentage = totalVolume > 0 ? (buyVolume / totalVolume) * 100 : 0;

            return {
                flexBasis: `${buyPercentage.toFixed(2)}%`,
            };
        },
        redPercentageText() {
            // 显示sellOrders的百分比
            const totalVolume = this.orders.reduce((sum, order) => sum + (order.volume || 0), 0);
            const sellVolume = this.sellOrders.reduce((sum, order) => sum + (order.volume || 0), 0);
            const sellPercentage = totalVolume > 0 ? (sellVolume / totalVolume) * 100 : 0;
            return `${Math.round(sellPercentage)}%`;
        },
        greenPercentageText() {
            // 显示buyOrders的百分比
            const totalVolume = this.orders.reduce((sum, order) => sum + (order.volume || 0), 0);
            const buyVolume = this.buyOrders.reduce((sum, order) => sum + (order.volume || 0), 0);
            const buyPercentage = totalVolume > 0 ? (buyVolume / totalVolume) * 100 : 0;
            return `${Math.round(buyPercentage)}%`;
        },
    },
    beforeDestroy() {
        // 清理定时器
        this.stopDepthAnimation();
        if (this.intervalId2) {
            clearInterval(this.intervalId2);
        }
    },
    methods: {
        transformToOrderList(data) {
            if(!data) return []
            const formatSide = (prices, volumes, type) => {
                if(!prices || !volumes) return []
                return prices.map((price, index) => ({
                    price,
                    count: 0,
                    volume: Number(volumes[index] || 0),
                    type
                }))
            }

            // 生成挂单列表
            let asks = formatSide(data.ask.price, data.ask.volume, 'sell')
            let bids = formatSide(data.bid.price, data.bid.volume, 'buy')

            // 所有挂单合并，用于计算最大成交量
            const allItems = [...asks, ...bids]
            const maxVolume = Math.max(...allItems.map(item => item.volume || 0))

            // 添加 percent 字段
            asks = asks.map(item => ({
                ...item,
                percent: maxVolume > 0 ? Math.round((item.volume / maxVolume) * 100) : 0
            }))

            bids = bids.map(item => ({
                ...item,
                percent: maxVolume > 0 ? Math.round((item.volume / maxVolume) * 100) : 0
            }))

            return [...asks, ...bids]
        },
        transformOrderBook(data) {
            const formatSide = (prices, volumes) => {
                return prices.map((price, index) => ({
                    price,
                    count: 0,
                    volume: Number(volumes[index] || 0)
                }))
            }

            return {
                lang: null,
                symbol: data.symbol,
                asks: formatSide(data.ask.price, data.ask.volume),
                bids: formatSide(data.bid.price, data.bid.volume),
                account: null
            }
        },
        async getrealtimeQuote() {
            let res = await this.$api.realtimeQuote({
                symbol: this.$store.state.stockcode
            })
            if (res.code == 200) {
                console.log(res, 'res');
                // this.realtimeQuoteData = res.result[0];
                // store.commit("changeUslatestPrice", res.result[0].latestPrice);
            }
        },
        // 启动盘口数据动画
        startDepthAnimation() {
            // 每2-4秒随机更新一些数据
            this.animationTimer = setInterval(() => {
                this.updateDepthData();
            }, Math.random() * 2000 + 2000); // 2-4秒随机间隔
        },

        // 更新盘口数据，模拟真实交易的微小变化
        updateDepthData() {
            if (!this.orders || this.orders.length === 0) return;

            // 随机选择1-3个订单进行微调
            const updateCount = Math.floor(Math.random() * 3) + 1;
            const ordersToUpdate = [];

            for (let i = 0; i < updateCount; i++) {
                const randomIndex = Math.floor(Math.random() * this.orders.length);
                if (!ordersToUpdate.includes(randomIndex)) {
                    ordersToUpdate.push(randomIndex);
                }
            }

            ordersToUpdate.forEach(index => {
                const order = this.orders[index];

                // 价格微调：±0.01-0.05的随机变化
                const priceChange = (Math.random() - 0.5) * 0.1; // -0.05 到 +0.05
                order.price = Math.max(0.01, order.price + priceChange);
                order.price = Math.round(order.price * 100) / 100; // 保留2位小数

                // 数量微调：±5%-15%的变化
                const volumeChangePercent = (Math.random() - 0.5) * 0.3; // -15% 到 +15%
                const volumeChange = Math.floor(order.volume * volumeChangePercent);
                order.volume = Math.max(1, order.volume + volumeChange);

                // 重新计算百分比
                const allVolumes = this.orders.map(item => item.volume);
                const maxVolume = Math.max(...allVolumes);
                order.percent = Math.round((order.volume / maxVolume) * 100);

                // 添加高亮标记，用于CSS动画
                order.isHighlighted = true;

                // 0.5秒后移除高亮
                setTimeout(() => {
                    if (this.orders[index]) {
                        this.orders[index].isHighlighted = false;
                    }
                }, 500);
            });

            // 重新排序
            this.resortOrders();
        },

        // 重新排序订单
        resortOrders() {
            const asks = this.orders.filter(item => item.type === 'sell')
                .sort((a, b) => b.price - a.price);
            const bids = this.orders.filter(item => item.type === 'buy')
                .sort((a, b) => a.price - b.price);

            this.orders = [...asks, ...bids];
        },

        // 停止动画
        stopDepthAnimation() {
            if (this.animationTimer) {
                clearInterval(this.animationTimer);
                this.animationTimer = null;
            }
        },
        // 处理数据的函数
        processDepthData(data) {
            if (!data || !data.asks || !data.bids) return [];

            // 复制数组避免修改原数据
            let asks = [...data.asks];
            let bids = [...data.bids];

            // 给asks添加type:"sell"并按price从大到小排序
            asks = asks.map(item => ({ ...item, type: "sell" }))
                .sort((a, b) => b.price - a.price);

            // 给bids添加type:"buy"并按price从小到大排序
            bids = bids.map(item => ({ ...item, type: "buy" }))
                .sort((a, b) => a.price - b.price);

            // 合并所有数据找出最大volume
            const allItems = [...asks, ...bids];
            const maxVolume = Math.max(...allItems.map(item => item.volume));

            // 给每个对象添加percent字段
            asks = asks.map(item => ({
                ...item,
                percent: Math.round((item.volume / maxVolume) * 100)
            }));

            bids = bids.map(item => ({
                ...item,
                percent: Math.round((item.volume / maxVolume) * 100)
            }));

            // 合并asks和bids数组
            return [...asks, ...bids];
        },
        async getusdepthItems(e) {
            console.log('getusdepthItems');
            this.orders = []
            let res = await this.$api.usdepthItems({
                symbol: this.$store.state.stockcode,
                market: e == 0 ? "US" : 'HK'
            })
            if (res.code == 200) {
                console.log(res, 'res');
                const mockData = res.result[0]
                // {
                //     lang: "zh_CN",
                //     symbol: "00005",
                //     asks: [
                //         { price: 100.08, count: 2, volume: 239 },
                //         { price: 100.28, count: 3, volume: 362 },
                //         { price: 100.48, count: 2, volume: 216 },
                //         { price: 100.68, count: 1, volume: 172 },
                //         { price: 100.88, count: 3, volume: 382 },
                //         { price: 101.08, count: 2, volume: 216 },
                //         { price: 101.28, count: 3, volume: 362 },
                //         { price: 101.48, count: 2, volume: 239 },
                //         { price: 101.68, count: 1, volume: 172 },
                //         { price: 101.88, count: 3, volume: 382 },
                //     ],
                //     bids: [
                //         { price: 99.68, count: 1, volume: 486 },
                //         { price: 99.48, count: 3, volume: 382 },
                //         { price: 99.28, count: 1, volume: 172 },
                //         { price: 99.08, count: 2, volume: 239 },
                //         { price: 98.88, count: 3, volume: 362 },
                //         { price: 98.68, count: 2, volume: 216 },
                //         { price: 98.48, count: 1, volume: 172 },
                //         { price: 98.28, count: 3, volume: 382 },
                //         { price: 98.08, count: 2, volume: 216 },
                //     ],
                //     account: "mock_account"
                // },


                // 给上述对象里面的数组asks里面的数组对象每一个都加一个type:"sell",bids加一个type:"buy",
                // ask数组按照price从大到小排序，bids按照price从小到大排序，再加一个percent字段，每一个对象都有，是当前asks的数组对象中的volume/循环数组最大的否的volume的百分比（整数
                // 然后将asks和bids合并到一个数组中，然后将这个数组赋值给orders


                // 使用示例数据处理
                // const mockData = {
                //     lang: "zh_CN",
                //     symbol: "00005",
                //     asks: [
                //         { price: 100.08, count: 2, volume: 239 },
                //         { price: 100.28, count: 3, volume: 362 },
                //         { price: 100.48, count: 2, volume: 216 },
                //         { price: 100.68, count: 1, volume: 172 },
                //         { price: 100.88, count: 3, volume: 382 },
                //         { price: 101.08, count: 2, volume: 216 },
                //         { price: 101.28, count: 3, volume: 362 },
                //         { price: 101.48, count: 2, volume: 239 },
                //         { price: 101.68, count: 1, volume: 172 },
                //         { price: 101.88, count: 3, volume: 382 },
                //     ],
                //     bids: [
                //         { price: 99.68, count: 1, volume: 486 },
                //         { price: 99.48, count: 3, volume: 382 },
                //         { price: 99.28, count: 1, volume: 172 },
                //         { price: 99.08, count: 2, volume: 239 },
                //         { price: 98.88, count: 3, volume: 362 },
                //         { price: 98.68, count: 2, volume: 216 },
                //         { price: 98.48, count: 1, volume: 172 },
                //         { price: 98.28, count: 3, volume: 382 },
                //         { price: 98.08, count: 2, volume: 216 },
                //     ],
                //     account: "mock_account"
                // };



                // 处理数据并赋值给orders
                this.orders = this.processDepthData(mockData);
                console.log(this.orders, 'orders');

                // 启动盘口数据动画
                // this.startDepthAnimation();
            }
        },
        transformOrderBookData(data) {
            // if (!data || !data.symbol || !data.ask || !data.bid) {
            //     return {
            //         lang: "zh_CN",
            //         symbol: data?.symbol || "UNKNOWN",
            //         asks: [],
            //         bids: [],
            //         account: "mock_account"
            //     };
            // }
            console.log("data:", data);

            return {
                lang: "zh_CN",
                symbol: data.symbol,
                asks: this.formatSide(data.ask),
                bids: this.formatSide(data.bid),
                account: "mock_account"
            };
        },
        formatSide(side) {
            const prices = side.price || [];
            const volumes = side.volume || [];
            const counts = side.orderCount || [];

            const length = Math.min(prices.length, volumes.length, counts.length);
            const result = [];

            for (let i = 0; i < length; i++) {
                result.push({
                    price: parseFloat(prices[i]),
                    volume: parseInt(volumes[i]),
                    count: parseInt(counts[i])
                });
            }

            return result;
        },

        toggle() {
            this.isOpen = !this.isOpen
        },
        formatThousand(val) {
            if (isNaN(val)) return val
            let value = Number(val).toFixed(2)
            return parseFloat(value).toLocaleString('en-US')
        },
        updateBuyPercents() {
            this.orders = this.orders.map(order => {
                const variation = Math.floor(Math.random() * 50) + 50 // 50 ~ 99
                return {
                    ...order,
                    percent: variation
                }
                return order
            })
            // this.orders = order
        },
        updateProgressBar() {
            const change = (Math.random() - 0.5) * 50;
            let newGreenPercentage = this.currentGreenPercentage + change;
            this.currentGreenPercentage = Math.max(0, Math.min(100, newGreenPercentage));
        },
    }
}
</script>

<style lang="scss" scoped>
.order-book {
    /* // flex: 1; */
    width: 200rpx;
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .rate {
        font-family: Gilroy-Medium;
        font-weight: 400;
        font-size: 20rpx;
        line-height: 28rpx;
        color: rgba(0, 0, 0, .5);

        .name {
            width: fit-content;
            padding-bottom: 4rpx;
            border-bottom: 1rpx dashed #808080
        }

        .rate-value {
            margin-top: 8rpx;
        }
    }

    .order-side {
        height: 290rpx;

        .order-title {
            margin: 18rpx 0 12rpx 0;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .priceamount {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;

                text {
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 20rpx;
                    /* // line-height: 28rpx; */
                    color: rgba(0, 0, 0, .5);
                }
            }
        }

        .order-list {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            .order-row {
                width: 100%;
                display: flex;
                justify-content: space-between;
                /* // background-image: linear-gradient(to right, rgba(22, 199, 132, 0.2), transparent); */
                background-repeat: no-repeat;
                background-size: 0% 100%;

                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 40rpx;
                height: 40rpx;
                position: relative;
                transition: all 0.3s ease-in-out;
                /* 添加整体过渡动画 */

                .bg {
                    position: absolute;
                    right: 0;
                    background: rgba(255, 130, 163, .2);
                    transition: width 0.3s ease;
                    height: 100%;
                }

                .text {
                    width: 100% !important;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin: 0 8rpx;
                    padding: 9rpx 0;

                    .price {
                        color: #FF82A3;
                        transition: all 0.2s ease-in-out;

                        &.highlight {
                            background-color: rgba(255, 130, 163, 0.3);
                            transform: scale(1.05);
                        }
                    }

                    .amount {
                        color: #000;
                        transition: all 0.2s ease-in-out;

                        &.highlight {
                            background-color: rgba(255, 255, 255, 0.2);
                            transform: scale(1.05);
                        }
                    }
                }

            }

            .order-row-buy {
                width: 100%;
                display: flex;
                justify-content: space-between;
                background-repeat: no-repeat;
                background-size: 0% 100%;

                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 28rpx;
                position: relative;
                transition: all 0.3s ease-in-out;
                /* 添加整体过渡动画 */

                .bg {
                    position: absolute;
                    right: 0;
                    background: rgba(48, 193, 71, .2);
                    transition: width 0.3s ease;
                    height: 100%;
                }

                .text {
                    width: 100% !important;
                    display: flex;
                    align-items: center;
                    padding: 10rpx 0;
                    justify-content: space-between;
                    margin: 0 8rpx;
                    height: 100%;

                    .price {
                        color: #30C147;
                        transition: all 0.2s ease-in-out;

                        &.highlight {
                            background-color: rgba(48, 193, 71, 0.3);
                            transform: scale(1.05);
                        }
                    }

                    .amount {
                        color: #000;
                        transition: all 0.2s ease-in-out;

                        &.highlight {
                            background-color: rgba(255, 255, 255, 0.2);
                            transform: scale(1.05);
                        }
                    }
                }

            }

        }


    }

    .middle-price {
        margin: 9rpx 0 9rpx 0rpx;
        display: flex;
        justify-content: space-between;

        .markPrice {
            font-family: Gilroy-Medium;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 40rpx;
            color: #FF82A3;
        }

        .newlatest {
            font-family: Gilroy-Medium;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 40rpx;
            color: #A3A3A3;
        }

        .icon {
            font-size: 24rpx;
            background-color: #F5F5F5;
            width: 92rpx;
            height: 48rpx;
            border-radius: 48rpx;
            text-align: center;
            line-height: 48rpx;
            color: rgba(0, 0, 0, 1);
        }
    }

    .dropdown-wrapper {
        margin-top: 16rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .dropdown-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #F6F6F6;
            border-radius: 8rpx;
            width: 184rpx;
            height: 48rpx;
            padding: 0 13rpx 0 16rpx;

            .dropdown-value {
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
            }

            .dropdown-arrow {
                width: 17rpx;
                height: 11rpx;
                transition: transform 0.3s ease;

                &.rotated {
                    transform: rotate(180deg);
                }
            }
        }

        .depth-bars {
            display: flex;
            align-items: center;
            gap: 4rpx;


            .left {
                display: flex;
                flex-direction: column;
                gap: 6rpx;

                .red {
                    width: 7rpx;
                    height: 13rpx;
                    background: #FF0000CC;
                }

                .green {
                    width: 7rpx;
                    height: 13rpx;
                    background: #30C147;
                }
            }

            .right {
                gap: 6rpx;
                display: flex;
                flex-direction: column;
                align-items: center;

                .bar {
                    width: 32rpx;
                    height: 7rpx;
                    background: #D9D9D9;
                }
            }


        }
    }
}



.app-container {
    background-color: #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20rpx;
    box-sizing: border-box;
}

.segmented-progress-bar-container {
    width: 100%;
    max-width: 254rpx;
    font-family: Gilroy;
}

.segmented-progress-bar {
    display: flex;
    justify-content: space-between;
    /* Added */
    width: 100%;
    height: 48rpx;
    box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
    border-radius: 8rpx;
    overflow: hidden;
    background-color: white;
    position: relative;
}

.segment {
    height: 100%;
    display: flex;
    align-items: center;
    position: relative;
    box-sizing: border-box;
    transition: flex-basis 0.4s ease-in-out;
    // min-width: 80rpx;
}

.segment.green-segment {
    min-width: 100rpx;
    background-color: #D7F0D8;
    color: #3E8E41;
    border-top-left-radius: 8rpx;
    border-bottom-left-radius: 8rpx;
    clip-path: polygon(0 0,
            calc(100% - 1rpx) 0,
            /* Changed 2rpx to 1rpx */
            calc(100% - 14rpx - 1rpx) 100%,
            /* Changed 2rpx to 1rpx (total 15rpx) */
            0 100%);
    // transform: skewY(15deg);
    /* Changed from 26rpx (10 + 14 + 1) */
    z-index: 1;
}

.segment.red-segment-bg {
    background-color: #FADBD8;
    clip-path: polygon(14rpx 0, 100% 0, 100% 100%, 0 100%);
    margin-left: -13rpx;
    /* Changed from -12rpx (-14 + 1) */
    z-index: 1;
    min-width: 100rpx;
}

.red-content-fixed {
    position: absolute;
    right: 0rpx;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 2;
    pointer-events: none;
    min-width: 46rpx;
}

.char-box {
    width: 46rpx;
    height: 46rpx;
    border-radius: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 700;
    flex-shrink: 0;
}

.green-segment .char-box {
    color: #30C147;
    margin-right: 4rpx;
    border: 1px solid #30C147;
}

.red-char-box-fixed {
    width: 46rpx;
    height: 46rpx;
    border-radius: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 700;
    flex-shrink: 0;
    color: #E36666;
    border: 1px solid #E36666;
}

.percentage {
    font-size: 20rpx;
    font-weight: 700;
    white-space: nowrap;
}

.red-percentage-fixed {
    font-size: 20rpx;
    font-weight: 700;
    white-space: nowrap;
    color: #C0392B;
    margin-right: 4rpx;
}
</style>