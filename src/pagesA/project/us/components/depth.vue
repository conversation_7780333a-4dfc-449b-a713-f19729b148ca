<template>
    <view class="order-book">
        <!-- 卖盘 -->
        <view class="order-side">
            <view class="order-title">
                <view class="priceamount">
                    <text>Price</text>
                </view>
                <view class="priceamount">
                    <text>Quantity</text>
                </view>

            </view>
            <view class="order-list">
                <view v-for="(item, index) in sellOrders.slice(0, 5)" :key="'sell-' + index" class="order-row">
                    <view class="bg" :style="{ width: `${item.percent}%` }"></view>
                    <view class="text">
                        <text class="price">{{ formatThousand(item.price) }}</text>
                        <text class="amount">{{ item.volume }}</text>
                    </view>
                </view>

            </view>
        </view>

        <view class="middle-price">
            <view class="">
                <view class="markPrice"> {{ formatThousand(62500.00) }} </view>
                <view class="newlatest">{{ formatThousand(62500.00) }} </view>
            </view>
        </view>
        <!-- 买盘 -->
        <view class="order-side">
            <view class="order-list">
                <view v-for="(item, index) in buyOrders.slice(0, 5)" :key="'sell-' + index" class="order-row-buy">
                    <view class="bg" :style="{ width: `${item.percent}%` }"></view>
                    <view class="text">
                        <text class="price">{{ formatThousand(item.price) }}</text>
                        <text class="amount">{{ item.volume }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 盘口只显示买票或者卖盘 -->
        <view class="segmented-progress-bar-container" style="margin:10rpx 0rpx;">
            <view class="segmented-progress-bar">
                <view class="segment green-segment" :style="greenSegmentStyle">
                    <view class="char-box">B</view>
                    <text class="percentage">{{ greenPercentageText }}</text>
                </view>
                <view class="segment red-segment-bg" :style="redSegmentStyle"></view>
                <view class="red-content-fixed">
                    <text class="percentage red-percentage-fixed">{{ redPercentageText }}</text>
                    <view class="char-box red-char-box-fixed">S</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            isOpen: false,
            orders: [
                { price: '62503.1', amount: '0.003', type: 'sell', percent: 60 },
                { price: '62503.0', amount: '0.003', type: 'sell', percent: 45 },
                { price: '62502.9', amount: '0.003', type: 'sell', percent: 30 },
                { price: '62502.9', amount: '0.003', type: 'sell', percent: 20 },
                { price: '62502.9', amount: '0.003', type: 'sell', percent: 10 },

                { price: '62500.0', amount: '0.003', type: 'buy', percent: 15 },
                { price: '62499.8', amount: '0.003', type: 'buy', percent: 25 },
                { price: '62499.8', amount: '0.003', type: 'buy', percent: 35 },
                { price: '62499.8', amount: '0.003', type: 'buy', percent: 45 },
                { price: '62499.8', amount: '0.003', type: 'buy', percent: 55 },
            ],
            intervalId: null,
            currentGreenPercentage: 99, // 初始值
            intervalId2: null,
        }
    },

    mounted() {
        // this.intervalId = setInterval(this.updateBuyPercents, 1000)
        this.intervalId2 = setInterval(this.updateProgressBar, 500);
        this.getusdepthItems()
    },
    computed: {
        sellOrders() {
            return this.orders.filter(o => o.type === 'sell')
        },
        buyOrders() {
            return this.orders.filter(o => o.type === 'buy')
        },
        currentRedPercentage() {
            return 100 - this.currentGreenPercentage;
        },
        displayMetrics() {
            const gP = this.currentGreenPercentage;
            const rP = 100 - gP;
            const minVisual = 20; // 最小视觉宽度百分比调整为20%

            let greenFlex = gP;
            let redFlex = rP;

            if (gP === 0) {
                greenFlex = 0;
                redFlex = 100;
            } else if (rP === 0) { // gP === 100
                redFlex = 0;
                greenFlex = 100;
            } else {
                // Both gP and rP are > 0
                if (gP < minVisual) { // Green is active but too small
                    greenFlex = minVisual;
                    redFlex = 100 - minVisual; // Red must be able to cover this (i.e., rP was originally >= 100 - minVisual)
                } else if (rP < minVisual) { // Red is active but too small
                    redFlex = minVisual;
                    greenFlex = 100 - minVisual; // Green must be able to cover this
                }
                // If neither of the above, it means both gP and rP (if > 0) are >= minVisual.
                // So, their initial values (gP, rP) are correct for flex-basis.
            }
            return {
                greenFlexBasis: greenFlex,
                redFlexBasis: redFlex
            };
        },
        greenSegmentStyle() {
            return {
                flexBasis: `${this.displayMetrics.greenFlexBasis.toFixed(2)}%`,
            };
        },
        redSegmentStyle() {
            return {
                flexBasis: `${this.displayMetrics.redFlexBasis.toFixed(2)}%`,
            };
        },
        greenPercentageText() {
            return `${Math.round(this.currentGreenPercentage)}%`;
        },
        redPercentageText() {
            return `${Math.round(this.currentRedPercentage)}%`;
        },
    },
    methods: {
        async getusdepthItems() {
            let res = await this.$api.usdepthItems({
                symbol: this.$store.state.stockcode
            })
            if (res.code == 200) {
                console.log(res, 'res');

                // {
                //     lang: "zh_CN",
                //     symbol: "00005",
                //     asks: [
                //         { price: 100.08, count: 2, volume: 239 },
                //         { price: 100.28, count: 3, volume: 362 },
                //         { price: 100.48, count: 2, volume: 216 },
                //         { price: 100.68, count: 1, volume: 172 },
                //         { price: 100.88, count: 3, volume: 382 },
                //         { price: 101.08, count: 2, volume: 216 },
                //         { price: 101.28, count: 3, volume: 362 },
                //         { price: 101.48, count: 2, volume: 239 },
                //         { price: 101.68, count: 1, volume: 172 },
                //         { price: 101.88, count: 3, volume: 382 },
                //     ],
                //     bids: [
                //         { price: 99.68, count: 1, volume: 486 },
                //         { price: 99.48, count: 3, volume: 382 },
                //         { price: 99.28, count: 1, volume: 172 },
                //         { price: 99.08, count: 2, volume: 239 },
                //         { price: 98.88, count: 3, volume: 362 },
                //         { price: 98.68, count: 2, volume: 216 },
                //         { price: 98.48, count: 1, volume: 172 },
                //         { price: 98.28, count: 3, volume: 382 },
                //         { price: 98.08, count: 2, volume: 216 },
                //     ],
                //     account: "mock_account"
                // },


                // 给上述对象里面的数组asks里面的数组对象每一个都加一个type:"sell",bids加一个type:"buy",
                // ask数组按照price从大到小排序，bids按照price从小到大排序，再加一个percent字段，每一个对象都有，是当前asks的数组对象中的volume/循环数组最大的否的volume的百分比（整数
                // 然后将asks和bids合并到一个数组中，然后将这个数组赋值给orders

                // 处理数据的函数
                const processDepthData = (data) => {
                    if (!data || !data.asks || !data.bids) return [];

                    // 复制数组避免修改原数据
                    let asks = [...data.asks];
                    let bids = [...data.bids];

                    // 给asks添加type:"sell"并按price从大到小排序
                    asks = asks.map(item => ({ ...item, type: "sell" }))
                        .sort((a, b) => b.price - a.price);

                    // 给bids添加type:"buy"并按price从小到大排序
                    bids = bids.map(item => ({ ...item, type: "buy" }))
                        .sort((a, b) => a.price - b.price);

                    // 合并所有数据找出最大volume
                    const allItems = [...asks, ...bids];
                    const maxVolume = Math.max(...allItems.map(item => item.volume));

                    // 给每个对象添加percent字段
                    asks = asks.map(item => ({
                        ...item,
                        percent: Math.round((item.volume / maxVolume) * 100)
                    }));

                    bids = bids.map(item => ({
                        ...item,
                        percent: Math.round((item.volume / maxVolume) * 100)
                    }));

                    // 合并asks和bids数组
                    return [...asks, ...bids];
                };

                // 使用示例数据处理
                const mockData = {
                    lang: "zh_CN",
                    symbol: "00005",
                    asks: [
                        { price: 100.08, count: 2, volume: 239 },
                        { price: 100.28, count: 3, volume: 362 },
                        { price: 100.48, count: 2, volume: 216 },
                        { price: 100.68, count: 1, volume: 172 },
                        { price: 100.88, count: 3, volume: 382 },
                        { price: 101.08, count: 2, volume: 216 },
                        { price: 101.28, count: 3, volume: 362 },
                        { price: 101.48, count: 2, volume: 239 },
                        { price: 101.68, count: 1, volume: 172 },
                        { price: 101.88, count: 3, volume: 382 },
                    ],
                    bids: [
                        { price: 99.68, count: 1, volume: 486 },
                        { price: 99.48, count: 3, volume: 382 },
                        { price: 99.28, count: 1, volume: 172 },
                        { price: 99.08, count: 2, volume: 239 },
                        { price: 98.88, count: 3, volume: 362 },
                        { price: 98.68, count: 2, volume: 216 },
                        { price: 98.48, count: 1, volume: 172 },
                        { price: 98.28, count: 3, volume: 382 },
                        { price: 98.08, count: 2, volume: 216 },
                    ],
                    account: "mock_account"
                };

                // 处理数据并赋值给orders
                this.orders = processDepthData(mockData);
            }
        },
        toggle() {
            this.isOpen = !this.isOpen
        },
        formatThousand(val) {
            if (isNaN(val)) return val
            let value = Number(val).toFixed(2)
            return parseFloat(value).toLocaleString('en-US')
        },
        updateBuyPercents() {
            this.orders = this.orders.map(order => {
                const variation = Math.floor(Math.random() * 50) + 50 // 50 ~ 99
                return {
                    ...order,
                    percent: variation
                }
                return order
            })
            // this.orders = order
        },
        updateProgressBar() {
            const change = (Math.random() - 0.5) * 50;
            let newGreenPercentage = this.currentGreenPercentage + change;
            this.currentGreenPercentage = Math.max(0, Math.min(100, newGreenPercentage));
        },
    }
}
</script>

<style lang="scss" scoped>
.order-book {
    /* // flex: 1; */
    width: 200rpx;
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .rate {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 20rpx;
        line-height: 28rpx;
        color: rgba(0, 0, 0, .5);

        .name {
            width: fit-content;
            padding-bottom: 4rpx;
            border-bottom: 1rpx dashed #808080
        }

        .rate-value {
            margin-top: 8rpx;
        }
    }

    .order-side {
        .order-title {
            margin: 18rpx 0 12rpx 0;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .priceamount {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;

                text {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    /* // line-height: 28rpx; */
                    color: rgba(0, 0, 0, .5);
                }
            }
        }

        .order-list {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-end;

            .order-row {
                width: 100%;
                display: flex;
                justify-content: space-between;
                /* // background-image: linear-gradient(to right, rgba(22, 199, 132, 0.2), transparent); */
                background-repeat: no-repeat;
                background-size: 0% 100%;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 40rpx;
                height: 40rpx;
                position: relative;

                .bg {
                    position: absolute;
                    right: 0;
                    background: rgba(255, 130, 163, .2);
                    transition: width 0.3s ease;
                    height: 100%;
                }

                .text {
                    width: 100% !important;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin: 0 8rpx;
                    padding: 9rpx 0;

                    .price {

                        color: #FF82A3;
                    }

                    .amount {

                        color: #000;
                    }
                }

            }

            .order-row-buy {
                width: 100%;
                display: flex;
                justify-content: space-between;
                background-repeat: no-repeat;
                background-size: 0% 100%;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 28rpx;
                position: relative;

                .bg {
                    position: absolute;
                    right: 0;
                    background: rgba(48, 193, 71, .2);
                    transition: width 0.3s ease;
                    height: 100%;
                }

                .text {
                    width: 100% !important;
                    display: flex;
                    align-items: center;
                    padding: 10rpx 0;
                    justify-content: space-between;
                    margin: 0 8rpx;
                    height: 100%;

                    .price {

                        color: #30C147;
                    }

                    .amount {

                        color: #000;
                    }
                }

            }

        }


    }

    .middle-price {
        margin: 9rpx 0 9rpx 0rpx;
        display: flex;
        justify-content: space-between;

        .markPrice {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 40rpx;
            color: #FF82A3;
        }

        .newlatest {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 40rpx;
            color: #A3A3A3;
        }

        .icon {
            font-size: 24rpx;
            background-color: #F5F5F5;
            width: 92rpx;
            height: 48rpx;
            border-radius: 48rpx;
            text-align: center;
            line-height: 48rpx;
            color: rgba(0, 0, 0, 1);
        }
    }

    .dropdown-wrapper {
        margin-top: 16rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .dropdown-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #F6F6F6;
            border-radius: 8rpx;
            width: 184rpx;
            height: 48rpx;
            padding: 0 13rpx 0 16rpx;

            .dropdown-value {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
            }

            .dropdown-arrow {
                width: 17rpx;
                height: 11rpx;
                transition: transform 0.3s ease;

                &.rotated {
                    transform: rotate(180deg);
                }
            }
        }

        .depth-bars {
            display: flex;
            align-items: center;
            gap: 4rpx;


            .left {
                display: flex;
                flex-direction: column;
                gap: 6rpx;

                .red {
                    width: 7rpx;
                    height: 13rpx;
                    background: #FF0000CC;
                }

                .green {
                    width: 7rpx;
                    height: 13rpx;
                    background: #30C147;
                }
            }

            .right {
                gap: 6rpx;
                display: flex;
                flex-direction: column;
                align-items: center;

                .bar {
                    width: 32rpx;
                    height: 7rpx;
                    background: #D9D9D9;
                }
            }


        }
    }
}



.app-container {
    background-color: #f0f0f0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20rpx;
    box-sizing: border-box;
}

.segmented-progress-bar-container {
    width: 100%;
    max-width: 254rpx;
    font-family: Gilroy;
}

.segmented-progress-bar {
    display: flex;
    justify-content: space-between;
    /* Added */
    width: 100%;
    height: 48rpx;
    box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
    border-radius: 8rpx;
    overflow: hidden;
    background-color: white;
    position: relative;
}

.segment {
    height: 100%;
    display: flex;
    align-items: center;
    position: relative;
    box-sizing: border-box;
    transition: flex-basis 0.4s ease-in-out;
    // min-width: 80rpx;
}

.segment.green-segment {
    min-width: 100rpx;
    background-color: #D7F0D8;
    color: #3E8E41;
    border-top-left-radius: 8rpx;
    border-bottom-left-radius: 8rpx;
    clip-path: polygon(0 0,
            calc(100% - 1rpx) 0,
            /* Changed 2rpx to 1rpx */
            calc(100% - 14rpx - 1rpx) 100%,
            /* Changed 2rpx to 1rpx (total 15rpx) */
            0 100%);
    // transform: skewY(15deg);
    /* Changed from 26rpx (10 + 14 + 1) */
    z-index: 1;
}

.segment.red-segment-bg {
    background-color: #FADBD8;
    clip-path: polygon(14rpx 0, 100% 0, 100% 100%, 0 100%);
    margin-left: -13rpx;
    /* Changed from -12rpx (-14 + 1) */
    z-index: 1;
    min-width: 100rpx;
}

.red-content-fixed {
    position: absolute;
    right: 0rpx;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    z-index: 2;
    pointer-events: none;
    min-width: 46rpx;
}

.char-box {
    width: 46rpx;
    height: 46rpx;
    border-radius: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 700;
    flex-shrink: 0;
}

.green-segment .char-box {
    color: #30C147;
    margin-right: 4rpx;
    border: 1px solid #30C147;
}

.red-char-box-fixed {
    width: 46rpx;
    height: 46rpx;
    border-radius: 6rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 700;
    flex-shrink: 0;
    color: #E36666;
    border: 1px solid #E36666;
}

.percentage {
    font-size: 20rpx;
    font-weight: 700;
    white-space: nowrap;
}

.red-percentage-fixed {
    font-size: 20rpx;
    font-weight: 700;
    white-space: nowrap;
    color: #C0392B;
    margin-right: 4rpx;
}
</style>