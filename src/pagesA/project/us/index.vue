<template>
    <view class="page">
        <view class="barHeight"></view>

        <view class="tabber">
            <u-tabs bg-color="transparent" name="cate_name" :font-size="32" :list="list" :is-scroll="false"
                :item-width="100" :active-item-style="itemStyle" inactive-color="rgba(0, 0, 0, 0.4)"
                active-color="#000000" :current="current" @change="change"></u-tabs>
        </view>
        <view class="us_apple">
            <view class="us_apple_item">
                <view class="us_apple_item_name" @click="drawerShow = true">
                    <view class="us_apple_item_name_title">
                        {{ $store.state.stockcode }}
                        {{ $store.state.stockname }}
                    </view>
                    <view class="us_apple_item_name_tag">
                        <image src="https://pro-oss.pinkwallet.com/image/1380576011052736512.png" mode="widthFix">
                        </image>
                    </view>
                </view>
                <view class="rise">
                    <view class="left">
                        <view>最新:<text v-if="realtimeQuoteData.latestPrice">{{ realtimeQuoteData.latestPrice }}</text>
                        </view>
                        <view>额：{{ formatAmount(turnoverNamount.amount) || '--' }}</view>
                        <view>换：{{ turnoverNamount.turnover || '--' }}</view>
                        <view @click="goDetails">行情></view>
                    </view>
                    <view class="right">
                        收起
                    </view>
                </view>
            </view>
        </view>

        <buySellOrder :current="current" ref="buySellOrder" />
        <position :current="current" ref="position"></position>

        <!-- stock List -->
        <marketDrawerPopup :show.sync="drawerShow" :current="current" />

    </view>
</template>

<script>
import marketDrawerPopup from "./components/marketDrawer"
import buySellOrder from './components/buySellOrder'
import position from "./components/position"
import store from "@/store"
import { startSocketus, onSocketOpenus } from "./websockets"
export default {
    components: {
        buySellOrder,
        position,
        marketDrawerPopup
    },
    data() {
        return {
            turnoverNamount: {
                amount: 0,
                turnover: 0,
            },
            realtimeQuoteData: {
                latestPrice: ""
            },
            drawerShow: false,
            list: [{
                cate_name: '美股'
            }, {
                cate_name: '港股'
            }],
            current: 0,
            itemStyle: {
                'font-size': '32rpx',
                'color': '#000',
            },
            stockinfomation: {}

        }
    },
    mounted() {
        this.getrealtimeQuote()
        this.getstockmarketstatus()
        // this.getstockInfo()
    },
    watch: {
        current(val) {
            if (val == 1) {
                store.commit("changeStockcode", "02312")
                store.commit("changeStockname", "中国金融租赁")
            } else {
                store.commit("changeStockcode", "ATER")
                store.commit("changeStockname", "Aterian Inc")
            }
        },
        "$store.state.stockcode"(val) {
            console.log(val);
            // store.commit("changeStockcode", item.symbol)
            // store.commit("changeStockname", item.name)
            onSocketOpenus()
            startSocketus()
            this.getrealtimeQuote()
            this.getstockmarketstatus()
            this.$refs.buySellOrder.getdepth()
            this.$refs.buySellOrder.getIndexData()
            this.$refs.buySellOrder.getstockInfo()

        },
        "$store.state.hqbdList"(val) {
            console.log(val);
            // val为对象，先判断val是否为空，然后再赋值
            if (val && typeof val === 'object' && Object.keys(val).length > 0) {
                this.turnoverNamount.amount = val.amount;
                this.turnoverNamount.turnover = val.turnoverRate;
            }
        }
    },
    methods: {

        async getstockmarketstatus() {
            let res = await this.$api.stockmarketstatus({
                market: this.current == 0 ? 'US' : 'HK'
            })
            if (res.code == 200 && res.result.length > 0) {
                console.log(res, 'res');
                //                 [
                //     {
                //         "lang": null,
                //         "market": "US",
                //         "status": "CLOSING",
                //         "marketStatus": "已收盘",
                //         "openTime": "06-26 09:30:00 EDT",
                //         "account": null
                //     }
                // ]
                uni.showToast({
                    title: res.result[0].marketStatus,
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        // 获取顶部数据
        async getrealtimeQuote() {
            let res = await this.$api.realtimeQuote({
                symbol: this.$store.state.stockcode
            })
            if (res.code == 200 && res.result.length > 0) {
                console.log(res, 'res');
                this.realtimeQuoteData = res.result[0];
                store.commit("changeUslatestPrice", res.result[0].latestPrice);

            }
        },
        // async getrealtimeQuote() {
        //     let res = await this.$api.realtimeQuote({
        //         symbol: this.$store.state.stockcode
        //     })
        //     if (res.code == 200) {
        //         console.log(res, 'res');
        //     }
        // },
        socketoff() {
            console.log('socketoff');
            stopSocketus()
        },
        socketon() {
            console.log('socketon');
            startSocketus()
            this.$refs.buySellOrder.getdepth()
        },
        formatAmount(amount) {
            if (!amount || isNaN(amount)) return '--';
            const num = Number(amount);

            if (num >= 1000000000) {
                // 十亿以上显示为 B
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                // 百万以上显示为 M
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                // 千以上显示为 K
                return (num / 1000).toFixed(1) + 'K';
            } else {
                // 小于千的直接显示
                return num.toFixed(0);
            }
        },
        change(index) {
            this.current = index
        },
        goDetails() {
            this.$Router.push({
                name: 'usDetails',
                params: {
                    market: this.current == 0 ? 'US' : 'HK'
                }
            })
        }
    }
} 
</script>

<style scoped lang="scss">
.page {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 10rpx 30rpx;

    .tabber {
        width: 300rpx;
        height: 80rpx;
        background-color: #fff;
    }

    .us_apple {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 32rpx;
        font-family: 'PingFang SC';

        .us_apple_item {
            width: 100%;

            .us_apple_item_name {
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .us_apple_item_name_title {
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #000;
                }

                .us_apple_item_name_tag {
                    font-size: 24rpx;
                    color: #000;
                    height: 48rpx;
                    line-height: 48rpx;
                    padding: 0 20rpx;
                    border-radius: 24rpx;
                    font-weight: 600;

                    image {
                        width: 24rpx;
                        height: 24rpx;
                    }
                }
            }

            .rise {
                font-size: 24rpx;
                height: 48rpx;
                line-height: 40rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;

                .left {
                    display: flex;
                    justify-content: space-between;

                    >view {
                        color: #000;
                        margin-right: 24rpx;
                        min-width: 120rpx;
                    }
                }

                .right {
                    color: #FF82A3;
                }
            }
        }

        .right_icon {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10rpx;

            image {
                width: 72rpx;
                height: 72rpx;
            }
        }
    }
}
</style>