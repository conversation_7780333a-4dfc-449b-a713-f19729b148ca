import Pako from "pako";
import store from "@/store";
let socketUs = null;
export function startSocketus() {
  console.log("开始连接");
  let stocksymbol = store.state.stockcode;

  socketUs = uni.connectSocket({
    // market_ticker  24小时涨跌幅  channel

    // #ifdef H5
    url: process.env.VUE_APP_WS_API_URL_WALLET,
    // #endif

    // #ifdef APP
    url: getApp().globalData.socket_wallet_Api,
    // #endif

    success: (res) => {
      console.log("连上了us");
    },
  });

  socketUs.onOpen(() => {
    // console.log(res, "打开的消息ususus");
    let stockIndex = {
      event: "sub",
      params: { channel: `stock-jbhq-${stocksymbol}`, cb_id: "" },
    };
    let stockIndexMsg = JSON.stringify(stockIndex);
    socketUs.send({
      data: stockIndexMsg,
      complete(res) {
        console.log("stockIndex", res);
      },
    });

    // let stockKLine = {
    //   event: "sub",
    //   params: { channel: `stock-k-line-${stocksymbol}`, cb_id: "" },
    // };
    // let stockLinkMsg = JSON.stringify(stockKLine);
    // socketUs.send({
    //   data: stockLinkMsg,
    //   complete(res) {
    //     console.log("stockLinkMsg", res);
    //   },
    // });

    let stockhqbd = {
      event: "sub",
      params: { channel: `stock-hqbd-${stocksymbol}`, cb_id: "" },
    };
    let stockhqbdMsg = JSON.stringify(stockhqbd);
    socketUs.send({
      data: stockhqbdMsg,
      complete(res) {
        console.log("stockhqbdMsg", res);
      },
    });

    let stocksdhq = {
      event: "sub",
      params: { channel: `stock-sdhq-${stocksymbol}`, cb_id: "" },
    };
    let stocksdhqMsg = JSON.stringify(stocksdhq);
    socketUs.send({
      data: stocksdhqMsg,
      complete(res) {
        console.log("stocksdhqMsg", res);
      },
    });

    let stockzbhq = {
      event: "sub",
      params: { channel: `stock-zbhq-${stocksymbol}`, cb_id: "" },
    };
    let stockzbhqMsg = JSON.stringify(stockzbhq);
    socketUs.send({
      data: stockzbhqMsg,
      complete(res) {
        console.log("stockzbhqMsg", res);
      },
    });

    let stockzyjg = {
      event: "sub",
      params: { channel: `stock-zyjg-${stocksymbol}`, cb_id: "" },
    };
    let stockzyjgMsg = JSON.stringify(stockzyjg);
    socketUs.send({
      data: stockzyjgMsg,
      complete(res) {
        console.log("stockzyjgMsg", res);
      },
    });



    let uid = uni.getStorageSync("uid");
    let uidSub = {
      event: "sub",
      params: {
        channel: `user_setting_${uid}`,
        cb_id: "",
      },
    };
    let msgUid = JSON.stringify(uidSub);

    socketUs.send({
      data: msgUid,
      complete(res) {
        console.log("message", res);
      },
    });


  });

  socketUs.onMessage((event) => {
    var uint8array = new Uint8Array(event.data);
    const output = Pako.inflate(uint8array, {
      to: "string",
    });

    let res = JSON.parse(output);
    console.log(res, "美股channelsubscribe");

    // 行情榜单
    if (res.type == "hqbd") {
      if (res?.data && res.data.length > 0) {
        try {
          const parsedData = JSON.parse(res.data);
          store.commit("changehqbdList", parsedData);
          // console.log("hqbd", parsedData);
        } catch (error) {
          console.error("解析hqbd数据失败:", error, res.data);
        }
      }
    }

    // 盘口
    if (res.type == "sdhq") {
      if (res?.data && res.data.length > 0) {
        try {
          const parsedData = JSON.parse(res.data);
          store.commit("changeUsDepth", parsedData);
          // console.log("sdhq", parsedData);
        } catch (error) {
          console.error("解析sdhq数据失败:", error, res.data);
        }
      }
    }
  });

  uni.onSocketError(function (res) {
    uni.connectSocket({
      // url: 'wss://ws.okx.com:8443/ws/v5/public',
      // #ifdef H5
      url: process.env.VUE_APP_WS_API_URL,
      // #endif
      // #ifdef APP
      url: getApp().globalData.socketApi,
      // #endif
      success: (res) => {},
    });
  });
}

let socketTime = []; //这个时间用来记录第一次触发的时间
let timeIndex = -1; //记录时间索引 第一次收到消息是0，第二次收到小时就+1
function calculationTime() {
  timeIndex += 1;

  socketTime[timeIndex] = new Date().getTime();
  if (socketTime.length >= 2) {
    if (socketTime[timeIndex] / 1000 - socketTime[timeIndex - 1] / 1000 > 5) {
      uni.closeSocket();
      uni.onSocketClose(function (res) {
        console.log("WebSocket 已关闭！");
        uni.connectSocket({
          // url: 'wss://ws.okx.com:8443/ws/v5/public',
          // #ifdef H5
          url: process.env.VUE_APP_WS_API_URL_WALLET,
          // #endif
          // #ifdef APP
          url: getApp().globalData.socket_wallet_Api,
          // #endif
          success: (res) => {
            console.log("连接", res);
          },
        });
      });
    }
  }

  if (timeIndex == 200) {
    socketTime.length = 0;
    timeIndex = -1;
  }
}

export function onSocketOpenus() {
  uni.closeSocket();
}
