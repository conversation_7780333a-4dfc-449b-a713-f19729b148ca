<template>
    <view class="page-container">
        <!-- 返回按钮 -->
        <u-navbar :border-bottom="false" :title='$t("Security.title")'>
        </u-navbar>

        <view class="text">
            <view class="title">Account Login and Recovery</view>
            <view class="content">Manage your passwords, login preferences, and account recovery methods.</view>
        </view>


        <view class="security-list">
            <view class="security-item" @click="nav_to(item.url)" v-for="(item, index) in list" :key="index">
                <view class="left">
                    <image class="icon" :src="item.icon" mode="aspectFit" />
                    <text class="label">{{ item.label }}</text>
                </view>
                <image class="arrow" src="https://pro-oss.pinkwallet.com/image/1369713703967612928.png"
                    mode="aspectFit" />
            </view>
        </view>

    </view>
</template>

<script>
export default {
    data() {
        return {
            list: [
                {
                    label: 'Facial Recognition',
                    icon: "https://pro-oss.pinkwallet.com/image/1369712815249121280.png",
                    url: "facial"
                },
                {
                    label: '2FA（Google Authenticator/SMS/WhatsAPP）',
                    icon: "https://pro-oss.pinkwallet.com/image/1369712941573169152.png",
                    url: "2Step"
                },
                {
                    label: 'Change Password',
                    icon: "https://pro-oss.pinkwallet.com/image/1369713038147018752.png",
                    url: "Security"
                }
            ]
        }
    },
    onLoad() {

        uni.setNavigationBarTitle({
            title: this.$t("Security.title") // 切换语言后重新设置标题
        })


    },
    methods: {
        nav_to(e) {

            this.$Router.push({
                name: e
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.page-container {
    .text {
        margin-top: 35rpx;
        padding: 35rpx 32rpx 24rpx 32rpx;

        .title {
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 34rpx;
            color: #000;
        }

        .content {
            margin-top: 12rpx;
            font-family: Gilroy;
            font-weight: 500;
            font-size: 24rpx;
            line-height: 28rpx;
            color: rgba(143, 143, 143, 1);
        }
    }

    .security-list {
        margin: 0 32rpx;
        border-radius: 24rpx;
        background-color: rgba(223, 223, 223, .1);
        overflow: hidden;
        // box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
        border: 1.5px solid rgba(0, 0, 0, .1);

        .security-item {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 28rpx 32rpx;
            border-bottom: 1rpx solid rgba(51, 51, 51, .1);

            &:last-child {
                border-bottom: none;
            }

            .left {
                display: flex;
                flex-direction: row;
                align-items: center;

                .icon {
                    width: 32rpx;
                    height: 32rpx;
                    margin-right: 18rpx;
                }

                .label {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 34rpx;
                    color: #000;
                }
            }

            .arrow {
                width: 20rpx;
                height: 20rpx;
            }
        }
    }
}
</style>