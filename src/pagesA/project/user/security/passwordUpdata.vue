<template>
    <view class="body">
        <u-navbar :border-bottom="false" :title="title" :customBack="back">
        </u-navbar>
        <view class="container">
            <view class="top_desc">
                为保障您的资产安全，在您更改密码后，该账户可能会禁用支付服务、提币及C2C卖币24小时
            </view>
            <view class="form_item">
                <view class="item_title">新密码</view>
                <view class="input_wrapper">
                    <u-input v-model="form.newPassword" :type="form.newPasswordVisible ? 'text' : 'password'"
                        placeholder="请输入新密码" @input="validatePassword"></u-input>
                </view>
            </view>
            <view class="rules_container">
                <view class="rule_item" v-for="(rule, index) in rules" :key="index"
                    :class="{'satisfied': rule.satisfied}">
                    <u-icon :name="rule.satisfied ? 'checkmark' : 'checkmark'" size="28"
                        :color="rule.satisfied ? '#FF82A3' : '#e0e0e0'"></u-icon>
                    <text class="rule_text">{{ rule.text }}</text>
                </view>
            </view>
            <view class="form_item">
                <view class="item_title">确认密码</view>
                <view class="input_wrapper">
                    <u-input v-model="form.confirmPassword" :type="form.confirmPasswordVisible ? 'text' : 'password'"
                        placeholder="请再次输入新密码" :password-icon="true"></u-input>
                </view>
            </view>
        </view>
        <view class="bottom_button">
            <u-button @click="submit" :custom-style="customStyle">提交</u-button>
        </view>

        <!-- 成功提示弹窗 -->
        <u-popup v-model="showSuccessPopup" mode="bottom" border-radius="20" :min-height="500" :closeable="true"
            @close="closePopup">
            <view class="popup_content">
                <view class="popup_title">密码更改成功</view>
                <view class="popup_desc">您的登录密码已修改，请重新登录账户。密码修改成功后，<text>24</text>小时内禁止提币</view>
                <view class="popup_button">
                    <u-button @click="closePopup" :custom-style="customStyle">好的</u-button>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                title: '更改密码',
                form: {
                    newPassword: '',
                    newPasswordVisible: false,
                    confirmPassword: '',
                    confirmPasswordVisible: false,
                },
                rules: [
                    { text: '8-20个字符', satisfied: false, key: 'length' },
                    { text: '至少1个数字', satisfied: false, key: 'number' },
                    { text: '至少1个大写字母', satisfied: false, key: 'uppercase' }
                ],
                showSuccessPopup: false,
                customStyle: {
                    backgroundColor: '#FF82A3',
                    color: '#FFFFFF',
                    borderRadius: '48rpx',
                    height: '96rpx',
                    fontSize: '30rpx'
                }
            }
        },
        methods: {
            back() {
                uni.navigateBack()
            },
            validatePassword(password) {
                this.rules.forEach(rule => {
                    switch (rule.key) {
                        case 'length':
                            rule.satisfied = password.length >= 8 && password.length <= 128;
                            break;
                        case 'number':
                            rule.satisfied = /\d/.test(password);
                            break;
                        case 'uppercase':
                            rule.satisfied = /[A-Z]/.test(password);
                            break;
                    }
                });
            },
            submit() {
                const { newPassword, confirmPassword } = this.form;
                if (!newPassword || !confirmPassword) {
                    return uni.showToast({ title: '请填写所有字段', icon: 'none' });
                }
                if (newPassword !== confirmPassword) {
                    return uni.showToast({ title: '两次输入的新密码不一致', icon: 'none' });
                }

                const allRulesSatisfied = this.rules.every(rule => rule.satisfied);
                if (!allRulesSatisfied) {
                    return uni.showToast({ title: '新密码不符合所有规则', icon: 'none' });
                }

                console.log('密码更改提交:', this.form);
                this.submitEmail()
                
            },
            async submitEmail() {
                let res = await this.$api.changePassword({
                    newPassword: this.form.newPassword,
                    passwordType:'LOGIN',
                    changeType:'update'
                }) 
                if (res.code == 200) {
                    this.showSuccessPopup = true;
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none' 
                    })
                }  
            },
            closePopup() {
                this.showSuccessPopup = false;
                setTimeout(() => {
                   this.$Router.push({
                         name:"security"
                   })
                }, 300);
            }
        }
    }
</script>

<style lang="scss" scoped>
    .body {
        background: #fff;
        min-height: 100vh;
        font-family: PingFang SC;
        display: flex;
        flex-direction: column;
        padding-top: 40rpx;
    }

    ::v-deep .u-input__right-icon__item {
        image {
            width: 32rpx !important;
            height: 32rpx !important;
        }
    }

    .container {
        padding: 30rpx;
        flex: 1;

        .top_desc {
            font-size: 26rpx;
            color: #999;
            line-height: 1.6;
            margin-bottom: 60rpx;
        }

        .form_item {
            margin-bottom: 40rpx;

            .item_title {
                font-size: 32rpx;
                color: #333;
                font-weight: 600;
                margin-bottom: 20rpx;
            }

            .input_wrapper {
                position: relative;
                background-color: #f7f8fa;
                border-radius: 16rpx;

                ::v-deep .u-input {
                    background-color: transparent;
                    padding: 20rpx 24rpx !important;
                }

                .eye_icon {
                    position: absolute;
                    right: 24rpx;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #c0c4cc;
                }
            }
        }

        .rules_container {
            margin-top: -20rpx;
            margin-bottom: 40rpx;

            .rule_item {
                display: flex;
                align-items: center;
                margin-bottom: 20rpx;

                .rule_text {
                    margin-left: 16rpx;
                    font-size: 26rpx;
                    color: #999;
                }

                &.satisfied .rule_text {
                    color: #FF82A3;
                }

                &.satisfied .u-icon {
                    color: #FF82A3 !important;
                }
            }
        }
    }

    .bottom_button {
        padding: 20rpx 30rpx;
        padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
        background-color: #fff;

        .u-button::after {
            border: none;
        }
    }

    .popup_content {
        padding: 40rpx;

        .popup_title {
            font-size: 36rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 40rpx;
            text-align: left;
        }

        .popup_desc {
            font-size: 28rpx;
            color: #666;
            text-align: left;
            line-height: 1.8;
            margin-bottom: 100rpx;

            text {
                color: #FF82A3;
            }
        }

        .popup_button {
            width: 100%;
        }
    }

    ::v-deep .u-close {
        top: 40rpx !important;
        right: 40rpx !important;
    }
</style>