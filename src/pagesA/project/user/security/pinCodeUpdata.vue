<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="title" :customBack="back">
		</u-navbar>
		<view class="container">
			<view class="top_desc">支付PIN码将用来授权支付交易</view>

			<view class="pin_section">
				<view class="title_wrapper">
					<text class="section_title">创建支付PIN码</text>
					<u-icon :name="pinVisible ? 'eye-fill' : 'eye-off'" size="36"
						@click="pinVisible = !pinVisible"></u-icon>
				</view>
			</view>
			<u-message-input v-model="pinCode" :maxlength="6" :dot-fill="!pinVisible" active-color="#FF82A3"
				inactive-color="#000" :font-size="28" width="80rpx" class="message-box" @finish="finish"></u-message-input>
			<view class="pin_section">
				<view class="rules_desc">
					<text>密码不能过于简单，同一个数字不能出现两次以上</text>
					<view>继续即代表接受<text class="link">使用条款</text></view>
				</view>
			</view>


		</view>
		<u-popup v-model="showSuccessPopup" mode="bottom" border-radius="20" :min-height="500" :closeable="true"
            @close="closePopup">
            <view class="popup_content">
                <view class="popup_title">密码更改成功</view>
                <view class="popup_desc">您的登录密码已修改，请重新登录账户。密码修改成功后，<text>24</text>小时内禁止提币</view>
                <view class="popup_button">
                    <u-button @click="nav_to" :custom-style="customStyle">好的</u-button>
                </view>
            </view>
        </u-popup>
		<view class="bottom_button">
			<u-button @click="submit" :custom-style="customStyle" :disabled="pinCode.length !== 6">确认</u-button>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '设置支付PIN码',
				pinCode: '',
				pinVisible: false,
				customStyle: {
					backgroundColor: '#FF82A3',
					color: '#FFFFFF',
					borderRadius: '48rpx',
					height: '96rpx',
					fontSize: '30rpx'
				}
			}
		},
		methods: {
			back() {
				uni.navigateBack();
			},
			finish(e) {
				console.log(e)
				this.pinCode = e
			},
			submit() {
				if (this.pinCode.length !== 6) {
					uni.showToast({ title: '请输入6位PIN码', icon: 'none' });
					return;
				}

				const counts = {};
				for (const digit of this.pinCode) {
					counts[digit] = (counts[digit] || 0) + 1;
				}
				const hasMoreThanTwo = Object.values(counts).some(count => count > 2);

				if (hasMoreThanTwo) {
					uni.showToast({ title: '同一个数字不能出现两次以上', icon: 'none' });
					return;
				}
				this.submitPinCode()

			},
			async submitPinCode() {
				let res = await this.$api.changePassword({
					newPassword: this.pinCode,
					passwordType: 'PAY',
					changeType: 'update'
				})
				if (res.code == 200) {
					console.log('PIN码设置成功:', this.pinCode);
					uni.showToast({
						title: '成功',
						icon: 'success'
					});
					this.showSuccessPopup = true
					
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			},
			nav_to(){
				this.showSuccessPopup = false
				this.$Router.push({
					name: "security",
				})
			}

		}
	}
</script>

<style lang="scss" scoped>
	.body {
		background: #fff;
		min-height: 100vh;
		font-family: PingFang SC;
		display: flex;
		flex-direction: column;
	}

	.container {
		flex: 1;

		.top_desc {
			font-size: 26rpx;
			color: #999;
			text-align: center;
			margin: 40rpx 0 80rpx 0;
		}

		.pin_section {
			padding: 0rpx 68rpx;

			.title_wrapper {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				margin-bottom: 30rpx;

				.section_title {
					font-size: 32rpx;
					color: #333;
					font-weight: 600;
				}

				.u-icon {
					color: #c0c4cc;
					margin-left: 30rpx;
				}
			}

			.rules_desc {
				margin-top: 30rpx;
				font-size: 24rpx;
				color: #999;
				line-height: 1.8;

				.link {
					color: #FF82A3;
					margin-left: 8rpx;
				}
			}
		}
	}

	.bottom_button {
		padding: 20rpx 30rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		background-color: #fff;

		.u-button {
			&[disabled] {
				background-color: #fab3c5 !important;
				color: #fff !important;
			}

			&::after {
				border: none;
			}
		}
	}

	::v-deep .u-char-flex {
		width: 100%;

		.u-box-active u-box {
			background-color: red;
			border: none
		}
	}

	.message-box {
		margin-top: 40rpx;
		color: #ffffff;

		::v-deep .u-box {
			border-radius: 0rpx;
			border: none !important;
			background-color: #f2f2f2;
			border-radius: 18rpx;
			width: 80rpx;
			height: 80rpx !important;
		}

		::v-deep .u-box-active {
			border: 1px solid #FF82A3 !important;
			opacity: 1 !important;
		}

		::v-deep .u-dot {
			font-size: 28rpx;
		}
	}
	.popup_content {
        padding: 40rpx;

        .popup_title {
            font-size: 36rpx;
            font-weight: 600;
            color: #333;
            margin-bottom: 40rpx;
            text-align: left;
        }

        .popup_desc {
            font-size: 28rpx;
            color: #666;
            text-align: left;
            line-height: 1.8;
            margin-bottom: 100rpx;

            text {
                color: #FF82A3;
            }
        }

        .popup_button {
            width: 100%;
        }
    }

    ::v-deep .u-close {
        top: 40rpx !important;
        right: 40rpx !important;
    }
</style>