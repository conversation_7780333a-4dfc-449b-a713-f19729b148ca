<template>
  <view class="body">
    <u-navbar :border-bottom="false" :title="title" :customBack="back">
    </u-navbar>
    <view class="avatar_section_">
      <view class="avatar_box_">
        <u-avatar :src="userInfo.avatar" size="164"></u-avatar>
        <view class="avatar_camera_" @tap="chooseAvatar">
          <u-icon name="https://pro-oss.pinkwallet.com/image/1385287768325316608.png"
            size="48" />
        </view>
      </view>
      <view class="avatar_tip_">头像每30天只能修改一次</view>
    </view>
    <view class="nickname_section_">
      <view class="nickname_label_">昵称</view>
      <view class="nickname_input_box_">
        <input v-model="userInfo.userName" :maxlength="10" placeholder="请输入昵称" border="none" class="nickname_input_" />
        <view class="nickname_count_">{{ userInfo.userName.length }}/10</view>
      </view>
      <view class="nickname_tip_">昵称每30天只能修改1次</view>
    </view>
    <view class="save_btn_box_">
      <u-button type="primary" hover-class="none" class="save_btn_" @tap="save" :custom-style="btnStyle">保存</u-button>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        title: '编辑个人资料',
        avatarUrl: 'https://pro-oss.pinkwallet.com/image/1385271526499639296.png',
        nickname: '',
        btnStyle: {
          width: '100%',
          background: '#ff8eb3',
          color: '#fff',
          borderRadius: '50rpx',
          height: '90rpx',
          fontSize: '32rpx',
        },
        userInfo: {
          userName:''
        }
      }
    },
    onLoad() {
      this.getUserInfos()
    },
    methods: {
      chooseAvatar() {
        // 头像选择逻辑
        uni.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
          success: (res) => {
            this.avatarUrl = res.tempFilePaths[0]
            this.uploadPic()
          }
        })
      },
      uploadPic() { //上传
        let url
        // #ifdef APP
        url = `${getApp().globalData.apiUrl}pinkwallet/appApi/oss/uploadImage`
        // #endif
        // #ifdef H5
        url = `${getApp().globalData.apiUrl}pinkwallet/appApi/oss/uploadImage`
        // #endif
        console.log("url", url)
        uni.uploadFile({
          url,
          filePath: this.avatarUrl,
          header: {
            'Authorization': uni.getStorageSync("token"),
          },
          name: 'file',
          complete: (res) => {
            console.log("res", JSON.parse(res.data))
            this.setUserPicture(JSON.parse(res.data).result)
          }
        });
      },
      save() {
        if (!this.userInfo.userName) {
          uni.$u.toast('请输入昵称')
          return
        }
        this.setUserName()
      },
      back() {
        this.$Router.back()
      },
      async getUserInfos() {
        let res = await this.$api.getUserInfo()
        if (res.code == 200) {
          this.userInfo = res.result
        }

      },
      async setUserPicture(avatar) {
        let res = await this.$api.setUserPicture({
          avatar
        })
        if (res.code == 200) {
          uni.$u.toast('修改成功')
          this.getUserInfos()
        } else {
          uni.$u.toast(res.msg)
        }
      },
      async setUserName() {
        let res = await this.$api.setUserName({
          userName: this.userInfo.userName
        })
        if (res.code == 200) {
          uni.$u.toast('保存成功')
          this.getUserInfos()
        } else {
          uni.$u.toast(res.msg)
        }
      },

    }
  }
</script>

<style lang="scss" scoped>
  .body {
    background: #fff;
    min-height: 100vh;
    padding-bottom: 120rpx;
    font-family: PingFang SC;
  }

  .avatar_section_ {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 32rpx;
  }

  .avatar_box_ {
    position: relative;
    width: 164rpx;
    height: 164rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .avatar_camera_ {
    position: absolute;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .avatar_tip_ {
    color: #bdbdbd;
    font-size: 24rpx;
    margin-top: 24rpx;
    font-family: PingFang SC;
  }

  .nickname_section_ {
    margin: 48rpx 32rpx 0 32rpx;
  }

  .nickname_label_ {
    font-size: 30rpx;
    color: #000;
    margin-bottom: 16rpx;
    font-family: PingFang SC;
  }

  .nickname_input_box_ {
    background: #F7F7F7;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    height: 80rpx;
    position: relative;
    border: none;
  }

  .nickname_input_ {
    flex: 1;
    font-size: 28rpx;
    background: transparent;
    color: #000;
    font-family: PingFang SC;
  }

  .nickname_count_ {
    color: #bdbdbd;
    font-size: 26rpx;
    margin-left: 12rpx;
  }

  .nickname_tip_ {
    color: #bdbdbd;
    font-size: 24rpx;
    margin-top: 24rpx;
    font-family: PingFang SC;
  }

  .save_btn_box_ {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 32rpx;
    padding: 0 32rpx;
    z-index: 10;
  }

  .save_btn_ {
    width: 100%;
    background: #ff8eb3;
    color: #fff;
    border-radius: 50rpx;
    height: 90rpx;
    font-size: 32rpx;
  }
</style>