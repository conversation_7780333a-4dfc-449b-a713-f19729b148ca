<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="$t('realName.center')" :customBack="back"></u-navbar>
		<u-line-progress :percent="20" :show-percent="false" active-color="#F97C9A" inactive-color="#F3F3F3"
			:height="6"></u-line-progress>
		<view class="height_60"></view>
		<CountrySelect :show.sync="show" @select="onCountrySelect" :type="selectType"></CountrySelect>
		<!-- 国籍选择 -->
		<view class="form_block">
			<view class="form_label">{{ $t('realName.residence') }}</view>
			<view @click="showCountrySelect('country')" class="country_cell">
				<image :src="country.icon" class="country_flag" />
				<text class="country_name">{{ country.label }}</text>
				<u-icon slot="right-icon" name="arrow-down" color="#bbb" size="28" />
			</view>
		</view>

		<!-- 姓名输入 -->
		<view class="form_block">
			<view class="form_label">{{ $t('realName.name') }}</view>
			<view class="input_box_view">
				<u-input v-model="name" placeholder="请输入姓名" class="input_box" />
			</view>
		</view>
		<!-- 姓名输入 -->
		<view class="form_block">
			<view class="form_label">{{ $t('realName.idNumber') }}</view>
			<view class="input_box_view">
				<u-input v-model="idNumber" placeholder="请输入身份证号码" class="input_box" />
			</view>
		</view>

		<!-- 出生日期 -->
		<view class="form_block">
			<view class="form_label">{{ $t('realName.birth') }}</view>
			<view class="date_row">
				<view class="date_box" @click="showDatePicker = true">{{ birth.year || '年' }}</view>
				<view class="date_box" @click="showDatePicker = true">{{ birth.month || '月' }}</view>
				<view class="date_box" @click="showDatePicker = true">{{ birth.day || '日' }}</view>
			</view>
		</view>

		<view class="footer">
			<view class="desc">{{ $t('realName.confirmTip') }}</view>
			<view class="btn_box">
				<u-button type="primary" class="next_btn" hover-class="none"
					:custom-style="{background:'#F97C9A',borderRadius:'50rpx'}" @click="submit">继续</u-button>
			</view>
		</view>

		<!-- 出生日期选择器 -->
		<u-picker v-model="showDatePicker" mode="time" :start-year="1900" :end-year="new Date().getFullYear()"
			@confirm="onDateConfirm"></u-picker>

		<u-popup v-model="showAuditPopup" mode="bottom" border-radius="16" :mask-close-able="false" height="600rpx">
			<view class="audit_popup">
				<view class="audit_popup_header">
					<text class="audit_popup_title">{{ $t('realName.audit') }}</text>
					<!-- <u-icon name="close" size="36" color="#bbb" @click="showAuditPopup = false" /> -->
				</view>
				<view class="audit_popup_desc">
					{{ $t('realName.auditDesc') }}
				</view>
				<view class="audit_popup_btn_box">
					<u-button
						type="primary"
						class="audit_popup_btn"
						:custom-style="{background:'#F97C9A',borderRadius:'50rpx'}"
						@click="goHome"
					>前往首页</u-button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import CountrySelect from '@/components/select/CountrySelect.vue'
	export default {
		data() {
			return {
				title: '确认个人信息',
				showCountry: false,
				country: { label: '中国', value: 'CN', icon: 'https://pro-oss.pinkwallet.com/image/1385640135935746048.png' },
				name: '',
				idNumber: '',
				birth: { year: '', month: '', day: '' },
				show: false,
				selectType: 'country',
				showDatePicker: false,
				showAuditPopup: true,
			}
		},
		components: {
			CountrySelect
		},
		methods: {
			back() {
				uni.navigateBack();
			},
			onCountryConfirm(e) {
				this.country = this.countryList[e[0]]
			},
			onDateConfirm(e) {
				console.log(e)
				// e: [year, month, day]
				this.birth.year = e.year
				this.birth.month = e.month
				this.birth.day = e.day
			},
			onCountrySelect(item) {
				this.country = item
				this.show = false
			},
			showCountrySelect(type) {
				console.log(type)
				this.selectType = type
				this.show = true
			},
			goHome() {
				this.showAuditPopup = false
				this.$Router.pushTab({
					name: 'Home',
				})
			},
			async submit(){
				let realNameInfo = uni.getStorageSync('realNameInfo')
				
				// 校验姓名、出生日期、国籍非空
				if (!this.name) {
					uni.$u.toast(this.$t('realName.namePlaceholder'))
					return
				}
				if (!this.birth.year || !this.birth.month || !this.birth.day) {
					uni.$u.toast(this.$t('realName.birthPlaceholder'))
					return
				}
				if (!this.country || !this.country.label) {
					uni.$u.toast(this.$t('realName.nationalityPlaceholder'))
					return
				}

				let query = {
					...realNameInfo,
					nationality: this.country.label,
					name: this.name,
					birthDate: this.birth.year + '-' + this.birth.month + '-' + this.birth.day,
					idNumber: this.idNumber
				}
				let res = await this.$api.kycAdd(query)
				if(res.code==200){
					this.showAuditPopup = true
				}else{
					uni.$u.toast(res.msg)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.height_60{
		height: 60rpx;
	}
	.body {
		background: #fff;
		min-height: 100vh;
		padding: 0 32rpx;
		font-family: PingFang SC;
		padding-top: 30rpx;
	}

	.form_block {
		margin-bottom: 40rpx;

	}

	.form_label {
		font-size: 28rpx;
		font-weight: 500;
		margin-bottom: 12rpx;
	}

	.country_cell {
		background: #fafafa;
		border-radius: 16rpx;
		padding: 0 24rpx;
		min-height: 94rpx;
		display: flex;
		align-items: center;
		height: 94rpx;

	}

	.country_flag {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		margin-right: 16rpx;
	}

	.country_name {
		font-size: 28rpx;
		color: #222;
	}

	.input_box_view {
		background: #fafafa;
		border-radius: 16rpx;
		padding: 0 24rpx;
		font-size: 28rpx;
		height: 94rpx;
		display: flex;
		align-items: center;
	}

	.input_box {
		font-size: 28rpx;
	}

	.date_row {
		display: flex;
		gap: 20rpx;
	}

	.date_box {
		flex: 1;
		background: #fafafa;
		border-radius: 16rpx;
		min-height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #222;
	}

	.desc {
		margin: 60rpx 0 32rpx 0;
		color: #999;
		font-size: 24rpx;
		text-align: left;
	}

	.btn_box {
		margin-bottom: 32rpx;
	}

	.next_btn {
		width: 100%;
		height: 88rpx;
		font-size: 32rpx;
		font-weight: 600;
		background: #F97C9A;
		border-radius: 50rpx;
		color: #fff;
	}
	.footer{
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 0 32rpx;
		
	}	

	.audit_popup {
		background: #fff;
		border-radius: 16rpx;
		padding: 40rpx 32rpx 32rpx 32rpx;
		box-sizing: border-box;
	}
	.audit_popup_header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24rpx;
	}
	.audit_popup_title {
		font-size: 32rpx;
		font-weight: 600;
	}
	.audit_popup_desc {
		font-size: 26rpx;
		color: #444;
		margin-bottom: 48rpx;
		line-height: 1.7;
	}
	.audit_popup_btn_box {
		display: flex;
		justify-content: center;
		position: absolute;
		bottom: 40rpx;
		left: 0;
		right: 0;
		padding: 0 32rpx;
	}
	.audit_popup_btn {
		width: 100%;
		height: 88rpx;
		font-size: 30rpx;
		background: #F97C9A;
		border-radius: 50rpx;
		color: #fff;
	}
</style>