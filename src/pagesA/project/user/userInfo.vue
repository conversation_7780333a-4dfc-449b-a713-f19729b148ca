<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="$t('userInfo.title')" :customBack="back">
		</u-navbar>
		<view class="user_info_wrap" @click="nav_setNamePic">
			<view class="avatar_wrap">
				<image class="avatar"
					:src="userInfo.avatar?userInfo.avatar:'https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385271526499639296.png'">
				</image>
				<view class="edit_icon">
					<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385577020133040128.png"
						size="40"></u-icon>
				</view>
			</view>
			<view class="info" @click="nav_setNamePic">
				<view class="name">{{ userInfo.userName }}</view>
				<view class="uid" @click.stop="copyUid">
					<text>UID: {{ userInfo.uid }}</text>
					<u-icon name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385577329609760768.png"
						custom-prefix="custom-icon" style="margin-left: 10rpx;" color="#999" size="28"></u-icon>
				</view>
			</view>
		</view>

		<u-cell-group :border="false" class="cell_group">
			<u-cell-item :border-bottom="false" :arrow="false">
				<view slot="title" class="cell_title huise">{{ $t('userInfo.registerInfo') }}</view>
				<view slot="right-icon">
					<view class="value">
						<text>{{ userInfo.email }}</text>
						<!-- <u-icon name="eye" color="#999" size="32" style="margin-left: 10rpx;" @click="showEmail"></u-icon> -->
					</view>
				</view>
			</u-cell-item>
			<u-cell-item :border-bottom="false" :arrow="false" @click="nav_realName()">
				<u-icon slot="icon"
					name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385577863439802368.png"
					color="#000" size="32"></u-icon>
				<view slot="title" class="cell_title">{{ $t('userInfo.auth') }}</view>
				<view slot="right-icon" class="auth_btn_wrap">
					<view :class="['auth_btn', status == 0 ? 'auth_btn_gray' : 'auth_btn_green']">
						{{ status == 0 ? (authStatus == 'INIT' ? $t('userInfo.authPending') : $t('userInfo.authUnverified')) : $t('userInfo.authVerified') }}
					</view>
					<u-icon name="arrow-right" size="32" color="#BDBDBD" />
				</view>
			</u-cell-item>
			<u-cell-item :border-bottom="false" :arrow="true" @click="nav_security()">
				<u-icon slot="icon"
					name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385577910046908416.png"
					color="#000" size="32"></u-icon>
				<view slot="title" class="cell_title">{{ $t('userInfo.security') }}</view>
			</u-cell-item>
		</u-cell-group>
	</view>
</template>

<script>
export default {
	data() {
		return {
			title: '基本信息',
			userInfo: {},
			status: 0
		}
	},
	onLoad() {
		this.getUserInfos()
		this.getAuthInfo()
	},
	methods: {
		back() {
			uni.navigateBack();
		},
		copyUid() {
			uni.setClipboardData({
				data: this.userInfo.uid,
				success: () => {
					this.$u.toast('UID已复制');
				}
			});
		},
		nav_realName() {
			console.log(this.status);
			this.$Router.push({
				name: 'realName'
			})
		},
		nav_security() {
			this.$Router.push({
				name: 'security'
			})
		},
		nav_setNamePic() {
			this.$Router.push({
				name: 'setNamePic'
			})
		},
		async getUserInfos() {
			let res = await this.$api.getUserInfo()
			if (res.code == 200) {
				this.userInfo = res.result
				this.qrcodeUrl = res.result.email
			}

		},
		async getAuthInfo() {
			let res = await this.$api.authInfo({})
			if (res.code == 200) {
				this.status = res.result.applyForCf
			}
		},
		onShow(){
			this.getUserInfos()
		},
		onLoad() {
			this.getUserInfos()
			this.getAuthInfo()
		},
		methods: {
			back() {
				uni.navigateBack();
			},
			copyUid() {
				uni.setClipboardData({
					data: this.userInfo.uid,
					success: () => {
						this.$u.toast(this.$t('userInfo.copySuccess'));
					}
				});
			},
			nav_realName() {
				console.log(this.status);
				if (this.status == 0) {
					if(this.authStatus == 'INIT'){
						return
					}else{
						this.$Router.push({
							name: 'realName'
						})
					}
				} else {
					this.$Router.push({
						name: 'realNameHigh'
					})
				}

			},
			nav_security() {
				this.$Router.push({
					name: 'security'
				})
			},
			nav_setNamePic() {
				this.$Router.push({
					name: 'setNamePic'
				})
			},
			async getUserInfos() {
				let res = await this.$api.getUserInfo()
				if (res.code == 200) {
					this.userInfo = res.result
					this.qrcodeUrl = res.result.email
				}

			},
			async getAuthInfo() {
				let res = await this.$api.authInfo({})
				if (res.code == 200) {
					this.status = res.result.applyForCf
					this.authStatus = res.result.status
				}
			}
			
		}
	}
}
</script>

<style lang="scss" scoped>
.body {
	background: #fff;
	min-height: 100vh;
	padding: 0 32rpx;
	font-family: PingFang SC;
}

.user_info_wrap {
	display: flex;
	align-items: center;
	padding: 40rpx 0;

	.avatar_wrap {
		position: relative;

		.avatar {
			width: 120rpx;
			height: 120rpx;
			border-radius: 50%;
		}

		.edit_icon {
			position: absolute;
			bottom: 0;
			right: 0;
			width: 40rpx;
			height: 40rpx;
			border-radius: 8rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}

	.info {
		margin-left: 20rpx;

		.name {
			font-size: 40rpx;
			font-weight: 500;
			color: #17191A;
		}

		.uid {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			color: #B4B7BA;
			margin-top: 16rpx;
		}
	}

	.auth_btn_wrap {
		display: flex;
		align-items: center;

		.auth_btn {
			min-width: 94rpx;
			height: 46rpx;
			line-height: 46rpx;
			border-radius: 6rpx;
			font-size: 20rpx;
			color: #fff;
			text-align: center;
			margin-right: 16rpx;
			padding: 0 24rpx;
			font-weight: 500;
		}

		.auth_btn_gray {
			background: #999899;
		}

		.auth_btn_green {
			background: #08B819;
		}
	}
}
</style>