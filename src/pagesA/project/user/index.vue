<template>
    <view class="service-page">
		<view class="header_view">
			<view class="back" @click="goBack()">
				<image src="https://pro-oss.pinkwallet.com/image/1385268879365332992.png" />
			</view>
			<view class="right">
				<image @click="nav_Notification()" src="https://pro-oss.pinkwallet.com/image/1385269125436760064.png" />
				<image src="https://pro-oss.pinkwallet.com/image/1385269174229098496.png" @click="nav_set()" />
			</view>
		</view>
        
        <view class="user_info_card" @click="nav_userinfo()">
            <view class="user_info_card_left">
                <image class="user_avatar" :src="userInfo.avatar" />
                <view class="user_verified_icon_bg">
                    <image class="user_verified_icon"
                        src="https://pro-oss.pinkwallet.com/image/1385271480274214912.png" />
                </view>
            </view>
            <view class="user_info_card_center">
                <view class="user_name_row">
                    <text class="user_name">{{ userInfo.userName }}</text>
                    <image class="user_name_link"
                        src="https://pro-oss.pinkwallet.com/image/1385272041883131904.png" />
                </view>
                <view class="user_id_row">
                    <text class="user_id">ID: {{userInfo.uid}}</text>
                    <image class="user_id_copy" src="https://pro-oss.pinkwallet.com/image/1385272153057353728.png" @click.stop="copyId(userInfo.uid)" />
                </view>
                <view class="user_tag_row">
                    <view class="user_tag user_tag_normal" v-if="status == 0">普通用户</view>
                    <view class="user_tag user_tag_verified" v-else>已认证</view>
                </view>
            </view>
            <view class="user_info_card_right">
                <image class="user_arrow"
                    src="https://pro-oss.pinkwallet.com/image/1385272273706508288.png"
                    mode="widthFix" />
            </view>
        </view>
        <!-- 推荐功能 -->
        <view class="view_item" v-for="(item, index) in serviceMap" :key="index">
            <view class="grid_title">
                {{ index }}
            </view>
            <view class="grid">
                <view v-for="(item, index) in item" :key="index" class="grid-item" @click="nav_to(item)">
                    <view class="bg flex_all">
                        <image :src="item.icon" class="icon" :style="{ width: item.w + 'rpx' }" mode="widthFix" />
                    </view>
                    <text class="label">{{ item.name }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { tabList, serviceMap } from '../../../utils/serviceData.js'

export default {
    data() {
        return {
            current: 0,
            barStyle: {
                'background': '#FF82A3',
                'width': '38rpx',
                'height': '6rpx',
                'border-radius': '2rpx',
                'bottom': '10rpx',
                'z-index': '1'
            },
            itemStyle: {
                'color': '#000',
                // 'line-height': '40rpx',
                'font-weight': '600',
                'font-size': '28rpx',
                // 'min-width': '120rpx',
                'z-index': '2'
            },
            tabList,
            serviceMap,
            currentTab: 0,
            recommendList: serviceMap['常用功能'].slice(0, 4),
            link: "../../../static/serve.html",
            userInfo: {},
            qrcodeUrl: '',
            status: ''

        }
    },
    onShow(){
        this.getUserInfos()
    },
    onLoad() {
        this.getUserInfos()
        this.getAuthInfo()
    },
    methods: {
        change(index) {
            this.current = index
            // this.accounts = []
            // this.getsearchUserWithdrawAccountPaged(this.sendObj.symbol) // 选中币种查询地址

        },
        goSearch() {
            this.$Router.push({
                name: 'ServiceSearch'
            })
            // this.$Router.push('/pages/ServiceSearch')
        },
        goChat() {
            this.$Router.push({
                name: 'webView',
                params: {
                    url: this.link,

                }
            })
        },
        nav_to(name) {
            console.log(name);

            const hasSlash = name.path.includes('/');

            if (hasSlash) {
                this.$Router.push({
                    name: 'comming',
                    params: {
                        title: name.name
                    }
                });
                return;
            }
            if (name.path == 'support') {
                this.goChat()
                return
            }
            if (name.path == 'faq') {
                this.$Router.push({
                    name: 'webView',
                    params: {
                        url: "https://pinkwallet.zendesk.com/hc/zh-sg"
                    }
                })
                return
            }
            this.$Router.push({
                name: name.path
            })
        },
        goBack() {
            uni.navigateBack()
        }, 
        copyId(id) {
            uni.setClipboardData({
                data: id,
                success: () => {
                    uni.showToast({ title: '已复制', icon: 'none' });
                }
            });
        },
        nav_set() {
            this.$Router.push({
                name: 'setting'
            })
        },
        nav_userinfo() {
            this.$Router.push({
                name: 'userInfo'
            })
        },
        async getUserInfos() {
            let res = await this.$api.getUserInfo()
            if (res.code == 200) {
                this.userInfo = res.result
                this.qrcodeUrl = res.result.email
            }

        },
        async getAuthInfo() {
			let res = await this.$api.authInfo({})
			if (res.code == 200) {
				this.status = res.result.applyForCf
			}
		},
        nav_Notification(){
				this.$Router.push({
					name: 'Notification'
				})
			}
        // nav_name(){
        //     this.$Router.push({ 
        //         name: 'setNamePic'
        //     })
        // }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-scroll-box {
    display: flex !important;
    align-items: center;
    // margin-left: -30rpx !important;
}

::v-deep .u-tab-item {
    font-size: 28rpx !important;
    // width: 98*2rrpx !important;
    padding: 0 !important;
}


.service-page {
    margin-top: 70rpx;

    .header {
        display: flex;
        align-items: center;
        font-size: 30rpx;
        font-weight: bold;
        margin-bottom: 20rpx;

        .back {
            margin-right: 20rpx;
        }

        .title {
            flex: 1;
            text-align: center;
        }
    }

    .grid {
        margin: 40rpx 60rpx 86rpx 64rpx;
        display: flex;
        flex-wrap: wrap;
        gap: 50rpx 90rpx;
        justify-content: flex-start;

        .grid-item {
            max-width: 84rpx;
            // width: 25%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            // margin-bottom: 30rpx;

            .bg {
                width: 84rpx;
                height: 84rpx;
                border-radius: 24rpx;
                background: #FEFAFE;
            }

            .icon {
                // width: 80rpx;
                // height: 80rpx;
                margin-bottom: 10rpx;
            }

            .label {
                margin-top: 22rpx;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 34rpx;
                color: #000;
                text-align: center;
                white-space: nowrap;
            }
        }
    }

    .tabbar_view {
        margin-top: 90rpx;

    }

    .tab-bar {
        display: flex;
        justify-content: space-between;
        margin: 30rpx 0;

        .tab {
            font-size: 24rpx;
            color: #999;

            &.active {
                color: #000;
                font-weight: 600;
                border-bottom: 4rpx solid #ff77a2;
                padding-bottom: 6rpx;
            }
        }
    }
}

.header_view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100rpx;
    background: #fff;
    padding: 0 28rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;

    .back {
        width: 84rpx;
        height: 84rpx;
        margin-right: 20rpx;

        image {
            width: 84rpx;
            height: 84rpx;
        }
    }

    .right {
        display: flex;
        align-items: center;
        gap: 12rpx;

        image {
            width: 72rpx;
            height: 72rpx;
        }

    }
}

.user_info_card {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 16rpx;
    padding: 52rpx 24rpx 40rpx 32rpx;
    position: relative;

    .user_info_card_left {
        position: relative;

        .user_avatar {
            width: 96rpx;
            height: 96rpx;
            border-radius: 50%;
            background: #f5f5f5;
            display: block;
        }

        .user_verified_icon_bg {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 32rpx;
            height: 32rpx;
            background: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2rpx 8rpx 0 rgba(33, 195, 91, 0.08);

            .user_verified_icon {
                width: 28rpx;
                height: 28rpx;
                display: block;
            }
        }
    }

    .user_info_card_center {
        flex: 1;
        margin-left: 32rpx;

        .user_name_row {
            display: flex;
            align-items: center;

            .user_name {
                font-size: 36rpx;
                font-weight: bold;
                color: #111;
            }

            .user_name_link {
                width: 28rpx;
                height: 28rpx;
                margin-left: 8rpx;
            }
        }

        .user_id_row {
            display: flex;
            align-items: center;
            margin-top: 8rpx;

            .user_id {
                font-size: 24rpx;
                color: #888;
            }

            .user_id_copy {
                width: 32rpx;
                height: 32rpx;
                margin-left: 8rpx;
            }
        }

        .user_tag_row {
            margin-top: 18rpx;
            display: flex;
            align-items: center;

            .user_tag {
                font-size: 22rpx;
                padding: 0 18rpx;
                height: 40rpx;
                line-height: 40rpx;
                border-radius: 8rpx;
                margin-right: 16rpx;
                border: 2rpx solid #999899;
                color: #999899;
            }

            .user_tag_verified {
                background: #fff;
                color: #21C35B;
                border: 2rpx solid #21C35B;
            }
        }
    }

    .user_info_card_right {
        width: 40rpx;

        .user_arrow {
            width: 15rpx;
            height: 8rpx;
            display: block;
        }
    }
}

.view_item {
    margin-bottom: 26rpx;

    .grid_title {
        font-size: 28rpx;
        color: #111;
        font-weight: bold;
        margin-bottom: 60rpx;
        font-family: Gilroy-SemiBold;
        padding-left: 30rpx;
    }

}
</style>