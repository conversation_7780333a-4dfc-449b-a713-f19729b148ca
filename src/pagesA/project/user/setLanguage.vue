<template>
    <view class="body">
        <u-navbar :border-bottom="false" :title="title" :customBack="back" />
        <view class="search_box_">
            <view class="search_inner_">
                <image class="search_icon_" src="https://pro-oss.pinkwallet.com/image/1385297482358546432.png" />
                <input v-model="search" class="search_input_" placeholder="搜索"
                    placeholder-style="color:#bdbdbd;font-size:30rpx;" type="text" />
            </view>
        </view>
        <view class="lang_list_">
            <view v-for="(item, idx) in filteredLangs" :key="item.value" class="lang_item_"
                @tap="selectLang(item)">
                <view>{{ item.name }}</view>
                <u-icon v-if="item.name === selected"
                    name="https://pro-oss.pinkwallet.com/image/1385297006351179776.png" color="#ff8eb3" size="32" />
            </view>
        </view>
    </view>
</template>
 
<script>
    export default {
        data() {
            return {
                title: '语言',
                search: '',
                selected: '',
                LangList: [
                    {
                        name: "english",
                        value: 'en'
                    },
                    {
                        name: "简体中文",
                        value: 'zh'
                    },
                    {
                        name: "繁體中文",
                        value: 'zhhant'
                    }
                ],
            }
        },
        onLoad() {
            this.selected = uni.getStorageSync('__language__') == 'zh' ? '简体中文' : uni.getStorageSync('__language__') == 'en' ? 'english' : '繁體中文'
            console.log(this.selected)
        },
        computed: {
            filteredLangs() {
                if (!this.search) return this.LangList
                return this.LangList.filter(l =>
                    l.name.toLowerCase().includes(this.search.toLowerCase())
                )
            }
        },
        methods: {
            selectLang(item) {
                this.$i18n.locale = item.value
                uni.setStorageSync("__language__", item.value)
                this.lang = item.name
                this.showLang = false
                this.setLgs(item.value)
                setTimeout(() => {
                    // #ifdef H5
                    location.reload(); // H5 用浏览器刷新
                    // #endif

                    // #ifdef APP-PLUS
                    // const pages = getCurrentPages();
                    // const currentPage = pages[pages.length - 1];
                    // const route = currentPage.route;
                    // const options = currentPage.options || {};

                    // const queryString = Object.entries(options).map(([k, v]) => `${k}=${v}`).join('&');
                    // const url = `/${route}${queryString ? '?' + queryString : ''}`;

                    // uni.reLaunch({
                    // 	url
                    // });
                    // #endif
                }, 100);
            },
            back() {
                this.$Router.back()
            },
            SetLang(item) {
               
            },
            async setLgs(e) {
                let res = await this.$api.setLg({
                    lg: e == 'en' ? "en-US" : e == "zh" ? "zh-CN" : "zh-HK",
                })
            },
        }
    }
</script>

<style lang="scss" scoped>
    .body {
        background: #fff;
        min-height: 100vh;
        font-family: PingFang SC;
    }

    .search_box_ {
        padding: 24rpx 24rpx 0 24rpx;
        margin-top: 40rpx;
    }

    .search_inner_ {
        display: flex;
        align-items: center;
        background: #f7f7f7;
        border-radius: 32rpx;
        height: 74rpx;
        padding: 0 24rpx;
    }

    .search_icon_ {
        width: 52rpx;
        height: 52rpx;
        margin-right: 16rpx;
    }

    .search_input_ {
        flex: 1;
        border: none;
        outline: none;
        background: transparent;
        font-size: 30rpx;
        color: #111;
        height: 74rpx;
        line-height: 74rpx;
    }

    .lang_list_ {
        margin-top: 40rpx;
    }

    .lang_item_ {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 32rpx;
        font-size: 28rpx;
        color: #111;
        font-family: PingFang SC;
        margin-bottom: 50rpx;
    }
</style>