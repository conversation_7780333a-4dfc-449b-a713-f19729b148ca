<template>
	<view class="body">
		<u-navbar :border-bottom="false" :title="$t('realName.uploadTitle')" :customBack="back"></u-navbar>
		<u-line-progress :percent="20" :show-percent="false" active-color="#F97C9A" inactive-color="#F3F3F3"
			:height="6"></u-line-progress>
		<view class="height_60"></view>
		<view v-for="(item, idx) in uploadList" :key="item.key" class="upload_block">
			<view class="upload_label">
				{{ $t(item.label) }}
				<u-icon v-if="item.fileList.length" name="https://pro-oss.pinkwallet.com/image/1385676231226777600.png" @click="handleRemove(idx)" size="30" color="#999"></u-icon>
			</view>
			<view class="upload_area">
				<u-upload
					:action="uploadUrl"
					:file-list="item.fileList"
					:max-count="1"
					:show-upload-list="false"
					:custom-btn="true"
					:header="uploadHeader"
					:ref="`idCartZ${idx}`"
					@on-success="(res, index, lists) => handleUploadSuccess(res, idx, lists)"
					@on-remove="(index, lists) => handleRemove(idx, lists)"
				>
					<view slot="addBtn" class="upload_btn" v-if="!item.fileList.length">
						<u-icon name="cloud-upload" size="40" color="#fff" />
						<text>{{ $t('realName.upload') }}</text>
					</view>
				</u-upload>
				<image
					v-if="item.fileList.length"
					:src="item.fileList[0].url"
					class="upload_img"
					mode="aspectFill"
				/>
			</view>
		</view>
		<view class="btn_box">
			<u-button type="primary" class="next_btn" hover-class="none"
				:custom-style="{background:'#F97C9A',borderRadius:'50rpx'}" @click="next">{{ $t('realName.continue') }}</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '上传证件',
				uploadList: [
					{ key: 'front', label: '身份证人像面', fileList: [] },
					{ key: 'back', label: '身份证国徽面', fileList: [] },
					{ key: 'hand', label: '手持身份证', fileList: [] }
				],
				uploadUrl: `${getApp().globalData.apiUrl}pinkwallet/appApi/oss/uploadImage`,
				uploadHeader: {
					// 如需 token 可在此加上
					'Authorization': uni.getStorageSync("token"),
				},
				show: false,
				selectType: '',
				formData: {
					residence: {
						icon: '',
						label: ''
					},
					visa: {
						icon: '',
						label: ''
					},
					idType: '0'
				}
			}
		},
		onLoad(options) {
			
			let realNameInfo = uni.getStorageSync('realNameInfo')
			console.log(realNameInfo)
		// 判断证件类型，动态设置上传项
		if (realNameInfo && realNameInfo.documentType === '1') {
			// 1代表护照
			this.uploadList = [
				{ key: 'front', label: '护照人像页', fileList: [] },
				{ key: 'back', label: '护照签证页', fileList: [] },
				{ key: 'hand', label: '手持护照', fileList: [] }
			]
		} else if (realNameInfo && realNameInfo.documentType === '2') {
			// 2代表其他证件
			this.uploadList = [
				{ key: 'front', label: '证件正面', fileList: [] },
				{ key: 'back', label: '证件反面', fileList: [] },
			]
		} else {
			// 默认身份证
			this.uploadList = [
				{ key: 'front', label: '身份证人像面', fileList: [] },
				{ key: 'back', label: '身份证国徽面', fileList: [] },
				{ key: 'hand', label: '手持身份证', fileList: [] }
			]
		}
		},
		methods: {
			back() {
				uni.navigateBack();
			},
			handleUploadSuccess(res, idx, lists) {
				// let data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data
				// 只保留最新上传的图片
				this.uploadList[idx].fileList = [{ url: res.result }]
			},
			handleRemove(idx) {
				this.uploadList[idx].fileList = []
				// 清空u-upload内部文件列表
				this.$refs[`idCartZ${idx}`][0].clear()
			},
			next(){
				console.log(this.uploadList)
			// 校验3个图片都已上传
			const isAllUploaded = this.uploadList.every(item => item.fileList.length > 0 && item.fileList[0].url);
			if (!isAllUploaded) {
				uni.$u.toast('请上传所有证件照片');
				return;
			}
			let realNameInfo = uni.getStorageSync('realNameInfo')
			realNameInfo = {
				...realNameInfo,
				documentFrontUrl: this.uploadList[0].fileList[0].url,
				documentBackUrl: this.uploadList[1].fileList[0].url,
				documentHoldUrl: this.uploadList[2].fileList[0].url,
			}
			uni.setStorageSync('realNameInfo', realNameInfo)
			// 校验通过，跳转下一步
			this.$Router.push({
				name: 'realNameInfo'
			});
				
			},
			onCountrySelect(country) {
				this.formData.residence.icon = country.icon;
				this.formData.residence.label = country.name;
			},
			showCountrySelect(type) {
				this.selectType = type;
				this.show = true;
			},
			onContinue() {
				console.log(this.formData);
				// 继续逻辑
			}
		}
	}
</script>

<style lang="scss" scoped>
	.height_60{
		height: 60rpx;
	}
	.body {
		background: #fff;
		min-height: 100vh;
		padding: 0 32rpx;
		font-family: PingFang SC;
		padding-top: 30rpx;
	}

	.upload_block {
		margin-bottom: 70rpx;
		
	}

	.upload_label {
		font-size: 28rpx;
		font-weight: 500;
		margin-bottom: 26rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.upload_area {
		background: #fafafa;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		background-color:#F9F9F9;
		height: 300rpx;
		border-radius:26rpx;
	}

	.upload_btn {
		width: 268rpx;
		height: 70rpx;
		background: #F97C9A;
		color: #fff;
		border-radius: 16rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		font-weight: 500;
		margin: 0 auto;
		>.u-icon {
			margin-right: 12rpx;
		}
	}

	.upload_img {
		width: 100%;
		height: 300rpx;
		border-radius: 20rpx;
		object-fit: cover;
	}

	.btn_box {
		margin-top: 60rpx;
		margin-bottom: 32rpx;
		position: fixed;
		bottom: 0;
		left: 0; 
		right: 0;
		padding: 0 32rpx;
	}

	.next_btn {
		width: 100%;
		height: 88rpx;
		font-size: 32rpx;
		font-weight: 600;
		background: #F97C9A;
		border-radius: 50rpx;
		color: #fff;
	}

	.form {
		margin-top: 30rpx;
	}

	.form_item {
		margin-bottom: 30rpx;
	}

	.form_label {
		font-size: 28rpx;
		font-weight: 500;
		margin-bottom: 26rpx;
	}

	.select_box {
		background: #fafafa;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		height: 70rpx;
		border-radius: 26rpx;
		border: 1px solid #F3F3F3;
	}

	.country_flag {
		width: 30rpx;
		height: 30rpx;
	}

	.card_group {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.card_option {
		background: #fafafa;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		height: 70rpx;
		border-radius: 26rpx;
		border: 1px solid #F3F3F3;
	}

	.card_option_active {
		border-color: #F97C9A;
	}

	.icon_id {
		margin-right: 12rpx;
	}

	.card_text {
		font-size: 28rpx;
		font-weight: 500;
	}

	.tag_recommend {
		background: #F97C9A;
		color: #fff;
		border-radius: 16rpx;
		padding: 4rpx 8rpx;
		font-size: 24rpx;
		font-weight: 500;
		margin-left: 12rpx;
	}

	.icon_check {
		margin-left: 12rpx;
	}

	.btn_continue {
		width: 100%;
		height: 88rpx;
		font-size: 32rpx;
		font-weight: 600;
		background: #F97C9A;
		border-radius: 50rpx;
		color: #fff;
	}
</style>