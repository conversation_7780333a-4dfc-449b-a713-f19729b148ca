<template>
  <view class="body">
    <u-navbar :border-bottom="false" :title="title" :customBack="back">
    </u-navbar>

    <view class="section_title_">通用类</view>
    <view class="item_" @tap="onTheme">
      <view class="item_label_">主题</view>
      <view class="item_right_">
        <view class="item_value_">跟随系统</view>
        <u-icon name="arrow-right" size="28" color="#C7C7C7" />
      </view>
    </view>
    <view class="item_" @tap="onColor">
      <view class="item_label_">颜色配置</view>
      <view class="item_right_">
        <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385294577748500480.png"
          style="width:16rpx;height:20rpx;vertical-align:middle;margin-right:8rpx;" />
        <u-icon name="arrow-right" size="28" color="#C7C7C7" />
      </view>
    </view>
    <view class="item_" @tap="onRoute">
      <view class="item_label_">路线配置</view>
      <view class="item_right_">
        <view class="item_value_">北美</view>
        <u-icon name="arrow-right" size="28" color="#C7C7C7" />
      </view>
    </view>
    <view class="divider_"></view>
    <view class="section_title_">主题背景</view>
    <view class="item_" @tap="onCoin">
      <view class="item_label_">币种偏好</view>
      <view class="item_right_">
        <view class="item_value_">{{coinLike?coinLike:'USDT'}}</view>
        <u-icon name="arrow-right" size="28" color="#C7C7C7" />
      </view>
    </view>
    <view class="item_" @tap="onLang">
      <view class="item_label_">语言偏好</view>
      <view class="item_right_">
        <view class="item_value_">跟随系统</view>
        <u-icon name="arrow-right" size="28" color="#C7C7C7" />
      </view>
    </view>
    <view class="divider_"></view>
    <view class="section_title_">更新</view>
    <view class="item_" @tap="onCheckUpdate">
      <view class="item_label_">检查更新</view>
      <view class="item_right_">
        <view class="item_value_">{{version}}</view>
        <u-icon name="arrow-right" size="28" color="#C7C7C7" />
      </view>
    </view>
    <view class="item_desc_">您当前版本为最新版本</view>
    <view class="save_btn_box_">
      <u-button type="primary" hover-class="none" class="save_btn_" @tap="logout" :custom-style="btnStyle">退出登录</u-button>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        title: '设置',
        btnStyle: {
          width: '100%',
          background: '#ff8eb3',
          color: '#fff',
          borderRadius: '50rpx',
          height: '90rpx',
          fontSize: '32rpx'
        },
        version: ''
      }
    },
    onLoad() {
      this.version = `v${uni.getSystemInfoSync().appVersion}`
      this.get_coin_like()
    },
    methods: {
      onTheme() { },
      onColor() { },
      onRoute() { },
      onCoin() {
        this.$Router.push({
          name: 'setB'
        })
      },
      onLang() {
        this.$Router.push({
          name: 'setLanguage'
        })
      },
      onCheckUpdate() { },
      save() {

      },
      back() {
        this.$Router.back()
      },
      logout() {
        this.signOutHandle()
        setTimeout(() => {
          uni.hideLoading();
          uni.removeStorageSync('token');
          this.$Router.push({
            name: "login"
          })
        }, 500);
      },
      async signOutHandle() {
        uni.showLoading();
        let res = await this.$api.signOut();
        if (res.code == 200) {
        }
      },
      async get_coin_like() {
        let res = await this.$api.getCoinLike()
        if (res.code == 200) {
          this.coinLike = res.result
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .body {
    background: #fff;
    min-height: 100vh;
    padding-bottom: 120rpx;
    font-family: PingFang SC;
  }

  .section_title_ {
    color: #999899;
    font-size: 28rpx;
    font-weight: 600;
    margin: 48rpx 0 16rpx 32rpx;
    font-family: PingFang SC;
  }

  .item_ {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    height: 96rpx;
    background: #fff;
    font-family: PingFang SC;
  }

  .item_label_ {
    font-size: 28rpx;
    color: #000;
    font-family: PingFang SC;
  }

  .item_right_ {
    display: flex;
    align-items: center;
    font-family: PingFang SC;
  }

  .item_value_ {
    color: rgba(0, 0, 0, 0.5);
    font-size: 28rpx;
    margin-right: 8rpx;
    font-family: PingFang SC;
  }

  .divider_ {
    height: 2rpx;
    background: #F7F7F7;
    margin: 24rpx 0;
    width: 100%;
  }

  .item_desc_ {
    color: #C7C7C7;
    font-size: 24rpx;
    margin-left: 32rpx;
    margin-top: 0;
    margin-bottom: 32rpx;
    font-family: PingFang SC;
  }

  .save_btn_box_ {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 32rpx;
    padding: 0 32rpx;
    z-index: 10;
  }

  .save_btn_ {
    width: 100%;
    background: #ff8eb3;
    color: #fff;
    border-radius: 50rpx;
    height: 90rpx;
    font-size: 32rpx;
    font-family: PingFang SC;
  }
</style>