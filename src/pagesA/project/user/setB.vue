<template>
  <view class="body">
    <u-navbar :border-bottom="false" :title="$t('userInfo.title')" :customBack="back">
    </u-navbar>
    <view class="item_title" @click="show = true">
      <view class="item_title_left">
        {{$t('setting.coinLike')}}
      </view>
      <u-icon name="https://pro-oss.pinkwallet.com/image/1385272273706508288.png" size="15" />
    </view>
    <u-popup v-model="show" mode="bottom" border-radius="14" height="1400rpx">
      <view>
        <view class="popup_title">
         <text>{{$t('setting.coinLike')}}</text>
          <u-icon @click="show = false" name="https://pro-oss.pinkwallet.com/image/1385342962270560256.png" size="48" />
        </view>
        <view class="search-box-n">
          <image src="https://pro-oss.pinkwallet.com/image/1385297482358546432.png" />
          <input v-model="searchKeyword" :placeholder="$t('Deposit.Search')" class="search-input" @input="onSearch" />
        </view>

        <view class="coin-list">
          <view class="coin-item" v-for="coin in filteredCoins" :key="coin.name" @click="selectCoin(coin)">
            <image :src="coin.images" class="coin-icon" />
            <text class="coin-name">{{ coin.name }}</text>
            <!-- <text class="arrow">></text> -->
            <image class="arrow" src="https://pro-oss.pinkwallet.com/image/1374825043983949824.png" />
          </view>
          <nodata v-if="filteredCoins.length === 0" />
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        title: this.$t('setting.coinLike'),
        show: false,
        searchKeyword: '',
        coins: [
          // { name: 'BTC', icon: 'https://cryptoicon-api.vercel.app/api/icon/btc' },
          // { name: 'ETH', icon: 'https://cryptoicon-api.vercel.app/api/icon/eth' },
          // { name: 'USDT', icon: 'https://cryptoicon-api.vercel.app/api/icon/usdt' },
          // { name: 'BNB', icon: 'https://cryptoicon-api.vercel.app/api/icon/bnb' },
        ],
        coin:""
      }
    },
    onLoad() {
      this.getCoin()
    },
    computed: {
      filteredCoins() {
        if (!this.searchKeyword) {
          return this.coins
        }
        const keyword = this.searchKeyword.trim().toLowerCase()
        return this.coins.filter(coin =>
          coin.coin.toLowerCase().includes(keyword)
        )
      }
    },
    methods: {
      onSearch() {
        const keyword = this.searchKeyword.trim().toLowerCase()
        this.filteredCoins = this.coins.filter(coin =>
          coin.coin.toLowerCase().includes(keyword)
        )
      },
      selectCoin(coin) {
        console.log('Selected:', coin.symbol)
        this.coin = coin.symbol
        this.show = false
        this.setCoin()
      },
      async getCoin() {
        let res = await this.$api.getCoinList()
        if (res.code == 200) {
          this.coins = res.result
        }
      },
      async setCoin() {
        let res = await this.$api.getCoinLike({
          coin: this.coin
        })
        if (res.code == 200) {
          uni.$u.toast(this.$t('title.success'))
        }
      },
      
      back() {
        this.$Router.back()
      },
      copyUid() {
        uni.setClipboardData({
          data: this.userInfo.uid,
          success: () => {
            this.$u.toast(this.$t('userInfo.copySuccess'));
          }
        });
      }
    }
  }
</script>

<style lang="scss" scoped>
  .body {
    background: #fff;
    min-height: 100vh;
    padding-bottom: 120rpx;
    font-family: PingFang SC;
    padding-top: 40rpx;

    .item_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32rpx;
      margin-top: 32rpx;

      .item_title_left {
        font-size: 28rpx;
        font-weight: 600;
        color: #000;
      }
    }


  }
  .popup_title{
    display: flex;
    justify-content: space-between;
    padding: 32rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #000;
  }
  .search-box-n {
    display: flex;
    justify-content: center;
    position: relative;

    image {
      position: absolute;
      top: 50%;
      left: 44rpx;
      transform: translateY(-50%);
      width: 52rpx;
      height: 52rpx;
    }
  }

  .search-input {
    text-indent: 76rpx;
    border: none;
    outline: none;
    width: 100%;
    margin: 0 32rpx;
    height: 38*2rpx;
    background: rgba(217, 217, 217, .2);
    border-radius: 34rpx;

  }

  .coin-list {
    padding: 0 32rpx;
    display: flex;
    flex-direction: column;
  }

  .coin-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
  }

  .coin-icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 18rpx;
    border-radius: 50%;
  }

  .coin-name {
    flex: 1;
    font-family: PingFang HK;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 40rpx;
    color: #000;
  }

  .arrow {

    width: 36rpx;
    height: 36rpx;
  }
</style>