<template>
    <view class="container">
        <u-navbar back-icon-color="#121212" :border-bottom="false" :title="this.$t('Notification.Notification')">
        </u-navbar>
        <!-- <view style="height: 54rpx;"></view> -->
        <view class="bg-space">
            <!-- <view class="ads flex_divide">
                <view class="ad-left flex_column">
                    <view class="title">Don’t have Fab Bank Account?</view>
                    <view class="title2">Hedge against risks/Secure withdrawals & Deposit/ in your own name/No need
                        to be Present.</view>
                    <view class="title3">Open Now</view>

                </view>
                <view class="ad-right">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/********/152a41da39d45c2f71270a17b76302e6_500x500.gif" />
                </view>
            </view> -->
            <u-swiper :list="swiperList" bg-color="transparent" :height="200" @click="bannerClick"
                :title-style="{ 'font-size': '24rpx' }" :border-radius="20" mode="none"></u-swiper>
        </view>


        <view class="notification-list">
            <view class="notification-item-notice" @click="goChat">
                <view class="bgs flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/********/40650f78637803774d05a5850a96d3fa_88x101.png" />
                    <!-- <view class="dots"></view> -->
                </view>
                <view class="notification-content">
                    <text class="notification-text">{{ $t("Notification.OnlineCustomerService") }}</text>
                    <text class="notification-message">{{ $t("Notification.text") }}</text>
                    <view class="time">{{ currentTime }}</view>
                </view>
            </view>
            <view class="notification-item" @click="readinnerMsgread(item)" v-for="(item, index) in noticeList"
                :key="index">
                <view class="bgs flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/********/bc3a16e50948a7922475d5a935b1e9d5_84x84.png" />
                    <view class="dots" v-if="item.read == 0"></view>
                </view>
                <view class="notification-content">
                    <text class="notification-text">{{ item.msgInnerInfo.title }}</text>
                    <text class="notification-message">{{ item.msgInnerInfo.content }}</text>
                    <view class="time">{{ formatTimestamp(item.ct) }}</view>
                </view>
            </view>
        </view>

    </view>
</template>

<script>
export default {
    name: "NotificationPage",
    data() {
        return {
            swiperList: [],
            link: "../../../static/serve.html",
            pageNum: 1,
            pageSize: 10,
            noticeList: [],
            hasNext: false,
            currentTime: ''
        }
    },
    onLoad() {
        uni.setNavigationBarTitle({
            title: this.$t("page.Notification") // 切换语言后重新设置标题
        })
        this.getList()
        this.getBannerList()
        // this.innerMsgreadAllList()
        // this.getList()
        // 组件挂载时立即更新时间
        this.updateTime();
        // 每秒更新一次时间
        this.timer = setInterval(this.updateTime, 1000);
    },
    onUnload() {
        // 组件销毁时清除定时器
        clearInterval(this.timer);
    },
    onReachBottom() {
        if (this.hasNext) {
            this.pageNum++
            this.getList()
        }
    },
    methods: {
        updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需+1
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        async getBannerList() {
            let res = await this.$api.bannerList({
                type: 'banner'
            })
            if (res.code == 200) {
                this.swiperList = res.result
            }

        },
        bannerClick(index) {
            const {
                needLogin,
                linkType,
                link
            } = this.swiperList[index]
            console.log(this.swiperList[index].link)
            if (linkType == 0) {
                return false
            } else {
                if (needLogin == 1) {
                    if (uni.getStorageSync('token')) {
                        // #ifdef APP
                        this.$Router.push({
                            name: "webView",
                            params: {
                                url: link,
                            }
                        })
                        // #endif
                        // #ifdef H5
                        window.location.href = link
                        // #endif
                    } else {
                        this.$Router.push({
                            name: "login"
                        })
                    }
                } else {
                    // #ifdef APP
                    this.$Router.push({
                        name: "webView",
                        params: {
                            url: link,
                        }
                    })
                    // #endif
                    // #ifdef H5
                    window.location.href = link
                    // #endif
                }
            }
        },
        async readinnerMsgread(item) {
            if (item.read == 1) return
            let res = await this.$api.innerMsgread({
                msgId: item.id
            })
            if (res.code == 200) {
                this.getList()
            }
        },
        async innerMsgreadAllList() {
            let res = await this.$api.innerMsgreadAll({})
            if (res.code == 200) {
                this.getList()
            }
        },
        async getList() {
            let e = uni.getStorageSync('__language__')
            let res = await this.$api.innerMsg({
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                lg: e == 'en' ? "en-US" : e == "zh" ? "zh-CN" : "zh-HK",
            })
            if (res.code == 200) {
                this.hasNext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.noticeList = res.result.data
                } else {
                    this.noticeList = this.noticeList.concat(res.result.data)
                }
            }
        },
        goChat() {
            this.$Router.push({
                name: 'webView',
                params: {
                    url: this.link,

                }
            })
        },
        formatTimestamp(ts) {
            const date = new Date(ts * 1000) // 转毫秒

            const Y = date.getFullYear()
            const M = String(date.getMonth() + 1).padStart(2, '0')
            const D = String(date.getDate()).padStart(2, '0')
            const h = String(date.getHours()).padStart(2, '0')
            const m = String(date.getMinutes()).padStart(2, '0')
            const s = String(date.getSeconds()).padStart(2, '0')

            return `${Y}-${M}-${D} ${h}:${m}:${s}`
        }
    }
};
</script>

<style scoped lang="scss">
.container {
    min-height: 100vh;

    .notification-list {
        padding: 32rpx;

        .notification-item-notice {
            background: #FFF3F6;
            border-radius: 34rpx;
            border: 2rpx solid #D9D6D6;
            margin-bottom: 32rpx;
            display: flex;
            padding: 32rpx;

            &:last-child {
                margin-bottom: 0;
            }


            .bgs {
                width: 82rpx;
                height: 82rpx;
                border-radius: 50%;
                background: #FFF3F6;
                border: 0.68*2rpx solid #FFCDDA;
                position: relative;

                image {
                    width: 44rpx;
                    height: 50rpx;
                }

                .dots {
                    position: absolute;
                    width: 12rpx;
                    height: 12rpx;
                    top: 6rpx;
                    left: 62rpx;
                    background: #D72D4A;
                    border-radius: 50%;
                }
            }



            .notification-content {
                margin-left: 24rpx;
                display: flex;
                flex-direction: column;

                .time {
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    line-height: 120%;
                }

                .notification-text {
                    margin-top: 26rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    color: #000;
                }

                .notification-message {
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    color: #666666;
                    margin: 12rpx 0;
                }
            }
        }

        .notification-item {
            margin-bottom: 32rpx;
            display: flex;

            &:last-child {
                margin-bottom: 0;
            }


            .bgs {
                width: 82rpx;
                height: 82rpx;
                border-radius: 50%;
                background: #FFF3F6;
                border: 0.68*2rpx solid #FFCDDA;
                position: relative;

                image {
                    width: 42rpx;
                    height: 42rpx;
                }

                .dots {
                    position: absolute;
                    width: 12rpx;
                    height: 12rpx;
                    top: 6rpx;
                    left: 62rpx;
                    background: #D72D4A;
                    border-radius: 50%;
                }
            }



            .notification-content {
                margin-left: 24rpx;
                display: flex;
                flex-direction: column;

                .time {
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    line-height: 120%;
                }

                .notification-text {
                    margin-top: 26rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    color: #000;
                }

                .notification-message {
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    color: #666666;
                    margin: 12rpx 0;
                }
            }
        }
    }

    .bg-space {
        padding: 39rpx 32rpx 24rpx 36rpx;
        border-bottom: 2rpx solid #E0E0E0;

        .ads {
            /* margin: 0 32rpx; */
            border-top-left-radius: 60rpx;
            border-top-right-radius: 60rpx;
            height: 226rpx;
            /* height: fit-content; */
            background-image: url("https://pro-oss.pinkwallet.com/image/********/2b487aa2e4872866d9d29b8085061bae_1194x339.png");
            background-size: 100% 100%;

            .ad-left {
                .title {
                    font-family: Gilroy-Bold;
                    font-weight: 400;
                    font-size: 14*2rpx;
                    line-height: 120%;
                    color: #000;
                }

                .title2 {
                    margin: 8rpx 0;
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    color: #000;
                }

                .title3 {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 24rpx;

                    line-height: 40rpx;
                    letter-spacing: 0px;
                    text-decoration: underline;
                    text-decoration-style: solid;
                    text-decoration-offset: 25%;
                    text-decoration-thickness: 13%;
                    color: #3B9B62;
                }
            }

            .ad-right {
                image {
                    width: 176rpx;
                    height: 176rpx;
                }
            }
        }
    }
}
</style>