<template>
    <view class="trade-page">
        <view class="barHeight"></view>
        <view class="title">合约</view>
        <!-- 顶部信息 -->
        <view class="header">
            <view class="symbol">
                <image @click="drawerShow = true" src="https://pro-oss.pinkwallet.com/image/1375530215441784832.png" />
                <view class="name">{{ $store.state.symbol.toUpperCase() }}</view>
                <view class="change-rate" :style="{ color: rate >= 0 ? '#00C853' : '#FF82A3' }"> {{ rate > 0 ? '+' : ''
                }}{{ rate + '%' }}</view>
                <!-- FF82A3 -->
            </view>

            <view class="candle">
                <image src="https://pro-oss.pinkwallet.com/image/1375530604681584640.png" @click="nav_to('detail')" />
                <image src="https://pro-oss.pinkwallet.com/image/1375530755789774848.png" @click="open_more" />
            </view>
        </view>

        <view class="main-content">
            <!-- 左侧盘口区域 -->
            <depths />

            <!-- 右侧下单区域 -->
            <makeorder @submitorder="submitorder" ref="makeorder" />
        </view>

        <!-- 底部导航 -->
        <position ref="positions" @cancel="cancel" @close="close" />

        <!-- 币对列表 -->
        <marketDrawerPopup :show.sync="drawerShow" />

        <morepopup :show.sync="moreshow" />

        <!-- <BottomTabBar v-model="tabIndex" @change="onTabChange" /> -->

    </view>
</template>

<script>
import BottomTabBar from "../../../components/public/BottomTabBar"
import depths from "./components/depth"
import makeorder from "./components/makeorder"
import position from "./components/position"
import marketDrawerPopup from "./popup/marketDrawer"
import morepopup from "./popup/more"
export default {
    components: {
        depths,
        makeorder,
        position,
        marketDrawerPopup,
        BottomTabBar,
        morepopup
    },
    data() {
        return {
            moreshow: false,
            tabIndex: 2,
            drawerShow: false,
            animatedOrders: [],
            currentContract: {},
            latestPrice: ""
        }
    },
    watch: {
        "$store.state.symbol"(val) {
            const coin = uni.getStorageSync('currentContract') || {};
            this.currentContract = coin; // 触发响应式更新
        },
        "$store.state.realprice"(val) {
            let a = Number(val)
            let b = a.toFixed(3)
            // this.latestPrice = b
            this.currentContract.closePrice = b
        },
    },
    computed: {
        rate() {
            const closePrice = this.currentContract?.closePrice || 0;
            const openPrice = this.currentContract?.openPrice || 0;
            const change = closePrice - openPrice;
            const percentage = openPrice != 0 ? (change / openPrice) * 100 : 0;
            return openPrice != 0 ? percentage.toFixed(this.currentContract.pricePrecision) : '0.00';
        }
    },
    mounted() {
        this.currentContract = uni.getStorageSync('currentContract')
        this.currentContract.closePrice = uni.getStorageSync('currentContract').closePrice
        // this.latestPrice = uni.getStorageSync('currentContract').closePrice
    },
    // onReachBottom() {
    // },
    methods: {
        fetchBalance() {
            this.$refs.makeorder.symbolAvailableBalance()
        },
        updateRate() {
            // 强制重新计算rate computed属性
        },
        close(e) {
            this.$refs.makeorder.symbolAvailableBalance()
        },
        cancel(e) {
            this.$refs.makeorder.symbolAvailableBalance()
            // this.$refs.positions.getEntrustList()
            // this.$refs.position.onTabClick(1)
        },
        open_more() {
            console.log(123);

            this.moreshow = true
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            });
        },
        onBottom() {
            console.log('onBottom');
            this.$refs.positions.rearchBottom()
        },
        submitorder(orderNo, side) {
            this.$refs.positions.fetchPositions(orderNo, side)
            this.$refs.positions.getEntrustList()
            this.$refs.positions.onTabClick(1)
        },
        animateOrders() {
            let i = 0
            this.animatedOrders = []
            const interval = setInterval(() => {
                if (i >= this.orders.length) {
                    clearInterval(interval)
                } else {
                    this.animatedOrders.push(this.orders[i])
                    i++
                }
            }, 100)
        }
    }
}
</script>

<style scoped lang="scss">
.trade-page {
    min-height: 100vh;
    padding: 0 32rpx;

    .title {
        margin: 32rpx 0 54rpx 0;
        font-family: Gilroy-Medium;
        font-weight: 500;
        font-size: 32rpx;
        line-height: 32rpx;
        color: #000;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 6rpx;

        .symbol {
            display: flex;
            align-items: center;

            image {
                width: 24rpx;
                height: 20rpx;
            }

            .name {
                margin: 0 16rpx 0 24rpx;
                font-family: Gilroy-Medium;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
            }

            .change-rate {
                // color: #16c784;
                // color: #FF82A3;
                font-family: Gilroy-Medium;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 40rpx;
            }

        }


        .candle {
            display: flex;
            align-items: center;
            gap: 28rpx;

            image {
                width: 52rpx;
                height: 50rpx;
            }
        }
    }

    .main-content {
        display: flex;
        gap: 20rpx;
        margin-top: 20rpx;
    }






    .no-data {
        margin-top: 60rpx;
        text-align: center;
        color: #999;
        font-size: 26rpx;

        .no-data-img {
            width: 160rpx;
            height: 160rpx;
            margin-bottom: 20rpx;
        }
    }
}
</style>