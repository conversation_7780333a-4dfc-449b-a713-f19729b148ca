<template>
    <view class="trade-box">

        <view class="select-group">
            <view class="fixed-box" @click="changeMode()">
                <text>{{ marginType == 'full' ? '全仓' : '逐仓' }}·{{ positionType == 'merged' ? '合仓' : '分仓' }}</text>
                <image class="arrow" :class="{ rotated: Mode }"
                    src="https://pro-oss.pinkwallet.com/image/1375548628155326464.png" />
            </view>
            <view class="padded-box" @click="changeLeverage()">
                <text class="green-text">{{ levelNum }}x</text>
                <image class="arrow" :class="{ rotated: Lev }"
                    src="https://pro-oss.pinkwallet.com/image/1375548628155326464.png" />
            </view>
        </view>

        <view class="Trading_pair-tab">
            <view class="item" :class="{ acleft: leftactive == 1 }" @click="changeTab(1)">
                <text>买入</text>
            </view>
            <!-- this.tradeType = 'sell' -->
            <view class="item" :class="{ acleft: leftactive == 2 }" @click="changeTab(2)">
                <text>卖出</text>
            </view>
            <view class="bgleft" :style="{ marginLeft: tradeType === 'sell' ? ' 214rpx' : '0' }" :class="[
                tradeType === 'buy' ? 'buybg' : 'sellbg', 'left2', leftactive == 2,
            ]">
            </view>
        </view>

        <view class="input-group">
            <view class="input-row" @click="showmodePopup = true">
                <image class="icon" @click.stop="openPopupIntc"
                    src="https://pro-oss.pinkwallet.com/image/1375561424695812096.png" />
                <text class="mid">{{ orderType == 'MARKET' ? '市价委托' : '限价委托' }}</text>
                <image class="arrow"
                    src="https://pro-oss.pinkwallet.com/image/1375548628155326464.png" />
            </view>
            <view class="input-row gray" style="height: 58rpx;" v-if="orderType == 'MARKET'">
                <text class="mid">最优追价</text>
            </view>
            <view class="input-row_limit" style="height: 58rpx;" v-else>
                <view class="input">
                    <u-input placeholder="价格" :height="58" class="input-price" :clearable="false" v-model="orderprice"
                        type="digit" maxlength="20" />
                </view>
                <view class="onmarket flex_all" @click="onMarket">市价</view>
            </view>
            <view class="input-row" style="height: 58rpx;" @click="changeNumPopup = true">
                <!-- <text class="num">数量</text>
                <view class="flex-expand" /> -->
                <u-input placeholder="数量" :clearable="false" v-model="ordervolume" type="digit" maxlength="20"
                    @input="onOrderVolumeInput" />
                <text class="dan">{{ formattedNumTypes(NumTypesvalue) }}</text>
                <image class="arrow"
                    src="https://pro-oss.pinkwallet.com/image/1375548628155326464.png" />
            </view>
        </view>

        <view class="leverage-section">
            <u-slider max="100" @end="endMove" v-model="sliderValue" inactive-color="rgba(231, 231, 231, .6)"
                :active-color="tradeType == 'buy' ? 'rgba(48, 193, 71, .6)' : 'rgba(255, 130, 163, .6)'" step="25"
                :levelList="levelList" :tradeType="tradeType" :use-slot="true">
            </u-slider>
            <!-- <VueSlider v-model="levelNum" :height="3" :data="levelList"></VueSlider> -->
            <view class="slidelevel">
                <text v-for="(item, index) in levelList"
                    :style="{ color: currentfff >= index ? '#000' : 'rgba(0, 0, 0, .6)' }" :key="index">{{ item }}
                </text>
            </view>
            <!-- <view class="checkbox-row">
                <image class="check" src="https://pro-oss.pinkwallet.com/image/1375565971157770240.png" />
                <text>止盈/止损</text>
                <image class="icon" src="https://pro-oss.pinkwallet.com/image/1375561424695812096.png"
                    mode="widthFix" />
            </view> -->

            <view class="availability">
                <view class="row">
                    <view class="label">可用</view>
                    <!-- <view class="spacer" /> -->

                    <view class="righticon">
                        <text class="value">{{ balance }}USDT</text>
                        <image class="icon" @click="nav_to('Mutualtransfer')"
                            src="https://pro-oss.pinkwallet.com/image/1375567028332093440.png"
                            mode="widthFix" />
                    </view>

                </view>
                <view class="row">
                    <view class="label">可开</view>
                    <view class="value">{{ zhang }}张</view>
                </view>
            </view>

            <view class="submit-button">
                <u-button class="btn" hover-class="none"
                    :style="{ background: tradeType == 'buy' ? '#30C147' : '#FF82A3' }" @click="submitOrder">
                    {{ tradeType == 'buy' ? '开多' : '开空' }}
                </u-button>
            </view>
        </view>

        <!-- 市价，限价，计划委托--委托模式选择 -->
        <ordermodePopup :show.sync="showmodePopup" @select="handleSelect" />

        <!-- 数量单位选择 -->
        <numunitPopup :show.sync="changeNumPopup" :options="NumTypes" @select="handleSelectNum" />

        <!-- 逐仓，合仓--仓位模式 -->
        <posotionSelectPopup :show.sync="showPosotionSelectPopupPopup" ref="posotionSelectPopup"
            @confirm="onModeConfirm" />

        <!-- 杠杆 -->
        <u-popup v-model="showPopup" mode="bottom" border-radius="16" safe-area-inset-bottom>
            <LeverageSelector @close="closeLevel" @confirm="onConfirm" :balance="balance" :orderprice="orderprice"
                :orderType="orderType" :levelNum="levelNum" />
        </u-popup>

        <!-- 限价市价介绍 -->
        <introducePop ref="introducePopRef" />
    </view>
</template>

<script>
// import VueSlider from 'vue-slider-component'
// import 'vue-slider-component/theme/default.css'
import introducePop from "../popup/introduce"
import LeverageSelector from "./leverageSelector"
import ordermodePopup from '../popup/mode'
import numunitPopup from '../popup/changenum'
import posotionSelectPopup from "../popup/positionSelector"
export default {
    components: {
        // VueSlider,
        ordermodePopup,
        numunitPopup,
        posotionSelectPopup,
        LeverageSelector,
        introducePop
    },
    data() {
        return {
            orderprice: "",
            orderType: "MARKET",
            ordervolume: '',
            marginType: 'isolated',
            positionType: 'merged',
            showPopup: false,
            showPosotionSelectPopupPopup: false,
            NumTypesvalue: 'CONTRACT',
            NumTypes: [
                { label: '张', value: 'CONTRACT' },
                { label: 'USDT', value: 'QUOTE' },
                { label: this.$store.state.base, value: 'BASE' }
            ],
            showmodePopup: false,
            changeNumPopup: false,
            levelList: ["0%", "25%", "50%", "75%", "100%"],
            levelNum: "10",
            tradeType: 'buy',
            Mode: false,
            Lev: false,
            leftactive: 1,
            sliderValue: "",
            currentfff: "",
            nowsymbol: "USDT",
            balance: 0,
            isManualInput: false, // 标记是否为手动输入
            latestPrice: "",
        }
    },
    computed: {
        zhang() {
            let price
            let balance_level = Number(this.balance) * Number(this.levelNum)
            if (this.orderType == 'MARKET') {
                price = uni.getStorageSync("currentContract").closePrice || 1
            } else {
                price = this.orderprice || 1
            }
            // let price = uni.getStorageSync("currentContract").closePrice || 1
            let multiplier = uni.getStorageSync("currentContract").multiplier
            console.log("balance_level", balance_level, price, multiplier);
            return (balance_level / price / multiplier).toFixed(0)
        }
    },
    watch: {
        showPopup(val, oldval) {
            if (val) {
                this.Lev = true
            } else {
                this.Lev = false
            }
        },

        // 监听滑块值变化，自动计算订单数量
        sliderValue(newVal) {
            // 避免在手动输入时触发循环计算
            if (!this.isManualInput) {
                this.calculateOrderVolume();
            }
        },

        // 监听数量类型变化，重新计算订单数量
        NumTypesvalue() {
            // 如果当前有输入值，优先保持输入值并反向计算滑块
            if (this.ordervolume && Number(this.ordervolume) > 0) {
                this.calculateSliderValue();
            } else {
                this.calculateOrderVolume();
            }
        },

        // 监听余额变化，重新计算订单数量
        balance() {
            this.calculateOrderVolume();
        },
        showPosotionSelectPopupPopup(val) {
            if (val) {
                this.Mode = true
            } else {
                this.Mode = false
            }
        },
        "$store.state.realprice"(val) {
            // console.log(val, ' realprice');
            let a = Number(val)
            let b = a.toFixed(3)
            this.latestPrice = b
        },
    },
    mounted() {
        this.symbolAvailableBalance()
        this.latestPrice = uni.getStorageSync('_realPrice')
    },
    methods: {
        openPopupIntc() {
            this.$refs.introducePopRef.show = true
        },
        onMarket() {
            this.orderprice = this.latestPrice
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        },
        formattedNumTypes(e) {
            return this.NumTypes.find(item => item.value == e)?.label || ''
        },

        // 处理订单数量输入
        onOrderVolumeInput(value) {
            this.isManualInput = true;
            this.ordervolume = value;

            // 反向计算滑块值
            // this.calculateSliderValue();

            // 延迟重置标志，避免影响其他计算
            this.$nextTick(() => {
                setTimeout(() => {
                    this.isManualInput = false;
                }, 100);
            });
        },

        // 反向计算滑块值
        calculateSliderValue() {
            if (!this.ordervolume || Number(this.ordervolume) === 0) {
                this.sliderValue = 0;
                return;
            }

            // 获取当前数量类型
            const numTypeLabel = this.formattedNumTypes(this.NumTypesvalue);

            // 根据数量类型选择对应的余额
            let targetBalance;
            if (numTypeLabel === '张') {
                targetBalance = this.zhang; // 使用张数余额
            } else {
                targetBalance = this.balance; // 使用普通余额
            }

            if (Number(targetBalance) === 0) {
                this.sliderValue = 0;
                return;
            }

            // 反向计算滑块值：(ordervolume / targetBalance) * 100
            const calculatedSlider = (Number(this.ordervolume) / Number(targetBalance)) * 100;

            // 限制滑块值在0-100之间
            this.sliderValue = Math.min(100, Math.max(0, Math.round(calculatedSlider)));
        },

        // 计算订单数量
        calculateOrderVolume() {
            if (this.sliderValue === 0) {
                this.ordervolume = '';
                return;
            }

            // 获取当前数量类型
            const numTypeLabel = this.formattedNumTypes(this.NumTypesvalue);

            // 根据数量类型选择对应的余额
            let targetBalance;
            if (numTypeLabel == '张') {
                targetBalance = this.zhang; // 使用张数余额
            } else {
                if (this.NumTypesvalue == 'BASE') {
                    let multiplier = uni.getStorageSync("currentContract").multiplier || 0
                    targetBalance = this.zhang * multiplier; // 使用普通余额  
                    // targetBalance = this.balance ; // 使用普通余额

                } else {
                    targetBalance = this.balance; // 使用普通余额
                }
            }

            // 计算订单数量：sliderValue/100 * targetBalance
            const calculatedVolume = (this.sliderValue / 100) * Number(targetBalance || 0);

            // 保留适当的小数位数
            if (numTypeLabel == '张') {
                // 张数通常为整数
                this.ordervolume = Math.floor(calculatedVolume).toString();
            } else {
                // 其他类型保留6位小数
                let prescision = uni.getStorageSync('currentContract').pricePrecision;
                this.ordervolume = calculatedVolume.toFixed(prescision);
            }

            // 如果计算结果为0，则清空
            if (Number(this.ordervolume) === 0) {
                this.ordervolume = '';
            }
        },
        async symbolAvailableBalance() {
            let res = await this.$api.symbolAvailableBalance({
                account: 'CONTRACT',
                symbol: this.nowsymbol
            })
            if (res.code == 200) {
                if (this.nowsymbol == res.result.symbol) {
                    this.balance = res.result.balance.toFixed(2)
                    // this.balance = 1000
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })
            }
        },
        async submitOrder() {
            // 判断市价委托的张数的为空情况，和限价委托的张数和orderprice是否为空
            if (this.orderType == 'MARKET') {
                if (!this.ordervolume) {
                    uni.showToast({
                        title: '请输入有效的数量',
                        icon: 'none',
                        duration: 2000
                    });
                    return;
                }
            } else {
                if (!this.ordervolume || !this.orderprice) {
                    uni.showToast({
                        title: '请输入有效的数量和价格',
                        icon: 'none',
                        duration: 2000
                    });
                    return;
                }
            }
            let res = await this.$api.createOrder({
                price: this.orderprice,
                orderVolumeType: this.NumTypesvalue,
                volume: this.ordervolume,
                leverageLevel: this.levelNum - 0,
                contractSymbolId: uni.getStorageSync("currentContract").id,
                orderType: this.orderType == 'MARKET' ? 2 : 1,
                // positionType:  this.positionType == 'merged' ? 1 : 2
                side: this.tradeType.toUpperCase(),
            })
            if (res.code == 200) {
                this.ordervolume = ''
                this.orderprice = ''
                this.sliderValue = 0
                this.symbolAvailableBalance()
                uni.showToast({
                    title: '下单成功',
                    icon: 'none',
                    duration: 2000
                });
                this.$emit('submitorder', res.result, this.orderType)
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 2000
                });
            }
        },
        closeLevel() {
            this.showPopup = false
            // this.Lev = !this.Lev;
        },
        onConfirm(data) {
            console.log("确认选择：", data);
            this.showPopup = false;
            this.levelNum = data.value
        },
        onModeConfirm({ marginType, positionType }) {
            this.marginType = marginType
            this.positionType = positionType
            console.log('确认选择的交易模式:', marginType, positionType)
            // this.Mode = false
            // 这里可以继续逻辑：发送接口或更新状态等
        },
        handleSelect(item) {
            if (item) {
                this.orderType = item.value
            }
        },
        handleSelectNum(item) {
            // uni.showToast({
            //     title: `选择了：${item.label}`,
            //     icon: 'none'
            // })
            this.ordervolume = ''
            this.sliderValue = 0
            this.NumTypesvalue = item.value
        },
        changeTab(e) {
            if (this.leftactive == e) return
            // if (this.disabledPoints) {
            // } else {
            //     this.sliderValue = 0
            // }
            // this.orderamount = ''
            this.leftactive = e;
            if (e == 2) {
                this.tradeType = 'sell'
            } else {
                this.tradeType = 'buy'
            }
            console.log('选择了：' + this.tradeType);

            // this.isBuy = !this.isBuy;
            // if (this.marktype == 2) {
            //     return
            // } else {
            //     let price = e == 2 ? this.orderBook.buy[0].price : this.orderBook.sell[this.orderBook.sell.length - 1].price;
            //     this.orderprice = price
            // }
        },
        endMove(e) {
            if (e == 0) {
                this.currentfff = 0
            } else if (e == 25) {
                this.currentfff = 1
            } else if (e == 50) {
                this.currentfff = 2
            } else if (e == 75) {
                this.currentfff = 3
            } else {
                this.currentfff = 4
            }

            // 滑块结束移动时重新计算订单数量
            this.calculateOrderVolume();
        },
        changeMode() {
            // this.Mode = !this.Mode;
            this.$refs.posotionSelectPopup.fetchgetUserInfos()
            this.showPosotionSelectPopupPopup = true
        },
        changeLeverage() {
            this.showPopup = true
            // this.Lev = !this.Lev;
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-slider__button {
    display: none !important;
}

::v-deep .u-input__input {
    color: #000 !important;
    font-family: PingFang SC !important;
    font-weight: 400 !important;
    font-size: 24rpx !important;
}

.trade-box {
    flex: 1;

    .Trading_pair-tab {
        // margin: 39rpx 27rpx 0 21rpx;

        font-family: PingFang SC;
        font-weight: 500;
        font-size: 24rpx;
        color: rgba(0, 0, 0, .4);

        height: 52rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        border-radius: 40rpx;
        width: 100%;
        background: #F6F6F6;

        .buybg {
            background: #30C147
        }

        .sellbg {
            background: #FF82A3
        }

        .left2 {
            margin-left: 224rpx;
        }

        .acleft {
            color: #fff;
        }

        .item {
            display: flex;
            align-items: center;
            justify-content: center;
            // text-align: center;
            width: 200rpx;
            height: 52rpx;
            border-radius: 40rpx;
            position: relative;
            z-index: 2;

            text {
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: bold;
                font-size: 28rpx;
                // color: #555555;
                //   color: #464646;
                // line-height: 60rpx;
            }

            .item {
                &:nth-of-type(1) {
                    border-right: none;
                }

                &:nth-of-type(2) {
                    border-left: none;
                }
            }
        }

        .bgleft {
            position: absolute;
            left: 0;
            top: 0;
            z-index: 1;
            transition: all 0.5s;
            width: 200rpx;
            height: 52rpx;
            border-radius: 40rpx;
        }
    }



    .select-group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 2rpx 0 16rpx 0;

        .fixed-box {
            width: 164rpx;
            height: 54rpx;
            background: #F6F6F6;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 24rpx;
            color: #000;
            position: relative;

            .arrow {
                width: 14rpx;
                height: 10rpx;
                margin-left: 12rpx;

                transition: transform 0.3s ease;

                &.rotated {
                    transform: rotate(180deg);
                }
            }
        }

        .padded-box {
            padding: 8rpx 18rpx;
            background: #F6F6F6;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            position: relative;


            font-family: PingFang SC;
            font-weight: 500;
            font-size: 24rpx;

            color: #30C147;

            .arrow {
                width: 14rpx;
                height: 10rpx;
                margin-left: 12rpx;

                transition: transform 0.3s ease;

                &.rotated {
                    transform: rotate(180deg);
                }
            }
        }
    }


    .input-group {
        display: flex;
        flex-direction: column;
        gap: 16rpx;
        width: 100%;
        margin-top: 16rpx;

        .input-row_limit {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12rpx;

            .input {
                width: 262rpx;
                height: 58rpx;
                border-radius: 8rpx;
                background: #F6F6F6;
            }

            .input-price {
                margin: 0 20rpx;

                .uni-input-placeholder {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: rgba(0, 0, 0, .5) !important;
                }
            }

            .onmarket {
                height: 58rpx;
                border-radius: 8rpx;
                background: #F6F6F6;
                flex: 1;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #000;
                opacity: .5;
            }
        }

        .input-row {
            background: #f6f6f6;
            border-radius: 8rpx;
            padding: 0 22rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 52rpx;
            color: #000;

            // .u-input__input {
            //     font-family: PingFang SC !important;
            //     font-weight: 400 !important;
            //     font-size: 24rpx !important;
            //     color: red !important;
            // }

            .uni-input-placeholder {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                color: rgba(0, 0, 0, .5) !important;
            }

            .num,
            .dan {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                color: rgba(0, 0, 0, .5) !important;

            }

            .dan {
                margin-right: 12rpx;
            }

            .mid {

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                // line-height: 40rpx;
            }

            .icon {
                width: 24rpx;
                height: 24rpx;
            }

            .arrow {
                width: 14rpx;
                height: 10rpx;
            }
        }

        .gray {
            color: rgba(0, 0, 0, .5) !important;

        }

        .flex-expand {
            flex: 1;
        }
    }


    .leverage-section {
        // padding: 40rpx 0;
        margin-top: 60rpx;
        display: flex;
        flex-direction: column;
        // gap: 40rpx;


        .badge-button {
            z-index: 999;
            width: 20rpx;
            height: 20rpx;
            background: #ffffff;
            // border: 2rpx solid #DDDDDD;
            border-radius: 50%;
        }

        .borderbuy {
            border: 4rpx solid #30C147;
        }

        .bordersell {
            border: 4rpx solid #FF82A3;
        }

        .slidelevel {
            // margin-top: 26rpx;
            margin: 17rpx 0 0 0;
            width: 100%;
            display: flex;
            justify-content: space-between;
            flex: 1;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 34rpx;


            text {
                display: block;
                width: 50rpx;
                // margin: 0 10rpx 0 10rpx;
            }
        }
    }

    .leverage-slider {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .leverage-point {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 26rpx;
            color: #000;

            .circle {
                width: 24rpx;
                height: 24rpx;
                border-radius: 50%;
                border: 4rpx solid #e5e5e5;
                margin-bottom: 8rpx;
                background-color: #f6f6f6;
            }

            &.active .circle {
                border-color: #ff69b4;
            }

            &.active {
                color: #ff69b4;
            }
        }
    }

    .checkbox-row {
        display: flex;
        align-items: center;
        margin-top: 30rpx;
        font-weight: 400;
        font-size: 20rpx;
        line-height: 40rpx;
        color: #000;

        .check {
            width: 24rpx;
            height: 24rpx;
        }

        text {
            margin: 0 12rpx;
        }

        .icon {
            width: 24rpx;
            height: 24rpx;
        }
    }

    .availability {
        margin-top: 80rpx;
        display: flex;
        flex-direction: column;
        gap: 20rpx;

        .row {
            display: flex;
            align-items: center;
            justify-content: space-between;


            .label {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 40rpx;
                color: rgba(0, 0, 0, .5);
            }

            .righticon {
                .icon {
                    width: 24rpx;
                    height: 24rpx;
                    margin-left: 12rpx;
                }

                display: flex;
                align-items: center;
            }

            .value {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 28rpx;
                color: #000;
            }


        }
    }

    .submit-button {
        text-align: center;
        margin-top: 68rpx;

        .btn {
            // background: #30C147;
            color: #fff;
            font-family: PingFang SC;
            font-weight: 500;
            border-radius: 40rpx;
            font-size: 28rpx;
            height: 72rpx;
        }
    }


    .price-type,
    .leverage-options,
    .available {
        font-size: 26rpx;
        margin-bottom: 16rpx;
        color: #333;
    }



    .percent-slider {
        display: flex;
        justify-content: space-between;
        margin: 20rpx 0;

        text {
            font-size: 24rpx;
            color: #666;
        }
    }

    .submit-btn {
        background: #16c784;
        color: #fff;
        text-align: center;
        padding: 24rpx;
        border-radius: 16rpx;
        font-size: 28rpx;
        font-weight: bold;
    }
}
</style>