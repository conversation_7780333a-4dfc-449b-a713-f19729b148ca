<template>

    <view class="top-tabs">
        <view class="bottom-tabs-container">
            <!-- tab 滑动栏 -->
            <scroll-view scroll-x class="bottom-tabs-scroll">
                <view class="tab-wrap">
                    <view v-for="(tab, index) in bottomTabs" :key="index" class="tab-item"
                        :class="{ active: currentTab === index }" @click="onTabClick(index)">
                        {{ tab }}
                    </view>
                    <view class="tab-item absolute" @click="nav_to('All-order')">
                        <image src="https://pro-oss.pinkwallet.com/image/1376964711030546432.png" />
                        全部
                    </view>
                </view>
            </scroll-view>

            <!-- 对应内容滑块 -->
            <!-- <swiper class="tab-swiper" :style="{ height: swiperHeight + 'px' }" :current="currentTab"
                @change="onSwiperChange" circular>
                <swiper-item v-for="(tab, index) in bottomTabs" :key="index"> -->
            <view class="tab-swiper">
                <!-- 持仓:id="'content-' + index"-->
                <view class="position-detail" v-if="currentTab == 0">
                    <!-- <view class="platform">
                            <u-button hover-class="none" class="platform-button flex_all">全部平仓</u-button>
                        </view> -->
                    <view class="order-detail">
                        <view class="position-card" v-for="(order, index) in positionList" :key="index">

                            <view class="position-info">
                                <view class="title-row">
                                    <view class="side flex_all"
                                        :style="{ backgroundColor: order.side == 'BUY' ? '#30C147' : '#FF82A3' }">{{
                                            order.side == 'BUY' ? '多' : '空' }}</view>
                                    <text class="symbol">{{ $store.state.symbol.toUpperCase() }} {{ order.leverageLevel + 'X'
                                    }}</text>
                                    <!-- <text class="rate">收益率</text> -->
                                </view>

                                <view class="unrealized">
                                    <view class="item left">
                                        <view class="label">未实现盈亏(USDT)</view>
                                        <view class="value pnl-value" :class="getPnLClass(order)">
                                            {{ formatUnrealizedPnL(order) }}
                                        </view>
                                    </view>

                                    <view class="item right">
                                        <view class="label" style="opacity: 0.4;">收益率</view>
                                        <view class="value profit-rate" :class="getPnLClass(order)">
                                            {{ formatProfitRate(order) }}
                                        </view>
                                    </view>
                                </view>

                                <view class="meta">
                                    <view class="meta-item">
                                        <text class="label">持仓量(张)</text>
                                        <text class="value">{{ order.volume }}</text>
                                    </view>
                                    <view class="meta-item">
                                        <text class="label">保证金</text>
                                        <text class="value">{{ Number(order.holdAmount).toFixed(2) }}</text>
                                    </view>
                                    <view class="meta-item">
                                        <text class="label">开仓价格</text>
                                        <text class="value">{{ order.avgPrice }}</text>

                                    </view>
                                    <view class="meta-item">
                                        <text class="label">开仓时间</text>
                                        <text class="value">{{ formatTimestampms(order.createAt) }}</text>

                                    </view>
                                    <view class="meta-item">
                                        <text class="label">预估强平价</text>
                                        <text class="value">{{ order.reducePrice }}</text>

                                    </view>
                                    <view class="meta-item">
                                        <text class="label">止盈止损</text>
                                        <text class="value"> {{ (order.otoProfitPrice || '--') + '/' +
                                            (order.otoLossPrice || '--') }}</text>
                                    </view>

                                    <!-- { label: '持仓量(张)', value: '333' },
                                        { label: '保证金', value: '3158047' },
                                        { label: '开仓价格', value: '333' },
                                        { label: '标记价格', value: '3158047' },
                                        { label: '预估强平价', value: '-' },
                                        { label: '止盈止损', value: '333/2334' } -->
                                </view>
                            </view>
                            <view class="actions">
                                <u-button @click="handleOpen(i, order)" hover-class="none"
                                    v-for="(btn, i) in filteredButtons(order)" :key="i" class="action-button">{{ btn
                                    }}</u-button>
                            </view>
                            <!-- v-show="!(i === actionButtons.length - 1 && (!order.otoProfitPrice || !order.otoLossPrice))" -->

                        </view>
                        <nodata v-if="!positionList.length" />
                    </view>

                </view>

                <!-- 当前委托 -->
                <view class="position-detail" v-if="currentTab == 1">
                    <view class="order-detail">
                        <view class="order-card" v-for="(order, index) in entrustList" :key="index">
                            <view class="header">
                                <view class="left">
                                    <text class="symbol">{{ $store.state.symbol.toUpperCase() }}永续逐仓 {{ order.leverageLevel + 'X'
                                    }}</text>


                                    <text class="direction" :class="order.side">{{ getOrderDirection(order)
                                    }}</text>
                                </view>
                                <view class="right">
                                    <u-button hover-class="none" class="cancel-btn flex_all"
                                        @click="onCancel(order)">撤单</u-button>
                                </view>
                            </view>

                            <view class="info-list">
                                <!-- v-for="(item, i) in order.info" :key="i" -->
                                <view class="info-item">
                                    <text class="label">委托量(张)</text>
                                    <text class="value">{{ order.volume }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">委托价(USDT)</text>
                                    <text class="value">{{ order.price }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">手续费</text>
                                    <text class="value">{{ order.tradeFee }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">成交量(USDT)</text>
                                    <text class="value">{{ order.dealMoney }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">成交均价(USDT)</text>
                                    <text class="value">{{ order.avgPrice }}</text>
                                </view>
                                <view class="info-item">
                                    <text class="label">下单时间</text>
                                    <text class="value">{{ formatTimestampms(order.createAt) }}</text>
                                </view>
                                <!-- { label: '委托量(张)', value: '1' },
                        { label: '委托价(USDT)', value: '31588047' },
                        { label: '手续费', value: '31588047' },
                        { label: '成交量(USDT)', value: '0' },
                        { label: '成交均价(USDT)', value: '0' },
                        { label: '下单时间', value: '2025–05–14 15:05:41' } -->
                            </view>
                        </view>
                        <nodata v-if="!entrustList.length" />
                    </view>
                </view>

                <!-- 计划委托 -->
                <!-- <view class="position-detail" :id="'content-' + index" v-if="index == 2">
                        <view class="order-detail">
                            <view class="order-card" v-for="(order, index) in Planorders" :key="index">
                                <view class="header">
                                    <view class="left">
                                        <text class="symbol">{{ order.symbol }}</text>
                                        <text class="direction" :class="order.directionClass">{{ order.direction
                                        }}</text>
                                    </view>
                                    <view class="right">
                                        <u-button hover-class="none" class="cancel-btn flex_all"
                                            @click="onCancel(order)">撤单</u-button>
                                    </view>
                                </view>

                                <view class="info-list">
                                    <view class="info-item" v-for="(item, i) in order.info" :key="i">
                                        <text class="label">{{ item.label }}</text>
                                        <text class="value">{{ item.value }}</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <nodata v-if="!Planorders.length" />
                    </view> -->

                <!-- <view class="position-detail" :id="'content-' + index" v-if="index == 3">
                    <nodata />
                </view> -->
            </view>

            <!-- </swiper-item>
            </swiper> -->
        </view>

        <u-popup v-model="showClose" mode="bottom" border-radius="30">
            <closePopup :order="selectedOrder" :pnl="pnl" :rate="rate" @close="showClose = false"
                @confirm="handleCloseConfirm" />
        </u-popup>

        <u-popup v-model="showProfitLoss" mode="bottom" border-radius="30">
            <profitlossPopup :order="selectedOrder" :pnl="pnl" :rate="rate" @close="showProfitLoss = false"
                @confirm="handleshowProfitLossConfirm" />
        </u-popup>

        <!-- <zero-loading type="love" :mask="true" v-if="loading"></zero-loading> -->

    </view>

</template>

<script>
import nodata from "./nodata"
import closePopup from "../popup/close"
import profitlossPopup from "../popup/profitloss"
import { formatTimestampms } from '@/utils/utils'
export default {
    components: {
        nodata,
        closePopup,
        profitlossPopup
    },
    data() {
        return {
            loading: false,
            pnl: "",
            rate: "",
            showProfitLoss: false,
            showClose: false,
            selectedOrder: {},
            nowsymbolobj: {},
            orderStatusTimer: null, // 订单状态查询定时器
            tabClickThrottling: false, // Tab点击节流状态
            orders: [
                {
                    symbol: 'BTC/USDT永续·全仓·合仓100X',
                    direction: '买入开多',
                    directionClass: 'long',
                    info: [
                        { label: '委托量(张)', value: '1' },
                        { label: '委托价(USDT)', value: '31588047' },
                        { label: '手续费', value: '31588047' },
                        { label: '成交量(USDT)', value: '0' },
                        { label: '成交均价(USDT)', value: '0' },
                        { label: '下单时间', value: '2025–05–14 15:05:41' }
                    ]
                },
                // 可继续添加更多订单...
            ],
            Planorders: [
                // {
                //     symbol: 'BTC/USDT永续·全仓·合仓100X',
                //     direction: '买入开多',
                //     directionClass: 'long',
                //     info: [
                //         { label: '委托量(张)', value: '1' },
                //         { label: '触发价(USDT)', value: '31588047' },
                //         { label: '计划类型', value: '31588047' },
                //         { label: '委托价格', value: '0' },
                //         { label: '下单时间', value: '2025–05–14 15:05:41' }
                //     ]
                // },
                // 可继续添加更多订单...
            ],
            // , '计划委托', '跟单持仓'
            bottomTabs: ['持仓', '当前委托'],
            activeBottomTab: '持仓',
            currentTab: 0,
            swiperHeight: 0,
            position: {
                unrealized: '-0.6438',
                percent: '-3.12%'
            },
            holdList: [
                { label: '持仓量(张)', value: '333' },

            ],
            metaList: [
                { label: '持仓量(张)', value: '333' },
                { label: '保证金', value: '3158047' },
                { label: '开仓价格', value: '333' },
                { label: '标记价格', value: '3158047' },
                { label: '预估强平价', value: '-' },
                { label: '止盈止损', value: '333/2334' }
            ],
            // '一键反向',
            actionButtons: ['平仓', '止盈止损', '重置止盈止损'],
            page: {
                pageSize: 10,
                pageNum: 1
            },
            entruHasnext: false,
            entrustList: [],
            positionHasnext: false,
            positionList: [],
            latestPrice: 0,
        }
    },
    watch: {
        currentTab(newVal, oldVal) {

            this.page.pageNum = 1
            this.entrustList = []
            this.positionList = []
            // if (index == 1) {
            //     this.getEntrustList()
            // } else {
            //     this.getCurrentPositionList()
            // }
            this.$nextTick(() => {
                setTimeout(() => {
                    this.updateSwiperHeight()
                }, 500)
            })

            if (newVal === 0) {
                this.getCurrentPositionList()
            } else if (newVal === 1) {
                this.getEntrustList()
            }
        },
        "$store.state.symbol"(val) {
            this.currentTab = 0
            this.$nextTick(() => {
                setTimeout(() => {
                    this.updateSwiperHeight()
                }, 500)
            })
            this.getCurrentPositionList()


        },
        "$store.state.realprice"(val) {
            // console.log(val, ' realprice');
            let a = Number(val)
            let b = a.toFixed(3)
            this.latestPrice = b
        },
    },
    mounted() {
        this.getCurrentPositionList()
        console.log(123);
        this.latestPrice = uni.getStorageSync('currentContract').closePrice
        this.$nextTick(() => {
            setTimeout(() => {
                this.updateSwiperHeight()
            }, 500);
            console.log(133);

        })
        this.nowsymbolobj = uni.getStorageSync('currentContract')
        // this.currentTab = 0;
    },
    // 组件销毁时清除定时器
    beforeDestroy() {
        if (this.orderStatusTimer) {
            clearInterval(this.orderStatusTimer);
            this.orderStatusTimer = null;
        }
    },
    methods: {
        filteredButtons(order) {
            // 如果止盈或止损价格存在，则显示所有按钮
            if (order.otoProfitPrice || order.otoLossPrice) {
                return ['平仓', '止盈止损', '重置止盈止损'];
            }
            // 否则只显示前两个按钮
            return ['平仓', '止盈止损'];
        },
        handleOpen(i, order) {
            console.log(i);

            if (i == 0) {
                this.selectedOrder = order
                this.showClose = true
                this.rate = this.formatProfitRate(order)
                this.pnl = this.formatUnrealizedPnL(order)
            } else if (i == 1) {
                this.showProfitLoss = true
                this.selectedOrder = order
                this.pnl = this.formatUnrealizedPnL(order)
                this.rate = this.formatProfitRate(order)
            } else if (i == 2) {
                this.resetOtoPositions(order)
            }

        },
        async resetOtoPositions(order) {
            let res = await this.$api.resetOtoPosition({
                positionId: order.positionId
            })
            if (res.code == 200) {
                this.pageNum = 1
                this.positionList = []
                uni.showToast({
                    title: '重置止盈止损成功',
                    icon: 'none',
                    duration: 3000
                })
                this.getCurrentPositionList()
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                })
            }
        },
        handleshowProfitLossConfirm(e) {
            if (e) {
                this.showProfitLoss = false
                this.getCurrentPositionList()

                this.$nextTick(() => {
                    setTimeout(() => {
                        this.updateSwiperHeight()
                    }, 500);

                })
            }
        },
        handleCloseConfirm(e) {
            if (e) {
                this.$emit('close', true)
                this.showClose = false
                this.getCurrentPositionList()
                console.log(123);

                this.$nextTick(() => {
                    setTimeout(() => {
                        this.updateSwiperHeight()
                    }, 500);
                    console.log(133);

                })
            }
            // console.log('点击了平仓：', data)
            // 你可以继续调用 API 或展示确认提示
        },
        // 计算未实现盈亏
        calculateUnrealizedPnL(position) {
            if (!position) return 0;

            const multiplier = uni.getStorageSync('currentContract').multiplier || ''
            // const marketPrice = uni.getStorageSync('currentContract').closePrice || ''
            const marketPrice = this.latestPrice
            const {
                volume = 0,           // 张数
                side = '',            // 方向：'BUY'(多) 或 'SELL'(空)
                avgPrice = 0,        // 开仓价
            } = position;

            // 张数 * 面值 * (多 ? 市价 - 开仓价 : 开仓价 - 市价)
            let priceDiff = 0;

            if (side == 'BUY') {
                // 多头：市价 - 开仓价
                priceDiff = Number(marketPrice) - Number(avgPrice);
            } else if (side == 'SELL') {
                // 空头：开仓价 - 市价
                priceDiff = Number(avgPrice) - Number(marketPrice);
            }

            const unrealizedPnL = (Number(volume) * Number(multiplier) * priceDiff)

            return unrealizedPnL;
        },

        // 计算收益率
        calculateProfitRate(position) {
            if (!position) return 0;

            const {
                holdAmount = 0            // 保证金
            } = position;

            // 获取未实现盈亏
            const unrealizedPnL = this.calculateUnrealizedPnL(position);

            // 收益率 = 未实现盈亏 / 保证金
            if (Number(holdAmount) === 0) {
                return 0;
            }

            const profitRate = (unrealizedPnL / Number(holdAmount)) * 100; // 转换为百分比

            return profitRate;
        },

        // 格式化未实现盈亏显示
        formatUnrealizedPnL(position) {
            const pnl = this.calculateUnrealizedPnL(position);
            const formatted = Math.abs(pnl).toFixed(2);
            if (pnl > 0) {
                return `+${formatted}`;
            } else if (pnl < 0) {
                return `-${formatted}`;
            } else {
                return '0.00';
            }
        },

        // 格式化收益率显示
        formatProfitRate(position) {
            const rate = this.calculateProfitRate(position);
            const formatted = Math.abs(rate).toFixed(2);

            if (rate > 0) {
                return `+${formatted}%`;
            } else if (rate < 0) {
                return `-${formatted}%`;
            } else {
                return '0.00%';
            }
        },

        // 获取盈亏状态类名
        getPnLClass(position) {
            const pnl = this.calculateUnrealizedPnL(position);

            if (pnl > 0) {
                return 'profit'; // 盈利
            } else if (pnl <= 0) {
                return 'loss';   // 亏损
            }
        },
        getOrderDirection(order) {
            const { kind, side } = order;
            if (kind === 'OPEN' && side === 'BUY') return '买入开多';
            if (kind === 'OPEN' && side === 'SELL') return '卖出开空';
            if (kind === 'CLOSE' && side === 'BUY') return '买入平空';
            if (kind === 'CLOSE' && side === 'SELL') return '卖出平多';
            return '--';
        },
        fetchPositions(orderNo, side) {
            console.log('12312312', orderNo, side);
            if (side == 'LIMIT') {
                this.startPollingOrderStatus(orderNo);
            }
        },

        // 开始轮询订单状态
        startPollingOrderStatus(orderNo) {
            // 清除之前的定时器（如果存在）
            if (this.orderStatusTimer) {
                clearInterval(this.orderStatusTimer);
            }

            let requestCount = 0;
            const maxRequests = 6;

            // 立即执行第一次请求
            this.fetchorderStatus(orderNo, () => {
                requestCount++;
            });

            // 设置定时器，每秒执行一次
            this.orderStatusTimer = setInterval(async () => {
                requestCount++;

                if (requestCount >= maxRequests) {
                    // 达到最大请求次数，停止查询
                    clearInterval(this.orderStatusTimer);
                    this.orderStatusTimer = null;
                    console.log('订单状态查询已达到最大次数，停止查询');
                    this.getEntrustList();
                    return;
                }

                await this.fetchorderStatus(orderNo, () => {
                    // 请求完成后的回调
                });
            }, 1000);
        },

        async fetchorderStatus(orderNo, callback) {
            try {
                let res = await this.$api.orderStatus({
                    contractSymbolId: uni.getStorageSync('currentContract').id,
                    orderIdStr: orderNo
                });

                console.log('订单状态查询结果:', res);

                // 检查返回结果
                if (res.code === 200 && res.result) {
                    const status = res.result;

                    // 如果状态为2或4，停止查询并调用getEntrustList
                    if (status == 2 || status == 4) {
                        console.log('订单状态已完成，状态码:', status);

                        // 清除定时器
                        if (this.orderStatusTimer) {
                            clearInterval(this.orderStatusTimer);
                            this.orderStatusTimer = null;
                        }

                        // // 调用getEntrustList函数
                        // if (typeof this.getEntrustList === 'function') {
                        this.getEntrustList();
                        // } else {
                        //     console.warn('getEntrustList函数不存在');
                        // }
                    }
                }

                // 执行回调
                if (callback && typeof callback === 'function') {
                    callback();
                }

            } catch (error) {
                console.error('查询订单状态失败:', error);

                // 执行回调
                if (callback && typeof callback === 'function') {
                    callback();
                }
            }
        },
        rearchBottom() {
            if (this.currentTab == 1) {
                if (this.entruHasnext) {
                    this.page.pageNum += 1
                    this.getEntrustList()
                }
            } else {
                if (this.positionHasnext) {
                    this.page.pageNum += 1
                    this.getCurrentPositionList()
                }
            }
        },
        async getEntrustList() {
            let res = await this.$api.delegatingOrder({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                contractSymbolId: uni.getStorageSync("currentContract").id,
            })
            if (res.code == 200) {
                // this.entrustList = res.result.data
                this.entruHasnext = res.result.hasNext
                if (this.page.pageNum != 1) {
                    this.entrustList = this.entrustList.concat(res.result.data)
                } else {
                    this.entrustList = res.result.data
                    // this.entrustList = this.entrustList.concat(res.result.data)

                }
            }
        },
        async getCurrentPositionList() {
            this.loading = true
            let res = await this.$api.currentPosition({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                contractSymbolId: uni.getStorageSync("currentContract").id,
            })
            if (res.code == 200) {
                this.positionHasnext = res.result.hasNext

                if (this.page.pageNum == 1) {
                    this.positionList = res.result.data
                } else {
                    this.positionList = this.positionList.concat(res.result.data)
                }
                // if (this.page.pageNum != 1) {
                //     this.positionList = this.positionList.concat(res.result.data)
                // } else {
                //     this.positionList = res.result.data
                // }
                setTimeout(() => {
                    this.loading = false                    
                }, 3000);
            }
            
        },
        async onCancel(order) {
            if (order.status == 0 || order.status == 1 || order.status == 3 || order.status == 5) {
                let res = await this.$api.cancelOrder({
                    orderIdStr: order.id,
                    contractSymbolId: uni.getStorageSync("currentContract").id,
                })
                if (res.code == 200) {
                    uni.showToast({
                        title: '撤单成功',
                        icon: 'none',
                        duration: 3000
                    })
                    this.$emit('cancel', true)
                    this.getEntrustList()
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none',
                        duration: 3000
                    })
                }
            }
        },
        onTabClick(index) {
            // 节流控制，防止快速点击
            if (this.tabClickThrottling) {
                return;
            }

            // this.$nextTick(() => {

            // })

            this.tabClickThrottling = true;

            // 500ms 后重置节流状态
            setTimeout(() => {
                this.tabClickThrottling = false;
            }, 500);


            this.currentTab = index;


            // this.page.pageNum = 1
            // this.entrustList = []
            // this.positionList = []
            // if (index == 1) {
            //     this.getEntrustList()
            // } else {
            //     this.getCurrentPositionList()
            // }


            // this.$nextTick(() => {
            //     setTimeout(() => {
            //         this.updateSwiperHeight()
            //     }, 500)
            // })

        },
        onSwiperChange(e) {
            this.currentTab = e.detail.current;
        },
        updateSwiperHeight() {
            const id = `content-${this.currentTab}`
            uni.createSelectorQuery()
                .in(this)
                .select(`#${id}`)
                .boundingClientRect((res) => {
                    console.log(res);

                    if (res) this.swiperHeight = res.height
                })
                .exec()
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        },
        formatTimestampms
    }
}
</script>

<style lang="scss" scoped>
.top-tabs {
    margin-top: 34rpx;
    margin-bottom: 100rpx;
}

.bottom-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 24rpx;
    width: 100%;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 34rpx;

    .tab {
        color: rgba(0, 0, 0, .4);

        &.active {
            background: #F6F6F6;
            border-radius: 8rpx;
            padding: 11rpx 19rpx;
            color: #000;
            font-weight: 500;
        }
    }
}

.bottom-tabs-container {

    // tab栏
    .bottom-tabs-scroll {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        background: #fff;

        .tab-wrap {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            // height: 64rpx;
            // padding: 0 16rpx;
            gap: 16rpx;

            // 52
            .absolute {
                position: absolute;
                right: 0;
            }

            .tab-item {
                // flex: 1 0 auto;
                padding: 11rpx 19rpx;
                font-size: 24rpx;
                color: rgba(0, 0, 0, 0.4);
                text-align: center;
                z-index: 1;
                // position: relative;
                display: flex;
                align-items: center;
                justify-content: center;

                image {
                    margin-right: 12rpx;
                    width: 28rpx;
                    height: 30rpx;
                }
            }

            .tab-item.active {
                background: #F6F6F6;
                padding: 11rpx 19rpx;
                border-radius: 8rpx;
                color: #000;
                font-weight: 500;
            }
        }

    }

    // 内容
    .tab-swiper {
        // padding-bottom: 100rpx;

        margin: 24rpx 0 0 0;
        // height: fit-content;
        min-height: 500rpx;

        // 当前委托,计划委托
        .order-detail {
            padding-bottom: 100rpx;

            .order-card {
                margin-bottom: 56rpx;

                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 24rpx;

                    .left {
                        display: flex;
                        flex-direction: column;

                        .symbol {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 24rpx;
                            line-height: 34rpx;
                            color: #000;
                        }

                        .direction {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 24rpx;
                            line-height: 34rpx;

                            &.SELL {
                                color: #FF82A3;
                            }

                            &.BUY {
                                color: #30C147;
                            }
                        }
                    }

                    .right {
                        .cancel-btn {
                            width: 122rpx;
                            height: 60rpx;
                            background-color: #F6F6F6;
                            font-family: PingFang SC;
                            font-weight: 500;
                            font-size: 24rpx;
                            color: #000;
                            border-radius: 40rpx;
                            margin: 0;
                        }
                    }
                }

                .info-list {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: flex-start;

                    .info-item {
                        width: 33.33%;
                        margin-bottom: 20rpx;
                        display: flex;
                        flex-direction: column;

                        &:nth-child(3n + 1) {
                            align-items: flex-start;
                        }

                        &:nth-child(3n + 2) {
                            align-items: center;
                        }

                        &:nth-child(3n) {
                            align-items: flex-end;
                        }

                        .label {
                            font-family: PingFang SC;
                            font-weight: 400;
                            font-size: 20rpx;
                            line-height: 28rpx;
                            color: rgba(0, 0, 0, 0.4);
                        }

                        .value {
                            font-family: PingFang SC;
                            font-weight: 500;
                            font-size: 20rpx;
                            line-height: 28rpx;
                            margin-top: 4rpx;
                            color: #000;
                        }
                    }
                }
            }

            .position-card {
                margin-bottom: 56rpx;

                .position-info {

                    .title-row {
                        display: flex;
                        align-items: center;
                        margin-bottom: 18rpx;

                        .side {
                            width: 30rpx;
                            height: 30rpx;
                            border-radius: 7rpx;
                            margin-right: 16rpx;
                            font-family: PingFang SC;
                            font-weight: 500;
                            font-size: 14rpx;
                            color: #FFFFFF;
                        }

                        .symbol {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 24rpx;
                            line-height: 34rpx;
                            color: #000;
                        }
                    }

                    .unrealized {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 14rpx;

                        .left {
                            align-items: flex-start;
                        }

                        .right {
                            align-items: flex-end;
                        }

                        .item {
                            display: flex;
                            flex-direction: column;


                            .label {
                                font-family: PingFang SC;
                                font-weight: 400;
                                font-size: 20rpx;
                                line-height: 28rpx;
                                color: #000000;
                            }

                            .value {
                                margin-top: 8rpx;
                                font-family: PingFang SC;
                                font-weight: 500;
                                font-size: 20rpx;
                                line-height: 28rpx;
                                // color: #FF82A3;
                            }
                        }

                    }

                    .meta {
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: space-between;

                        .meta-item {
                            display: flex;
                            flex-direction: column;
                            // align-items: flex-start;
                            width: 33%;
                            margin-bottom: 18rpx;

                            .label {
                                font-family: PingFang SC;
                                font-weight: 400;
                                font-size: 20rpx;
                                line-height: 28rpx;
                                color: rgba(0, 0, 0, .4);
                            }

                            .value {
                                margin-top: 4rpx;
                                font-family: PingFang SC;
                                font-weight: 500;
                                font-size: 20rpx;
                                line-height: 28rpx;
                                color: #000;
                            }

                            // 第1列
                            &:nth-child(3n + 1) {
                                align-items: flex-start;
                            }

                            // 第2列
                            &:nth-child(3n + 2) {
                                align-items: center;
                            }

                            // 第3列
                            &:nth-child(3n) {
                                align-items: flex-end;
                            }
                        }
                    }
                }

                .actions {
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;
                    margin-top: 28rpx;
                    gap: 14rpx;

                    .action-button {
                        // flex: 1;
                        margin: 0;
                        width: 220rpx;
                        height: 60rpx;
                        border-radius: 40rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        font-size: 24rpx;
                        background-color: #f6f6f6;
                        color: #000;


                    }
                }
            }
        }

        .position-detail {
            margin-bottom: 56rpx;

            .platform {
                // display: flex;
                // justify-content: flex-start;
                margin-bottom: 24rpx;

                .platform-button {
                    margin: 0;
                    width: 182rpx;
                    height: 60rpx;
                    border-radius: 40rpx;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 24rpx;
                    background-color: #f6f6f6;
                    color: #000;
                }
            }

        }

    }
}

/* 盈亏状态样式 */
.profit {
    color: #00C853 !important;
    /* 绿色 - 盈利 */
}

.loss {
    color: #FF82A3 !important;
    /* 红色 - 亏损 */
}

.neutral {
    color: #9E9E9E !important;
    /* 灰色 - 持平 */
}

/* 盈亏数值样式 */
.pnl-value {
    font-weight: 500;
    font-size: 28rpx;
}

.profit-rate {
    font-size: 24rpx;
    margin-top: 4rpx;
}
</style>