import store from "@/store";
import zlib from "zlib";
import <PERSON><PERSON> from "pako";
function HQData() {}

let socketApi;
// #ifdef H5
(socketApi = process.env.VUE_APP_WS_API_URL),
  // #endif

  // #ifdef APP-PLUS
  (socketApi = getApp().globalData.socketApi),
  // #endif
  (HQData.KLineApiUrl = getApp().globalData.historyApi);
HQData.WSKLineUrl = socketApi;

//okx周期对应 hqchart内部的周期值
let PERIOD_NAME = new Map([
  [5, { HTTP: "5min", WS: "5min" }], //5 minute
  [7, { HTTP: "30min", WS: "30min" }], //30 minute
  [8, { HTTP: "60min", WS: "60min" }], //60 minute
  [4, { HTTP: "4h", WS: "4h" }], //240 minute
  [12, { HTTP: "1day", WS: "1day" }], //1day
  [6, { HTTP: "1week", WS: "1week" }], //1week
]);

HQData.GetPeriodName = function (period) {
  if (!PERIOD_NAME.has(period)) return null;
  return PERIOD_NAME.get(period);
};

HQData.GetPeriodID = function (periodName) {
  for (let item of PERIOD_NAME) {
    // if (item[1].HTTP == periodName) return item[0]
    if (periodName.includes(item[1].WS)) return item[0];
    // if (item[1].WS == periodName) return item[0];
  }

  return null;
};

HQData.RequestFlowCapitalData = function (data, callback) {
  data.PreventDefault = true;
  let hqChartData = { code: 0, stock: [] }; //如果没有数据就填空

  if (data.Self.IsDestroy == false) {
    callback(hqChartData);
  }
};

(HQData.ToOKXSymbol = function (symbol) {
  let upperSymbol = symbol.toUpperCase();
  let value = upperSymbol.replace(".BIT", "");
  return value;
}),
  (HQData.GetKLineUrl = function (symbol, period, limit) {
    let url = "";
    let time = Date.now();
    console.log("请求参数", time, symbol, limit, period);
    // let url = `https://www.okx.com/priapi/v5/market/candles?t=${time}&instId=${symbol}&limit=${limit}&bar=${period}`
    // console.log('请求参数', url)
    url = `${HQData.KLineApiUrl}?symbol=${symbol}&period=${period}`;
    // let url = `http://192.168.1.3:7001/ok?symbol=${symbol}&period=${period}`
    console.log("历史数据", url);
    return url;
  });

//全量日线数据
HQData.RequestHistoryData = function (data, callback, option) {
  console.log("日线数据", data);
  data.PreventDefault = true;
  let symbol = data.Request.Data.symbol; //请求的股票代码
  let period = data.Request.Data.period; //周期

  let url = HQData.GetKLineUrl(this.ToOKXSymbol(symbol));
  // HQData.GetPeriodName((period).HTTP, 200)
  console.log(`[HQData::RequestHistoryData] Symbol=${symbol}, url=${url}`);

  if (option && option.KLineWS) option.KLineWS.UnsubKLine();

  let subData = {
    event: "req",
    params: {
      channel:
        `market_e_${store.state.symbol}_kline_` +
        HQData.GetPeriodName(period).HTTP,
      cb_id: "1",
    },
  };
  console.log("这里");

  let message = JSON.stringify(subData);

  uni.sendSocketMessage({
    data: message,
    complete(res2) {
      setTimeout(() => {
        console.log("发送的消息req", hisdata, res2);
        let hisdata = store.state.klinedata;
        HQData.RecvHistoryMinuteData(hisdata, callback, data, option);
      }, 200);
    },
  });

  // uni.request(
  //     {
  //         url: url,
  //         type: "get",
  //         success: (recvData) => {
  //             console.log("整的历史数据", recvData)
  //             HQData.RecvHistoryData(recvData.data, callback, data, option)
  //         },
  //     })
};

HQData.RecvHistoryData = function (recvdata, callback, data, option) {
  // let symbol = data.Request.Data.symbol
  let hqChartData = {
    symbol: symbol,
    // symbol:'123',
    name: HQData.ToOKXSymbol(symbol),
    data: [],
  };

  let yClose = null; //前收盘
  for (let i = recvdata.data.length - 1; i >= 0; --i) {
    let item = recvdata.data[i];
    let dateTime = new Date();
    dateTime.setTime(item[0]);
    let date =
      dateTime.getFullYear() * 10000 +
      (dateTime.getMonth() + 1) * 100 +
      dateTime.getDate();
    let time = dateTime.getHours() * 100 + dateTime.getMinutes();
    let open = parseFloat(item[1]);
    let high = parseFloat(item[2]);
    console.log("最高", high);
    let low = parseFloat(item[3]);
    let close = parseFloat(item[4]);
    let vol = parseFloat(item[5]);
    let newItem = [date, yClose, open, high, low, close, vol, null, time];

    yClose = close;
    hqChartData.data.push(newItem);
  }

  if (data.Self.IsDestroy == false) {
    //console.log("[HQData.RecvHistoryData] hqchartData ", hqChartData);

    // #ifdef H5
    callback(hqChartData);
    // #endif

    // #ifndef H5
    callback({ data: hqChartData });
    // #endif

    //ws数据订阅
    if (option && option.KLineWS) {
      let requestData = data.Request.Data;
      let symbol = this.ToOKXSymbol(requestData.symbol); //请求的股票代码
      let period = requestData.period; //周期

      console.log(
        `[HQData.RecvHistoryMinuteData]  SubKLine(${symbol}, ${period} ) `
      );
      option.KLineWS.SubKLine(symbol, period);
    }
  }
};

//增量日线数据
HQData.RecvRealtimeData = function (recvdata, internalChart) {
  console.log("[HQData::RecvRealtimeData] recvdata", recvdata);
  if (!recvdata.data || recvdata.data.length <= 0) return;
  let arg = recvdata.arg;
  let newItem = { symbol: arg.instId + ".bit", name: arg.instId };
  for (let i = recvdata.data.length - 1; i >= 0; --i) {
    let item = recvdata.data[i];
    let dateTime = new Date();
    dateTime.setTime(item[0]);
    let date =
      dateTime.getFullYear() * 10000 +
      (dateTime.getMonth() + 1) * 100 +
      dateTime.getDate();
    let time =
      dateTime.getHours() * 10000 +
      dateTime.getMinutes() * 100 +
      dateTime.getSeconds();
    let open = parseFloat(item[1]);
    let high = parseFloat(item[2]);
    let low = parseFloat(item[3]);
    let close = parseFloat(item[4]);
    let vol = parseFloat(item[5]);
    let amount = parseFloat(item[7]);

    newItem.date = date;
    newItem.time = time;
    newItem.price = close;
    newItem.high = high;
    newItem.low = low;
    newItem.open = open;
    newItem.yclose = null;
    newItem.vol = vol;
    newItem.amount = amount;
  }

  let hqChartData = { stock: [newItem], code: 0 };

  // #ifdef H5
  internalChart.RecvRealtimeData(hqChartData);
  // #endif

  // #ifndef H5
  internalChart.RecvRealtimeData({ data: hqChartData });
  // #endif
};

//拿到全量分钟数据
HQData.RequestHistoryMinuteData = function (data, callback, option) {
  console.log("拿到全量分钟数据", data);
  data.PreventDefault = true;
  let period = data.Request.Data.period;
  let symbol = data.Request.Data.symbol;
  console.log(
    data,
    callback,
    option,
    "market_e_" + symbol + "_kline_" + HQData.GetPeriodName(period).HTTP,
    "options"
  );
  // store.commit("changeKline", []);
  let subData = {
    event: "req",
    params: {
      channel:
        "market_e_" + symbol + "_kline_" + HQData.GetPeriodName(period).HTTP,
      cb_id: "1",
    },
  };

  let message = JSON.stringify(subData);

  uni.sendSocketMessage({
    data: message,
    complete(res2) {
      // setTimeout(() => {
      console.log("发送的消息req", res2);
      if (res2.errMsg != "sendSocketMessage:ok") {
        console.log("发送失败");
        // uni.sendSocketMessage({
        //   data: message,
        //   complete(res) {},
        // });
      }

      setTimeout(() => {
        let hisdata = store.state.klinedata;
        console.log(hisdata, "RecvHistoryMinuteDataK里呢");
        HQData.RecvHistoryMinuteData(hisdata, callback, data, option);
      }, 1000);
    },
  });
};

//处理全量分钟数据
HQData.RecvHistoryMinuteData = function (res, callback, data, option) {
  console.log(store.state.klinedata, res, "历史kRecvHistoryMinuteData");

  // let symbol = data.Request.Data.symbol
  console.log(res, callback, data, option, "RecvHistoryMinuteData");
  // console.log(res);

  let hqChartData = {
    symbol: "ETH-USDT.bit",
    name: 321,
    data: [],
  };

  let reversedArray = [];

  for (let i = res.length - 1; i >= 0; i--) {
    reversedArray.push(res[i]);
  }

  let yClose = null; //前收盘
  for (let i = reversedArray.length - 1; i >= 0; --i) {
    let item = reversedArray[i];
    let dateTime = new Date();

    // let dates = new Date(item.ds);
    // var timestamp = dates.getTime();
    // var timestampSeconds = Math.floor(timestamp);
    dateTime.setTime(item.idx * 1000);

    let date =
      dateTime.getFullYear() * 10000 +
      (dateTime.getMonth() + 1) * 100 +
      dateTime.getDate();
    let time = dateTime.getHours() * 100 + dateTime.getMinutes();
    let open = item.open;
    let high = item.high;
    // console.log('最高', high)
    let low = item.low;
    // console.log('最低', low)
    let close = item.close;
    let vol = item.vol;
    let newItem = [date, yClose, open, high, low, close, vol, null, time];
    //最高最低数据

    yClose = close;
    hqChartData.data.push(newItem);
    // console.log("最终数据", hqChartData)
  }
  // let hqChartData = {
  //   symbol: "BTC-USDT.bit",
  //   name: "BTC-USDT",
  //   data: [
  //     [20231208, null, 43219.2, 43241.6, 43216.2, 43216.2, 1, null, 1553],
  //     [20231208, 43216.2, 43216.2, 43234.9, 43216.2, 43220.3, 1, null, 1554],
  //     [20231208, 43220.3, 43220.3, 43235.4, 43219.9, 43225.7, 1, null, 1555],
  //     [20231208, 43225.7, 43225.6, 43228.4, 43223.5, 43225.8, 1, null, 1556],
  //     [20231208, 43225.8, 43225.6, 43225.6, 43187.9, 43189.7, 1, null, 1557],
  //     [20231208, 43189.7, 43189.7, 43192.8, 43187.2, 43190, 1, null, 1558],
  //     [20231208, 43190, 43190, 43190.8, 43163.8, 43163.9, 1, null, 1559],
  //     [20231208, 43163.9, 43163.9, 43189.7, 43163.2, 43188, 1, null, 1600],
  //     [20231208, 43188, 43188.3, 43199, 43142.6, 43147.1, 1, null, 1601],
  //     [20231208, 43147.1, 43146.7, 43167.1, 43136.4, 43166.4, 1, null, 1602],
  //     [20231208, 43166.4, 43166.5, 43190.8, 43166.2, 43176.8, 1, null, 1603],
  //     [20231208, 43176.8, 43176.8, 43213.5, 43176.8, 43206, 1, null, 1604],
  //     [20231208, 43206, 43206, 43212.9, 43186.5, 43210.7, 1, null, 1605],
  //     [20231208, 43210.7, 43210.7, 43214.2, 43198.1, 43202.1, 1, null, 1606],
  //     [20231208, 43202.1, 43202.2, 43204.2, 43158, 43158, 1, null, 1607],
  //     [20231208, 43158, 43158, 43168.5, 43152, 43152.4, 1, null, 1608],
  //     [20231208, 43152.4, 43152.1, 43157.9, 43114, 43127.1, 1, null, 1609],
  //     [20231208, 43127.1, 43127.1, 43127.1, 43103.3, 43103.3, 1, null, 1610],
  //     [20231208, 43103.3, 43103.3, 43177.5, 43099.9, 43169.1, 1, null, 1611],
  //     [20231208, 43169.1, 43169.1, 43183.3, 43158.1, 43183.3, 1, null, 1612],
  //     [20231208, 43183.3, 43183.3, 43189.1, 43172.2, 43189.1, 1, null, 1613],
  //     [20231208, 43189.1, 43189.1, 43196.9, 43188.9, 43196, 1, null, 1614],
  //     [20231208, 43196, 43196, 43204.4, 43167.1, 43185.1, 1, null, 1615],
  //     [20231208, 43185.1, 43185.9, 43186.1, 43161.7, 43166.6, 1, null, 1616],
  //     [20231208, 43166.6, 43166.6, 43195.6, 43163.9, 43170.3, 1, null, 1617],
  //     [20231208, 43170.3, 43170.1, 43191.5, 43164.4, 43189.7, 1, null, 1618],
  //     [20231208, 43189.7, 43189.4, 43189.4, 43164.5, 43167, 1, null, 1619],
  //     [20231208, 43167, 43167, 43184.9, 43165.5, 43184.4, 1, null, 1620],
  //     [20231208, 43184.4, 43184.4, 43209.3, 43170.2, 43208.5, 1, null, 1621],
  //     [20231208, 43208.5, 43208.5, 43222.4, 43200.5, 43214.5, 1, null, 1622],
  //     [20231208, 43214.5, 43214.5, 43223.4, 43212.4, 43222.2, 1, null, 1623],
  //     [20231208, 43222.2, 43222.2, 43227.5, 43212.3, 43227.4, 1, null, 1624],
  //     [20231208, 43227.4, 43227.5, 43234.6, 43225.2, 43234.6, 1, null, 1625],
  //     [20231208, 43234.6, 43234.6, 43260.5, 43234.6, 43248.4, 1, null, 1626],
  //     [20231208, 43248.4, 43248.4, 43248.4, 43230.4, 43240.9, 1, null, 1627],
  //     [20231208, 43240.9, 43241, 43249.1, 43233.8, 43233.8, 1, null, 1628],
  //     [20231208, 43233.8, 43233.8, 43239.6, 43231.7, 43232, 1, null, 1629],
  //     [20231208, 43232, 43232, 43232.4, 43228.7, 43230, 1, null, 1630],
  //     [20231208, 43230, 43230, 43247.2, 43230, 43242.6, 1, null, 1631],
  //     [20231208, 43242.6, 43242.6, 43251.1, 43242.6, 43250.5, 1, null, 1632],
  //     [20231208, 43250.5, 43250.5, 43254.6, 43249.3, 43254.2, 1, null, 1633],
  //     [20231208, 43254.2, 43254.2, 43254.2, 43250.4, 43252.3, 1, null, 1634],
  //     [20231208, 43252.3, 43252.3, 43253.8, 43238.3, 43253.3, 1, null, 1635],
  //     [20231208, 43253.3, 43253.3, 43253.3, 43230.9, 43230.9, 1, null, 1636],
  //     [20231208, 43230.9, 43230.8, 43257.4, 43228.4, 43257.2, 1, null, 1637],
  //     [20231208, 43257.2, 43257.2, 43258.2, 43248.8, 43258.1, 1, null, 1638],
  //     [20231208, 43258.1, 43258.1, 43258.9, 43255.3, 43255.3, 1, null, 1639],
  //     [20231208, 43255.3, 43255.6, 43257.5, 43250.1, 43257.5, 1, null, 1640],
  //     [20231208, 43257.5, 43257.5, 43257.5, 43255.4, 43255.4, 1, null, 1641],
  //     [20231208, 43255.4, 43255.4, 43255.4, 43229.2, 43229.5, 1, null, 1642],
  //     [20231208, 43229.5, 43229.5, 43229.5, 43228, 43228.2, 1, null, 1643],
  //     [20231208, 43228.2, 43228.2, 43246.1, 43221.6, 43246.1, 1, null, 1644],
  //     [20231208, 43246.1, 43245.7, 43246.3, 43211.4, 43211.9, 1, null, 1645],
  //     [20231208, 43211.9, 43211.9, 43212.3, 43206.2, 43206.2, 1, null, 1646],
  //     [20231208, 43206.2, 43205.9, 43225.1, 43203.8, 43203.8, 1, null, 1647],
  //     [20231208, 43203.8, 43203.3, 43203.3, 43186.8, 43186.9, 1, null, 1648],
  //   ],
  //   ver: 2,
  //   code: 0,
  // };

  console.log(hqChartData, data, option, "6666"); // 在这里订阅
  if (data.Self.IsDestroy == false) {
    // #ifdef H5
    callback(hqChartData);
    // #endif

    // #ifndef H5
    callback({ data: hqChartData });
    // #endif

    //ws数据订阅
    if (option && option.KLineWS) {
      let requestData = data.Request.Data;
      let symbol = this.ToOKXSymbol(requestData.symbol); //请求的股票代码
      let period = requestData.period; //周期

      console.log(
        `[HQData.RecvHistoryMinuteData]  SubKLine(${symbol}, ${period} ) `
      );
      setTimeout(() => {
        option.KLineWS.SubKLine(symbol, period);
      }, 100);
    }
  }
};

// 处理增量分钟数据
HQData.RecvMinuteRealtimeData = function (res, internalChart) {
  // console.log("[HQData::RecvMinuteRealtimeData] recvdata", res)
  // console.log('进来RecvMinuteRealtimeData');

  if (!res.tick) return;
  let hqChartData = {
    symbol: res.channel,
    Name: res.channel,
    data: [],
    ver: 2,
  };

  let yClose = null; //前收盘
  let arr = [];
  arr.push(res.tick);
  // console.log(arr,'[HQData::RecvMinuteRealtimeData]');

  for (let i = arr.length - 1; i >= 0; --i) {
    let item = arr[i];
    let dateTime = new Date();

    // let dates = new Date(item.ds);
    // var timestamp = dates.getTime();
    // var timestampSeconds = Math.floor(timestamp);
    // dateTime.setTime(timestampSeconds)
    // let dates = new Date(item.ds);
    // var timestamp = dates.getTime();
    // var timestampSeconds = Math.floor(timestamp);
    dateTime.setTime(item.idx * 1000);

    let date =
      dateTime.getFullYear() * 10000 +
      (dateTime.getMonth() + 1) * 100 +
      dateTime.getDate();
    let time = dateTime.getHours() * 100 + dateTime.getMinutes();
    let open = item.open;
    let high = item.high;
    // console.log('最高', high)
    let low = item.low;
    // console.log('最低', low)
    let close = item.close;
    let vol = item.vol;
    let newItem = [date, yClose, open, high, low, close, vol, null, time];
    //最高最低数据

    yClose = close;
    hqChartData.data.push(newItem);
  }

  // console.log("最终数据", hqChartData)
  // #ifdef H5
  internalChart.RecvMinuteRealtimeData(hqChartData);
  // #endif

  // #ifndef H5
  internalChart.RecvMinuteRealtimeData({ data: hqChartData });
  // #endif
};

function JSHQWebSocket() {
  this.Url = HQData.WSKLineUrl;
  this.Socket = null;
  this.HQChart = null; //hqchart实例
  this.LastSubData = null; //最后一次订阅的信息
  this.InitalSubData = null;
  this.IsReady = false;

  this.Create = function () {
    if (!this.Socket) {
      console.log("开始打开socket");
      this.IsReady = true;
      if (this.InitalSubData) {
        console.log("进去咩有");
        let subData = this.InitalSubData;
        this.InitalSubData = null;
        let message = JSON.stringify(subData);
        console.log("发送的数据", message);
        let that = this;
        // uni.connectSocket({
        //     url: getApp().globalData.socketApi,
        //     success: (res) => { },
        // })
        uni.sendSocketMessage({
          data: message,
          complete(res) {
            console.log("发送的消息", res);

            // uni.sendSocketMessage({
            //     data: message,

            // })

            // if (res.errMsg && res.errMsg.includes('fail')) {
            //     console.log("消息发送失败，尝试重新连接", res);
            //     that.reconnectAndSend(message);
            // } else {
            //     console.log("发送的消息", res);
            // }
          },
        });
        this.LastSubData = subData;
      }
    }

    uni.onSocketMessage((event) => {
      // setTimeout(() => {
      this.OnRecvMessage(event);
      // }, 300)
    });
    HQData.ReceiveSocketData = (data) => {
      // console.log("传过来的值", data)
      let event = data;
      this.OnRecvMessage(event);
    };
  };

  this.OnConnectSuccess = function () {
    console.log("[JSHQWebSocket::OnConnectSuccess]");
  };

  //取消订阅
  this.UnsubKLine = function () {
    if (this.LastSubData) {
      let unsubData = { event: "unsub", params: this.LastSubData };
      let message = JSON.stringify(unsubData);
      uni.sendSocketMessage({ data: message });
      this.LastSubData = null;
    }
  };

  this.SubKLine = function (symbol, period) {
    console.log(symbol, 1231231212);
    let symbols = symbol.toLowerCase();
    // args: [{channel: HQData.GetPeriodName(period).WS, instId: symbols}],
    //
    console.log(HQData.GetPeriodName(period).HTTP);

    let subData = {
      event: "sub",
      params: {
        channel:
          "market_e_" + symbols + "_kline_" + HQData.GetPeriodName(period).HTTP,
        cb_id: "e_aaveusdt",
      },
    };
    if (!this.IsReady) {
      this.InitalSubData = subData;
      return;
    }

    // this.UnsubKLine()

    let message = JSON.stringify(subData);
    uni.sendSocketMessage({ data: message });
    this.LastSubData = subData;
  };

  // 转化orderbook 数据格式
  this.transformData = function (data) {
    const result = {
      buy: [],
      sell: [],
    };

    data.buys.forEach(([price, amount]) => {
      result.buy.push({ price, amount });
    });

    data.asks.forEach(([price, amount]) => {
      result.sell.push({ price, amount });
    });

    const results = {
      buy: [...result.buy].sort((a, b) => b.price - a.price),
      sell: [...result.sell].sort((a, b) => b.price - a.price),
    };
    // console.log(results, 'resultss');
    return results;
  };

  this.OnRecvMessage = function (event) {
    var uint8array = new Uint8Array(event.data);
    const output = Pako.inflate(uint8array, {
      to: "string",
    });

    let res = JSON.parse(output);
    let arg = res.channel; // ws的channel

    console.log(res, "hqchartdata receive");

    // 存储历史k线
    if (res.event_rep == "rep" && !res.channel.includes("_info")) {
      // console.log(res, "历史k");
      if (store.state.klineid != HQData.GetPeriodID(res.channel)) {
        // store.commit("changeKline", [])
      }
      if (res.data) {
        // store.commit("changeKline", []);
        // console.log("拿到全量分钟数据12");
        store.commit("changeKline", res.data);
        console.log(store.state.klinedata, "历史k");
      }
    }

    // // .includes(keyword)
    // // 存储资金费率
    // // if (arg == "market_aaveusdt_info") {
    // if (arg.includes("info")) {
    //   store.commit("changeIndexPrice", res.data.indexPrice);
    //   store.commit("changeFundRate", res.data.nextFundRate);
    // }

    // // if (arg == "market_e_aaveusdt_depth_step0") {
    // if (arg.includes("depth_step0")) {
    //   // 盘口和最新价格
    //   if (res.data) {
    //     uni.setStorageSync("depthStorage", this.transformData(res.data));
    //     store.commit("changeDepths", this.transformData(res.data));
    //   }
    //   // } else if (arg == "market_e_aaveusdt_trade_ticker") {
    // } else if (arg.includes("trade_ticker")) {
    //   if (res.tick) {
    //     const tickId = res.tick.id;
    //     const matchingDataObject = res.tick.data.find(
    //       (obj) => obj.id === tickId
    //     );
    //     if (matchingDataObject) {
    //       const price = matchingDataObject.price;
    //       store.commit("changePrice", matchingDataObject.price);
    //       store.commit("changeSide", matchingDataObject.side);
    //       uni.setStorageSync(res.channel + "_realPrice", price);
    //       uni.setStorageSync("side", matchingDataObject.side);
    //     } else {
    //       // console.log("No matching data object found.");
    //     }
    //   } else {
    //     // uni.setStorageSync(res.channel + "_realPrice", 0.00);
    //   }
    // }

    // if (!res.data) return
    // let recvdata = event.data
    // if (!recvdata.data || recvdata.data.length <= 0 || !recvdata.arg) return
    let period = HQData.GetPeriodID(arg); // 获取周期ID
    // store.commit("changeKlineid", period);
    let internalChart = this.HQChart.JSChartContainer; //  获取HQChart实例
    if (period != internalChart.Period) return; // 不是订阅k线的周期，不处理
    HQData.RecvMinuteRealtimeData(res, internalChart);
  };

  this.Close = function () {
    if (this.Socket) {
      this.Socket.close();
      this.Socket = null;
    }
  };
}

export default {
  HQData: HQData,
  HQWebSocket: JSHQWebSocket,
};
