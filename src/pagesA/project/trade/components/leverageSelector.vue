<template>
    <view class="leverage-selector">
        <view class="header">
            <text class="title">调整杠杆</text>
            <image class="close" @click="$emit('close')"
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377388318231715840.png" />
        </view>

        <view class="slider-box">
            <view class="input-box">
                <u-input :height="80" v-model="sliderValue" placeholder="" :clearable="false" type="digit" />
                <view class="right">
                    <text class="multiply">X</text>
                    <view class="shu"></view>
                    <text class="max-leverage" @click="makehigh">最高倍数{{ maxLeverage }}X</text>
                </view>

            </view>

            <!-- <view class="slider-bar" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd"
                @mousedown="onMouseDown">
                <view class="slider-track"></view>
                <view class="slider-fill" :style="{ width: percent + '%' }"></view>
                <view class="slider-dot" :style="{ left: percent + '%' }"></view>
            </view> -->
            <view class="slider">
                <u-slider max="100" @end="endMove" v-model="sliderValue" inactive-color="rgba(231, 231, 231, .6)"
                    :active-color="'rgba(255, 130, 163, .6)'" step="1" :levelList="percents" :use-slot="true">
                </u-slider>
            </view>

            <view class="percent-labels">
                <text v-for="(item, index) in percents" :key="index" :class="{ active: Math.round(percent) === item }">
                    {{ item }}
                </text>
            </view>

            <view class="status">
                当前杠杆数量最高可开：<text class="num"> {{ zhang }} 张</text>
            </view>
            <view class="tip flex_all">
                可开数量同时受杠杆、可用资金、仓位档位最大开仓量的限制
            </view>
        </view>

        <view class="btn-confirm" @click="handleConfirm">
            <!-- <u-button hover-class="none" class="btn">确定</u-button> -->
            确定
        </view>
    </view>
</template>

<script>
export default {
    name: "LeverageSelector",
    props: ['balance', 'orderType', 'orderprice'],
    data() {
        return {
            sliderValue: 10,
            maxLeverage: 100,
            inputValue: 60,
            percent: 10,
            percents: ['0%', '10%', '20%', '30%', '40%', '50%', '60%', '70%', '80%', '90%', '100%'], // 都加上%

            isDragging: false
        };
    },
    mounted() {
        console.log("this.balance", this.balance, this.orderType);

    },
    computed: {
        zhang() {
            let price
            let balance_level = Number(this.balance) * Number(this.sliderValue)
            if (this.orderType == 'MARKET') {
                price = uni.getStorageSync("currentContract").closePrice || 1
            } else {
                price = this.orderprice || 1
            }
            // let price = uni.getStorageSync("currentContract").closePrice || 1
            let multiplier = uni.getStorageSync("currentContract").multiplier
            console.log("balance_level", balance_level, price, multiplier);
            return (balance_level / price / multiplier).toFixed(0)
        }
    },
    methods: {
        makehigh() {
            this.inputValue = this.maxLeverage
            // inputValue = maxLeverage
            this.sliderValue = this.maxLeverage
        },
        endMove(e) {
            this.inputValue = e
        },
        setPercentByClientX(clientX) {
            const bar = this.$el.querySelector(".slider-bar");
            const rect = bar.getBoundingClientRect();
            const offset = clientX - rect.left;
            let percent = (offset / rect.width) * 100;
            percent = Math.max(0, Math.min(100, percent));
            this.percent = percent;
            this.inputValue = Math.round((this.maxLeverage * percent) / 100);
        },
        onTouchStart(e) {
            this.isDragging = true;
            this.setPercentByClientX(e.touches[0].clientX);
        },
        onTouchMove(e) {
            if (this.isDragging) {
                this.setPercentByClientX(e.touches[0].clientX);
            }
        },
        onTouchEnd() {
            this.isDragging = false;
        },
        onMouseDown(e) {
            this.isDragging = true;
            this.setPercentByClientX(e.clientX);
            const moveHandler = (e) => {
                this.setPercentByClientX(e.clientX);
            };
            const upHandler = () => {
                this.isDragging = false;
                window.removeEventListener("mousemove", moveHandler);
                window.removeEventListener("mouseup", upHandler);
            };
            window.addEventListener("mousemove", moveHandler);
            window.addEventListener("mouseup", upHandler);
        },
        handleConfirm() {
            this.$emit("confirm", {
                value: this.inputValue,
                percent: this.percent
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.leverage-selector {
    width: 100%;
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx 32rpx 100rpx 32rpx;


    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 30rpx;

        .title {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 48rpx;
            color: #000;
        }

        .close {
            width: 48rpx;
            height: 48rpx;
        }
    }

    .slider-box {
        margin-top: 26rpx;

        .input-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 28rpx;
            border-radius: 8rpx;
            background: #F7F7F7;
            height: 80rpx;


            .uni-input-placeholder {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 40rpx;
                color: rgba(0, 0, 0, .5);
            }

            input {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 40rpx;
                width: 100rpx;
                color: #000;
            }


            .right {
                display: flex;
                align-items: center;

                .multiply {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 32rpx;
                    color: #000;
                }

                .shu {
                    margin: 0 16rpx;
                    width: 1rpx;
                    height: 21rpx;
                    background: rgba(0, 0, 0, .05);
                }

                .max-leverage {

                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 28rpx;
                    line-height: 32rpx;
                    color: #FF82A3;
                    white-space: nowrap;
                }
            }

        }

        .slider-bar {
            position: relative;
            height: 12rpx;
            background: #f5f5f5;
            border-radius: 10rpx;
            margin: 40rpx 0 20rpx;

            .slider-track {
                position: absolute;
                height: 100%;
                width: 100%;
                background: #f5f5f5;
                border-radius: 10rpx;
            }

            .slider-fill {
                position: absolute;
                height: 100%;
                background: #f88ca0;
                border-radius: 10rpx 0 0 10rpx;
            }

            .slider-dot {
                position: absolute;
                width: 30rpx;
                height: 30rpx;
                background: #f88ca0;
                border-radius: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
            }
        }

        .slider {
            margin: 40rpx 0 20rpx 0;
        }

        .percent-labels {
            display: flex;
            justify-content: space-between;
            font-size: 22rpx;
            color: #999;

            text {
                &.active {
                    color: #f88ca0;
                    font-weight: bold;
                }
            }
        }

        .status {
            font-size: 24rpx;
            margin-top: 28rpx;
            color: #000;
            font-family: PingFang SC;
            font-weight: 400;
            line-height: 100%;
            opacity: 0.5;

        }

        .tip {
            height: 64rpx;
            margin-top: 24rpx;
            background: rgba(255, 130, 163, .1);
            border-radius: 4rpx;
            color: #f88ca0;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #FF82A3;
        }
    }

    .btn-confirm {
        width: 100%;
        margin-top: 112rpx;
        background: #FF82A3;
        color: white;
        text-align: center;
        padding: 24rpx;
        border-radius: 60rpx;
        font-size: 28rpx;

        font-family: PingFang SC;
        font-weight: 500;

    }
}
</style>