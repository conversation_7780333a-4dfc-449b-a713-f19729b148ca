<template>
    <view class="order-book">

        <view class="rate">
            <view class="name">资金费率/结算时间</view>
            <view class="rate-value">{{ '--' + ' / ' + '--' }}</view>
        </view>


        <!-- 卖盘 -->
        <view class="order-side">
            <view class="order-title">
                <view class="priceamount">
                    <text>价格</text>
                    <text>({{ $store.state.quote }})</text>
                </view>
                <view class="priceamount">
                    <text>数量</text>
                    <text>({{ $store.state.base }})</text>
                </view>

            </view>
            <!-- <view class="order-list" :class="{ 'fit-content': depthMode === 1 || depthMode === 2 }"> -->
            <view :class="['order-list', depthMode === 1 || depthMode === 2 ? 'fit-content' : '']">
                <view v-for="(item, index) in (sellOrders || []).slice(0, getSellCount)" :key="'sell-' + index"
                    class="order-row">
                    <view class="bg" :style="{ width: getWidth(item.amount, 'sell') + '%' }"></view>
                    <view class="text">
                        <text class="price">{{ formatThousand(item.price) }}</text>
                        <text class="amount">{{ item.amount }}</text>
                    </view>
                </view>

            </view>
        </view>

        <view class="middle-price">
            <view class="markPrice" :style="{ color: midside == 'BUY' ? '#FF82A3' : '#30C147' }"> {{
                formatThousand(latestPrice) }} </view>
            <view class="newlatest">{{ formatThousand(0) }} </view>
        </view>
        <!-- 买盘 -->
        <view class="order-side">
            <!-- <view class="order-list" :class="{ 'fit-content': depthMode === 1 || depthMode === 2 }"> -->
            <view :class="['order-list', depthMode === 1 || depthMode === 2 ? 'fit-content' : '']">
                <view v-for="(item, index) in (buyOrders || []).slice(0, getBuyCount)" :key="'buy-' + index"
                    class="order-row-buy">
                    <view class="bg" :style="{ width: getWidth(item.amount, 'buy') + '%' }"></view>
                    <view class="text">
                        <text class="price">{{ formatThousand(Number(item.price)) }}</text>
                        <text class="amount">{{ item.amount }}</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 盘口只显示买票或者卖盘 -->

        <view class="dropdown-wrapper">
            <view class="dropdown-box" @click="toggle">
                <text class="dropdown-value">{{ nowprecision }}</text>
                <image class="dropdown-arrow" :class="{ rotated: isOpen }"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1375548628155326464.png"
                    mode="widthFix" />
            </view>
            <view class="depth-bars" @click="toggleDepthMode">
                <view class="left">
                    <view class="red" :style="{ height: getRedBarHeight }"></view>
                    <view class="green" :style="{ height: getGreenBarHeight }"></view>
                </view>

                <view class="right">
                    <view class="bar"></view>
                    <view class="bar"></view>
                    <view class="bar"></view>
                </view>
            </view>
        </view>

        <!-- 精度选择 -->
        <precisionPopup :show.sync="changePrePopup" :options="PreTypes" @select="handleSelectNum" />
    </view>
</template>

<script>
import precisionPopup from "../popup/precision"
import { startSocket, onSocketOpen } from "@/utils/websockets.js"
export default {
    components: {
        precisionPopup
    },
    data() {
        return {
            marketPrice:"",
            midside: 'BUY',
            latestPrice: "",
            nowprecision: '0.01',
            sellall: 5,
            buyall: 5,
            PreTypes: [
                { label: '0.1', value: '0.1' },
                { label: '0.01', value: '0.01' },
                { label: '0.001', value: '0.001' },
                { label: '0.0001', value: '0.0001' },
            ],
            changePrePopup: false,
            isOpen: false,
            currentPrecision: '0.01', // 当前选择的精度
            depthMode: 0, // 0: 5条卖单+5条买单, 1: 10条卖单, 2: 10条买单
            orders: {},
            // { price: '62503.1', amount: '0.003', type: 'sell', percent: 60 },
            // { price: '62503.0', amount: '0.003', type: 'sell', percent: 45 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 30 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 20 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 10 },
            // { price: '62503.1', amount: '0.003', type: 'sell', percent: 60 },
            // { price: '62503.0', amount: '0.003', type: 'sell', percent: 45 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 30 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 20 },
            // { price: '62502.9', amount: '0.003', type: 'sell', percent: 10 },
            // { price: '62500.0', amount: '0.003', type: 'buy', percent: 15 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 25 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 35 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 45 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 55 },
            // { price: '62500.0', amount: '0.003', type: 'buy', percent: 15 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 25 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 35 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 45 },
            // { price: '62499.8', amount: '0.003', type: 'buy', percent: 55 },
            intervalId: null
        }
    },
    watch: {
        "$store.state.Depths"(val) {
            // console.log(val, ' Depths');
            this.orders = val
        },
        "$store.state.side"(val) {
            this.midside = val
        },
        "$store.state.realprice"(val) {
            console.log(val, ' realprice');
            let a = Number(val)
            let b = a.toFixed(3)
            this.latestPrice = b
        },
        "$store.state.market_ticker_data"(val) {
            console.log(val, ' market_ticker_data');
        },
        "$store.state.symbol"(val) {
            this.orders = []
            this.latestPrice = ""
            this.marketPrice = ""
            onSocketOpen()
            startSocket()
        }
    },
    mounted() {
        this.latestPrice = uni.getStorageSync('_realPrice')
        // this.intervalId = setInterval(this.updateBuyPercents, 1000)
    },
    computed: {
        sellOrders() {
            // return Array.isArray(this.orders) ? this.orders.sell : []
            return this.orders.sell
        },
        buyOrders() {
            return this.orders.buy
        },
        getSellCount() {
            switch (this.depthMode) {
                case 0: return 5;  // 5条卖单+5条买单
                case 1: return 10; // 10条卖单
                case 2: return 0;  // 10条买单（不显示卖单）
                default: return 5;
            }
        },
        getBuyCount() {
            switch (this.depthMode) {
                case 0: return 5;  // 5条卖单+5条买单
                case 1: return 0;  // 10条卖单（不显示买单）
                case 2: return 10; // 10条买单
                default: return 5;
            }
        },
        // 计算红色条（卖单）的高度
        getRedBarHeight() {
            const sellCount = this.getSellCount;
            if (sellCount === 0) return '0rpx';
            if (sellCount === 5) return '13rpx';
            if (sellCount === 10) return '26rpx';
            return '13rpx';
        },
        // 计算绿色条（买单）的高度
        getGreenBarHeight() {
            const buyCount = this.getBuyCount;
            if (buyCount === 0) return '0rpx';
            if (buyCount === 5) return '13rpx';
            if (buyCount === 10) return '26rpx';
            return '13rpx';
        }
    },
    methods: {
        getWidth(amounts, side) {
            if (side == 'buy') {
                if (this.orders?.buy && this.orders?.buy?.length) {
                    let maxAmount = this.orders.buy.length === 1 ? this.orders.buy[0].amount : Math.max(...this.orders.buy.slice(0, 5).map(item => item.amount));
                    return (amounts / maxAmount) * 100;
                }

            } else {
                if (this.orders?.sell && this.orders?.sell?.length) {

                    let maxAmount = this.orders.sell.length === 1 ? this.orders.sell[0].amount : Math.max(...this.orders.sell.slice(-5).map(item => item.amount));
                    return (amounts / maxAmount) * 100;
                }
            }
            // 在这里计算宽度
            // 如果buy或sell数组只有一个元素，那么就将该元素的amount值作为最大长度
        },
        handleSelectNum(item) {
            this.currentPrecision = item.value;
            this.nowprecision = item.value;
            // uni.showToast({
            //     title: `选择了精度：${item.label}`,
            //     icon: 'none'
            // })
        },

        // 切换深度显示模式
        toggleDepthMode() {
            this.depthMode = (this.depthMode + 1) % 3;

            let modeText = '';
            switch (this.depthMode) {
                case 0:
                    modeText = '5条卖单 + 5条买单';
                    break;
                case 1:
                    modeText = '10条卖单';
                    break;
                case 2:
                    modeText = '10条买单';
                    break;
            }

            // uni.showToast({
            //     title: `切换到：${modeText}`,
            //     icon: 'none'
            // });
        },
        toggle() {
            this.changePrePopup = true
            this.isOpen = !this.isOpen
        },
        formatThousand(val) {
            if (isNaN(val)) return val
            const precision = this.getPrecisionDigits(this.currentPrecision);
            let value = Number(val).toFixed(precision)
            return (value).toLocaleString('en-US')
        },

        // 根据精度字符串获取小数位数
        getPrecisionDigits(precisionStr) {
            const precision = Number(precisionStr);
            if (precision >= 1) return 0;
            if (precision >= 0.1) return 1;
            if (precision >= 0.01) return 2;
            if (precision >= 0.001) return 3;
            if (precision >= 0.0001) return 4;
            return 2; // 默认2位小数
        },
        updateBuyPercents() {
            this.orders = this.orders.map(order => {
                const variation = Math.floor(Math.random() * 50) + 50 // 50 ~ 99
                return {
                    ...order,
                    percent: variation
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.order-book {
    // flex: 1;
    width: 254rpx;
    display: flex;
    flex-direction: column;
    align-items: stretch;

    .rate {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 20rpx;
        line-height: 28rpx;
        color: #000;
        color: rgba(0, 0, 0, .5);

        .name {
            width: fit-content;
            padding-bottom: 4rpx;
            border-bottom: 1rpx dashed #808080
        }

        .rate-value {
            margin-top: 8rpx;
        }
    }

    .order-side {
        .order-title {
            margin: 18rpx 0 12rpx 0;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .priceamount {
                display: flex;
                flex-direction: column;
                justify-content: flex-start;

                text {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    // line-height: 28rpx;
                    color: rgba(0, 0, 0, .5);
                }
            }
        }

        .order-list {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            height: 225rpx;

            &.fit-content {
                height: fit-content;
            }

            .order-row {
                width: 100%;
                display: flex;
                justify-content: space-between;
                // background-image: linear-gradient(to right, rgba(22, 199, 132, 0.2), transparent);
                background-repeat: no-repeat;
                background-size: 0% 100%;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 28rpx;
                position: relative;

                .bg {
                    position: absolute;
                    right: 0;
                    background: rgba(255, 130, 163, .2);
                    transition: width 0.3s ease;
                    height: 100%;
                }

                .text {
                    width: 100% !important;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin: 0 8rpx;
                    padding: 9rpx 0;

                    .price {

                        color: #FF82A3;
                    }

                    .amount {

                        color: #000;
                    }
                }

            }

            .order-row-buy {
                width: 100%;
                display: flex;
                justify-content: space-between;
                // background-image: linear-gradient(to right, rgba(22, 199, 132, 0.2), transparent);
                background-repeat: no-repeat;
                background-size: 0% 100%;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 28rpx;
                position: relative;

                .bg {
                    position: absolute;
                    right: 0;
                    background: rgba(48, 193, 71, .2);
                    transition: width 0.3s ease;
                    height: 100%;
                }

                .text {
                    width: 100% !important;
                    display: flex;
                    align-items: center;
                    padding: 10rpx 0;
                    justify-content: space-between;
                    margin: 0 8rpx;
                    height: 100%;

                    .price {

                        color: #30C147;
                    }

                    .amount {

                        color: #000;
                    }
                }

            }

        }


    }

    .middle-price {
        margin: 9rpx 0 9rpx 24rpx;
        // padding: 9rpx 0;

        .markPrice {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 40rpx;
            // color: #FF82A3;
        }

        .newlatest {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 40rpx;
            color: #A3A3A3;
        }
    }

    .dropdown-wrapper {
        margin-top: 16rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .dropdown-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #F6F6F6;
            border-radius: 8rpx;
            width: 184rpx;
            height: 48rpx;
            padding: 0 13rpx 0 16rpx;

            .dropdown-value {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
            }

            .dropdown-arrow {
                width: 17rpx;
                height: 11rpx;
                transition: transform 0.3s ease;

                &.rotated {
                    transform: rotate(180deg);
                }
            }
        }

        .depth-bars {
            display: flex;
            align-items: center;
            gap: 4rpx;


            .left {
                display: flex;
                flex-direction: column;
                gap: 6rpx;

                .red {
                    width: 7rpx;
                    height: 13rpx;
                    background: #FF0000CC;
                    transition: height 0.3s ease;
                }

                .green {
                    width: 7rpx;
                    height: 13rpx;
                    background: #30C147;
                    transition: height 0.3s ease;
                }
            }

            .right {
                gap: 6rpx;
                display: flex;
                flex-direction: column;
                align-items: center;

                .bar {
                    width: 32rpx;
                    height: 7rpx;
                    background: #D9D9D9;
                }
            }


        }
    }


}
</style>