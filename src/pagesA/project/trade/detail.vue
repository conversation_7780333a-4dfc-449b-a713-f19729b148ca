<template>
    <view class="trade-detail">
        <view class="barHeight"></view>

        <!-- 顶部栏 -->
        <view class="top-bar">
            <view class="left">
                <view class="back flex_all" @click="back">
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384984428089532416.png" />
                </view>
                <text class="symbol">{{ $store.state.symbol.toUpperCase() }}</text>
            </view>

            <view class="star">
                <image @click="starClick(info)" v-if="!info"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384985216803233792.png" />
                <image @click="starClick(info)" v-else
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384985542650322944.png" />
            </view>
        </view>

        <!-- 价格区域 -->
        <view class="price-section">
            <view class="left">
                <text class="price">{{ formatThousand(latestPrice) }}</text>
                <view class="price-info">
                    <text class="cny">≈--</text>
                    <text class="percent flex_all" :class="rate > 0 ? 'up' : 'down'"> {{ rate > 0 ? '+' : ''
                    }}{{ rate + '%' }}</text>
                </view>
            </view>

            <view class="high-low">
                <view class="item">
                    <text class="label">高</text>
                    <text class="value">{{ formatThousand(coininfo.highPrice) }}</text>
                </view>
                <view class="item">
                    <text class="label">低</text>
                    <text class="value">{{ formatThousand(coininfo.lowPrice) }}</text>
                </view>
                <view class="item">
                    <text class="label">24h量</text>
                    <text class="value">{{ formatThousand(coininfo.vol) }}</text>
                </view>
            </view>
        </view>

        <!-- 指标和周期选择区域 -->
        <view class="indicator-section">
            <!-- 基础时间周期 -->
            <view class="time-periods">
                <view class="period-item" :class="{ active: selectedPeriod === '分时' }" @tap="selectPeriod('分时')">分时</view>
                <view class="period-item" :class="{ active: selectedPeriod === '1m' }" @tap="selectPeriod('1m')">1m</view>
                <view class="period-item" :class="{ active: selectedPeriod === '15m' }" @tap="selectPeriod('15m')">15m</view>
                <view class="period-item" :class="{ active: selectedPeriod === '1h' }" @tap="selectPeriod('1h')">1h</view>
                <view class="period-item" :class="{ active: selectedPeriod === '4h' }" @tap="selectPeriod('4h')">4h</view>

                <!-- 更多周期按钮 -->
                <view class="more-periods" @tap="toggleMorePeriods">
                    <text>更多</text>
                    <image class="arrow" :class="{ rotated: showMorePeriods }"
                           src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1375548628155326464.png" />
                </view>

                <!-- 指标按钮 -->
                <view class="indicators-btn" @tap="toggleIndicators">
                    <text>指标</text>
                    <image class="arrow" :class="{ rotated: showIndicators }"
                           src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1375548628155326464.png" />
                </view>

                <!-- 全屏按钮 -->
                <view class="fullscreen-btn" @tap="toggleFullscreen">
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/fullscreen-icon.png" />
                </view>
            </view>

            <!-- 更多周期选项 -->
            <view class="more-periods-panel" v-show="showMorePeriods">
                <view class="period-item" :class="{ active: selectedPeriod === '5m' }" @tap="selectPeriod('5m')">5m</view>
                <view class="period-item" :class="{ active: selectedPeriod === '30m' }" @tap="selectPeriod('30m')">30m</view>
                <view class="period-item" :class="{ active: selectedPeriod === '1d' }" @tap="selectPeriod('1d')">1d</view>
                <view class="period-item" :class="{ active: selectedPeriod === '1w' }" @tap="selectPeriod('1w')">1w</view>
                <view class="period-item" :class="{ active: selectedPeriod === '30d' }" @tap="selectPeriod('30d')">30d</view>
            </view>

            <!-- 指标选择面板 -->
            <view class="indicators-panel" v-show="showIndicators">
                <!-- 主图指标 -->
                <view class="indicator-group">
                    <text class="group-title">主图</text>
                    <view class="indicator-options">
                        <view class="indicator-item" :class="{ active: selectedMainIndicator === 'MA' }" @tap="selectMainIndicator('MA')">MA</view>
                        <view class="indicator-item" :class="{ active: selectedMainIndicator === 'BOLL' }" @tap="selectMainIndicator('BOLL')">BOLL</view>
                    </view>
                </view>

                <!-- 副图指标 -->
                <view class="indicator-group">
                    <text class="group-title">副图</text>
                    <view class="indicator-options">
                        <view class="indicator-item" :class="{ active: selectedSubIndicator === 'MACD' }" @tap="selectSubIndicator('MACD')">MACD</view>
                        <view class="indicator-item" :class="{ active: selectedSubIndicator === 'KDJ' }" @tap="selectSubIndicator('KDJ')">KDJ</view>
                        <view class="indicator-item" :class="{ active: selectedSubIndicator === 'RSI' }" @tap="selectSubIndicator('RSI')">RSI</view>
                    </view>
                </view>
            </view>
        </view>

        <view class="Kline">
            <KForm ref="child" :period="period" :selectTopHeight='selectTopHeight'
                :selectBottomHeight='selectBottomHeight' :statusBarHeight="statusBarHeight">
            </KForm>
        </view>

        <view class="toggle-container">
            <!-- {{ activeTab }} -->
            <view class="toggle-bg" :class="activeTab"></view>
            <view class="toggle-item" :class="{ active: activeTab == 'depth' }" @tap="switchTab('depth')">
                交易深度
            </view>
            <view class="toggle-item" :class="{ active: activeTab == 'num' }" @tap="switchTab('num')">
                成交数量
            </view>
            <view class="toggle-item" :class="{ active: activeTab == 'rule' }" @tap="switchTab('rule')">
                交易规则
            </view>
        </view>

        <view class="tab-content">
            <view v-if="activeTab == 'depth'" class="depth">
                <view class="depth-header">
                    <view class="cells">数量(BTC)</view>
                    <view class="cells" style="text-align: right;margin-right: 16rpx;">价格(USDT)</view>
                    <view class="cells" style="text-align: left;margin-left: 16rpx;">价格(USDT)</view>
                    <view class="cells" style="text-align: right;">数量(BTC)</view>
                </view>
                <view class="depth-body">
                    <view class="depth-row" >
                        <view class="buybox" >
                            <view class="buy" :style="{ background: 'rgba(8, 184, 25, .2)' }" v-for="(item, index) in (buyOrders || []).slice(0, 25)" :key="'buy-' + index">
                                <view class="cell">{{ item.amount }}</view>
                                <view class="cell green">{{ item.price }}</view>
                            </view>
                        </view>

                        <view class="sellbox">
                            <view class="sell"  v-for="(item, index) in (sellOrders || []).slice(0, 25)" :key="'sell-' + index" :style="{ background: 'rgba(255, 130, 163, .2)' }">
                                <view class="cell red">62,503.1</view>
                                <view class="cell">0.003</view>
                            </view>
                        </view>

                    </view>
                </view>
            </view>

            <view v-if="activeTab == 'num'" class="trades">
                <view class="trades-header">
                    <view class="cell">时间</view>
                    <view class="cell">方向</view>
                    <view class="cell">价格(USDT)</view>
                    <view class="cell" style="text-align: right;">数量(BTC)</view>
                </view>
                <view class="trades-body">
                    <view class="trades-row" v-for="(item, index) in 20" :key="'trade' + index">
                        <view class="cell" style="width: 25%;">04:56:19</view>
                        <view class="cell" :class="index % 2 === 0 ? 'green' : 'red'" style="width: 25%;">{{ index %
                            2 === 0 ? '买入' :
                            '卖出'
                            }}</view>
                        <view class="cell" :class="index % 2 === 0 ? 'green' : 'red'">103192.8000</view>
                        <view class="cell" style="text-align: right;">190.4</view>

                    </view>
                </view>
            </view>

            <view v-if="activeTab == 'rule'" class="rules">
                <view class="title">{{ $store.state.symbol.toUpperCase() }} 永续</view>
                <view class="rule-row">
                    <text class="label">最小下单数量</text>
                    <text class="value">{{ coininfo.minPiece || 0 }}张</text>
                </view>
                <view class="rule-row">
                    <text class="label">单笔下单最大数量</text>
                    <text class="value">{{ coininfo.maxPiece || 0 }}张</text>
                </view>
                <view class="rule-row">
                    <text class="label">最大持仓数量</text>
                    <text class="value">{{ coininfo.TotalPiece || 0 }}张</text>
                </view>
                <view class="rule-row">
                    <text class="label">市价成交价格限制</text>
                    <text class="value">{{ coininfo.marketPriceLimit || 0 }}</text>
                </view>
                <view class="rule-row">
                    <text class="label">限价成交价格限制</text>
                    <text class="value">{{ coininfo.limitPriceLimit || 0 }}</text>
                </view>
            </view>
        </view>

        <view class="btn">
            <u-button hover-class="none" @click="back" class="buy">买入</u-button>
            <u-button hover-class="none" @click="back" class="sell">卖出</u-button>
        </view>
    </view>
</template>

<script>
import store from '@/store'
import { startSocket, onSocketOpen } from "@/utils/websockets.js"
// #ifdef H5
const form = (resolve) => require(["./components/form/index"], resolve);
// #endif

// #ifdef APP-PLUS
import form from "./components/form/index";
// #endif

export default {
    name: 'TradeDetailPage',
    components: {
        KForm: form,
    },
    data() {
        return {
            tvUrl: '',
            activeTab: 'depth',
            statusBarHeight: 0,
            selectTopHeight: 0,
            selectBottomHeight: 0,
            period: 'area',  // 传过去的周期
            info: null,
            coininfo: {},
            latestPrice: "",
            orders: {},
            // 指标和周期相关数据
            selectedPeriod: '15m',
            showMorePeriods: false,
            showIndicators: false,
            selectedMainIndicator: 'MA',
            selectedSubIndicator: 'MACD'
        }
    },
    onShow() {
        onSocketOpen()
        startSocket()
    },
    computed: {
        rate() {
            const closePrice = this.latestPrice || 0;
            const openPrice = this.coininfo?.openPrice || 0;
            const change = closePrice - openPrice;
            const percentage = openPrice != 0 ? (change / openPrice) * 100 : 0;
            return openPrice != 0 ? percentage.toFixed(2) : '0.00';
        },
        sellOrders() {
            // return Array.isArray(this.orders) ? this.orders.sell : []
            return this.orders.sell
        },
        buyOrders() {
            return this.orders.buy
        },
    },
    watch: {
        // "$store.state.market_ticker_data"(val) {
        //     if (!val || typeof val !== 'object') return;
        //     // 获取当前symbol
        //     const currentSymbol = this.$store.state.symbol; // 例如: btcusdt
        //     if (!currentSymbol) return;
        //     // 构造对应的key (添加e_前缀)
        //     const targetKey = `e_${currentSymbol.toLowerCase()}`;

        //     // 查找对应的数据
        //     if (val[targetKey] && val[targetKey].indexPrice) {
        //         const indexPrice = val[targetKey].indexPrice;
        //         console.log(`找到${currentSymbol}的indexPrice:`, indexPrice);

        //         // 可以将indexPrice存储到data中或者提交到store
        //         this.currentIndexPrice = indexPrice;
        //         // 或者提交到store: this.$store.commit('setIndexPrice', indexPrice);
        //     } else {
        //         console.log(`未找到${currentSymbol}对应的indexPrice数据`);
        //     }
        // },
        "$store.state.Depths"(val) {
            console.log(val, ' Depths');
            this.orders = val
        },
        "$store.state.realprice"(val) {
            let a = Number(val)
            let b = a.toFixed(3)
            this.latestPrice = b
        },
    },
    onLoad() {
        this.getContractSymbols()
        this.getContractSymbolsInfo()
        this.tvUrl = `${location.origin}/static/tradingview.html`
    },
    methods: {
        formatThousand(val) {
            if (isNaN(val)) return val
            // const precision = this.getPrecisionDigits(this.currentPrecision);
            let value = Number(val).toFixed(2)
            return (value).toLocaleString('en-US')
        },
        async getContractSymbolsInfo() {
            let res = await this.$api.getContractSymbol({
                id: uni.getStorageSync("currentContract").id
            })
            if (res.code == 200) {
                this.coininfo = res.result
                this.latestPrice = res.result.closePrice
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        // 指标和周期相关方法
        selectPeriod(period) {
            this.selectedPeriod = period;
            this.showMorePeriods = false;
            console.log('选择周期:', period);
        },
        toggleMorePeriods() {
            this.showMorePeriods = !this.showMorePeriods;
            if (this.showMorePeriods) {
                this.showIndicators = false;
            }
        },
        toggleIndicators() {
            this.showIndicators = !this.showIndicators;
            if (this.showIndicators) {
                this.showMorePeriods = false;
            }
        },
        selectMainIndicator(indicator) {
            this.selectedMainIndicator = indicator;
            console.log('选择主图指标:', indicator);
        },
        selectSubIndicator(indicator) {
            this.selectedSubIndicator = indicator;
            console.log('选择副图指标:', indicator);
        },
        toggleFullscreen() {
            console.log('切换全屏');
        },
        switchTab(tab) {
            // this.list = []
            // this.page.pageNum = 1
            this.activeTab = tab
            // this.getshopOrderList()

        },
        async starClick(e) {
            if (e) {
                let res = await this.$api.deleteContractSelf({
                    contractSymbolId: uni.getStorageSync("currentContract").id
                })
                if (res.code == 200) {
                    this.getContractSymbols()
                    uni.showToast({
                        title: "取消自选成功",
                        icon: 'none'
                    })
                }
            } else {
                let res = await this.$api.addContractSelf({
                    contractSymbolId: uni.getStorageSync("currentContract").id
                })
                if (res.code == 200) {
                    this.getContractSymbols()
                    uni.showToast({
                        title: "添加自选成功",
                        icon: 'none'
                    })
                }
            }
        },
        async getContractSymbols() {
            let res = await this.$api.isSelect({
                contractSymbolId: uni.getStorageSync("currentContract").id
            })
            if (res.code == 200) {
                this.info = res.result
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        // 获取元素高度
        getSelectheight() {

            // #ifdef H5
            const DomTopHeight = uni.getStorageSync("selectTopHeight")
            const DomBottomHeight = uni.getStorageSync("selectBottomHeight")
            if (DomTopHeight && DomBottomHeight) {
                this.selectTopHeight = DomTopHeight
                this.selectBottomHeight = DomBottomHeight
                this.isStartForm = true
                return
            }
            // #endif

            // #ifdef APP-PLUS
            setTimeout(() => {
                uni.createSelectorQuery().in(this).select("#contentTop").boundingClientRect(data => {
                    // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
                    this.selectTopHeight = JSON.parse(JSON.stringify(data)).height

                    uni.setStorageSync("selectTopHeight", this.selectTopHeight)
                }).exec()

                uni.createSelectorQuery().in(this).select("#contentBottom").boundingClientRect(data => {
                    // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
                    this.selectBottomHeight = JSON.parse(JSON.stringify(data)).height
                    uni.setStorageSync("selectBottomHeight", this.selectBottomHeight)
                }).exec()

                this.isStartForm = true
            }, 1500)
            // #endif

            // #ifdef H5
            this.$nextTick(() => {
                setTimeout(() => {
                    uni.createSelectorQuery().select("#contentBottom").boundingClientRect(data => {
                        this.selectBottomHeight = JSON.parse(JSON.stringify(data)).height
                        uni.setStorageSync("selectBottomHeight", this.selectBottomHeight)
                    }).exec()
                    uni.createSelectorQuery().select("#contentTop").boundingClientRect(data => {
                        // {"id":"","dataset":{},"left":12,"right":308,"top":12,"bottom":315,"width":296,"height":303}
                        this.selectTopHeight = JSON.parse(JSON.stringify(data)).height
                        uni.setStorageSync("selectTopHeight", this.selectTopHeight)
                    }).exec()
                    this.isStartForm = true
                }, 1500)
            })
            // #endif

        },
        back() {
            uni.navigateBack({
                delta: 1
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.tv-container,
web-view {
    width: 100%;
    height: 600rpx;
    /* 高度可按需调整 */
}

.trade-detail {
    padding: 0 32rpx;
    margin-top: 28rpx;

    .top-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // padding: 20rpx 0;
        // font-size: 28rpx;

        .left {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .back {
                background: #0000000D;
                width: 84rpx;
                height: 84rpx;
                border-radius: 50rpx;

                image {
                    width: 35rpx;
                    height: 35rpx;
                }

            }

            .symbol {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
            }
        }

        .star {
            image {
                width: 48rpx;
                height: 48rpx;
            }
        }

    }

    .price-section {
        margin-top: 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
            .price {
                color: #000;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 48rpx;
                line-height: 68rpx;
            }

            .price-info {
                display: flex;
                align-items: center;
                gap: 16rpx;
                margin-top: 2rpx;

                .cny {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 34rpx;
                    color: #000;
                }

                .percent {
                    font-size: 24rpx;
                    width: 112rpx;
                    height: 44rpx;
                    border-radius: 10rpx;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;

                    &.up {
                        background: rgba(55, 164, 4, .1);
                        color: #37A404;
                    }

                    &.down {
                        background: rgba(255, 130, 163, .1);
                        color: #FF82A3;
                    }
                }
            }
        }


        .high-low {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 284rpx;
            gap: 4rpx;

            .item {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .label {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: #000;
                }

                .value {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .4);
                }
            }


        }
    }

    .Kline {
        position: relative;
        background-image: linear-gradient(to bottom, transparent calc(66rpx - 0.19px), #E1E1E1 calc(66rpx - 0.19px), #E1E1E1 66rpx, transparent 66rpx);
        background-size: 100% 66rpx;
        background-repeat: repeat-y;
    }

    .toggle-container {
        margin-top: 44rpx;
        position: relative;
        // width: 500rpx;
        // margin: 27rpx 28rpx 0 28rpx;
        // padding-top: 16rpx;
        // width: 100%;
        height: 96rpx;
        // background-color: #F6F6F6;
        border-radius: 80rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6rpx;
        // box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
        background-image: url("https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385033667536576512.png");
        background-size: 100% 100%;

        // overflow: hidden;
        // filter: blur(13.800000190734863px);
        .rounded-triangle-svg {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .toggle-item {
            width: 50%;
            text-align: center;
            font-size: 28rpx;
            z-index: 1;
            font-family: Gilroy;
            font-weight: 500;
            line-height: 32rpx;
            transition: color 0.3s;
            color: rgba(0, 0, 0, .4);
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .toggle-item.active {
            color: #fff;
        }

        .toggle-bg {
            height: 72rpx;
            position: absolute;
            // top: -10rpx;
            // border: 1.07px solid #FFFFFF61;
            // bottom: 6rpx;
            width: 214rpx;
            border-radius: 80rpx;
            background-color: #ff6f96;
            // z-index: 0;
            transition: left 0.3s;
            border: 1.07px solid #FFFFFF61;
            display: flex;
            justify-content: center;
            align-items: center;
            // box-shadow: 0 0 6rpx rgba(255, 111, 150, 0.4);
        }

        .toggle-bg.depth {
            left: 6rpx;
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }

        .toggle-bg.num {
            left: 240rpx; // 500rpx - 50% + padding
            // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
        }

        .toggle-bg.rule {
            left: 470rpx;
        }
    }

    .tab-content {

        .depth,
        .trades,
        .rules {
            margin-top: 40rpx;

            .depth-header,
            .trades-header {
                margin-bottom: 8rpx;

                .cells {
                    width: 25%;
                    // text-align: center;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    line-height: 100%;
                    letter-spacing: 0%;
                    color: #000;
                    opacity: .5;
                }
            }

            .trades-row {
                height: 42rpx;
            }

            .depth-header,
            .trades-header,
            .depth-row,
            .trades-row {
                display: flex;
                justify-content: space-between;
                font-size: 24rpx;
                // padding: 12rpx 0;

                .buybox,
                .sellbox {
                    display: flex;
                    flex-direction: column;
                    width: 100%
                }

                .buy,
                .sell {
                    height: 42rpx;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    // width: 50%;
                    padding: 0 12rpx;
                }

                .cell {
                    width: 25%;
                    // text-align: center;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    line-height: 100%;
                    letter-spacing: 0%;
                    color: #000;
                    // opacity: .5;
                }

                .green {
                    color: #08B819;
                    text-align: right;
                }

                .red {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 20rpx;
                    line-height: 100%;
                    color: #FF82A3;
                }
            }
        }

        .rules {
            .title {
                font-size: 28rpx;
                font-weight: bold;
                margin-bottom: 20rpx;
            }

            .rule-row {
                display: flex;
                justify-content: space-between;
                font-size: 24rpx;
                padding: 16rpx 0;

                .label {
                    color: #666;
                }

                .value {
                    color: #111;
                }
            }
        }
    }

    .btn {
        padding-bottom: 60rpx;
        margin-top: 90rpx;
        width: 100%;
        display: flex;
        align-items: center;
        gap: 12rpx;


        .buy {
            flex: 1;
            border-radius: 112rpx;
            background: #08B819;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            line-height: 120%;
            color: #fff;
        }

        .sell {
            border-radius: 112rpx;
            flex: 1;
            background: #FF82A3;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            line-height: 120%;
            color: #fff;
        }
    }

    .chart-placeholder {
        height: 400rpx;
        background: #f5f5f5;
        margin: 20rpx 0;
        text-align: center;
        line-height: 400rpx;
        color: #aaa;
    }

    .tabs {
        display: flex;
        justify-content: space-around;
        margin: 20rpx 0;

        .tab {
            padding: 10rpx 20rpx;
            border-radius: 30rpx;
            background: #f0f0f0;

            &.active {
                background: #FF5E86;
                color: #fff;
            }
        }
    }

    .depth-list {
        .depth-row {
            display: flex;
            justify-content: space-between;
            padding: 10rpx 0;
            font-size: 24rpx;
            border-bottom: 1px solid #f2f2f2;

            &.header {
                font-weight: bold;
                color: #999;
            }

            .buy,
            .sell {
                display: flex;
                gap: 20rpx;

                .price {
                    color: #07C39F;
                }

                .amount {
                    color: #222;
                }
            }

            .sell .price {
                color: #FF4D4F;
            }
        }
    }

    .action-buttons {
        display: flex;
        justify-content: space-between;
        margin: 30rpx 0;

        .buy-btn,
        .sell-btn {
            flex: 1;
            text-align: center;
            padding: 20rpx 0;
            font-size: 28rpx;
            border-radius: 12rpx;
            color: #fff;
        }

        .buy-btn {
            background: #07C39F;
            margin-right: 10rpx;
        }

        .sell-btn {
            background: #FF4D4F;
            margin-left: 10rpx;
        }
    }

    // 指标和周期选择区域样式
    .indicator-section {
        margin: 20rpx 0;
        background: #fff;

        .time-periods {
            display: flex;
            align-items: center;
            padding: 20rpx 30rpx;
            gap: 30rpx;

            .period-item {
                font-size: 28rpx;
                color: #666;
                padding: 10rpx 0;
                cursor: pointer;

                &.active {
                    color: #000;
                    font-weight: 600;
                }
            }

            .more-periods, .indicators-btn {
                display: flex;
                align-items: center;
                gap: 8rpx;
                font-size: 28rpx;
                color: #666;
                cursor: pointer;

                .arrow {
                    width: 20rpx;
                    height: 20rpx;
                    transition: transform 0.3s ease;

                    &.rotated {
                        transform: rotate(180deg);
                    }
                }
            }

            .fullscreen-btn {
                margin-left: auto;

                image {
                    width: 32rpx;
                    height: 32rpx;
                }
            }
        }

        .more-periods-panel {
            display: flex;
            align-items: center;
            padding: 0 30rpx 20rpx;
            gap: 30rpx;
            border-top: 1rpx solid #f0f0f0;

            .period-item {
                font-size: 28rpx;
                color: #666;
                padding: 10rpx 0;
                cursor: pointer;

                &.active {
                    color: #000;
                    font-weight: 600;
                }
            }
        }

        .indicators-panel {
            padding: 20rpx 30rpx;
            border-top: 1rpx solid #f0f0f0;

            .indicator-group {
                margin-bottom: 30rpx;

                &:last-child {
                    margin-bottom: 0;
                }

                .group-title {
                    font-size: 28rpx;
                    color: #333;
                    font-weight: 600;
                    margin-bottom: 20rpx;
                    display: block;
                }

                .indicator-options {
                    display: flex;
                    gap: 30rpx;

                    .indicator-item {
                        font-size: 28rpx;
                        color: #666;
                        padding: 10rpx 0;
                        cursor: pointer;

                        &.active {
                            color: #000;
                            font-weight: 600;
                        }
                    }
                }
            }
        }
    }
}
</style>