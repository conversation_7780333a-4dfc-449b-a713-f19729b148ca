<template>
    <u-popup v-model="show" mode="bottom" border-radius="16" :mask-close-able="true">
        <view class="popup-container">
            <view class="drag-handle"></view>

            <!-- <view class="tabs">
                <view v-for="(item, index) in tabs" :key="index" :class="['tab', { active: currentTab === index }]"
                    @click="currentTab = index">
                    {{ item }}
                </view>
            </view> -->

            <view class="tabs_div">
                <view class="left">
                    <view class="utab">
                        <view @click="changeTab(0)" :class="currentTab == 0 ? 'utabact' : ''">限价单</view>
                        <view @click="changeTab(1)" :class="currentTab == 1 ? 'utabact' : ''">市价单</view>
                        <!-- <view class="utabbar" :style="{ left: currentTab == 0 ? '40rpx' : '200rpx' }"></view> -->
                    </view>
                </view>
            </view>

            <view class="limit" v-if="currentTab == 0">

                <view class="titles">限价委托是指以特定或更优价格进行买卖，限价单不能保证执行。</view>

                <view class="picbox">
                    <view class="section-title">图示说明</view>
                    <view class="radio-group">
                        <view v-for="(item, index) in radioOptions" :key="index"
                            :class="['radio-item', { active: selectedRadio === index }]" @click="selectedRadio = index">
                            <view class="circle" />
                            <text>{{ item }}</text>
                        </view>
                    </view>
                </view>

                <view class="pic">
                    <image :src="imageMap[selectedRadio]" class="illustration" mode="widthFix" />
                </view>
                <view class="text-content" v-if="selectedRadio == 0">
                    当价格(A)下跌到订单的限价(C)或以下，订单将会自动执行。如果买单的限价高于或等于当前价格，买单将会立即成交。
                    因此，当需要限价买入时，委托价格应低于当前价格。
                    <br>
                    举例说明:
                    <br>

                    1)上图中，当前价格2400(A)。设置一个委托价格为1500(C)的限价买入订单，价格未下跌到1500(C)或以下或以下之前，订单是不会执行成交的;
                    <br>
                    2)相反，如果设置限价买入单的委托价格为 3000(B)，高于当前价格，该订单将会以对手价立即成交。其成交价应在 2400左右，而不是3000。
                </view>
                <view class="text-content" v-else>
                    当价格(A)上涨至订单的限价(B)或以上，订单将会自动执行。如果卖单的限价低于或等于当前价格，卖单可能会立即成交。因此，当需要限价卖出时，委托价格应高于当前价格。
                    <br>
                    举例说明:
                    <br>
                    1)上图中，当前价格 2400(A)。设置一个委托价格为 3000(B)的限价卖出订单，价格未上涨至 3000(B)或以上 或以上之前，订单是不会执行成交的;
                    <br>
                    2)相反，如果设置限价卖出单的委托价格为1500(C)，低于当前价格，该订单将会以对手价立即成交。其成交价在 2400左右，而不是1500。
                </view>
            </view>
            <view class="market" v-if="currentTab == 1">
                <view class="titles">市价委托是指以当前市场上可获得的最优价格立即成交的委托方式，优先保证成交速度，但不保证成交价格。</view>
                <view class="picbox">
                    <view class="section-title">图示说明</view>
                </view>

                <view class="pic">
                    <image :src="imageMap[2]" class="illustration" mode="widthFix" />
                </view>

                <view class="text-content">
                    当前价格2400，此时下单一笔市价单，它将会根据对手价直接成交，但成交均价可能不等于2400。
                    <br>
                    提示:
                    <br>
                    根据买卖方向不同，成交均价可能低于，也可能高于2400。对于市价买单，成交均价会略高于当前价格;对于市价卖单，成交均价会略低于当前价格。
                </view>
            </view>

            <view class="btn">
                <u-button hover-class="none" class="confirm-btn" @click="show = false">确定</u-button>
            </view>
        </view>
    </u-popup>
</template>

<script>
export default {
    data() {
        return {
            show: false,
            currentTab: 0,
            selectedRadio: 0,
            tabs: ['限价单', '市价单'],
            radioOptions: ['买入', '卖出'],
            imageMap: {
                0: "https://test-oss.pinkwallet.com/image/2025-06-28/1388602117712838656.png", // 图一
                1: "https://test-oss.pinkwallet.com/image/2025-06-28/1388603339362598912.png", // 图二
                2: "https://test-oss.pinkwallet.com/image/2025-06-28/1388606533480284160.png",  // 图三
            },
        };
    },
    methods: {
        changeTab(e) {
            if (this.currentTab == e) return
            this.currentTab = e

        },
    }
};
</script>

<style lang="scss" scoped>
.popup-container {
    background: #fff;
    padding: 68rpx 32rpx;
    height: 90vh;
    font-family: PingFang SC;
    position: relative;
    width: 100%;

    .drag-handle {
        position: absolute;
        width: 98rpx;
        height: 8rpx;
        left: 50%;
        /* 让左边对齐屏幕中心 */
        transform: translateX(-50%);
        /* 向左移动自身50%宽度，确保居中 */
        top: 16rpx;
        border-radius: 680rpx;
        background: #D9D6D6;

    }

    .tabs_div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 18rpx;

        .left {
            .utab {
                display: flex;
                font-weight: 400;
                font-size: 28rpx;
                color: rgba(0, 0, 0, .4);
                position: relative;
                // margin: 30rpx 0;

                font-family: PingFang SC;
                font-weight: 600;
                line-height: 40rpx;
                height: 48rpx;


                .utabbar {

                    background: #FF82A3;
                    width: 32rpx;
                    height: 6rpx;
                    border-radius: 2rpx;

                    position: absolute;
                    bottom: -10rpx;
                    // left: 67rpx;
                    transition: all 0.3s ease-in-out;
                }


                view {
                    &:nth-of-type(1) {
                        margin: 0 40rpx 0 0;
                    }
                }
            }

            .utabact {
                color: #000;
            }

        }

        .right {
            image {
                width: 36rpx;
                margin-top: 14rpx;
            }
        }
    }

    .titles {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 40rpx;
        color: rgba(0, 0, 0, 0.5);
    }

    .picbox {
        margin: 22rpx 0 24rpx 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .section-title {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 48rpx;
            color: #000;
        }

        .radio-group {
            display: flex;
            // margin-bottom: 20rpx;

            .radio-item {
                display: flex;
                align-items: center;
                margin-right: 40rpx;
                font-size: 26rpx;
                color: #999;

                .circle {
                    width: 26rpx;
                    height: 26rpx;
                    border-radius: 50%;
                    border: 2rpx solid #E2E2E2;
                    margin-right: 6rpx;
                }

                &.active {
                    color: #000;

                    .circle {
                        border: 8rpx solid #000000;
                        width: 28rpx;
                        height: 28rpx;
                        background: #fff;
                        border-radius: 50%;
                        // background-color: #f96c90;
                        // border-color: #f96c90;
                    }
                }
            }
        }

    }


    .pic {
        width: 100%;
        text-align: center;

        .illustration {
            width: 598rpx;
            margin-bottom: 48rpx;
            // border-radius: 12rpx;
            // background: #f8f8f8;
        }

    }


    .text-content {
        line-height: 40rpx;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #00000080;
    }

    .btn {
        width: 688rpx;
        height: fit-content;
        position: fixed;
        bottom: 68rpx;

        .confirm-btn {
            background-color: #FF82A3;
            color: #fff;
            width: 100%;
            border-radius: 112rpx;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            text-align: center;
            height: 88rpx;
            line-height: 88rpx;
        }
    }
}
</style>