<template>
    <view class="close-popup">
        <view class="header">
            <text class="title">平仓</text>
            <image class="close" @click="$emit('close')"
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1377388318231715840.png" />


        </view>

        <view class="info">
            <view class="row">
                <text>合约</text>
                <text style="color: #FF82A3;">{{ contractInfo }}</text>
            </view>
            <view class="row">
                <text>开仓均价(USDT)</text>
                <text>{{ order.avgPrice }}</text>
            </view>
            <!-- <view class="row">
                <text>标记价格</text>
                <text>{{ order.markPrice }}</text>
            </view> -->
            <view class="row">
                <text>收益率</text>
                <text :class="{ red: rate < 0, green: rate >= 0 }">{{ rate
                }}</text>
            </view>
        </view>

        <view class="form">
            <view class="input-group">
                <view class="label">
                    <text>价格</text>
                    <text>类型</text>
                </view>
                <view class="input-box">
                    <view class="left">
                        <text v-if="orderType == 'MARKET'">{{ latestPrice || '--' }}</text>
                        <u-input placeholder="请输入委托价格" :height="80" class="input-price" :clearable="false"
                            v-model="orderprice" type="digit" maxlength="20" v-if="orderType == 'LIMIT'" />
                        <text>USDT</text>
                    </view>
                    <!-- <input class="input" disabled :value="order.price || '--'" /> -->
                    <!-- <text class="unit">USDT</text> -->
                    <view @click="showmodePopup = true" class="type flex_all">{{ orderType == 'MARKET' ? '市价委托' : '限价委托'
                        }}</view>
                </view>
            </view>

            <view class="input-group">
                <text class="label">数量</text>
                <view class="input-box-num">
                    <input :height="80" class="input" v-model="amount" maxlength="20" placeholder="数量" type="number" />
                    <!-- @click="changeNumPopup = true" -->
                    <view class="unit">{{ formattedNumTypes(NumTypesvalue) }}
                        <image
                            src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1384611272942247936.png" />
                    </view>
                </view>
            </view>

            <view class="slider">
                <!-- <view class="step" v-for="(item, index) in percents" :key="index" :class="{ active: percent === item }"
                    @click="handlePercent(item)">
                    <view class="dot" />
                    <text>{{ item }}%</text>
                </view> -->
                <!-- <u-slider max="100" @end="endMove" v-model="sliderValue" inactive-color="rgba(231, 231, 231, .6)"
                    :active-color="tradeType == 'buy' ? 'rgba(48, 193, 71, .6)' : 'rgba(255, 130, 163, .6)'" step="1"
                    :levelList="levelList" :tradeType="tradeType" :use-slot="true">
                </u-slider> -->
                <!-- <view class="slidelevel">
                    <text v-for="(item, index) in levelList"
                        :style="{ color: currentfff >= index ? '#FF82A3' : 'rgba(0, 0, 0, .6)' }" :key="index">{{ item
                        }}
                    </text>
                </view> -->
            </view>

            <view class="summarys">
                <view class="summary">
                    <text>仓位数量</text>
                    <text>{{ order.volume }}张</text>
                </view>
                <view class="summary">
                    <text>预计盈亏</text>
                    <text class="pnl">{{ formatUnrealizedPnL(order) }}USDT</text>
                </view>
            </view>
        </view>

        <view class="btns">
            <u-button hover-class="none" class="btn flex_all" @click="handleConfirm">确定</u-button>
        </view>

        <!-- 数量单位选择 -->
        <numunitPopup :show.sync="changeNumPopup" :options="NumTypes" @select="handleSelectNum" />



        <!-- 市价，限价，计划委托--委托模式选择 -->
        <ordermodePopup :show.sync="showmodePopup" @select="handleSelect" />
    </view>
</template>

<script>
import numunitPopup from './changenum'
import ordermodePopup from './mode'

import slider from "../components/slider"
export default {
    name: "ClosePositionPopup",
    components: {
        numunitPopup,
        slider,
        ordermodePopup
    },
    props: {
        order: {
            type: Object,
            default: () => ({})
        },
        rate: {
            type: String,
            default: 0
        },
        pnl: {
            type: String,
            default: 0
        },
    },
    watch: {
        sliderValue(newVal) {
            // 避免在手动输入时触发循环计算
            // if (!this.isManualInput) {
            // this.calculateOrderVolume();
            // }
        },
        "$store.state.realprice"(val) {
            console.log(val, ' realprice');
            let a = Number(val)
            let b = a.toFixed(3)
            this.latestPrice = b
        },
    },
    data() {
        return {
            latestPrice: "",
            contractInfo: "",
            orderprice: "",
            orderType: "MARKET",
            showmodePopup: false,
            levelList: ["0%", "20%", "40%", "60%", "80%", "100%"],
            tradeType: 'sell',
            sliderValue: "",
            NumTypesvalue: 'CONTRACT',
            NumTypes: [
                { label: '张', value: 'CONTRACT' },
                { label: 'USDT', value: 'QUOTE' },
                { label: 'BTC', value: 'BASE' }
            ],
            changeNumPopup: false,
            amount: '',
            percent: 0,
            percents: [0, 20, 40, 60, 80, 100],
            currentfff: 0
        };
    },
    mounted() {
        // this.latestPrice = uni.getStorageSync('_realPrice')
        this.latestPrice = uni.getStorageSync('currentContract').closePrice

        console.log(this.order);
        let symbol = uni.getStorageSync('currentContract').symbol
        this.contractInfo = symbol + '永续·' + (this.order.side == 'BUY' ? '多·' : '空·') + this.order.leverageLevel + 'X';
    },
    methods: {
        // 计算未实现盈亏
        calculateUnrealizedPnL(position) {
            if (!position) return 0;

            const multiplier = uni.getStorageSync('currentContract').multiplier || ''
            // const marketPrice = uni.getStorageSync('currentContract').closePrice || ''
            let marketPrice
            const {
                volume = 0,           // 张数
                side = '',            // 方向：'BUY'(多) 或 'SELL'(空)
                avgPrice = 0,        // 开仓价
            } = position;

            // 张数 * 面值 * (多 ? 市价 - 开仓价 : 开仓价 - 市价)
            let priceDiff = 0;

            if (this.orderType == 'MARKET') {
                marketPrice = this.latestPrice
            } else {
                marketPrice = this.orderprice;
            }

            if (side == 'BUY') {
                // 多头：市价 - 开仓价
                priceDiff = Number(marketPrice) - Number(avgPrice);
            } else if (side == 'SELL') {
                // 空头：开仓价 - 市价
                priceDiff = Number(avgPrice) - Number(marketPrice);
            }


            // if (this.orderType == 'MARKET') {
            //     marketPrice = this.latestPrice
            // } else {
            //     marketPrice = this.orderprice;
            // }
            let volumes = this.amount || 0;
            const unrealizedPnL = (Number(volumes) * Number(multiplier) * priceDiff)

            return unrealizedPnL;
        },

        // 计算收益率
        calculateProfitRate(position) {
            if (!position) return 0;

            const {
                margin = 0            // 保证金
            } = position;

            // 获取未实现盈亏
            const unrealizedPnL = this.calculateUnrealizedPnL(position);

            // 收益率 = 未实现盈亏 / 保证金
            if (Number(margin) === 0) {
                return 0;
            }

            const profitRate = (unrealizedPnL / Number(margin)) * 100; // 转换为百分比

            return profitRate;
        },
        // 格式化未实现盈亏显示
        formatUnrealizedPnL(position) {
            const pnl = this.calculateUnrealizedPnL(position);

            const formatted = Math.abs(pnl).toFixed(2);
            console.log(formatted, pnl);

            if (pnl >= 0) {
                console.log(formatted, pnl);

                return `+${formatted}`;
            } else if (pnl < 0) {
                return `-${formatted}`;
            }
        },
        handleSelect(item) {
            if (item) {
                this.orderType = item.value
            }
        },
        endMove(e) {
            if (e == 0) {
                this.currentfff = 0
            } else if (e == 20) {
                this.currentfff = 1
            } else if (e == 40) {
                this.currentfff = 2
            } else if (e == 65) {
                this.currentfff = 3
            } else if (e == 80) {
                this.currentfff = 4
            } else if (e == 100) {
                this.currentfff = 5
            }
            // this.amount = e
            // 滑块结束移动时重新计算订单数量
            // this.calculateOrderVolume();
        },
        formattedNumTypes(e) {
            return this.NumTypes.find(item => item.value == e)?.label || ''
        },

        formatPercent(value) {
            return (value * 100).toFixed(2) + "%";
        },
        handlePercent(p) {
            this.percent = p;
            this.amount = Math.floor((this.order.holdAmount || 0) * (p / 100));
        },
        async handleConfirm() {
            // this.$emit('confirm', {
            //     order: this.order,
            //     amount: this.amount
            // });

            // 校验输入的是否为空

            if (!this.amount) {
                this.$toast.fail('请输入要平仓的仓位金额');
                return;
            }

            let res = await this.$api.closePosition({
                contractId: uni.getStorageSync('currentContract').id || '',
                positionId: this.order.positionId,
                orderType: this.orderType == 'LIMIT' ? 1 : 2,
                price: this.orderType == 'LIMIT' ? this.orderprice : this.latestPrice,
                volume: this.amount
            })
            if (res.code == 200) {
                this.$emit('confirm', true);
                uni.showToast({
                    title: '平仓提交成功',
                    icon: 'none'
                });
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        handleSelectNum(item) {
            // this.ordervolume = ''
            // this.sliderValue = 0
            this.NumTypesvalue = item.value
        },
    }
};
</script>

<style lang="scss" scoped>
::v-deep .u-slider__button {
    display: none !important;
}

// ::v-deep .u-slider__button {
//     z-index: 9999999 !important;
//     width: 20rpx !important;
//     height: 20rpx !important;
//     background: #ffffff;
//     // border: 2rpx solid #DDDDDD;
//     border-radius: 50%;
//     border: 4rpx solid #FF82A3;

// }

.close-popup {
    padding: 40rpx 32rpx 100rpx 32rpx;
    background: #fff;
    border-radius: 16rpx 16rpx 0 0;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 30rpx;
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, .05);

        .title {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 48rpx;
            color: #000;
        }

        .close {
            width: 48rpx;
            height: 48rpx;
        }
    }

    .info {
        margin: 28rpx 0 40rpx 0;
        padding-bottom: 28rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, .05);

        .row {
            display: flex;
            justify-content: space-between;
            // margin-bottom: 12rpx;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 32rpx;
            margin-bottom: 24rpx;

            &:last-child {
                margin-bottom: 0;
            }

            text {
                &:nth-of-type(1) {
                    color: rgba(0, 0, 0, .5);
                }

                &:nth-of-type(2) {
                    color: #000;
                }

            }

            .red {
                color: #FF82A3;
            }

            .green {
                color: #08B819;
            }
        }
    }

    .form {
        .input-group {
            margin-bottom: 24rpx;

            .label {
                margin-bottom: 16rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 34rpx;

                text {
                    &:last-child {
                        font-weight: 400;

                    }
                }

            }

            .input-box-num {
                display: flex;
                align-items: center;
                justify-content: space-between;
                background: #f6f6f6;
                height: 80rpx;
                border-radius: 8rpx;
                padding: 0 24rpx 0 20rpx;

                .uni-input-placeholder {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                }

                input {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: #000;
                    flex: 1;
                }

                .unit {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                    display: flex;
                    align-items: center;
                    gap: 12rpx;

                    image {
                        width: 14rpx;
                        height: 10rpx;
                    }

                    &.rotated {
                        transform: rotate(180deg);
                    }
                }
            }

            .input-box {
                display: flex;
                align-items: center;
                gap: 16rpx;


                .uni-input-placeholder {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                }

                ::v-deep input {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx !important;
                    line-height: 40rpx;
                    color: #000;
                    flex: 1
                }

                .left {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 538rpx;
                    height: 80rpx;
                    background: #f6f6f6;
                    padding: 0 20rpx;
                    border-radius: 8rpx;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: rgba(0, 0, 0, .5);
                }

                // .input {
                //     flex: 1;
                //     font-size: 28rpx;
                // }

                .type {
                    height: 80rpx;

                    background: #f6f6f6;
                    padding: 0 20rpx;
                    border-radius: 8rpx;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    color: rgba(0, 0, 0, .5);
                }
            }
        }

        .slider {
            // display: flex;
            // justify-content: space-between;
            // margin: 24rpx 0;

            .slidelevel {
                // margin-top: 26rpx;
                margin: 17rpx 0 0 0;
                width: 100%;
                display: flex;
                justify-content: space-between;
                flex: 1;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 34rpx;


                text {
                    display: block;
                    width: 50rpx;
                    // margin: 0 10rpx 0 10rpx;
                }
            }
        }

        .summarys {
            margin-top: 20rpx;

            .summary {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 28rpx;
                margin-bottom: 24rpx;

                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;


                text {
                    &:nth-of-type(1) {
                        color: rgba(0, 0, 0, .5);
                    }

                    &:nth-of-type(2) {
                        // color: rgba(0, 0, 0, 1);
                    }
                }

                .pnl {
                    color: #FF82A3;
                }
            }
        }


    }

    .btns {
        width: 100%;
        margin-top: 40rpx;

        .btn {
            background: #FF82A3;
            color: white;
            border-radius: 110rpx;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;

        }
    }

}
</style>