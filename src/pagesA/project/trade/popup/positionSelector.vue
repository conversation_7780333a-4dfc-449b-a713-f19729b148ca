<template>
    <u-popup mode="bottom" border-radius="16" v-model="show" @close="close" round safe-area-inset-bottom>
        <view class="mode-popup">
            <!-- 顶部标题栏 -->
            <view class="popup-header">
                <text class="title">调整交易模式</text>
                <view class="close" @click="close">
                    <image src="https://pro-oss.pinkwallet.com/image/1377388318231715840.png" />
                </view>
            </view>

            <!-- 模式选项 -->
            <view class="mode-block" v-for="(item, index) in modeList" :key="index">
                <text class="mode-title">{{ item.title }}</text>
                <text class="mode-desc">{{ item.desc }}</text>
                <view class="btn-group">
                    <view v-for="(btn, i) in item.options" :key="i"
                        :class="['mode-btn flex_all', selected[item.key] === btn.value ? 'active' : '']"
                        @click="selectMode(item.key, btn.value)">
                        {{ btn.label }}
                    </view>
                </view>
            </view>

            <!-- 底部确认按钮 -->
            <u-button hover-class="none" class="confirm-btn" @click="onConfirm">确定</u-button>
        </view>
    </u-popup>
</template>

<script>
export default {
    name: 'ModeSelector',
    props: {
        show: Boolean
    },
    data() {
        return {
            userInfo: {},
            selected: {
                marginType: 'isolated',
                positionType: 'split'
            },
            modeList: [
                {
                    key: 'marginType',
                    title: '全仓模式：',
                    desc: '保证金资产相同的全仓仓位共享该资产的全仓保证金。在强平事件中，交易者可能会损失全部保证金和该保证金资产下的所有全仓仓位。',
                    options: [
                        // { label: '全仓', value: 'full' },
                        { label: '逐仓', value: 'isolated' }
                    ]
                },
                {
                    key: 'positionType',
                    title: '逐仓合仓：',
                    desc: '同一方向(开多或开空)且同一交易对下单将合并为一个仓位。合仓模式下同一交易对最多只有全仓和逐仓两个仓位，杠杆只能统一调整，所有仓位保证金互相独立。',
                    options: [
                        { label: '合仓', value: 'merged' },
                        { label: '分仓', value: 'split' }
                    ]
                }
            ]
        }
    },
    mounted() {
        this.fetchgetUserInfos()
    },
    methods: {
        async fetchgetUserInfos() {
            let res = await this.$api.getUserInfos()
            if (res.code == 200) {
                this.userInfo = res.result
                if (res.result.isMerge) {
                    this.selected.positionType = 'merged'
                } else {
                    this.selected.positionType = 'split'
                }
            }
        },
        selectMode(key, value) {
            this.selected[key] = value
        },
        close() {
            console.log('close123');
            this.$emit('update:show', false)
        },
        onConfirm() {
            this.$emit('confirm', {
                marginType: this.selected.marginType,
                positionType: this.selected.positionType
            })
            this.fetchchangeMergeStatus()
            this.close()
        },
        async fetchchangeMergeStatus() {
            let res = await this.$api.changeMergeStatus({})
            if (res.code != 200) {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.mode-popup {
    padding: 42rpx 32rpx;
    margin-bottom: 60rpx;

    .popup-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-family: PingFang SC;
        font-weight: 600;
        line-height: 48rpx;
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 32rpx;

        .title {
            color: #000;
        }

        .close {
            width: 48rpx;
            height: 48rpx;

            image {
                width: 48rpx;
                height: 48rpx;

            }
        }
    }

    .mode-block {
        .mode-title {
            color: #000;
            // margin-bottom: 10rpx;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 100%;

        }

        .mode-desc {
            color: #00000080;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 100%;
            // margin-bottom: 20rpx;
        }

        .btn-group {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 14rpx;
            margin: 32rpx 0 48rpx 0;

            .mode-btn {
                font-size: 26rpx;
                color: rgba(0, 0, 0, 0.4);
                padding: 26rpx 140rpx;
                border-radius: 111rpx;
                transition: none;
                font-family: PingFang SC;
                font-weight: 600;
                color: #000;
                background: #F2F2F2;
                white-space: nowrap;

                &.active {
                    color: #FF82A3;
                    font-weight: 500;
                    border: 1px solid #FF82A3;
                    background: transparent;

                }
            }
        }
    }

    .confirm-btn {
        margin-top: 26rpx;
        width: 100%;
        text-align: center;
        background: #FF82A3;
        color: #fff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        border-radius: 111rpx;
    }
}
</style>