<template>
    <u-popup border-radius="16" mode="bottom" v-model="show" @close="onClose" safe-area-inset-bottom round>
        <view class="popup-content">
            <view class="popup-handle" />
            <view class="options">
                <view class="option" v-for="(item, index) in options" :key="index" @click="onSelect(item)">
                    {{ item.label }}
                </view>
            </view>
            <view class="cancel" @click="onClose">取消</view>
        </view>
    </u-popup>
</template>

<script>
export default {
    name: 'mode',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: () => []
        }
    },
    methods: {
        onSelect(item) {
            this.$emit('select', item)
            this.onClose()
        },
        onClose() {
            this.$emit('update:show', false)
        }
    }
}
</script>

<style lang="scss" scoped>
.popup-content {
    padding: 24rpx 0 0 0 ;
    // margin-bottom: 60rpx;



    .popup-handle {
        background: #D9D9D9;
        border-radius: 8rpx;
        margin: 0 auto;
        width: 68rpx;
        height: 8rpx;
    }

    .options {

        .option {
            font-family: Gilroy-Medium;
            font-weight: 500;
            font-size: 28rpx;
            line-height: 40rpx;
            color: #000;
            padding: 34rpx 0 34rpx 32rpx;
            text-align: left;


            &:nth-of-type(1) {
                padding: 56rpx 0 34rpx 32rpx;
            }

            &:last-child {
                padding: 34rpx 0 100rpx 32rpx;
            }
        }

    }


    .cancel {
        border-top: 1.5px solid rgba(149, 149, 149, .1);
        font-size: 28rpx;
        font-family: Gilroy-Medium;
        font-weight: 500;
        color: #000;
        padding: 34rpx 0;
        text-align: center;
    }
}
</style>