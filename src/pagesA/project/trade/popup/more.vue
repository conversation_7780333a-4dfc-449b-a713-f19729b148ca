<template>
    <view class="tab-content">
        <u-popup @close="close" v-model="show" mode="bottom" border-radius="16" :safe-area-inset-bottom="false">
            <view class="more-popup">
                <view class="header">
                    <text class="title">更多</text>
                    <image class="close" @click="close"
                        src="https://pro-oss.pinkwallet.com/image/1377388318231715840.png" />
                </view>
                <view class="option-grid">
                    <view class="option-item" @click="nav_to(item.path)" v-for="(item, index) in moreList" :key="index">
                        <view class="icon-wrap">
                            <image :src="item.icon" />
                        </view>
                        <text class="label">{{ item.label }}</text>
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
export default {
    props: {
        show: Boolean
    },
    data() {
        return {
            moreList: [
                { label: '资金划转', icon: "https://pro-oss.pinkwallet.com/image/1385353782983483392.png", path: 'Mutualtransfer' },
                { label: '充币', icon: "https://pro-oss.pinkwallet.com/image/1385353916144246784.png", path: 'charge' },
                { label: '提币', icon: "https://pro-oss.pinkwallet.com/image/1385354009756917760.png", path: 'withdraw' },
                { label: '', icon: "https://pro-oss.pinkwallet.com/image/1385354359603814400.png", path: 'choose' },
                { label: '帮助中心', icon: "https://pro-oss.pinkwallet.com/image/1385354478323589120.png", path: 'faq' },
            ],
            info: null
        }
    },
    watch: {
        "$store.state.symbol"(val) {
            this.getContractSymbols()
        }
    },
    mounted() {
        this.getContractSymbols()
    },
    methods: {
        async getContractSymbols() {
            let res = await this.$api.isSelect({
                contractSymbolId: uni.getStorageSync("currentContract").id
            })
            if (res.code == 200) {
                this.info = res.result
                if (res.result) {
                    this.moreList[3].label = "取消自选"
                } else {
                    this.moreList[3].label = "自选"
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
            }
        },
        close() {
            this.$emit('update:show', false)
        },
        nav_to(e) {
            if (e == 'faq') {
                this.$Router.push({
                    name: 'webView',
                    params: {
                        // url: "https://pinkwallet.blog/?page_id=100"
                        url: "https://pinkwallet.zendesk.com/hc/zh-sg"
                    }
                })
                return
            }
            if (e == 'choose') {
                this.starClick(this.info)
                this.close()
                return
            }
            this.$Router.push({
                name: e
            });
        },
        async starClick(e) {
            if (e) {
                let res = await this.$api.deleteContractSelf({
                    contractSymbolId: uni.getStorageSync("currentContract").id
                })
                if (res.code == 200) {
                    this.getContractSymbols()
                    uni.showToast({
                        title: "取消自选成功",
                        icon: 'none'
                    })
                }
            } else {
                let res = await this.$api.addContractSelf({
                    contractSymbolId: uni.getStorageSync("currentContract").id
                })
                if (res.code == 200) {
                    this.getContractSymbols()
                    uni.showToast({
                        title: "添加自选成功",
                        icon: 'none'
                    })
                }
            }
        },
        // async getContractSymbols() {
        //     let res = await this.$api.getContractSymbol({
        //         id: uni.getStorageSync("currentContract").id
        //     })
        //     if (res.code == 200) {
        //         this.info = res.result
        //     } else {
        //         uni.showToast({
        //             title: res.msg,
        //             icon: 'none',
        //             duration: 3000
        //         });
        //     }
        // },
    }
};
</script>

<style lang="scss" scoped>
.more-popup {
    padding: 50rpx 32rpx;
    padding-bottom: 100rpx;
    background-color: #fff;
    border-radius: 16rpx;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 30rpx;
        padding-bottom: 24rpx;

        .title {
            font-family: Gilroy-Medium;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 48rpx;
            color: #000;
        }

        .close {
            width: 48rpx;
            height: 48rpx;
        }
    }

    .option-grid {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 12rpx;

        .option-item {

            background: #F9F9F9;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            width: 162rpx;
            height: 186rpx;
            border-radius: 24rpx;


            .icon-wrap {
                width: 68rpx;
                height: 68rpx;
                // border-radius: 50%;
                // background-color: #fff;
                // display: flex;
                // justify-content: center;
                // align-items: center;
                margin-bottom: 12rpx;

                image {
                    width: 68rpx;
                    height: 68rpx;
                }
            }

            .label {
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 40rpx;
                text-align: center;
                color: #000;
            }
        }
    }
}
</style>