<template>
    <u-popup v-model="show" mode="bottom" width="90%" border-radius="16" :mask-close-able="true">
        <view class="funding-popup">
            <!-- 标题 -->
            <view class="header">
                <view class="title">资金费率</view>
                <u-icon name="close" size="28" @click="show = false"></u-icon>
            </view>

            <!-- 表格内容 -->
            <view class="rate-list">
                <view class="rate-item">
                    <text class="label">时间周期</text>
                    <text class="value">4时</text>
                </view>
                <view class="rate-item">
                    <text class="label">资金费率上限/下限</text>
                    <text class="value">2.0000% / -2.0000%</text>
                </view>
                <view class="rate-item">
                    <text class="label">前一次资金费率 / 日利率</text>
                    <text class="value">0.0050% / 0.0300%</text>
                </view>
                <view class="rate-item">
                    <text class="label">预期资金费率 / 日利率</text>
                    <text class="value">0.0050% / 0.0300%</text>
                </view>
                <view class="rate-item">
                    <text class="label">预估资金费用</text>
                    <text class="value">--</text>
                </view>
            </view>

            <!-- 说明文字 -->
            <view class="description">
                做多仓位和做空仓位在下个资金费率时间点须交换的资金费率。<br />
                费率为正，做多仓位支付做空仓位；费率为负，做空仓位支付做多仓位。
            </view>

            <!-- 确定按钮 -->
            <button class="confirm-btn" @click="show = false">确定</button>
        </view>
    </u-popup>
</template>

<script>
export default {
    data() {
        return {
            show: false
        };
    }
};
</script>

<style lang="scss" scoped>
.funding-popup {
    padding: 40rpx 32rpx;
    font-family: PingFang SC;
    background: #fff;
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30rpx;

        .title {
            font-size: 32rpx;
            font-weight: 600;
            color: #000;
        }
    }

    .rate-list {
        .rate-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10rpx 0;
            font-size: 26rpx;

            .label {
                color: #999;
            }

            .value {
                color: #333;
                font-weight: 500;
            }
        }
    }

    .description {
        margin-top: 40rpx;
        padding-top: 30rpx;
        border-top: 1rpx solid #eee;
        font-size: 22rpx;
        color: #666;
        line-height: 38rpx;
    }

    .confirm-btn {
        margin-top: 40rpx;
        background-color: #f96c90;
        color: #fff;
        border-radius: 50rpx;
        font-size: 28rpx;
        height: 80rpx;
        line-height: 80rpx;
    }
}
</style>