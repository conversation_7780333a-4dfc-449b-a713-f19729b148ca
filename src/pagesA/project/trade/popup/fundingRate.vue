<template>
    <u-popup v-model="show" mode="bottom" width="90%" border-radius="16" :mask-close-able="true">
        <view class="funding-popup">
            <!-- 标题 -->
            <view class="header">
                <view class="title">资金费率</view>
                <u-icon name="close" size="24" @click="show = false"></u-icon>
            </view>

            <!-- 表格内容 -->
            <view class="rate-list">
                <view class="rate-item">
                    <text class="label">时间周期</text>
                    <text class="value">8时</text>
                </view>
                <view class="rate-item">
                    <text class="label">资金费率上限/下限</text>
                    <text class="value">0.75% / -0.75%</text>
                </view>
                <view class="rate-item">
                    <!-- currentFundRate -->
                    <text class="label">前一次资金费率 / 日利率</text>
                    <text class="value">{{ (($store.state.fundrate * 100 * 100) / 100).toFixed(2) || '0.00' }}% / 0.00%</text>
                </view>
                <view class="rate-item">
                    <!-- nextFundRate -->
                    <text class="label">预期资金费率 / 日利率</text>
                    <text class="value">{{ (($store.state.indexprice * 100 * 100) / 100).toFixed(2) || '0.00' }}% / 0.00%</text>
                </view>
                <!-- 所有持仓张数 -->
                <!-- 仓位价值 * nextFundRate -->
                <!-- <view class="rate-item">
                    <text class="label">预估资金费用</text>
                    <text class="value">--</text>
                </view> -->
            </view>

            <!-- 说明文字 -->
            <view class="description">
                做多仓位和做空仓位在下个资金费率时间点须交换的资金费率。费率为正，做多仓位支付做空仓位。费率为负，做空仓位支付做多仓位。
            </view>

            <!-- 确定按钮 -->
            <button class="confirm-btn" @click="show = false">确定</button>
        </view>
    </u-popup>
</template>

<script>
export default {
    data() {
        return {
            show: false,
            fundrate: "0.00",
            nextFundRate: "0.00"
        };
    },
    watch: {
        "$store.state.fundrate"(val) {
            // console.log(val);
            this.fundrate = val
        },
        "$store.state.indexprice"(val) {
            this.nextFundRate = val
        },
    }
};
</script>

<style lang="scss" scoped>
.funding-popup {
    padding: 40rpx 32rpx;
    font-family: Gilroy-Medium;
    background: #fff;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin-bottom: 30rpx;
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, .05);

        .title {
            font-family: Gilroy-Medium;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 48rpx;
            color: #000;
        }
    }

    .rate-list {
        margin-top: 28rpx;
        padding-bottom: 40rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, .05);

        .rate-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16rpx 0;
            font-family: Gilroy-Medium;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 32rpx;

            .label {
                color: rgba(0, 0, 0, .5);
            }

            .value {

                text-align: right;
                color: #000;
            }
        }
    }

    .description {
        margin-top: 40rpx;
        font-family: Gilroy-Medium;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 32rpx;
        color: rgba(0, 0, 0, .5);
    }

    .confirm-btn {
        margin-top: 72rpx;
        background-color: #FF82A3;
        color: #fff;
        border-radius: 112rpx;
        font-size: 28rpx;
        height: 88rpx;
        line-height: 88rpx;
        font-weight: 500;
    }
}
</style>