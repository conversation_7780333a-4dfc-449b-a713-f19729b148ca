<template>
    <view class="page-container">
        <!-- 顶部标题 -->
        <u-navbar :border-bottom="false" :title='$t("title.SetPasswordTitle")'>
        </u-navbar>
        <view class="register-page">
            <view class="register-container">
                <view class="input-group">
                    <!-- v-if="!phoneNumber" -->
                    <view class="input-title">{{ $t("Auth.SignUp.PhoneNumber") }}</view>
                    <!-- <view class="input-title" v-if="phoneNumber">Old Email</view> -->
                    <view class="phone-input">

                        <transition name="expand-slide">
                            <view class="helpoption" v-show="showRegion">
                                <view v-for="(item, index) in RightOption" :key="index" class="Roptions"
                                    @click="handleRight(item)">
                                    <image class="Rimg" :src="item.image"></image>
                                    <text>{{ item.label }}</text>
                                </view>
                                <!-- @click="nav_to('CurrencyCharge')" -->
                            </view>
                        </transition>
                        <!-- <view class="country-code flex_all" @click="toggleRotate"
                            :style="{ backgroundColor: phoneNumber ? '#D9D6D6' : '' }"> -->
                        <!-- :class="['helpoption', currentIndex === index ? 'expand' : 'collapse']" -->
                        <!-- <text>+{{ region }}</text> -->
                        <!-- <i class="fas fa-chevron-down" style="color: #9ca3af;"></i> -->
                        <!-- <image class="arrow" :class="{ rotated: isRotated }" -->
                        <!-- src="https://pro-oss.pinkwallet.com/image/20250303/e12a5adca8ec7fc0c836cfb822f1b7ee_16x8.png" /> -->
                        <!-- </view> -->
                        <!-- :style="{ backgroundColor: phoneNumber ? '#D9D6D6' : '' }" -->
                        <u-input type="text" v-model="phoneNumber" placeholder=" " height="102"
                            class="phone-number-input" />
                    </view>
                </view>
                <view class="input-group">
                    <view class="input-title">{{ $t("Auth.SignUp.VerificationCode") }}</view>
                    <view class="verification-input">
                        <u-input :clearable="false" type="text" v-model="verificationCode" placeholder="" height="102"
                            class="verification-code-input" />
                        <view class="get-code-btn flex_all" :disabled="isGettingCode" @click="getVerificationCode">
                            {{ isGettingCode ? `${countdown}s` : $t("Auth.SignUp.GetOTP") }}
                        </view>
                    </view>
                </view>

                <view class="input-group">
                    <view class="input-title">{{ $t("Security.Set") }}{{ types == 'login' ? $t("Security.Login") :
                        $t("Security.Payment") }}{{
                            $t("Security.pwd") }}
                    </view>
                    <view class="verification-input">
                        <u-input type="password" v-model="loginPassword" placeholder=" " height="102"
                            class="verification-code-input" />
                    </view>
                </view>
                <!-- :disabled="!isFormValid" -->
                <u-button hover-class="none" class="signup-btn flex_all !rounded-button" @click="handleSubmit">
                    {{ $t("title.submit") }}
                </u-button>
                <u-button hover-class="none" class="cancel-btn flex_all !rounded-button" @click="handleCancel">
                    {{ $t("title.cancel") }}
                </u-button>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            types: "",
            phoneNumber: "",
            region: "86",
            isGettingCode: false,
            timer: null,
            showRegion: false,
            countdown: 59,
            verificationCode: "",
            isRotated: false, // 控制旋转状态
            RightOption: [
                {
                    value: '1',
                    label: '1',
                    image: 'https://flagcdn.com/16x12/us.png'
                },
                {
                    value: '86',
                    label: '86',
                    image: 'https://flagcdn.com/16x12/cn.png'
                },
                {
                    value: '44',
                    label: '44',
                    image: 'https://flagcdn.com/16x12/gb.png'
                },
                {
                    value: '81',
                    label: '81',
                    image: 'https://flagcdn.com/16x12/jp.png'
                },
                {
                    value: '91',
                    label: '91',
                    image: 'https://flagcdn.com/16x12/in.png'
                },
                {
                    value: '33',
                    label: '33',
                    image: 'https://flagcdn.com/16x12/fr.png'
                },
                {
                    value: '61',
                    label: '61',
                    image: 'https://flagcdn.com/16x12/au.png'
                }
            ],
            eyeicon: "eye",
            loginPassword: '', // 登录密码
            paymentPassword: ['', '', '', '', '', ''] // 支付密码每位数字
        };
    },
    computed: {
        isFormValid() {
            return this.phoneNumber &&
                this.verificationCode
        }
    },
    onLoad(e) {
        uni.setNavigationBarTitle({
            title: this.$t("title.SetPasswordTitle") // 切换语言后重新设置标题
        })
        if (e.type) {
            this.types = e.type
        }
        if (e.email) {
            this.phoneNumber = e.email
        }
    },
    methods: {
        getVerificationCode() {
            if (!this.phoneNumber) {
                uni.showToast({
                    title: this.$t("Please.email"),
                    icon: 'none',
                    duration: 2000
                });
                return
            } else if (!/^[\w.-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$/.test(this.phoneNumber)) {
                uni.showToast({
                    title: this.$t("Please.erroremail"),
                    icon: 'none',
                    duration: 2000
                });
                return
            }
            if (this.isGettingCode) return;
            this.isGettingCode = true;
            this.countdown = 59;
            this.timer = setInterval(() => {
                if (this.countdown > 0) {
                    this.countdown--;
                } else {
                    this.isGettingCode = false;
                    clearInterval(this.timer);
                }
            }, 1000);
            this.sendEmailVerifyCode()
        },
        async sendEmailVerifyCode() {
            // let res = await this.$api.sendPhoneVerifyCode({
            let res = await this.$api.sendMailCaptcha({
                email: this.phoneNumber
            });
            if (res.code == 200) {
                this.$u.toast(this.$t("register.Send"));
                // 通知验证码组件内部开始倒计时
                this.$refs.uCode.start();
            } else {
                // if (res.code == 110001) {
                //     this.$u.toast(res.msg);
                //     setTimeout(() => {
                //         this.$Router.push({
                //             name: 'login',
                //         })
                //     }, 2000)
                // } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                    duration: 3000
                });
                // }
            }
        },
        toggleRotate() {
            this.showRegion = !this.showRegion; // 切换显示状态
            this.isRotated = !this.isRotated; // 点击时切换状态
        },
        toogleeye() {
            this.eyeicon = this.eyeicon == "eye" ? "eye-fill" : "eye";
        },
        change(e) {
        },
        finish(e) {
        },
        async handleSubmit() {
            // 此处可添加校验或提交逻辑
            if (!this.phoneNumber || !this.verificationCode || !this.loginPassword) {
                uni.showToast({
                    title: this.$t("Please.full"),
                    icon: 'none'
                });
                return
            }
            let res = await this.$api.changePasswordByEmail({
                email: this.phoneNumber,
                password: this.loginPassword,
                captcha: this.verificationCode,
                passwordType: this.types == 'login' ? 'login' : 'pay'
            })
            if (res.code == 200) {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
                setTimeout(() => {
                    uni.navigateBack();
                }, 1000);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        handleCancel() {
            // 取消操作，例如返回上一页
            uni.navigateBack();
        }
    }
};
</script>

<style scoped lang="scss">
.page-container {
    display: flex;
    flex-direction: column;
    padding: 44rpx;
    min-height: 100vh;

    .register-page {
        padding: 24px 0;

        .register-container {
            max-width: 375px;
            margin: 0 auto;
        }

        .input-group {
            margin-bottom: 16px;

            .input-title {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                color: #000;
                margin-bottom: 20rpx;
            }

            .phone-input {
                display: flex;
                border-radius: 8px;
                // overflow: hidden;
                position: relative;

                .helpoption {
                    width: 85*2rpx;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 122rpx;
                    left: 0;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;
                    }

                    >view {

                        padding: 15rpx 0;
                        display: flex;
                        align-items: center;

                        image {
                            width: 40rpx;
                            height: 30rpx;
                        }

                        text {
                            margin-left: 20rpx;
                            display: block;
                            font-family: Gilroy-Bold;
                            font-weight: 400;
                            font-size: 16*2rpx;
                            line-height: 19.2*2rpx;
                            color: #000;
                        }
                    }
                }

                .country-code {
                    // overflow: hidden;
                    width: 85*2rpx;
                    height: 51*2rpx;
                    border-radius: 10*2rpx;
                    border-width: 2rpx;
                    // padding: 16px;
                    border: 2rpx solid #999999;

                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;


                    display: flex;
                    align-items: center;



                    // padding: 12px 16px;
                    // border-right: 1px solid #e5e7eb;
                    // font-size: 14px;
                    // color: #374151;
                    // gap: 8px;
                    .arrow {
                        /* 图片宽度 */
                        /* 图片高度 */
                        transition: transform 0.3s ease;
                        /* 动画效果：0.3秒平滑旋转 */
                    }

                    .rotated {
                        transform: rotate(180deg);
                        /* 旋转180度 */
                    }

                    image {
                        margin-left: 22rpx;
                        width: 28rpx;
                        height: 14rpx;
                    }
                }

                .phone-number-input {
                    border: 2rpx solid #999999 !important;
                    height: 51*2rpx;
                    border-radius: 10*2rpx;
                    // margin-left: 20rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    color: #000;
                    padding: 0 32rpx !important;
                }
            }

            .verification-input {
                height: 51*2rpx;
                display: flex;
                border: 2rpx solid #999999 !important;
                border-radius: 10*2rpx;
                padding: 0 32rpx !important;
                overflow: hidden;
                position: relative;

                .verification-code-input {
                    flex: 1;
                    padding: 12px 16px;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    color: #000;
                }

                .get-code-btn {
                    font-size: 14*2rpx;
                    color: #000;
                    right: 14rpx;
                    top: 8rpx;
                    position: absolute;
                    width: 110*2rpx;
                    height: 42*2rpx;
                    border-radius: 10*2rpx;

                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    vertical-align: middle;
                    text-decoration: underline;
                    text-decoration-style: solid;
                    text-decoration-offset: 15%;
                    text-decoration-thickness: 10%;


                    &:disabled {
                        background: #FF82A3;
                    }
                }
            }
        }

        .agreement-section {
            margin: 24px 0;

            .agreement-checkbox {
                display: flex;
                align-items: flex-start;
                // gap: 8px;
                cursor: pointer;

                .checkbox-image {
                    margin-right: 20rpx;
                    width: 65rpx !important;
                    height: 40rpx;
                    cursor: pointer;
                }

                input {
                    display: none;
                }

                .checkbox-custom {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #e5e7eb;
                    border-radius: 4px;
                    position: relative;
                }

                input:checked+.checkbox-custom::after {
                    content: '\2714';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: #008E28;
                }

                .agreement-text {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 14*2rpx;
                    line-height: 22.4*2rpx;
                    color: #333333;
                }
            }
        }

        .cancel-btn {
            margin-top: 24rpx;
            width: 100%;
            background-color: #fff;
            color: 000;
            font-size: 16*2rpx;
            border-radius: 64*2rpx;
            height: 100rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            border: 2rpx solid #999999
        }

        .signup-btn {
            width: 100%;
            background-color: #FF82A3;
            color: white;
            border: none;
            font-size: 16*2rpx;
            border-radius: 64*2rpx;
            height: 100rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;

            &:disabled {
                // background-color: #e5e7eb;
            }
        }

        .divider {
            text-align: center;
            position: relative;
            margin: 42rpx 0;

            &::before,
            &::after {
                content: '';
                position: absolute;
                top: 50%;
                width: calc(50% - 100rpx);
                height: 2rpx;
                background-color: #e5e7eb;
            }

            &::before {
                left: 0;
            }

            &::after {
                right: 0;
            }

            text {
                padding: 0 7rpx;
                color: #6b7280;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                text-align: center;

            }
        }

        .google-btn {
            height: 50*2rpx;
            border-radius: 64*2rpx;
            border-width: 1*2rpx;

            width: 100%;
            // padding: 14px;
            background-color: white;
            border: 2rpx solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20rpx;
            color: #374151;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 32rpx;

            image {
                width: 52rpx;
                height: 52rpx;
            }
        }


    }
}

.header {
    text-align: center;
    margin-bottom: 20px;
}

.title {
    font-size: 22px;
    font-weight: bold;
    color: #333;
}

.form-container {
    flex: 1;
    margin-bottom: 20px;
}

.form-item {
    margin-bottom: 20px;
}

.label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.payment-input {
    display: flex;

    // justify-content: space-between;
    .icon {
        margin-left: 24rpx;
    }
}

.digit-input {
    width: 40px;
    height: 40px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.button-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.submit-btn {
    color: #fff;
}

.cancel-btn {
    color: #333;
    border: 1rpx solid #ddd;
}
</style>