 <template>
    <view class="page-container">
        <!-- 返回按钮 -->
        <u-navbar :border-bottom="false" :title='$t("Security.title")'>
        </u-navbar>
        <view style="height: 64rpx;"></view>

        <!-- 密码设置部分 -->
        <view class="subtitle">{{ $t("Security.pwd") }}</view>
        <view class="password-section">
            <view class="password-item">
                <view class="label">{{ $t("Security.LoginPassword") }}</view>
                <view class="setup-btn" @click="setupLoginPassword">
                    <view class="Verify"> {{ userInfo.loginPassSet ? $t("Security.Edit") : $t("Security.SetNow") }}
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250307/ef2cad14d74bf51030f9ae5b2b2e1522_96x96.png" />
                    </view>
                </view>
            </view>

            <view class="password-item">
                <view class="label">{{ $t("Security.PaymentPassword") }}</view>
                <view class="setup-btn" type="text" @click="setupPaymentPassword">
                    <view class="Verify">{{ userInfo.tradePassSet ? $t("Security.Edit") : $t("Security.SetNow") }}
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250307/ef2cad14d74bf51030f9ae5b2b2e1522_96x96.png" />
                    </view>
                </view>
            </view>
        </view>

        <!-- 登录设备部分 -->
        <!-- #ifdef APP -->
        <view class="subtitle">{{ $t("Security.devices") }}</view>
        <view class="device-section">
            <view class="device-info">
                <view class="device-name">{{ Device }}</view>
                <view class="device-id" @click="removeDevice">{{ $t("Security.Remove") }}</view>
            </view>
        </view>
        <!-- #endif -->
    </view>
</template>

<script>
export default {
    data() {
        return {
            Device: "",
            DeviceId: "",
            userInfo: {}
        }
    },
    onLoad() {
        // uni.getSystemInfo({
        //     success: function (res) {
        //         console.log( res)
        //     }
        // })
        uni.setNavigationBarTitle({
            title: this.$t("Security.title") // 切换语言后重新设置标题
        })
        this.Device = uni.getSystemInfoSync().model || '--'
        this.DeviceId = uni.getSystemInfoSync().deviceId || '--'
        this.getUserInfos()

    },
    methods: {
        async removeDevice() {
            let res = await this.$api.unBindDevice({
                deviceId: uni.getStorageSync('deviceToken') || '',
            })
            console.log(res);

            if (res.code == 200) {
                uni.showToast({
                    title: this.$t("Security.Removesuccess"),
                    icon: 'none'
                })
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })
            }
        },

        async getUserInfos() {
            let res = await this.$api.getUserInfo()
            console.log(res, 123);

            if (res.code == 200) {
                this.userInfo = res.result
            }

        },
        goBack() {
            uni.navigateBack();
        },
        setupLoginPassword() {
            this.$Router.push({
                name: 'SettingPwd',
                params: {
                    type: 'login',
                    email: this.userInfo.email
                }
            })
        },
        setupPaymentPassword() {
            this.$Router.push({
                name: 'SettingPwd',
                params: {
                    type: 'Pay',
                    email: this.userInfo.email
                }
            })
        }
    }
}
</script>

<style scoped lang="scss">
.page-container {
    padding: 32rpx;
    min-height: 100vh;

    .subtitle {
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 32rpx;
        line-height: 120%;
        letter-spacing: 0%;
        vertical-align: middle;
        color: #000;
        margin-bottom: 20rpx;
    }

    .password-section {
        width: 100%;
        margin-bottom: 40rpx;
        border-radius: 40rpx;
        border: 2rpx solid #D9D6D6;
        // width: 398;
        // height: 138;
        padding: 30*2rpx 21*2rpx 30*2rpx 21*2rpx;


        .password-item {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 65rpx;

            &:last-child {
                margin-bottom: 0;
            }

            .label {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                letter-spacing: 0%;
                vertical-align: middle;
                color: #000;
            }

            .setup-btn {

                .Verify {
                    margin-left: 20rpx;
                    display: flex;
                    align-items: center;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 32rpx;
                    line-height: 120%;
                    letter-spacing: 0%;
                    vertical-align: middle;
                    text-decoration: underline;
                    text-decoration-style: solid;
                    text-decoration-offset: 15%;
                    text-decoration-thickness: 10%;
                    color: #FF82A3;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }
                }
            }

        }
    }

    .device-section {
        // width: 396.5;
        // height: 79;
        border-radius: 40rpx;
        border: 2rpx solid #D9D6D6;

        padding: 60rpx 42rpx;

        .device-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .device-name {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;
                letter-spacing: 0%;
                vertical-align: middle;
                color: #000;
            }

            .device-id {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 32rpx;
                line-height: 120%;

                color: #FF82A3;
            }
        }
    }

}
</style>