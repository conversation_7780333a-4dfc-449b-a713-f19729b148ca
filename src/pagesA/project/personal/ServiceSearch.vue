<template>
    <view class="search-page">
        <view class="barHeight"></view>
        <view class="search-header">
            <u-input v-model="keyword" class="search-input" placeholder="搜索更多服务" @input="onInput" />
            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382671813699002368.png" />
            <text class="cancel" @click="goBack">取消</text>
        </view>

        <view class="search-result">
            <view v-for="(item, i) in filteredList" :key="i" class="result-item" @click="go(item)">
                <view class="bg flex_all">
                    <image :src="item.icon" :style="{ width: item.w + 'rpx' }" mode="widthFix" class="icon" />
                </view>
                <text class="label">{{ item.name }}</text>
            </view>

            <view class="nodata" v-if="!filteredList.length">
                <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382796840972935168.png" />
                <text>没有搜索结果</text>
            </view>
        </view>
    </view>
</template>

<script>
import { serviceMap } from '@/utils/serviceData'

export default {
    data() {
        return {
            keyword: '',
            allServices: [],
            filteredList: []
        }
    },
    mounted() {
        // 聚合所有服务项
        const list = []
        Object.values(serviceMap).forEach(group => {
            list.push(...group)
        })
        this.allServices = list
        this.filteredList = list
    },
    methods: {
        onInput() {
            const kw = this.keyword.trim().toLowerCase()
            this.filteredList = this.allServices.filter(item =>
                item.name.toLowerCase().includes(kw)
            )
        },
        goBack() {
            uni.navigateBack()
        },
        go(name) {
            const hasSlash = name.path.includes('/');

            if (hasSlash) {
                this.$Router.push({
                    name: 'comming',
                    params: {
                        title: name.name
                    }
                });
                return;
            }
            if (name.path == 'support') {
                this.goChat()
                return
            }
            if (name.path == 'faq') {
                this.$Router.push({
                    name: 'webView',
                    params: {
                        url: "https://pinkwallet.zendesk.com/hc/zh-sg"
                    }
                })
                return
            }
            this.$Router.push({
                name: name.path
            })
            // uni.navigateTo({ url: path })
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .uni-input-placeholder {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 32rpx;
    vertical-align: middle;
    color: #000;
    opacity: .4;
}

.search-page {
    padding: 24rpx;

    .search-header {
        display: flex;
        align-items: center;
        margin-bottom: 30rpx;
        position: relative;

        image {
            position: absolute;
            left: 31rpx;
            width: 36rpx;
            height: 36rpx;

        }

        .search-input {
            text-indent: 75rpx;
            flex: 1;
            background: #F7F7F7;
            border-radius: 8rpx;
            padding: 16rpx;
            // margin-right: 32rpx;
        }

        .cancel {
            margin-left: 34rpx;
            // margin-left: 20rpx;
            color: #FF82A3;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 40rpx;
            text-align: center;

        }
    }

    .search-result {

        .nodata {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 224rpx;

            image {
                width: 192rpx;
                height: 162rpx;
            }

            text {
                margin-top: 72rpx;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 32rpx;
                vertical-align: middle;
                color: #000;
                opacity: .4;
            }
        }

        .result-item {
            display: flex;
            align-items: center;
            // padding: 20rpx 0;
            margin-bottom: 36rpx;

            .bg {
                margin-right: 38rpx;
                width: 84rpx;
                height: 84rpx;
                border-radius: 24rpx;
                background: #FEFAFE;
            }



            .label {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 100%;
                color: #000;
            }
        }
    }
}
</style>