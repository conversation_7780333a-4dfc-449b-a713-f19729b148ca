<template>
    <view class="service-page">
        <u-navbar :border-bottom="false" title="服务">
        </u-navbar>
        <!-- 搜索框 -->
        <view class="search-bar" @click="goSearch">
            <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1382671813699002368.png" />
            <text class="placeholder">搜索更多服务</text>
        </view>

        <!-- 推荐功能 -->
        <view class="section-title">推荐</view>
        <view class="grid">
            <view v-for="(item, index) in recommendList" :key="index" class="grid-item" @click="nav_to(item.path)">
                <view class="bg flex_all">
                    <image :src="item.icon" class="icon" :style="{ width: item.w + 'rpx' }" mode="widthFix" />
                </view>
                <text class="label">{{ item.name }}</text>
            </view>
        </view>

        <!-- tab 分类 -->
        <!-- <view class="tab-bar">
            <view v-for="(tab, i) in tabList" :key="tab" :class="['tab', { active: currentTab === i }]"
                @click="currentTab = i">
                {{ tab }}
            </view>
        </view> -->
        <view class="tabbar_view">
            <u-tabs style="width: 100%;" name="cate_name" :bar-style="barStyle" :list="tabList" bold
                inactive-color="rgba(0, 0, 0, .4)" :active-item-style="itemStyle" active-color="#FF82A3"
                :current="current" @change="change"></u-tabs>
        </view>

        <!-- 功能列表 -->
        <view class="grid">
            <view v-for="(item, index) in serviceMap[tabList[current].name]" :key="index" class="grid-item"
                @click="nav_to(item)">
                <view class="bg flex_all">

                    <image :src="item.icon" class="icon" :style="{ width: item.w + 'rpx' }" mode="widthFix" />
                </view>
                <text class="label">{{ item.name }}</text>
            </view>
        </view>
    </view>
</template>

<script>
import { tabList, serviceMap } from '../../../utils/serviceData.js'

export default {
    data() {
        return {
            current: 0,
            barStyle: {
                'background': '#FF82A3',
                'width': '38rpx',
                'height': '6rpx',
                'border-radius': '2rpx',
                'bottom': '10rpx',
                'z-index': '1'
            },
            itemStyle: {
                'color': '#000',
                // 'line-height': '40rpx',
                'font-weight': '600',
                'font-size': '28rpx',
                // 'min-width': '120rpx',
                'z-index': '2'
            },
            tabList,
            serviceMap,
            currentTab: 0,
            recommendList: serviceMap['常用功能'].slice(0, 4),
            link: "../../../static/serve.html",

        }
    },
    onLoad() {
        console.log(tabList);
    },
    methods: {
        change(index) {
            this.current = index
            // this.accounts = []
            // this.getsearchUserWithdrawAccountPaged(this.sendObj.symbol) // 选中币种查询地址

        },
        goSearch() {
            this.$Router.push({
                name: 'ServiceSearch'
            })
            // this.$Router.push('/pages/ServiceSearch')
        },
        goChat() {
            this.$Router.push({
                name: 'webView',
                params: {
                    url: this.link,

                }
            })
        },
        nav_to(name) {
            console.log(name);
            
            const hasSlash = name.path.includes('/');

            if (hasSlash) {
                this.$Router.push({
                    name: 'comming',
                    params: {
                        title: name.name
                    }
                });
                return;
            }
            if (name.path == 'support') {
                this.goChat()
                return
            }
            if (name.path == 'faq') {
                this.$Router.push({
                    name: 'webView',
                    params: {
                        url: "https://pinkwallet.zendesk.com/hc/zh-sg"
                    }
                })
                return
            }
            this.$Router.push({
                name: name.path
            })
        },
        goBack() {
            uni.navigateBack()
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .u-scroll-box {
    display: flex !important;
    align-items: center;
    // margin-left: -30rpx !important;
}

::v-deep .u-tab-item {
    font-size: 28rpx !important;
    // width: 98*2rrpx !important;
    padding: 0 !important;
}


.service-page {
    margin-top: 70rpx;

    .header {
        display: flex;
        align-items: center;
        font-size: 30rpx;
        font-weight: bold;
        margin-bottom: 20rpx;

        .back {
            margin-right: 20rpx;
        }

        .title {
            flex: 1;
            text-align: center;
        }
    }

    .search-bar {
        background: #F7F7F7;
        border-radius: 8rpx;
        padding: 16rpx 0 16rpx 28rpx;
        margin: 0 32rpx 64rpx 32rpx;
        display: flex;
        align-items: center;
        gap: 26rpx;

        image {
            width: 36rpx;
            height: 36rpx;
        }

        .placeholder {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 32rpx;
            color: rgba(0, 0, 0, .4);
        }
    }

    .section-title {
        font-size: 26rpx;
        font-weight: bold;
        margin: 0 20rpx 0 32rpx;
    }

    .grid {
        margin: 40rpx 60rpx 0 64rpx;
        display: flex;
        flex-wrap: wrap;
        gap: 50rpx 90rpx; // 横向和纵向间距
        justify-content: flex-start;

        .grid-item {
            max-width: 84rpx;
            // width: 25%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            // margin-bottom: 30rpx;

            .bg {
                width: 84rpx;
                height: 84rpx;
                border-radius: 24rpx;
                background: #FEFAFE;
            }

            .icon {
                // width: 80rpx;
                // height: 80rpx;
                margin-bottom: 10rpx;
            }

            .label {
                margin-top: 22rpx;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 34rpx;
                color: #000;
                text-align: center;
                white-space: nowrap;
            }
        }
    }

    .tabbar_view {
        margin-top: 90rpx;
        // width: 100%;
        // margin: 0 32rpx;
    }

    .tab-bar {
        display: flex;
        justify-content: space-between;
        margin: 30rpx 0;

        .tab {
            font-size: 24rpx;
            color: #999;

            &.active {
                color: #000;
                font-weight: 600;
                border-bottom: 4rpx solid #ff77a2;
                padding-bottom: 6rpx;
            }
        }
    }
}
</style>