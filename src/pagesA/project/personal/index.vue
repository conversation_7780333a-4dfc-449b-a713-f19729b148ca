<template>
	<view class="profile-container">
		<!-- {{ $t("title.welcome") }} :title="$t('My.title')"-->
		<u-navbar :border-bottom="false" :title='$t("My.title")' :customBack="back">
		</u-navbar>
		<view class="header">
			<view class="user-info" @click="nav_to('profile')">
				<view class="right">
					<view class="qr_div">
						<uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="84rpx"
							:options="options"></uv-qrcode>
						<!-- <RoundedQrcode :text="qrcodeUrl" :size="74" /> -->
					</view>
					<view class="shu"></view>
					<text class="username">{{ userInfo.userName || '-- ' }}</text>
				</view>
				<image class="right_img" src=https://pro-oss.pinkwallet.com/image/1369698686635433984.png />
				<!-- <u-icon name="arrow-right" color="backIconColor" size="32"></u-icon> -->
			</view>
		</view>

		<view class="divide"></view>

		<!--  -->
		<view v-if="!userInfo.tradePassSet || !userInfo.loginPassSet" class="nopwd">{{ $t("My.NoidentityTips") }}
			<text class="set" @click="nav_to('Security')">{{ $t("My.SetNow") }}</text>
		</view>


		<view class="bgwhite">

			<view class="item">
				<view class="item_title">You May Like</view>
				<view class="menu-item flex_divide">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370060789745541120.png" />
						</view>
						<view class="name">{{ $t("My.Language") }}</view>
					</view>
					<view class="menu-right">
						<view class="language" @click.stop="toggleRotateLanguage">
							<!-- <image class="global"
								src="https://pro-oss.pinkwallet.com/image/20250410/ca536e196146966e840ad354eb75a102_64x65.png" /> -->
							<text class="lang">{{ lang }}</text>
							<image class="down arrow"
								src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
							<!-- :class="{ rotated: isLangRotated }" -->
							<transition name="expand-slide">
								<view class="helpoption" v-show="showLang">
									<view v-for="(item, index) in LangList" :key="index" class="Roptions"
										@click="SetLang(item)">
										<text>{{ item.name }}</text>
									</view>
								</view>
							</transition>
						</view>
					</view>
				</view>

				<view class="menu-item flex_divide">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370064745641107456.png" />
						</view>
						<view class="name">Display Currency</view>
					</view>
					<view class="menu-right">
						<view class="language" @click.stop="toggleRotateLanguage">
							<!-- <image class="global"
								src="https://pro-oss.pinkwallet.com/image/20250410/ca536e196146966e840ad354eb75a102_64x65.png" /> -->
							<text class="lang">USD</text>
							<image class="down arrow"
								src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
							<!-- :class="{ rotated: isLangRotated }" -->
							<!-- <transition name="expand-slide">
								<view class="helpoption" v-show="showLang">
									<view v-for="(item, index) in LangList" :key="index" class="Roptions"
										@click="SetLang(item)">
										<text>{{ item.name }}</text>
									</view>
								</view>
							</transition> -->
						</view>
					</view>
				</view>

				<view class="menu-item flex_divide" @click="nav_to('Notification')">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370065326594154496.png" />
						</view>
						<view class="name">{{ $t('My.Notification') }}</view>
					</view>
					<view class="menu-right">
						<image src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
					</view>
				</view>

				<!-- #ifdef APP -->
				<view class="menu-item flex_divide">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370065782389170176.png" />
						</view>
						<view class="name">{{ $t('My.VibrationSettings') }}</view>
					</view>
					<view class="menu-right">
						<u-switch size="41" active-color="#008E28" v-model="vibrationEnabled"
							@change="toggleVibrationSwitch" />
					</view>
				</view>
				<!-- #endif -->
			</view>



			<view class="item">
				<view class="item_title">Earn</view>
				<view class="menu-item flex_divide" @click="nav_to(' ')">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370067116114927616.png" />
						</view>
						<view class="name">Invite Friends</view>
					</view>
					<view class="menu-right">
						<image src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
					</view>
				</view>
				<view class="menu-item flex_divide" @click="nav_to(' ')">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370067417718939648.png" />
						</view>
						<view class="name">Reward</view>
					</view>
					<view class="menu-right">
						<image src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
					</view>
				</view>
			</view>


			<view class="item">
				<view class="item_title">Privacy and Security</view>
				<view class="menu-item flex_divide" @click="nav_to('security_index')">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370067652272807936.png" />
						</view>
						<view class="name">Security</view>
					</view>
					<view class="menu-right">
						<image src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
					</view>
				</view>

			</view>



			<view class="item">
				<view class="item_title">Support and Help</view>
				<view class="menu-item flex_divide" @click="nav_to('Support')">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370068116053778432.png" />
						</view>
						<view class="name">{{ $t('My.Support') }}</view>
					</view>
					<view class="menu-right">
						<image src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
					</view>
				</view>
				<view class="menu-item flex_divide" @click="nav_to('aboutus')">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370068304638074880.png" />
						</view>
						<view class="name">{{ 'About us' }}</view>
					</view>
					<view class="menu-right">
						<image src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
					</view>
				</view>
				<view class="menu-item flex_divide" @click="nav_to('faq')">
					<view class="menu-left flex_y">
						<view class="bg flex_all">
							<image src="https://pro-oss.pinkwallet.com/image/1370066766674878464.png" />
						</view>
						<view class="name">{{ $t('My.FAQ') }}</view>
					</view>
					<view class="menu-right">
						<image src="https://pro-oss.pinkwallet.com/image/1370062006622183424.png" />
					</view>
				</view>
			</view>
		</view>

		<view class="btn">
			<u-button hover-class="none" class="exchange-btn " @click="logout">{{ $t("profile.Logout") }}</u-button>
		</view>

	</view>
</template>

<script>
import PasswordSetting from '@/components/popup'
import RoundedQrcode from '@/components/public/RoundedQrcode.vue'
// #ifdef APP
// const n = uni.requireNativePlugin('sn-flutter');
// #endif

export default {
	components: {
		PasswordSetting,
		RoundedQrcode
	},
	data() {
		return {
			isLangRotated: false,
			showLang: false,
			link: "../../../static/serve.html",
			showPasswordSetting: false,
			LangList: [
				{
					name: "english",
					value: 'en'
				},
				{
					name: "简体中文",
					value: 'zh'
				},
				{
					name: "繁體中文",
					value: 'zhhant'
				}
			],
			lang: "",
			showAddPopup: false,
			// qrcodeUrl: '******************************************',
			qrcodeUrl: "",
			swiperList: [
				{
					image: "https://picx.zhimg.com/70/v2-96d6cd200b2960063779a126137d5c10_1440w.avis?source=172ae18b&biz_tag=Post",
					title: "Banner 1"
				},
				{
					image: "https://www.empireaccess.com/wp-content/uploads/2023/03/Refer_a_friend_coupon_jpeg_Page_1.jpg",
					title: "Banner 2"
				}
			],
			options: {
				useDynamicSize: false,
				errorCorrectLevel: 'Q',
				// margin: 10,
				areaColor: "#fff",
				style: 'round',
				// 指定二维码前景，一般可在中间放logo
				// foregroundImageSrc: require('static/image/logo.png')
			},

			languages: ['中文', 'English'],
			selectedLanguage: '中文',
			vibrationEnabled: this.$store.state.shouldVibrate,
			faceIDEnabled: true,
			userInfo: {}
		};
	},

	onLoad() {

		// #ifdef APP-PLUS
		// console.log(n, 'N Status');
		// #endif
		uni.setNavigationBarTitle({
			title: this.$t("My.title") // 切换语言后重新设置标题
		})
		// if (!this.$check.checkLogin()) {
		// 	this.$Router.push({
		// 		name: 'login'
		// 	})
		// 	return
		// }
		// this.lang = uni.getStorageSync('__language__')
		this.lang = uni.getStorageSync('__language__') == 'zh' ? '简体中文' : uni.getStorageSync('__language__') == 'en' ? 'english' : '繁體中文'
		this.getUserInfos()
		this.showPasswordSetting = true
		console.log(this.$store.state.shouldVibrate);
	},
	computed: {
		menuList() {
			return [
				{ name: this.$t('My.Security'), url: 'security_index', img: "https://pro-oss.pinkwallet.com/image/20250402/ae08753a0224e990cd6d571a6affa9a4_164x164.png" },
				// { name: this.$t('My.Refer'), url: 'Refer', img: "https://pro-oss.pinkwallet.com/image/20250306/e6bf8ba659f02aa049ccdb31a9dd19a1_116x116.png" },
				{ name: this.$t('My.Notification'), url: 'Notification', img: "https://pro-oss.pinkwallet.com/image/20250402/30e05ee65b0ad57d20ff9038ea710e69_164x164.png" },
				{ name: this.$t('My.Support'), url: 'Support', img: "https://pro-oss.pinkwallet.com/image/20250402/0b7f08f6cc24e9d5d10213679f9852cc_164x164.png" },
				{ name: this.$t('My.Aboutus'), url: 'Aboutus', img: "https://pro-oss.pinkwallet.com/image/20250402/b107b7c2cc105a2bff95fb07d7ccd8fc_164x164.png" }
			]
		}
	},
	methods: {
		logout() {
			this.signOutHandle()
			setTimeout(() => {
				uni.hideLoading();
				uni.removeStorageSync('token');
				this.$Router.push({
					name: "login"
				})
			}, 500);
		},
		async signOutHandle() {
			uni.showLoading();
			let res = await this.$api.signOut();
			if (res.code == 200) {
			}
		},
		async getUserInfos() {
			let res = await this.$api.getUserInfo()
			console.log(res, 123);

			if (res.code == 200) {
				this.userInfo = res.result
				this.qrcodeUrl = res.result.email
			}

		},
		toggleRotateLanguage() {
			this.showLang = !this.showLang;
			this.isLangRotated = !this.isLangRotated; // 
		},
		SetLang(item) {
			this.$i18n.locale = item.value
			uni.setStorageSync("__language__", item.value)
			this.lang = item.name
			this.showLang = false
			this.setLgs(item.value)

			setTimeout(() => {
				// #ifdef H5
				location.reload(); // H5 用浏览器刷新
				// #endif

				// #ifdef APP-PLUS
				// const pages = getCurrentPages();
				// const currentPage = pages[pages.length - 1];
				// const route = currentPage.route;
				// const options = currentPage.options || {};

				// const queryString = Object.entries(options).map(([k, v]) => `${k}=${v}`).join('&');
				// const url = `/${route}${queryString ? '?' + queryString : ''}`;

				// uni.reLaunch({
				// 	url
				// });
				// #endif
			}, 100);
		},
		async setLgs(e) {
			let res = await this.$api.setLg({
				lg: e == 'en' ? "en-US" : e == "zh" ? "zh-CN" : "zh-HK",
			})
		},
		goChat() {
			this.$Router.push({
				name: 'webView',
				params: {
					url: this.link,

				}
			})
		},
		handleButtonClick() {
			console.log('按钮点击事件')
			// 你可以在这里实现按钮的点击逻辑
		},
		handleCancelClick() {
			this.showPasswordSetting = false

		},
		toggleVibrationSwitch(value) {
			// const value = event.detail.value;  // 获取开关的状态
			this.$store.commit('toggleVibration', value);  // 更新全局震动状态
			console.log(this.$store.state.shouldVibrate);

		},
		// SetLang(item) {
		// 	this.$i18n.locale = item.value
		// 	uni.setStorageSync("__language__", item.value)
		// 	this.lang = item.name

		// 	getApp().changeLanguage(this.lang);
		// 	this.showAddPopup = false
		// },
		nav_to(e) {
			console.log(123);
			if (e == 'Support') {
				this.goChat()
				return
			}
			if (e == 'faq') {
				this.$Router.push({
					name: 'webView',
					params: {
						// url: "https://pinkwallet.blog/?page_id=100"
						url: "https://pinkwallet.zendesk.com/hc/zh-sg"
					}
				})
				return
			}

			this.$Router.push({
				name: e
			})
		},
		back() {
			// uni.navigateBack();
			this.$Router.push({
				name: 'index'

			})
		},
		handleMenuClick(item) {
			if (item.url == 'Support') {
				this.goChat()
				return
			}
			this.$Router.push({
				name: item.url
			})
		},
		changeLanguage(e) {
			this.selectedLanguage = this.languages[e.detail.value];
		}
	}
};
</script>

<style scoped lang="scss">
// ::v-deep .u-switch__node {
// 	// scale: 0.9 !important;
// 	width: 40rpx !important;
// 	height: 40rpx !important;
// 	margin: 4rpx 0 0 8rpx !important;
// }

.popup-content {
	padding: 20px;
	text-align: center;

	.popup-title {
		font-size: 16px;
		font-weight: bold;
		margin-bottom: 20px;
	}

	.popup-button {
		margin: 20px 0;
		width: 100%;
		font-size: 14px;

		&:hover {
			background-color: transparent;
			/* 取消 hover 背景颜色 */
		}

		&:active {
			background-color: transparent;
			/* 取消 active 背景颜色 */
		}
	}
}

.profile-container {
	// background: radial-gradient(114.19% 35.15% at 50% 16.76%, #FFBDCE 0%, #FF8DAB 100%);
	/* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */
	min-height: 100vh;
}

.header {
	margin-top: 60rpx;
	display: flex;
	align-items: center;
	padding: 32rpx;

	.user-info {
		flex: 1;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx 30rpx 32rpx 46rpx;
		border-radius: 32rpx;
		border-width: 2rpx;
		// border: 2rpx solid #333333;
		border: 3rpx solid rgba(0, 0, 0, .1);

		.right {
			display: flex;
			align-items: center;

			.shu {
				width: 2rpx;
				height: 44rpx;
				background: rgba(51, 51, 51, .1);
				margin: 0 32rpx;
			}

			.qr_div {
				padding: 10rpx;
				background: #fff;
				border-radius: 10rpx;
			}

			.username {
				width: 360rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				font-family: Gilroy-SemiBold;
				font-weight: 400;
				font-size: 18*2rpx;
				line-height: 120%;
				color: #000;
			}
		}

		.right_img {
			width: 40rpx;
			height: 48rpx;
		}
	}


}

.divide {
	height: 16rpx;
	background: #F5F5F5;
}

.nopwd {
	margin: 22rpx auto 0 auto;
	font-family: Gilroy-Medium;
	font-weight: 400;
	font-size: 14*2rpx;
	line-height: 120%;
	letter-spacing: 0%;
	text-align: center;
	color: #D72D4A;
	width: 302*2rpx;

	.set {
		margin-left: 20rpx;
		font-family: Gilroy-SemiBold;
		font-weight: 400;
		font-size: 14*2rpx;
		line-height: 160%;
		letter-spacing: 0%;
		text-align: center;
		text-decoration: underline;
		text-decoration-style: solid;
		text-decoration-offset: 18.5%;
		text-decoration-thickness: 12.5%;
		color: #000;
	}

}


.bgwhite {
	border-top-left-radius: 60rpx;
	border-top-right-radius: 60rpx;
	// background: #fff;
	padding-top: 46rpx;

	.item {
		padding: 0 32rpx;
		margin-bottom: 81rpx;

		.item_title {
			font-family: Gilroy-Bold;
			font-weight: 400;
			font-size: 30rpx;
			line-height: 38rpx;
			color: #000;
			margin-bottom: 34rpx;
		}

		.menu-item {
			text-align: center;
			margin-bottom: 40rpx;
			max-height: 36rpx;


			.menu-left {
				.name {
					font-family: Gilroy-SemiBold;
					font-weight: 500;
					font-size: 28rpx;
					line-height: 120%;
					color: #000;
					margin-left: 20rpx;
				}

				.bg {
					// width: 120rpx;
					// height: 120rpx;
					// border-width: 2rpx;
					// background: #FFFFFF33;
					// border: 2rpx solid #0	0000033;
					border-radius: 50%;

					image {
						width: 32rpx;
						height: 32rpx;
					}
				}
			}

			.menu-right {
				.language {
					// width: 108*2rpx;
					height: 33*2rpx;
					// background: #FF82A31A;
					// border-radius: 18*2rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-family: Gilroy;
					font-weight: 500;
					font-size: 28rpx;
					// color: #000;
					color: rgba(0, 0, 0, .4);
					position: relative;

					.helpoption {
						width: 108*2rpx;

						transition: transform 0.3s ease, opacity 0.3s ease;
						transform-origin: top;
						/* 设置变换的起点为顶部 */
						z-index: 11;
						position: absolute;
						top: 92rpx;
						left: 0;
						box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

						background: #fff;
						border-radius: 16*2rpx;
						padding: 16*2rpx;
						opacity: 1;
						display: flex;
						align-items: flex-start;
						flex-direction: column;

						&.collapse {
							transform: scaleY(0) translateY(-100%);
							/* 缩小至0，并向上移动 */
							opacity: 0;
						}

						&.expand {
							transform: scaleY(1) translateY(0%);
							/* 恢复到正常大小，并位置恢复 */
							opacity: 1;

						}

						>view {

							padding: 15rpx 0;
							display: flex;
							align-items: center;

							image {
								width: 40rpx;
								height: 30rpx;
							}

							text {
								margin-left: 20rpx;
								display: block;
								font-family: Gilroy-Bold;
								font-weight: 400;
								font-size: 16*2rpx;
								line-height: 19.2*2rpx;
								color: #000;
							}
						}
					}

					// .lang {
					// 	margin: 0 12rpx;
					// }

					.global {
						width: 16*2rpx;
						height: 16*2rpx;
					}

					.down {
						width: 16*2rpx;
						height: 15*2rpx;
					}
				}

				image {
					width: 16*2rpx;
					height: 15*2rpx;
				}
			}
		}
	}

	.bg-space {
		padding: 34rpx 32rpx 44rpx 64rpx;
		// border-bottom: 2rpx solid #E0E0E0;

		.ads {
			/* margin: 0 32rpx; */
			border-top-left-radius: 60rpx;
			border-top-right-radius: 60rpx;
			height: 226rpx;
			/* height: fit-content; */
			background-image: url("https://pro-oss.pinkwallet.com/image/20250306/2b487aa2e4872866d9d29b8085061bae_1194x339.png");
			background-size: 100% 100%;

			.ad-left {
				.title {
					font-family: Gilroy-Bold;
					font-weight: 400;
					font-size: 14*2rpx;
					line-height: 120%;
					color: #000;
				}

				.title2 {
					margin: 8rpx 0;
					font-family: Gilroy-Medium;
					font-weight: 400;
					font-size: 24rpx;
					line-height: 120%;
					letter-spacing: 0%;
					color: #000;
				}

				.title3 {
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 24rpx;

					line-height: 40rpx;
					letter-spacing: 0px;
					text-decoration: underline;
					text-decoration-style: solid;
					text-decoration-offset: 25%;
					text-decoration-thickness: 13%;
					color: #3B9B62;
				}
			}

			.ad-right {
				image {
					width: 176rpx;
					height: 176rpx;
				}
			}
		}
	}


	.swiper {
		margin: 36rpx 0;
	}

	.menu {
		padding: 32rpx;

		.menu-item {
			text-align: center;
			margin-bottom: 72rpx;



			.menu-left {
				.name {
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 32rpx;
					line-height: 120%;
					color: #000;
					margin-left: 38rpx;
				}

				.bg {
					// width: 120rpx;
					// height: 120rpx;
					// border-width: 2rpx;
					// background: #FFFFFF33;
					// border: 2rpx solid #0	0000033;
					border-radius: 50%;

					image {
						width: 82rpx;
						height: 82rpx;
					}
				}
			}

			.menu-right {
				.language {
					width: 108*2rpx;
					height: 33*2rpx;
					background: #FF82A31A;
					border-radius: 18*2rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 14*2rpx;
					color: #000;
					position: relative;

					.helpoption {
						width: 108*2rpx;

						transition: transform 0.3s ease, opacity 0.3s ease;
						transform-origin: top;
						/* 设置变换的起点为顶部 */
						z-index: 11;
						position: absolute;
						top: 92rpx;
						left: 0;
						box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

						background: #fff;
						border-radius: 16*2rpx;
						padding: 16*2rpx;
						opacity: 1;
						display: flex;
						align-items: flex-start;
						flex-direction: column;

						&.collapse {
							transform: scaleY(0) translateY(-100%);
							/* 缩小至0，并向上移动 */
							opacity: 0;
						}

						&.expand {
							transform: scaleY(1) translateY(0%);
							/* 恢复到正常大小，并位置恢复 */
							opacity: 1;

						}

						>view {

							padding: 15rpx 0;
							display: flex;
							align-items: center;

							image {
								width: 40rpx;
								height: 30rpx;
							}

							text {
								margin-left: 20rpx;
								display: block;
								font-family: Gilroy-Bold;
								font-weight: 400;
								font-size: 16*2rpx;
								line-height: 19.2*2rpx;
								color: #000;
							}
						}
					}

					.lang {
						margin: 0 12rpx;
					}

					.global {
						width: 16*2rpx;
						height: 16*2rpx;
					}

					.down {
						width: 14*2rpx;
						height: 14*2rpx;
					}
				}

				image {
					width: 48rpx;
					height: 48rpx;
				}
			}
		}

		.menu-item:last-child {
			margin-bottom: 0;

		}
	}

	.settings {
		margin-top: 20rpx;

		.setting-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
		}
	}
}

.btn {
	display: flex;
	justify-content: center;
	width: 100%;
	margin: 89rpx 0 0 0;
	padding-bottom: 20rpx;

	text {
		font-family: Gilroy-SemiBold;
		font-weight: 400;
		font-size: 14*2rpx;
		line-height: 22.4*2rpx;
		letter-spacing: 0%;
		color: #000;
	}

	.exchange-btn {
		width: 340*2rpx;
		// margin: 0 32rpx;
		height: 100rpx;
		// background: #FF82A3;
		border: 1.5px solid rgba(235, 54, 54, 1);
		border-radius: 64*2rpx;
		font-family: Gilroy-Bold;
		font-weight: 400;
		font-size: 32rpx;
		color: #EB3636;
	}

	.exchange-btn[disabled] {
		// background: #FF82A380; // 加透明度效果
		background: #D9D6D6;
		color: #666666;
	}
}
</style>