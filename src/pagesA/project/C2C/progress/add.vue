<template>
    <view class="wechat-info-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false"
            :title="type == 'wechat' ? '添加微信' : type == 'ali' ? '添加支付宝' : '添加银行卡'">
            <!-- class="search-box" @click="nav_to('Record', 'withdraw')" -->
            <view slot="right">
                <image @click="delete_info" src="https://pro-oss.pinkwallet.com/image/1372223939513245696.png"
                    class="delete-icon" />
            </view>
        </u-navbar>
        <view style="height: 48rpx;"></view>

        <view v-if="type == 'wechat'">
            <view class="form-group">
                <view class="label">姓名</view>
                <view class="bg">
                    <u-input height="80" v-model="form.name" placeholder="请输入姓名" class="form-input" />
                </view>
            </view>

            <view class="form-group">
                <view class="label">微信账号</view>
                <view class="bg">

                    <u-input height="80" v-model="form.wechat" placeholder="请输入微信账号" class="form-input" />
                </view>
            </view>

            <view class="form-group">
                <view class="label">二维码</view>
                <view class="upload-box" @click="chooseImage">
                    <image v-if="form.qrCode" :src="form.qrCode" mode="aspectFill" class="qr-image" />
                    <text v-else class="add-icon">+</text>
                </view>
            </view>
        </view>

        <view v-if="type == 'ali'">
            <view class="form-group">
                <view class="label">姓名</view>
                <view class="bg">
                    <u-input height="80" v-model="form.name" placeholder="请输入姓名" class="form-input" />
                </view>
            </view>

            <view class="form-group">
                <view class="label">支付宝账号</view>
                <view class="bg">

                    <u-input height="80" v-model="form.wechat" placeholder="请输入支付宝账号" class="form-input" />
                </view>
            </view>

            <view class="form-group">
                <view class="label">二维码</view>
                <view class="upload-box" @click="chooseImage">
                    <image v-if="form.qrCode" :src="form.qrCode" mode="aspectFill" class="qr-image" />
                    <text v-else class="add-icon">+</text>
                </view>
            </view>
        </view>

        <view class="bank-info-page" v-if="type == 'card' || type == 'bank'">
            <view class="auth-warning" v-if="info.realNameStatus != 1">未实名认证</view>

            <view class="form-group">
                <view class="label">姓名</view>
                <view class="bg">

                    <u-input height="80" v-model="form.name" placeholder="请输入姓名" class="form-input" />
                </view>
            </view>

            <view class="form-group">
                <view class="label">银行卡账号</view>
                <view class="bg">

                    <u-input height="80" v-model="form.wechat" placeholder="请输入银行卡号" class="form-input" />
                </view>
            </view>

            <view class="form-group">
                <view class="label">银行名称</view>
                <view class="bg">

                    <u-input height="80" v-model="form.bankName" placeholder="请输入银行名称" class="form-input" />
                </view>
            </view>

            <view class="form-group">
                <view class="label">开户支行(选填)</view>
                <view class="bg">

                    <u-input height="80" v-model="form.branchName" placeholder="请输入支行名称" class="form-input" />
                </view>
            </view>

            <view class="tips-title">特别提醒</view>
            <view class="tips-content">
                请确保您添加的银行卡号以进行即时付款。请勿包含其他银行或付款方式的详细信息。您必须添加所选银行的付款/收款信息。
            </view>
        </view>


        <u-button hover-class="none" class="save-btn" @click="save">保存</u-button>
    </view>
</template>

<script>
export default {
    data() {
        return {
            info: {},
            type: 'wechat',
            form: {
                name: '',
                wechat: '',
                qrCode: '',
                bankName: '',
                branchName: ''
            },
            form2: {
                name: '',
                cardNumber: '',
                bankName: '',
                branchName: ''
            }
        }
    },
    onLoad(option) {
        this.getC2cuserInfo()
        this.type = option.type
        if (option.account) {
            let account = JSON.parse(decodeURIComponent(option.account));
            console.log(account);

            if (account) {
                if (option.type == 'wechat') {
                    this.form.name = account.usernameWx
                    this.form.wechat = account.accountWx
                    this.form.qrCode = account.codeWx
                } else if (option.type == 'ali') {
                    this.form.name = account.usernameZfb
                    this.form.wechat = account.accountZfb
                    this.form.qrCode = account.codeZfb
                } else if (option.type == 'card' || option.type == 'bank') {
                    this.form.name = account.usernameBank
                    this.form.wechat = account.bankCode
                    this.form.bankName = account.bankName
                    this.form.branchName = account?.bankAddress
                }
            }
        }
    },
    methods: {
        async getC2cuserInfo() {
            let res = await this.$api.c2cuserInfo({});
            if (res.code == 200) {
                this.info = res.result
            }
        },
        async delete_info() {
            let res = await this.$api.c2cPayRemove({
                payType: this.type == 'wechat' ? 1 : this.type == 'ali' || this.type == 'alipay' ? 2 : 3,
            })
            if (res.code == 200) {
                this.$u.toast('删除成功')
                setTimeout(() => {
                    this.$Router.back()
                }, 300);
            } else {
                this.$u.toast(res.msg)
            }
        },
        chooseImage() {
            uni.chooseImage({
                count: 1,
                success: (res) => {
                    this.uploadPic(res.tempFilePaths[0])
                }
            })
        },
        uploadPic(e) { //上传
            // #ifdef APP
            let url = `${getApp().globalData.apiUrl}pinkwallet/appApi/oss/uploadImage`
            // #endif
            // #ifdef H5
            let url = process.env.VUE_APP_JAVA_UPLOADIMAGE
            // #endif
            console.log("url", url)
            uni.uploadFile({
                url,
                filePath: e,
                header: {
                    'Authorization': uni.getStorageSync("token"),
                },
                name: 'file',
                complete: (res) => {
                    console.log(res);
                    if (res.data) {
                        let resultData = JSON.parse(res.data)
                        if (resultData.code == 200) {
                            this.form.qrCode = resultData.result
                            uni.showToast({
                                title: '上传成功',
                                icon: 'none'
                            });
                        }
                    }

                }
            });
        },
        async save() {
            if (this.type != 'card') {
                if (!this.form.qrCode || !this.form.name || !this.form.wechat) {
                    uni.showToast({ title: '请填写完整信息', icon: 'none' })
                    return
                }
            }


            if (this.type == 'card') {
                if (!this.form.branchName || !this.form.name || !this.form.wechat || !this.form.bankName) {
                    uni.showToast({ title: '请填写完整信息', icon: 'none' })
                    return
                }
            }
            // uni.showToast({ title: '保存成功', icon: 'none' })
            const payType = this.type == 'wechat' ? 1 : this.type == 'ali' ? 2 : 3;

            let baseParams = {
                payType,
                name: this.form.name,
                accountCode: this.form.wechat
            };

            let extraParams = {};
            // "https://pro-oss.pinkwallet.com/image/1374133579956183040.png"
            if (payType == 1 || payType == 2) {
                extraParams.ewUrl = this.form.qrCode || ""; // 二维码图片链接
            } else if (payType == 3) {
                extraParams.bankName = this.form.bankName || '';
                extraParams.subBranchName = this.form.branchName || '';
            }

            const payload = {
                ...baseParams,
                ...extraParams
            };
            this.$api.c2cAddUpdate(payload).then(res => {
                if (res.code == 200) {
                    uni.showToast({ title: '保存成功', icon: 'none' })
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 300);
                } else {
                    uni.showToast({ title: res.msg, icon: 'none' })
                }

                // setTimeout(() => {
                //     uni.navigateBack()
                // })
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.wechat-info-page {
    padding: 32rpx;

    .bank-info-page {

        .auth-warning {
            background-color: #FFF2F5;
            color: #FF82A3;
            border-radius: 12rpx;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 28rpx;
            padding: 28rpx 16rpx;
            margin-bottom: 32rpx;
        }

        .form-group {
            margin-bottom: 36rpx;

            .label {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 32rpx;
                margin-bottom: 20rpx;
                color: #000;
            }

            .form-input {
                background-color: #f8f8f8;
                border-radius: 12rpx;
                padding: 20rpx;
                font-size: 28rpx;
            }
        }

        .tips-title {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            line-height: 32rpx;
            margin-top: 40rpx;
            margin-bottom: 16rpx;
        }

        .tips-content {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 32rpx;
            color: rgba(0, 0, 0, .5);
        }
    }

    .delete-icon {
        width: 56rpx;
        height: 60rpx;
        margin-right: 32rpx;
    }

    .form-group {
        margin-bottom: 44rpx;
        // padding-top: 48rpx;

        .label {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 32rpx;
            margin-bottom: 20rpx;
            color: #000;
        }

        .bg {
            padding: 0 28rpx;
            height: 80rpx;
            border-radius: 8rpx;

            background-color: #f8f8f8;
        }

        .form-input {
            font-size: 28rpx;
            font-family: PingFang SC;
            font-weight: 400;
            color: #000;

        }

        .upload-box {
            width: 166rpx;
            height: 166rpx;
            background-color: #f2f2f2;
            border-radius: 8rpx;
            justify-content: center;
            align-items: center;
            display: flex;

            .add-icon {
                font-size: 40rpx;
                color: #000;
            }

            .qr-image {
                width: 100%;
                height: 100%;
                border-radius: 12rpx;
            }
        }
    }

    .save-btn {
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        background-color: #FF82A3;
        color: #fff;
        text-align: center;
        padding: 24rpx 0;
        border-radius: 50rpx;
        font-size: 28rpx;

        font-family: PingFang SC;
        font-weight: 500;

    }
}
</style>