<template>
    <view class="order-detail">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="">
        </u-navbar>
        <!-- 订单头部 卖家角度-->
        <view class="order-header" v-if="items.userBuyOrSellStatus == 1">
            <view class="title">{{ step == 1 ? `等待${items.userBuyOrSellStatus != 1 ? '卖家' : '买家'}确认付款` : step == 2 ?
                `${items.userBuyOrSellStatus != 1 ? '卖家' : '买家'}已付款` : step == 3 ? '交易取消' : '交易已完成' }}</view>
            <view class="countdown" v-if="step == 1">预计<text class="time">{{ countdownText }}</text>内收到{{ items.coin }}
            </view>
            <view class="countdown" v-if="step == 2">{{ `${items.userBuyOrSellStatus != 1 ? '卖家' : '买家'}已标价为转账成功，请确认收款`
            }}
            </view>
            <view class="countdown" v-if="step == 3">您已主动取消订单</view>
            <view class="countdown" v-if="step == 4">资金已划转至您的账户</view>
        </view>
        <!-- 买家角度 
        this.step = this.items.status == 1 ? 3 : this.items.status == 0 ? 1 : this.items.status == 2 ? 2 : 4;
        -->
        <view class="order-header" v-if="items.userBuyOrSellStatus == 0">
            <view class="title">{{ step == 1 ? `请使用微信支付转账` : step == 2 ?
                `等待${items.userBuyOrSellStatus != 1 ? '卖家' : '买家'}确认收款` : step == 3 ? '交易取消' : '交易已完成' }}</view>
            <view class="countdown" v-if="step == 1">付款剩余时间: <text class="time">{{ countdownText }}</text></view>
            <view class="countdown" v-if="step == 2">{{ `${items.userBuyOrSellStatus == 1 ? '卖家' : '买家'}已标价为转账成功`
            }}
            </view>
            <view class="countdown" v-if="step == 3">您已主动取消订单</view>
            <view class="countdown" v-if="step == 4">资金已划转至您的账户</view>
        </view>

        <!-- 商家信息 -->
        <view class="merchant" v-if="step != 4">
            <view class="avatar"></view>
            <view class="info" v-if="step == 1">
                <view class="nickname">{{ items.userBuyOrSellStatus == 1 ? items.buyerName : items.sellerName }}</view>
                <view class="desc">[C2C订单消息]{{ items.payType == 1 ? '微信支付' : items.payType == 2 ? '支付宝支付' : '银行卡支付' }}
                </view>
            </view>
            <view class="info" v-if="step == 2">
                <view class="nickname">{{ items.userBuyOrSellStatus == 1 ? items.buyerName : items.sellerName }}</view>
                <view class="desc">必须使用本人实名制账号转账</view>
            </view>
            <view class="info" v-if="step == 3">
                <view class="nickname">取消原因</view>
                <view class="desc">{{ items.cancelMessage }}</view>
            </view>
        </view>

        <!-- 1等待买家确认收款 -->
        <view class="order-box" v-if="step == 1 && items.userBuyOrSellStatus == 1">
            <view class="top">
                <view class="usdt-icon">
                    <image class="icon" :src="items.coinUrl" />
                </view>
                <view class="label">{{ items.userBuyOrSellStatus == 1 ? '出售' : "买入" }}{{ items.coin }}</view>
            </view>

            <view class="item">
                <view class="label">单价</view>
                <view class="value">
                    {{ items.coinPrice }}
                </view>
            </view>

            <view class="item">
                <view class="label">数量</view>
                <view class="value">{{ items.coinCount }}{{ items.coin }}</view>
            </view>

            <view class="item">
                <view class="label">总金额</view>
                <view class="value">
                    {{ items.fiatAmount }}
                    <text class="copy-icon">
                        <image @click="copy(items.fiatAmount)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </text>
                </view>
            </view>

            <view class="item">
                <view class="label">商家昵称</view>
                <view class="value">{{ items.sellerName }}</view>
            </view>
        </view>

        <view class="order-box2" v-if="step == 1 && items.userBuyOrSellStatus == 1">
            <view class="nav">订单信息</view>
            <view class="item">
                <view class="label">订单编号</view>
                <view class="value">
                    {{ items.orderNo }}
                    <text class="copy-icon">
                        <image @click="copy(items.orderNo)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </text>
                </view>
            </view>

            <view class="item">
                <view class="label">下单时间</view>
                <view class="value">{{ formatTimestamp(items.createTime) }}</view>
            </view>


            <view class="item">
                <view class="label">微信支付账号</view>
                <view class="value">
                    {{ items.receiveCode }}
                    <text class="copy-icon">
                        <image @click="copy(items.receiveCode)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />

                    </text>
                </view>
            </view>
        </view>

        <view class="wechat-pay-container" v-if="step == 1 && items.userBuyOrSellStatus == 0">

            <view class="leftline">

            </view>

            <view>
                <view class="step-line">

                    <view class="dot"></view>
                    <view class="text">请打开您的微信支付</view>
                </view>

                <view class="step-line">
                    <view class="dot"></view>
                    <view class="text">向卖家转账{{ items.fiatAmount }}</view>
                </view>

                <view class="card-box">
                    <view class="row">
                        <text class="label">姓名</text>

                        <view class="right">
                            <text class="value">{{ items.sellerName }}</text>
                            <text class="copy">
                                <image src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                            </text>
                        </view>

                    </view>
                    <view class="row">
                        <text class="label">微信</text>
                        <view class="right">
                            <text class="value">{{ items.receiveCode }}</text>
                            <text class="copy">
                                <image src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                            </text>
                        </view>

                    </view>
                    <view class="row">
                        <text class="label">二维码</text>
                        <text class="value code-link" @click="showCode">查看二维码</text>
                    </view>
                </view>

                <view class="tip">

                    <view class="label">
                        <view class="dot"></view>

                        温馨提示
                    </view>
                    <text class="desc">
                        请确保付款人信息与平台实名一致，若不一致卖家有权不放币。请不要使用支付通道到账时间较长的付款方式。
                    </text>
                </view>

                <view class="step-line">
                    <view class="dot"></view>
                    <view class="text">转账完成后，点击按钮</view>
                </view>
            </view>



        </view>
        <view class="btn" v-if="step == 1 && items.userBuyOrSellStatus == 0">
            <u-button hover-class="none" class="exchange-btn " @click="confirmTransfer">确认转账</u-button>
        </view>
        <!-- 2买家已付款 -->
        <view class="order-box" v-if="step == 2">
            <view class="top">
                <view class="usdt-icon">
                    <image class="icon" :src="items.coinUrl" />
                </view>
                <view class="label">{{ items.userBuyOrSellStatus == 1 ? '出售' : "买入" }}{{ items.coin }}</view>
            </view>

            <view class="item">
                <view class="label">单价</view>
                <view class="value">
                    {{ items.coinPrice }}
                </view>
            </view>



            <view class="item">
                <view class="label">数量</view>
                <view class="value"> {{ items.coinCount }} {{ items.coin }}
                </view>
            </view>

            <view class="item">
                <view class="label">总金额</view>
                <view class="value">{{ items.fiatAmount }}
                    <text class="copy-icon">
                        <image @click="copy(items.fiatAmount)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </text>
                </view>
            </view>

            <view class="item">
                <view class="label">收款方式</view>
                <view class="value">{{ items.payType == 1 ? '微信支付' : items.payType == 2 ? '支付宝支付' : '银行卡支付' }}</view>
            </view>
            <view class="item">
                <view class="label">收款账号</view>
                <view class="value">{{ items.sellerReceiveCode }}</view>
            </view>
        </view>
        <view class="order-box2" v-if="step == 2">
            <view class="item">
                <view class="label">买家昵称</view>
                <view class="value">
                    {{ items.buyerName }}
                    <!-- <text class="copy-icon">
                        <image src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </text> -->
                </view>
            </view>

            <view class="item">
                <view class="label">订单编号</view>
                <view class="value"> {{ items.orderNo }}
                    <view class="copy-icon">
                        <image @click="copy(items.orderNo)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </view>
                </view>
            </view>

            <view class="item">
                <view class="label">下单时间</view>
                <view class="value">{{ formatTimestamp(items.createTime) }}</view>
            </view>

            <!-- <view class="item">
                <view class="label">卖家收款方式</view>
                <view class="value">微信支付</view>
            </view> -->

            <view class="alert-box">
                买家转账时备注或您收到非实名汇款，请联系客服将款项退回
            </view>

            <view class="btn" v-if="items.userBuyOrSellStatus == 1">
                <u-button hover-class="none" class="exchange-btn " @click="confirm">确认收款</u-button>
            </view>
        </view>


        <!-- 3交易取消 -->
        <view class="order-box" v-if="step == 3">
            <view class="top">
                <view class="usdt-icon">
                    <image class="icon" :src="items.coinUrl" />

                </view>
                <view class="label">{{ items.userBuyOrSellStatus == 1 ? '出售' : "买入" }}{{ items.coin }}</view>
            </view>

            <view class="item">
                <view class="label">单价</view>
                <view class="value">
                    {{ items.coinPrice }}
                    <!-- <text class="copy-icon">
                        <image src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </text> -->
                </view>
            </view>

            <view class="item">
                <view class="label">数量</view>
                <view class="value"> {{ items.coinCount }} {{ items.coin }}
                </view>
            </view>

            <view class="item">
                <view class="label">总金额</view>
                <view class="value">{{ items.fiatAmount }}
                    <text class="copy-icon">
                        <image @click="copy(items.fiatAmount)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </text>
                </view>
            </view>

            <view class="item">
                <view class="label">商家昵称</view>
                <view class="value">
                    {{ items.sellerName }}
                </view>
            </view>

        </view>
        <view class="order-box2" v-if="step == 3">
            <view class="nav">订单信息</view>
            <view class="item">
                <view class="label">订单编号</view>
                <view class="value">
                    {{ items.orderNo }}
                    <view class="copy-icon">
                        <image @click="copy(items.orderNo)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </view>
                </view>
            </view>

            <view class="item">
                <view class="label">下单时间</view>
                <view class="value">{{ formatTimestamp(items.createTime) }}</view>
            </view>


            <view class="item">
                <view class="label">微信支付账号</view>
                <view class="value">
                    {{ items.receiveCode }}
                    <view class="copy-icon">
                        <image @click="copy(items.receiveCode)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </view>
                </view>
            </view>

            <!-- <view class="item">
                <view class="label">卖家收款方式</view>
                <view class="value">微信支付</view>
            </view> -->
        </view>


        <!-- 4交易已完成 -->
        <view class="order-box" style="border-top: 1rpx solid rgba(0, 0, 0, .1);padding-top: 38rpx;" v-if="step == 4">
            <view class="top">
                <view class="usdt-icon">
                    <image class="icon" :src="items.coinUrl" />

                </view>
                <view class="label">{{ items.userBuyOrSellStatus == 1 ? '出售' : "买入" }}{{ items.coin }}</view>
            </view>

            <view class="item">
                <view class="label">单价</view>
                <view class="value">
                    {{ items.coinPrice }}
                    <!-- <text class="copy-icon">
                        <image src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </text> -->
                </view>
            </view>

            <view class="item">
                <view class="label">数量</view>
                <view class="value"> {{ items.coinCount }} {{ items.coin }}
                </view>
            </view>
            <view class="item">
                <view class="label">总金额</view>
                <view class="value">{{ items.fiatAmount }}
                    <text class="copy-icon">
                        <image src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </text>
                </view>
            </view>

            <view class="item">
                <view class="label">商家昵称</view>
                <view class="value">
                    {{ items.sellerName }}
                </view>
            </view>

        </view>
        <view class="order-box2" v-if="step == 4">
            <view class="nav">订单信息</view>
            <view class="item">
                <view class="label">订单编号</view>
                <view class="value">
                    {{ items.orderNo }}
                    <view class="copy-icon">
                        <image @click="copy(items.orderNo)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </view>
                </view>
            </view>

            <view class="item">
                <view class="label">下单时间</view>
                <view class="value">{{ formatTimestamp(items.createTime) }}</view>
            </view>


            <view class="item">
                <view class="label">微信支付账号</view>
                <view class="value">
                    {{ items.receiveCode }}
                    <view class="copy-icon">
                        <image @click="copy(items.receiveCode)"
                            src="https://pro-oss.pinkwallet.com/image/1371897827155337216.png" />
                    </view>
                </view>
            </view>

            <!-- <view class="item">
                <view class="label">卖家收款方式</view>
                <view class="value">微信支付</view>
            </view> -->
        </view>
        <!-- 底部按钮 -->
        <!-- <view class="footer" v-if="step < 3">
            <u-button hover-class="none" class="cancel-btn" @click="cancelOrder">取消订单</u-button>
            <u-button hover-class="none" class="next-btn" @click="nextStep">下一步</u-button>
        </view> -->

        <!-- 放大二维码 -->
        <u-popup v-model="qrcodeShow" mode="center" :mask="true" :close-on-click-mask="true">
            <div class="qr-modal">
                <div class="qr-container">
                    <div class="header">
                        <span>二维码</span>
                        <image class="close-btn" @click="qrcodeShow = false"
                            src="https://pro-oss.pinkwallet.com/image/1371931948699181056.png" />
                    </div>
                    <div class="qr-box">
                        <uv-qrcode ref="qrcodes" canvas-id="qrcodes" :value="qrcodeUrl" size="350rpx"
                            :options="options"></uv-qrcode>
                    </div>
                </div>
            </div>
        </u-popup>

    </view>
</template>

<script>
export default {
    name: "C2COrderDetail",
    data() {
        return {
            countdownText: '', // 显示文本
            countdownTimer: null,
            qrcodeUrl: "1111",
            kyclink: "",
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            qrcodeShow: false,
            step: 3,

        };
    },
    onLoad(options) {
        this.items = JSON.parse(decodeURIComponent(options.item));
        console.log(this.items);
        // this.step = 2
        this.step = this.items.status == 1 ? 3 : this.items.status == 0 ? 1 : this.items.status == 2 ? 2 : 4;
        this.startCountdown()
    },
    onHide() {
        clearInterval(this.countdownTimer)
    },
    methods: {
        async confirmTransfer() {
            let res = await this.$api.c2corderalreadyPaid({
                orderNo: this.items.orderNo
            })
            if (res.code == 200) {
                uni.showToast({
                    title: 'success',
                    icon: "none",
                    duration: 2000
                });
                setTimeout(() => {
                    this.$Router.back()
                }, 300);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: "none",
                    duration: 2000
                });
            }
        },
        async confirm() {
            let res = await this.$api.c2cOrderConfirmReceipt({
                orderNo: this.items.orderNo
            });
            if (res.code == 200) {
                uni.showToast({
                    title: 'success',
                    icon: "none",
                    duration: 2000
                });
                setTimeout(() => {
                    this.$Router.back();
                }, 300);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none',
                });
            }
        },
        copy(text) { //复制
            console.log(text);
            let that = this
            if (!text) {
                uni.showToast({
                    title: 'fail',
                    icon: 'none'
                })
                return
            }
            uni.setClipboardData({
                data: text.toString(),
                success() {
                    uni.showToast({
                        title: that.$t("title.copy"),
                        icon: 'none'
                    })
                }
            })
        },
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000);
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        formatCountdown(timestamp) {
            const now = Math.floor(Date.now() / 1000) // 当前时间戳（秒）
            let diff = timestamp - now
            if (diff <= 0) return '0:00'

            const minutes = Math.floor(diff / 60)
            const seconds = diff % 60
            return `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`
        },
        cancelOrder() {
            this.$Router.push({
                name: "progressCancel"
            })
            // this.$u.toast("取消订单");
        },
        nextStep() {
            this.step++;
        },
        showCode() {
            this.qrcodeShow = true;
        },
        startCountdown() {
            this.updateCountdown()
            this.countdownTimer = setInterval(() => {
                this.updateCountdown()
            }, 1000)
        },
        updateCountdown() {
            const now = Math.floor(Date.now() / 1000)
            let diff = this.items.autoCancelTimeAt - now
            if (diff <= 0) {
                this.countdownText = '0:00'
                clearInterval(this.countdownTimer)
                return
            }
            const minutes = Math.floor(diff / 60)
            const seconds = diff % 60
            this.countdownText = `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`
        }
    },
};
</script>

<style lang="scss" scoped>
.order-detail {
    padding: 32rpx;
    width: 100%;

    .qr-modal {
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 999;
        width: 544rpx;

        .qr-container {
            background: #fff;
            width: 100%;
            border-radius: 40rpx;
            padding: 46rpx 32rpx 102rpx 32rpx;
            box-shadow: 7px 10px 100.3px 0px #0000001A;
            position: relative;
            text-align: center;

            .header {
                margin-bottom: 40rpx;
                position: relative;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .close-btn {
                    // position: absolute;
                    // top: 0;
                    // right: 0;
                    width: 48rpx;
                    height: 48rpx;
                }
            }

            .qr-box {
                // border: 2rpx solid #FFCDDA;
                // border-radius: 32rpx;
                // padding: 42rpx 0;
                display: flex;
                justify-content: center;
            }
        }
    }


    .wechat-pay-container {
        margin-top: 80rpx;
        display: flex;
        align-items: center;
        height: 550rpx;

        .leftline {
            width: 1rpx;
            background: #FF82A3;
            height: 100%;
            margin-top: -38rpx;
        }

        .step-line {
            position: relative;
            padding-left: 18rpx;
            margin-bottom: 48rpx;
            display: flex;
            align-items: center;

            .dot {
                position: absolute;
                left: -5.5rpx;
                // margin-right: 18rpx;
                width: 12rpx;
                height: 12rpx;
                border-radius: 50%;
                background: rgba(255, 130, 163, 1);
            }

            .text {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
            }
        }

        .card-box {
            border: 2rpx solid #F2F2F2;
            border-radius: 24rpx;
            padding: 34rpx;
            margin: 8rpx 0 48rpx 18rpx;

            .row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 24rpx;

                .right {
                    display: flex;
                    align-items: center;

                    .value {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        line-height: 32rpx;
                        color: #000;
                        word-break: break-all;
                        margin-right: 4rpx;
                    }

                    .copy {
                        color: #999;
                        // margin-left: 12rpx;
                        width: 34rpx;
                        height: 34rpx;

                        image {
                            width: 34rpx;
                            height: 34rpx;
                        }
                    }

                }

                .label {
                    flex: 0 0 100rpx;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 32rpx;
                    color: rgba(0, 0, 0, .5);
                }


                .code-link {
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 24rpx;
                    line-height: 32rpx;
                    color: #FF82A3;
                }
            }

            .row:last-child {
                margin-bottom: 0;
            }
        }

        .tip {
            padding-left: 18rpx;
            margin-bottom: 32rpx;
            position: relative;

            .dot {
                position: absolute;
                left: -5rpx;
                // margin-right: 18rpx;
                width: 12rpx;
                height: 12rpx;
                border-radius: 50%;
                background: rgba(255, 130, 163, 1);
            }


            .label {
                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
            }

            .desc {
                display: block;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: rgba(0, 0, 0, .5);
                margin-top: 16rpx;

            }
        }
    }

    .order-header {
        margin-top: 32rpx;

        .title {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            line-height: 40rpx;
            color: #000;
        }

        .countdown {
            margin-top: 8rpx;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 34rpx;
            color: #808080;


            .time {
                color: #FF82A3;
            }
        }
    }

    .merchant {
        margin-top: 32rpx;
        display: flex;
        align-items: center;
        border-top: 1rpx solid rgba(0, 0, 0, .1);
        border-bottom: 1rpx solid rgba(0, 0, 0, .1);
        padding: 24rpx 0;

        .avatar {
            width: 84rpx;
            height: 84rpx;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.05);
        }

        .info {
            margin-left: 28rpx;

            .nickname {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 34rpx;
                color: #000;
            }

            .desc {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 34rpx;
                color: rgba(0, 0, 0, .5);
            }
        }
    }

    .order-box {

        margin-top: 32rpx;
        background: #fff;
        // border-radius: 16rpx;
        // padding: 24rpx;
        border-bottom: 1rpx solid rgba(0, 0, 0, .1);
        padding-bottom: 40rpx;

        .top {
            display: flex;
            align-items: center;
            margin-bottom: 20rpx;

            .usdt-icon {
                margin-right: 16rpx;
                height: 56rpx;
                width: 56rpx;

                .icon {
                    height: 56rpx;
                    width: 56rpx;
                    border-radius: 50%;
                }
            }

            .label {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
            }
        }

        .item {
            display: flex;
            justify-content: space-between;
            padding: 12rpx 0;

            .label {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: rgba(0, 0, 0, .5);
            }

            .value {

                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;



                .copy-icon {

                    image {
                        width: 34rpx;
                        height: 34rpx;
                    }


                    margin-left: 8rpx;
                }
            }

            &:last-child {
                border-bottom: none;
            }
        }
    }

    .order-box2 {
        margin-top: 32rpx;
        background: #fff;
        border-radius: 16rpx;
        // padding: 24rpx;
        padding-bottom: 40rpx;


        .btn {
            position: fixed;
            bottom: 60rpx;
            // width: 90%;
            width: 340*2rpx;

            text {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                letter-spacing: 0%;
                color: #000;
            }

            .exchange-btn {
                // margin: 0 32rpx;
                height: 88rpx;
                background: #FF82A3;
                border-radius: 64*2rpx;
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 32rpx;
                color: #fff;
            }

            .exchange-btn[disabled] {
                // background: #FF82A380; // 加透明度效果
                background: #D9D6D6;
                color: #666666;
            }
        }


        .alert-box {
            margin-top: 28rpx;
            // background-color: #FFF2F5;
            background: rgba(255, 130, 163, .2);
            color: #FF82A3;
            padding: 20rpx 16rpx;
            border-radius: 12rpx;

            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 32rpx;

        }

        .nav {
            margin-bottom: 20rpx;
            // 订单信息
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            line-height: 32rpx;
            color: #000;
        }

        .top {
            display: flex;
            align-items: center;
            margin-bottom: 20rpx;

            .usdt-icon {
                margin-right: 16rpx;
                height: 56rpx;
                width: 56rpx;

                .icon {
                    height: 56rpx;
                    width: 56rpx;
                    border-radius: 50%;
                }
            }

            .label {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;
            }
        }

        .item {
            display: flex;
            justify-content: space-between;
            padding: 12rpx 0;

            .label {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: rgba(0, 0, 0, .5);
            }

            .value {

                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: #000;



                .copy-icon {

                    image {
                        width: 34rpx;
                        height: 34rpx;
                    }


                    margin-left: 8rpx;
                }
            }

            &:last-child {
                border-bottom: none;
            }
        }
    }

    .tips {
        margin-top: 40rpx;
        display: flex;
        flex-direction: column;

        text {
            &:nth-of-type(1) {
                color: #000;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 32rpx;
            }

            &:nth-of-type(2) {
                margin-top: 32rpx;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 32rpx;
                color: rgba(0, 0, 0, .5);
            }

        }
    }

    .footer {
        width: 100%;
        position: fixed;
        bottom: 16rpx;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16rpx;
        font-family: PingFang HK;
        font-weight: 500;
        font-size: 28rpx;

        .cancel-btn {
            margin: 0;
            width: 222rpx;
            background: #FFF2F5;
            height: 88rpx;
            line-height: 88rpx;
            color: #FF82A3;
            border-radius: 112rpx;
        }

        .next-btn {
            width: 452rpx;
            margin: 0;
            height: 88rpx;
            line-height: 88rpx;
            background-color: #FF82A3;
            color: #fff;
            border-radius: 112rpx;

        }
    }

    .btn {
        position: fixed;
        bottom: 60rpx;
        // width: 90%;
        width: 340*2rpx;

        text {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            letter-spacing: 0%;
            color: #000;
        }

        .exchange-btn {
            // margin: 0 32rpx;
            height: 88rpx;
            background: #FF82A3;
            border-radius: 64*2rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 32rpx;
            color: #fff;
        }

        .exchange-btn[disabled] {
            // background: #FF82A380; // 加透明度效果
            background: #D9D6D6;
            color: #666666;
        }
    }
}
</style>