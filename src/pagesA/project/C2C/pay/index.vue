<template>
    <view class="payment-methods-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="支付方式">
        </u-navbar>
        <view style="height: 24rpx;"></view>
        <!-- v-for="(method, index) in method" :key="index" -->
        <!-- 微信支付 -->
        <view class="method-item" @click="editMethod(1)" v-if="methodAll.accountWx">
            <view class="method-header">
                <view class="type-dot" :style="{ backgroundColor: '#49A0B1' }" />
                <text class="type-name">{{ 'WeChat' }}</text>
                <!-- <u-icon name="edit-pen" size="32" color="#000" class="edit-icon" /> -->
                <image src="https://pro-oss.pinkwallet.com/image/1372264950453067776.png" class="edit-icon" />
            </view>

            <view class="method-info">
                <view>
                    <view class="info-text">{{ methodAll.usernameWx }}</view>
                    <view class="info-text">{{ methodAll.accountWx }}</view>
                </view>
                <image class="qr-image" :src="methodAll.codeWx" mode="aspectFill" />
            </view>
        </view>

        <!-- 支付宝支付 -->
        <view class="method-item" @click="editMethod(2)" v-if="methodAll.accountZfb">

            <view class="method-header">
                <view class="type-dot" :style="{ backgroundColor: '#627EEA' }" />
                <text class="type-name">{{ 'AliPay' }}</text>
                <!-- <u-icon name="edit-pen" size="32" color="#000" class="edit-icon" /> -->
                <image src="https://pro-oss.pinkwallet.com/image/1372264950453067776.png" class="edit-icon" />
            </view>

            <view class="method-info">
                <view>
                    <view class="info-text">{{ methodAll.usernameZfb }}</view>
                    <view class="info-text">{{ methodAll.accountZfb }}</view>
                </view>
                <image class="qr-image" :src="methodAll.codeZfb" mode="aspectFill" />
            </view>
        </view>
        <!-- 银行卡支付 -->
        <view class="method-item" @click="editMethod(3)" v-if="methodAll.bankAddress">
            <view class="method-header">
                <view class="type-dot" :style="{ backgroundColor: '#F7931A' }" />
                <text class="type-name">{{ 'Card' }}</text>
                <!-- <u-icon name="edit-pen" size="32" color="#000" class="edit-icon" /> -->
                <image src="https://pro-oss.pinkwallet.com/image/1372264950453067776.png" class="edit-icon" />
            </view>

            <view class="method-info">
                <view>
                    <view class="info-text">{{ methodAll.usernameBank }}</view>
                    <view class="info-text">{{ methodAll.bankAddress }}</view>
                </view>

                <!-- <image class="qr-image" :src="method.qrUrl" mode="aspectFill" /> -->
            </view>
        </view>

        <nodata v-if="isEmptyInfo" />

        <view class="footer-btn" @click="addMethod">添加收款方式</view>
    </view>
</template>

<script>
import nodata from "../components/nodata"
export default {
    components: {
        nodata
    },
    data() {
        return {
            method: [{
                name: '微信',
                type: 'wechat',
                account: '<EMAIL>',
                code: '556667',
                qrUrl: "https://pro-oss.pinkwallet.com/image/1372265508689764352.png",
                ac: '#49A0B1',

            },
            {
                name: '支付宝',
                type: 'ali',
                account: '<EMAIL>',
                code: '556667',
                qrUrl: "https://pro-oss.pinkwallet.com/image/1372265508689764352.png",
                ac: '#627EEA'
            },
            {
                name: '银行卡',
                type: 'card',
                account: '<EMAIL>',
                code: '556667',
                qrUrl: "https://pro-oss.pinkwallet.com/image/1372265508689764352.png",
                ac: '#F7931A'
            }],
            methodAll: {},
            error: false
        }
    },
    computed: {
        isEmptyInfo() {
            return Object.keys(this.methodAll).length == 0
        }
    },
    onShow() {
        this.getList()
    },
    methods: {
        async getList() {
            let res = await this.$api.c2cPayList({})
            if (res.code == 200) {
                if (!res.result) {
                    this.methodAll = {}
                } else {
                    this.methodAll = res.result
                }
            } else {
                // this.methodAll = null
                uni.showToast({ title: res.msg, icon: 'none' });
            }
        },
        editMethod(item) {
            // uni.showToast({ title: '点击编辑：' + item.type, icon: 'none' });
            this.$Router.push({
                name: 'progressAdd',
                params: {
                    type: item == 1 ? 'wechat' : item == 2 ? 'ali' : 'bank',
                    account: encodeURIComponent(JSON.stringify(this.methodAll))
                }
            })
        },
        addMethod() {
            this.$Router.push({
                name: 'addpay',

            })
            uni.showToast({ title: '添加收款方式', icon: 'none' });
        }
    }
}
</script>

<style lang="scss" scoped>
.payment-methods-page {
    padding: 32rpx;
    padding-bottom: 120rpx;

    .method-item {
        background-color: #fff;
        padding: 24rpx 0;
        border-bottom: 1px solid #f0f0f0;

        .method-header {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;

            .type-dot {
                width: 8rpx;
                height: 8rpx;
                border-radius: 50%;
                // background-color: #1c1c1c;
                margin-right: 8rpx;
            }

            .type-name {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 32rpx;
                flex: 1;
                color: #000;
            }

            .edit-icon {
                width: 24rpx;
                height: 24rpx;
                // margin-right: 20rpx;
            }
        }

        .method-info {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .info-text {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 32rpx;
                color: rgba(0, 0, 0, .4);
            }

            .qr-image {
                width: 48rpx;
                height: 48rpx;
                border-radius: 8rpx;
            }
        }
    }

    .footer-btn {
        position: fixed;
        bottom: 40rpx;
        left: 32rpx;
        right: 32rpx;
        background-color: #FF82A3;
        color: #fff;
        text-align: center;
        padding: 24rpx 0;
        border-radius: 113rpx;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 28rpx;

    }
}
</style>