<template>
    <view class="add-payment-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="添加支付方式">
        </u-navbar>
        <view style="height: 16rpx;"></view>

        <view class="payment-item" v-for="(item, index) in paymentList" :key="index" @click="handleAdd(item)">
            <view class="left-section">
                <view class="dot" :style="{ backgroundColor: item.color }" />
                <text class="text">{{ item.label }}</text>
            </view>
            <text class="arrow">
                <image src="https://pro-oss.pinkwallet.com/image/1372249936161759232.png" />

            </text>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            paymentList: [
                { label: '添加微信支付', type: 'wechat', color: '#2ebac6' },
                { label: '添加支付宝支付', type: 'ali', color: '#4c6ef5' },
                { label: '添加银行卡支付', type: 'card', color: '#f59f00' }
            ]
        }
    },
    onLoad(options) {
        // let account = JSON.parse(decodeURIComponent(options.account));
        // console.log(account);
    },
    methods: {
        handleAdd(item) {
            uni.showToast({
                title: `点击：${item.label}`,
                icon: 'none'
            })
            this.$Router.push({
                name: 'progressAdd',
                params: {
                    type: item.type
                }
            })
            // 可跳转：this.$Router.push(`/xxx?type=${item.type}`)
        }
    }
}
</script>
<style lang="scss" scoped>
.add-payment-page {
    padding: 24rpx;

    .payment-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 32rpx 0;
        transition: background-color 0.2s;

        &:active {
            background-color: #f8f8f8;
        }

        .left-section {
            display: flex;
            align-items: center;

            .dot {
                width: 8rpx;
                height: 8rpx;
                border-radius: 50%;
                margin-right: 16rpx;
            }

            .text {
                font-family: PingFang HK;
                font-weight: 500;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
            }
        }

        .arrow {
            image {
                width: 36rpx;
                height: 36rpx;
            }
        }
    }
}
</style>