<template>
    <view class="tabbar-container">
        <view class="tabbar">
            <view :class="['tab-item', { active: current == 1 }]" @click="nav_to('C2C', 1)">
                <image class="icon"
                    :src="current === 1 ? 'https://pro-oss.pinkwallet.com/image/1376668244218830848.png' : 'https://pro-oss.pinkwallet.com/image/1376667734799638528.png'" />
                <text>C2C</text>
            </view>

            <view :class="['tab-item', { active: current == 2 }]" @click="nav_to('C2Corder', 2)">
                <!-- <view class="bridge"> </view> -->

                <image class="icon"
                    :src="current == 2 ? 'https://pro-oss.pinkwallet.com/image/1376670477463412736.png' : 'https://pro-oss.pinkwallet.com/image/1376670001913225216.png'" />
                <text>订单</text>
            </view>

            <view :class="['tab-item', { active: current == 3 }]" @click="nav_to('merchant', 3)">
                <!-- <view class="bridge"></view> -->

                <image class="icon"
                    :src="current === 3 ? 'https://pro-oss.pinkwallet.com/image/1376668244218830848.png' : 'https://pro-oss.pinkwallet.com/image/1376877030946725888.png'" />
                <text>我的</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'TabBar',
    props: ['current'],
    data() {
        return {
            isActive: false,
            nowcurrent: 1
        }
    },
    watch: {
        current(newval, oldval) {
            console.log(newval);
        }
    },
    mounted() {
        console.log(this.current, 'mounted');
        this.nowcurrent = this.current
    },
    methods: {
        getTabStyle(isActive) {
            return {
                width: isActive ? '183px' : '80px', // 366rpx ≈ 183px，160rpx ≈ 80px
                transition: 'width 0.8s ease'
            }
        },
        nav_to(name, e) {
            if (this.current == e) return
            this.$emit('changeTab', e)
        }
    }
};
</script>

<style lang="scss" scoped>
.tabbar-container {
    position: fixed;
    bottom: 80rpx;
    left: 0;
    right: 0;
    z-index: 1;
    // box-shadow: 20px 4px 20px 0px #F6EAED;
    // backdrop-filter: blur(13.800000190734863px);

}

.tabbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 14rpx;
    height: 62rpx;
    position: relative;
    // background: #fff;
    background: #f6f6f6;
    border-radius: 80rpx;
    // box-shadow: 0px 4px 20px 0px #F6EAED;
    // box-shadow: 20px 4px 20px 0px #F6EAED;
    z-index: 9999;

    .tab-item {
        // box-shadow: 0 4px 20px 0px #F6EAED;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #929292;
        font-size: 24rpx;
        text-align: center;
        width: 160rpx;
        height: 120rpx;
        border-radius: 80rpx;
        // background: #F6EAED;
        background: #f6f6f6;

        // background: rgba(255, 255, 255, 1);



        // box-shadow: 0px 4px 20px 0px #F6EAED;
        // box-shadow: 0px 4px 20px 0px rgba(246, 234, 237, 1);
        // box-shadow: 0px 4px 20px 0px #F6EAED;
        transition: all 0.3s ease;
        opacity: 1;
        position: relative;
        z-index: 10; // 降低 z-index 到合理值，确保高于 bridge
        // 


        // &:nth-of-type(1) {
            // box-shadow:
            //     -4rpx 0 20rpx 0 #f6eaed, // 左阴影
            //     0 4rpx 20rpx 0 #f6eaed; // 上下阴影
        // }

        // &:nth-of-type(2) {
        //     box-shadow: 0rpx 4rpx 20rpx 0 #f6eaed;
        // }


        // &:nth-of-type(3) {
        //     box-shadow:
        //         4rpx 0 20rpx 0 #f6eaed, // 右阴影
        //         0 4rpx 20rpx 0 #f6eaed; // 上下阴影
        // }

        .bridge {
            all: initial;
            z-index: 0 !important;
            position: absolute;
            // z-index: 1 !important; // 确保低于 tab-item 和 active
            top: 30rpx;
            left: -60rpx;
            width: 100rpx;
            height: 62rpx;
            background: #fff;
            // box-shadow: 0px 4px 20px 0px #F6EAED;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            // border-radius: 0px 45px 45px 0px;
            // border: 1px solid red;
        }



        &.active {
            opacity: 1;
            animation: fadeIn 0.3s ease;
            width: 366rpx;
            background: #FF82A3;
            border: 1.07px solid #FFFFFF61;
            z-index: 999; // 确保 active 状态高于 bridge 和非 active 的 tab-item

            .icon {
                width: 56rpx;
                height: 56rpx;
            }

            text {
                color: #fff;
            }
        }

        .icon {
            width: 56rpx;
            height: 56rpx;
            transition: all 0.3s ease-in-out;
        }

        text {
            font-family: Gilroy;
            font-weight: 500;
            font-size: 20rpx;
            line-height: 24rpx;
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }
}
</style>