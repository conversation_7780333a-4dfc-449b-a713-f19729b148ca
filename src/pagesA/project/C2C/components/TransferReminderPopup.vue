<template>
    <view class="reminder-popup-overlay">
        <view class="reminder-popup-container">
            <view class="header">
                <text class="title">温馨提示</text>
                <image class="close-icon" src="https://pro-oss.pinkwallet.com/image/1371931948699181056.png"
                    @click="closePopup" />
            </view>
            <view class="content">
                <text class="message">
                    若您未转账就点击【确认已转账】，您的账户可能会由于涉嫌恶意操作而被冻结
                </text>
                <label class="checkbox-area" @click="toggleCheckbox">
                    <view class="checkbox-wrapper">
                        <view :class="`custom-checkbox${isChecked ? ' is-checked' : ''}`">
                            <text class="checkmark" v-if="isChecked">✓</text>
                        </view>
                    </view>
                    <text class="checkbox-label">我已通过微信支付向对方转账 {{ getCurrencySymbol(fiat) }} {{ transferAmount }}</text>
                </label>
            </view>
            <view class="actions">
                <!-- <button class="cancel-button" @click="handleCancel">取消</button>
                <button class="confirm-button" :class="{ 'is-disabled': !isChecked }" :disabled="!isChecked"
                    @click="handleConfirm">
                    我已转账，通知卖家
                </button> -->
                <view class="button-group">
                    <button class="btn cancel-btn" @click="handleCancel">取消</button>
                    <button class="btn confirm-btn" @click="handleConfirm">我已转账，通知卖家</button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { getCurrencySymbol } from "@/utils/utils.js"
export default {
    props: {
        // 转账金额，可以从父组件传入
        amount: {
            type: [Number, String],
            default: 7, // 默认金额，与图片一致
        },
        fiat: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            isVisible: this.visible,
            isChecked: false, // 控制复选框是否选中
            transferAmount: this.amount,
        };
    },
    watch: {
        visible(newVal) {
            this.isVisible = newVal;
            if (newVal) {
                // 每次弹窗显示时，重置复选框状态
                this.isChecked = false;
            }
        },
        amount(newVal) {
            this.transferAmount = newVal;
        }
    },
    methods: {
        getCurrencySymbol,
        closePopup() {
            this.isVisible = false;
            this.$emit('close'); // 通知父组件关闭
        },
        toggleCheckbox() {
            this.isChecked = !this.isChecked;
        },
        handleCancel() {
            this.closePopup();
            this.$emit('cancel'); // 通知父组件用户点击了取消
        },
        handleConfirm() {
            if (!this.isChecked) {
                // 理论上按钮的 disabled 状态会阻止此操作，但可作为额外校验
                uni.showToast({
                    title: '请先勾选确认信息',
                    icon: 'none'
                });
                return;
            }
            // 执行确认操作的逻辑
            console.log('用户已确认转账');
            this.$emit('confirm', { amount: this.transferAmount }); // 通知父组件用户点击了确认，并可以传递一些数据
            this.closePopup(); // 通常确认后也会关闭弹窗
        },
    },
};
</script>

<style lang="scss" scoped>
.reminder-popup-overlay {
    // position: fixed;
    // top: 0;
    // left: 0;
    width: 100%;
    // height: 100%;
    // background-color: rgba(0, 0, 0, 0.5); // 半透明遮罩
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999; //确保在最上层

    // height: 202;


}

.reminder-popup-container {
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    // background-color: #ffffff;
    // width: 85%; // 根据图片目测宽度
    // max-width: 600rpx; // 可以设置一个最大宽度
    // border-radius: 16rpx; // 圆角
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 44rpx 32rpx 16rpx 32rpx; // 内边距调整

        .title {
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 32rpx;
            line-height: 48rpx;
            color: #000;
        }

        .close-icon {
            width: 48rpx; // 关闭图标大小
            height: 48rpx;
            cursor: pointer; // 鼠标悬停手势
        }
    }

    .content {
        padding: 0 30rpx 30rpx; // 内边距

        .message {
            font-family: PingFang HK;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 1.5;
            color: rgba(0, 0, 0, .6);
            display: block; // 独占一行
            margin-bottom: 40rpx; // 与下方复选框的间距
        }

        .checkbox-area {
            display: flex;
            align-items: center;
            cursor: pointer; // 鼠标悬停手势

            .checkbox-wrapper {
                margin-right: 15rpx; // 复选框与文字的间距

                .custom-checkbox {
                    width: 36rpx; // 自定义复选框大小
                    height: 36rpx;
                    border: 1rpx solid #cccccc; // 边框颜色
                    border-radius: 50%; // 圆形
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    transition: background-color 0.2s, border-color 0.2s; // 过渡效果

                    &.is-checked {
                        background-color: #FF82A3; // 选中时的背景色 (根据图片调整)
                        // border-color: #ff4d6a; // 选中时的边框色
                        border: none;

                        .checkmark {
                            color: #ffffff; // 对勾颜色
                            font-size: 24rpx; // 对勾大小
                            font-weight: bold;
                        }
                    }
                }
            }

            .checkbox-label {
                font-family: PingFang HK;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 40rpx;
                color: rgba(0, 0, 0, .6);
            }
        }
    }

    .actions {
        display: flex;
        // border-top: 1rpx solid #f0f0f0; // 按钮区域与内容区的分割线，根据实际效果决定是否需要
        width: 100%;

        .button-group {
            display: flex;
            justify-content: center;
            gap: 24rpx;
            // padding: 40rpx 0;
            padding-bottom: 40rpx;
            width: 100%;

            .btn {
                text-align: center;
                height: 88rpx;
                line-height: 88rpx;
                border-radius: 100rpx;
                font-size: 28rpx;
                border: none;

                &.cancel-btn {
                    margin: 0;
                    width: 222rpx;
                    background: #FFF2F5;
                    height: 88rpx;
                    line-height: 88rpx;
                    color: #FF82A3;
                    border-radius: 112rpx;
                }

                &.confirm-btn {
                    width: 452rpx;
                    margin: 0;
                    height: 88rpx;
                    line-height: 88rpx;
                    background-color: #FF82A3;
                    color: #fff;
                    border-radius: 112rpx;
                }
            }
        }

        // button {
        //     flex: 1; // 按钮平分宽度
        //     height: 90rpx; // 按钮高度
        //     line-height: 90rpx;
        //     text-align: center;
        //     font-size: 30rpx; // 按钮文字大小
        //     border: none;
        //     border-radius: 0; // 移除默认圆角，因为父容器有圆角
        //     margin: 0; // 移除默认外边距
        //     padding: 0; // 移除默认内边距
        //     outline: none; // 移除点击时的轮廓

        //     &::after {
        //         border: none; // 移除 uni-app button 的默认边框
        //     }
        // }

        .cancel-button {
            background-color: #fce0e3; // 取消按钮背景色 (淡粉色)
            color: #ff4d6a; // 取消按钮文字颜色 (深粉色)
            border-bottom-left-radius: 16rpx; // 左下角圆角
        }

        .confirm-button {
            background-color: #ff4d6a; // 确认按钮背景色 (深粉色)
            color: #ffffff; // 确认按钮文字颜色
            border-bottom-right-radius: 16rpx; // 右下角圆角

            &.is-disabled {
                background-color: #f0a9b4; // 禁用时的背景色 (更浅的粉色)
                color: #fce0e3; // 禁用时的文字颜色 (更浅的文字)
                // pointer-events: none; // 如果需要完全禁止事件，可以加上，但 :disabled 属性通常足够
            }
        }
    }
}
</style>