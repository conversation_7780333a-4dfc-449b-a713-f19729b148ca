<template>
  <view class="wheel-wrapper">
    <scroll-view class="wheel-scroll" scroll-y :scroll-with-animation="true" show-scrollbar="false"
      @scroll="handleScroll" @touchstart="onTouchStart" @touchend="handleTouchEnd" ref="scrollView">
      <!-- 顶部 padding -->
      <view :style="{ height: centerPadding + 'rpx' }" />

      <!-- 每个选项 -->
      <view v-for="(item, index) in options" :key="index" :id="'item-' + index" class="wheel-item"
        :class="{ active: index === selectedIndex }" :style="getItemStyle(index)">
        <view class="wheel-card">
          <view class="left">
            <view class="dot" v-if="dotss"
              :style="{ backgroundColor: item.value == 1 ? '#2ebac6' : item.value == 2 ? '#4c6ef5' : '#f59f00' }" />
            <view class="label">{{ item.label }}</view>
          </view>

          <image class="right" v-if="index == value"
            src="https://pro-oss.pinkwallet.com/image/1380496191849586688.png" />
        </view>
      </view>

      <!-- 底部 padding -->
      <view :style="{ height: bottomPadding + 'rpx' }" />
    </scroll-view>

    <!-- 中心线 -->
    <view class="wheel-indicator" />
  </view>
</template>

<script>
export default {
  name: 'CustomWheelPicker',
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: {
      type: Number,
      default:2
    },
    dotss: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectedIndex: this.value,
      itemHeight: 100, // 单项高度 rpx
      centerPadding: 80, // 顶部填充
      bottomPadding:200, // ✅ 增加底部填充，防止最后项无法选中
      scrollTimer: null
    }
  },
  computed: {
    activeItemId() {
      return `item-${this.selectedIndex}`
    }
  },
  watch: {
    value(val) {
      this.selectedIndex = val
    },
    selectedIndex(val) {
      this.$emit('input', val)
      this.$emit('change', val)
    }
  },
  methods: {
    onTouchStart() {
      clearTimeout(this.scrollTimer)
    },
    handleTouchEnd() {
      clearTimeout(this.scrollTimer)
      this.scrollTimer = setTimeout(() => {
        this.scrollToSelected()
      }, 80)
    },
    scrollToSelected() {
      const id = `#item-${this.selectedIndex}`
      this.$nextTick(() => {
        uni
          .createSelectorQuery()
          .in(this)
          .select(id)
          .boundingClientRect((rect) => {
            if (rect) {
              const targetScrollTop =
                this.selectedIndex * this.itemHeight
              this.$refs.scrollView.scrollTo({
                scrollTop: targetScrollTop,
                duration: 150
              })
            }
          })
          .exec()
      })
    },
    handleScroll(e) {
      const scrollTop = e.detail.scrollTop
      const containerCenter = scrollTop + this.centerPadding

      // 找出最靠近中线的项
      let minDiff = Infinity
      let closestIndex = 0

      this.options.forEach((_, index) => {
        const itemCenter = index * this.itemHeight + this.itemHeight / 2
        const diff = Math.abs(containerCenter - itemCenter)
        if (diff < minDiff) {
          minDiff = diff
          closestIndex = index
        }
      })

      this.selectedIndex = closestIndex
    },
    getItemStyle(index) {
      const diff = index - this.selectedIndex
      const maxTilt = 25
      const maxScale = 1
      const minScale = 0.7

      const rotateX = -Math.max(-2, Math.min(2, diff)) * maxTilt
      const scale = maxScale - Math.abs(diff) * 0.15
      const opacity = 1 - Math.abs(diff) * 0.3

      return `
          transform: rotateX(${rotateX}deg) scale(${scale});
          opacity: ${opacity};
          transform-origin: center center;
        `
    }
  }
}
</script>

<style lang="scss" scoped>
.wheel-wrapper {
  height: 300rpx;
  overflow: hidden;
  position: relative;
  border-radius: 20rpx;
  perspective: 1000rpx; // 3D 透视
}

.wheel-scroll {
  height: 100%;
}

.wheel-item {
  // height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
  transform-style: preserve-3d; // 保持3D变换
}

.wheel-card {
  // width: 460rpx;
  width: 100%;
  // padding: 20rpx 0;
  padding: 32rpx;
  // text-align: center;
  border-radius: 12rpx;
  // background: linear-gradient(135deg, #2ebac6, #4c6ef5);
  // color: #fff;
  // font-size: 30rpx;
  // box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border: 3rpx solid #FF82A3;
  border-radius: 24rpx;

  display: flex;
  justify-content: space-between;
  align-items: center;

  .left {
    display: flex;
    align-items: center;

    .dot {
      width: 8rpx;
      height: 8rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }

    .label {
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #000;
    }
  }

  .right {
    width: 42rpx;
    height: 42rpx;
  }

}

.wheel-item:not(.active) .wheel-card {
  // background: #444;
  color: #aaa;
  box-shadow: none;
  border: 3rpx solid rgba(0, 0, 0, .1);
  border-radius: 24rpx;

}

.wheel-indicator {
  position: absolute;
  top: 100rpx;
  left: 30rpx;
  right: 30rpx;
  height: 100rpx;
  border-top: 2rpx solid rgba(255, 255, 255, 0.2);
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.2);
  pointer-events: none;
}
</style>