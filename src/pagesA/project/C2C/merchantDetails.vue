<template>
    <view class="merchant-info-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="">

        </u-navbar>
        <!-- 顶部信息 -->
        <view class="top-section">
            <image class="avatar" :src="avatar" />
            <view class="user-info">
                <view class="email">{{ info.email || '--' }}</view>
                <view class="register-time">{{ info.country }} 注册时间 {{ formatTimestamp(info.registerTime) }}</view>
            </view>
        </view>

        <!-- 标签 -->
        <view class="tags">
            <view :class="['tag', info.realNameStatus !== 1 ? 'disabled' : '']">身份认证
                <image src="https://pro-oss.pinkwallet.com/image/1372246610242265088.png" />
            </view>
            <view :class="['tag', !info.email ? 'disabled' : '']">电子邮箱
                <image src="https://pro-oss.pinkwallet.com/image/1372246610242265088.png" />

            </view>
            <view :class="['tag', info.shopStatus != 1 ? 'disabled' : '']" @click="LookReason"> {{ shopStatusLabel }}
                <image src="https://pro-oss.pinkwallet.com/image/1372246610242265088.png" v-if="info.shopStatus == 1" />
            </view>
        </view>

        <view class="tabs-container">
            <!-- 顶部标签 -->

            <view class="toggle-container">
                <view class="toggle-bg" :class="activeTab"></view>
                <view class="toggle-item" :class="{ active: activeTab === 'detail' }" @tap="switchTab('detail')">
                    detail
                </view>
                <view class="toggle-item" :class="{ active: activeTab === 'order' }" @tap="switchTab('order')">
                    order
                </view>
            </view>



            <!-- 内容区域 -->
            <view class="tab-content">
                <!-- 详情页 -->
                <view v-if="activeTab === 'detail'" class="detail-view">
                    <view class="row" v-for="(item, index) in detailList" :key="index">
                        <text class="label" v-if="!item.show">{{ item.label }}</text>
                        <text class="value" v-if="!item.show">{{ item.value }}</text>
                        <view :class="item.label" v-if="item.show"></view>
                    </view>
                </view>

                <!-- 委托单页 -->
                <view v-if="activeTab === 'order'" class="order-view">
                    <view class="order-card" v-for="(item, index) in orderList" :key="index">
                        <view class="top">
                            <text class="price">{{ item.totalAmount }}</text>
                            <text class="status">{{ item.orderType == 0 ? '买单' : '卖单' }}--{{ formatter(item.status)
                                }}</text>
                        </view>
                        <view class="desc">数量 {{ item.totalNum }} {{ item.coin }}</view>
                        <view class="desc">限额 {{ item.limitAmountStart + '-' + item.limitAmountEnd }}</view>

                        <view class="bom">
                            <view class="pay-type">
                                <view class="dot" :style="{
                                    backgroundColor: item.payType == 1 ? '#2ebac6' : item.payType == 2 ? '#4c6ef5' :
                                        '#f59f00'
                                }" />

                                {{ item.payType == 1 ? '微信支付' : item.payType == 2 ? '支付宝支付' :
                                    '银行卡支付' }}
                            </view>
                            <u-button hover-class="none" class="cancel-btn flex_all"
                                @click="c2cShopOrderCancelAc(item.orderNo)" v-if="item.status == 0">撤单</u-button>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- <tabbar /> -->
    </view>
</template>

<script>
import tabbar from "./components/tabbar.vue"

export default {
    components: {
        tabbar,
    },
    data() {
        return {
            activeTab: 'detail',
            detailList: [
                { label: '成交单数(近30天)', value: '' },
                { label: '成交率(近30天)', value: '' },
                { label: '购买', value: '' },
                { label: '出售', value: '' },
                { label: '交易对手数(近30天)', value: '' },
                { label: 'shu', value: 'shu', show: true },
                { label: '平均付款时间', value: "" },
                { label: '平均放币时间', value: "" },
                { label: '成交单数', value: '' },
                { label: '成交率', value: '' },
            ],
            orderList: [
                {
                    price: '76110.35',
                    amount: '6.85',
                    limit: '6.85~68.50 USDT',
                    payType: '微信支付',
                    status: '出售—未完成',
                    showCancel: true
                },
                {
                    price: '76110.35',
                    amount: '6.85',
                    limit: '6.85~68.50 USDT',
                    payType: '微信支付',
                    status: '出售—暂存',
                    showCancel: false
                }
            ],
            info: {},
            showModal: false,
            modalContent: '您的商家身份已被封禁，禁止出售交易',
            ismerchant: true,
            status: 1,
            avatar: "https://pro-oss.pinkwallet.com/image/1372244320160669696.png",
            email: '<EMAIL>',
            registerTime: 'China注册时间2025-01-09 15:56:45',
            stats: [
                { label: '成交单数', value: 4 },
                { label: '成交率', value: '22.22%' },
                { label: '平均付款时间', value: '2\'52"' },
                { label: '平均放币时间', value: '7\'22"' }
            ],
            options1: [
                {
                    label: '收款管理方式',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250214525132800.png",
                    action: 'payway',
                },
                {
                    label: '申请商家身份',
                    icon: "https://pro-oss.pinkwallet.com/image/1372252581853224960.png",
                    action: 'bindMerchant',
                },
            ],
            options: [
                {
                    label: '收款管理方式',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250214525132800.png",
                    action: 'payway',
                },
                {
                    label: '发布出售信息',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250379617132544.png",
                    action: 'publish_sell',
                },
                {
                    label: '发布购买信息',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250502321496064.png",
                    action: 'publish_buy',

                },
                {
                    label: '解除商家身份',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250622651883520.png",
                    action: 'unbind',
                },
            ],
            page: {
                pageNum: 1,
                pageSize: 10
            },
            hasNext: false,
        }
    },
    onShow() {
        this.getc2cShopOrderMyList()
        this.getC2cuserInfo()
    },
    onReachBottom() {
        if (this.hasNext) {
            this.page.pageNum++
            this.getc2cShopOrderMyList()
        }
    },
    computed: {
        shopStatusLabel() {
            const map = {
                '-1': '不是商家',
                0: '(初始化)待审核',
                1: '商家',
                2: '未通过',
                3: '商家冻结中',
                4: '申诉解冻中',
                5: '冻结申诉退款中',
                6: '申请退款审核中',
                7: '停用'
            }
            return map[this.info.shopStatus] || ''
        },
    },
    methods: {
        async c2cShopOrderCancelAc(e) {
            let res = await this.$api.c2cShopOrderCancel({
                orderNo: e,
                // uid: uni.getStorageSync('uid')
            })
            if (res.code == 200) {
                uni.showToast({
                    title: '撤单成功',
                    icon: "none",
                    duration: 2000
                });
                this.getc2cShopOrderMyList()
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: "none",
                    duration: 2000
                });
            }
        },
        formatSeconds(seconds) {
            seconds = Number(seconds)
            if (!seconds || seconds <= 0) return '0s'

            const h = Math.floor(seconds / 3600)
            const m = Math.floor((seconds % 3600) / 60)
            const s = Math.floor(seconds % 60)

            let result = ''
            if (h > 0) result += `${h}h`
            if (m > 0) result += `${m}min`
            if (s > 0) result += `${s}s`

            return result
        },
        formatter(e) {
            // 订单状态:0未完成  1 已完成  2已取消  3暂存状态
            return e == 0 ? '未完成' : e == 1 ? '已完成 ' : e == 2 ? '已取消' : e == 3 ? '暂存状态' : '--'
        },
        async getc2cShopOrderMyList() {
            let res = await this.$api.c2cShopOrderMyList({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
            })
            if (res.code == 200) {
                this.hasNext = res.result.hasNext
                if (this.page.pageNum == 1) {
                    this.orderList = res.result.data
                } else {
                    this.orderList = this.orderList.concat(res.result.data)
                }
            }
        },
        switchTab(tab) {
            this.orderList = []
            this.page.pageNum = 1
            this.activeTab = tab
            if (tab == 'order') {
                this.getc2cShopOrderMyList()
            } else {
                this.getC2cuserInfo()
            }
            // this.getshopOrderList()
        },
        async getC2cuserInfo() {
            let res = await this.$api.c2cuserInfo({});
            if (res.code == 200) {
                this.info = res.result
                this.stats[0].value = res.result.tradeCount // 总的-成交单数
                this.stats[1].value = Number(res.result.tradeRate).toFixed(2) + '%' // 总的-成交占比
                this.stats[2].value = res.result.avgPayTime // 总的-平均付款时间
                this.stats[3].value = res.result.avgReleaseTime // 总的-平均放币时间
                this.ismerchant = res.result.shopStatus == 1

                this.detailList[0].value = res.result.tradeCountLast30Days // 总的-30天交易量
                this.detailList[1].value = (Number(res.result.tradeRateLast30Days) * 100).toFixed(2) + '%' // 总的-30天交易率
                this.detailList[2].value = (Number(res.result.buyPercent) * 100).toFixed(2) + '%'// 购买
                this.detailList[3].value = (Number(res.result.sellPercent) * 100).toFixed(2) + '%'// 出售
                this.detailList[4].value = res.result.tradePartnerCountLast30Days // 交易对手数
                this.detailList[6].value = this.formatSeconds(res.result.avgPayTime) // 平均付款时间
                this.detailList[7].value = this.formatSeconds(res.result.avgReleaseTime) // 总平均放币时间
                this.detailList[8].value = res.result.tradeCount // 成交单数
                this.detailList[9].value = (Number(res.result.tradeRate) * 100).toFixed(2) + '%' // 成交率
            }
        },
        LookReason() {
            this.$Router.push({
                name: 'ban'
            })
        },
        async toogleStatus() {
            if (this.status == 1) {
                this.status = 0
            } else {
                this.status = 1
            }
            let res = await this.$api.c2cBaseInfoOnLine({
                status: this.status
            })
            if (res.code == 200) {
                this.$u.toast('toogle success')
            }
        },
        handleOptionClick(item) {

            this.$Router.push({
                name: item.action
            })
            // 可在此处做页面跳转或打开弹窗等交互
        },
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000);
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        }
    }
}
</script>


<style lang="scss" scoped>
::v-deep .u-model {
    background: #fff !important;
}

::v-deep .u-modal__title {
    text-align: left !important;
    margin-left: 50rpx !important
}

::v-deep .u-modal__content {
    font-family: PingFang SC !important;
    font-weight: 400 !important;
    font-size: 24rpx !important;
    line-height: 34rpx !important;
    color: #000 !important;
}

::v-deep .u-modal__footer {
    border-top: 1rpx solid #eee;
}

::v-deep .u-model__footer__button {
    height: 86rpx !important;
    font-family: PingFang SC !important;
    font-weight: 600 !important;
    font-size: 28rpx !important;
    line-height: 86rpx !important;
    color: #000 !important;
}

.merchant-info-page {
    padding: 32rpx;

    .tabs-container {

        .toggle-container {
            position: relative;
            // width: 500rpx;
            // margin: 27rpx 28rpx 0 28rpx;
            // padding-top: 16rpx;
            // width: 100%;
            height: 96rpx;
            // background-color: #F6F6F6;
            border-radius: 80rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6rpx;
            // box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.1);
            background-image: url("https://pro-oss.pinkwallet.com/image/1370432022240649216.png");
            background-size: 100% 100%;

            // overflow: hidden;
            // filter: blur(13.800000190734863px);
            .rounded-triangle-svg {
                position: absolute;
                width: 100%;
                height: 100%;
            }

            .toggle-item {
                width: 50%;
                text-align: center;
                font-size: 28rpx;
                z-index: 1;
                font-family: Gilroy;
                font-weight: 500;
                line-height: 32rpx;
                transition: color 0.3s;
                color: rgba(0, 0, 0, .4);
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .toggle-item.active {
                color: #fff;
            }

            .toggle-bg {
                height: 80rpx;
                position: absolute;
                // top: -10rpx;
                // border: 1.07px solid #FFFFFF61;
                // bottom: 6rpx;
                width: 328rpx;
                border-radius: 80rpx;
                background-color: #ff6f96;
                // z-index: 0;
                transition: left 0.3s;
                border: 1.07px solid #FFFFFF61;
                display: flex;
                justify-content: center;
                align-items: center;
                // box-shadow: 0 0 6rpx rgba(255, 111, 150, 0.4);
            }

            .toggle-bg.detail {
                left: 6rpx;
                // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
            }

            .toggle-bg.order {
                left: 362rpx; // 500rpx - 50% + padding
                // clip-path: path("M0,40 Q0,20 20,20 H230 Q250,20 250,40 Q250,60 230,60 H20 Q0,60 0,40 Z");
            }
        }




        .tab-content {
            margin-top: 8rpx;

            .detail-view {
                .row {
                    display: flex;
                    justify-content: space-between;
                    padding: 12rpx 0;

                    .shu {
                        width: 100%;
                        background: rgba(0, 0, 0, .1);
                        height: 1rpx;
                        margin: 8rpx 0;
                    }

                    .label {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        line-height: 32rpx;
                        color: rgba(0, 0, 0, .5);
                    }

                    .value {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        line-height: 32rpx;
                        color: #000;
                    }
                }
            }

            .order-view {
                .order-card {
                    padding: 32rpx 0;
                    border-bottom: 1rpx solid rgba(0, 0, 0, .1);

                    .top {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 10rpx;

                        .price {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 28rpx;
                            line-height: 32rpx;
                            color: #000;
                        }

                        .status {
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 20rpx;
                            line-height: 32rpx;
                            color: rgba(0, 0, 0, .6);
                        }
                    }

                    .desc {
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 20rpx;
                        line-height: 32rpx;
                        color: rgba(0, 0, 0, .4);
                    }

                    .bom {
                        display: flex;
                        align-items: flex-end;
                        justify-content: space-between;
                        height: 60rpx;

                        .pay-type {
                            display: flex;
                            align-items: center;
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 20rpx;
                            line-height: 32rpx;
                            color: rgba(0, 0, 0, .6);


                            .dot {
                                width: 8rpx;
                                height: 8rpx;
                                border-radius: 50%;
                                margin-right: 8rpx;
                            }
                        }

                        .cancel-btn {
                            width: 108rpx;
                            height: 58rpx;
                            border-radius: 12rpx;
                            background: #FF82A3;
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 28rpx;
                            line-height: 32rpx;
                            color: #fff;
                            margin: 0;
                        }
                    }


                }
            }
        }
    }

    .Right {
        margin-right: 32rpx;

        image {
            width: 46rpx;
            height: 46rpx;
        }
    }

    .top-section {
        display: flex;
        align-items: center;
        margin: 15rpx 0 24rpx 0;

        .avatar {
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            margin-right: 16rpx;
        }

        .user-info {
            flex: 1;

            .email {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
            }

            .register-time {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 34rpx;
                color: #808080;
                margin-top: 4rpx;
            }
        }

        .status-tag-off {
            background-color: #F2F2F2;
            color: #000;
            width: 140rpx;
            height: 58rpx;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;

            image {
                width: 18rpx;
                height: 18rpx;
                margin-left: 18rpx;
            }
        }

        .status-tag {
            background-color: #FF82A3;
            color: #fff;
            width: 140rpx;
            height: 58rpx;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;

            image {
                width: 18rpx;
                height: 18rpx;
                margin-left: 18rpx;
            }
        }
    }

    .tags {
        display: flex;
        margin-bottom: 32rpx;

        .tag {
            background-color: #FFF2F5;
            color: #FF82A3;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            padding: 12rpx 24rpx;
            border-radius: 16rpx;
            margin-right: 12rpx;
            display: flex;
            align-items: center;

            image {
                width: 14rpx;
                height: 14rpx;
                margin-left: 14rpx;
            }
        }

        .disabled {
            background-color: #FF82A3;
            color: #fff;
        }
    }

}
</style>