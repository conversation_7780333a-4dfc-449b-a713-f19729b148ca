<template>
    <view class="merchant-info-page">
        <u-navbar back-icon-color="#121212" :border-bottom="false" title="">
            <!--  -->
            <view slot="right" class="Right" @click="nav_to('merchantDetails')">
                <image src="https://pro-oss.pinkwallet.com/image/1375488657283702784.png" />
            </view>
        </u-navbar>
        <!-- 顶部信息 -->
        <view class="top-section">
            <image class="avatar" :src="avatar" />
            <view class="user-info">
                <view class="email">{{ info.email || '--' }}</view>
                <view class="register-time">{{ info.country }} 注册时间 {{ formatTimestamp(info.registerTime) }}</view>
            </view>
            <view class="status-tag" @click="toogleStatus" v-if="status == 1">
                <text>在线</text>
                <image src="https://pro-oss.pinkwallet.com/image/1372245267234512896.png" />
            </view>
            <view class="status-tag-off" @click="toogleStatus" v-if="status == 0">
                <text>离线</text>
                <image src="https://pro-oss.pinkwallet.com/image/1372245744223346688.png" />
            </view>
        </view>

        <!-- 标签 -->
        <view class="tags">
            <view :class="['tag', info.realNameStatus !== 1 ? 'disabled' : '']">身份认证
                <image src="https://pro-oss.pinkwallet.com/image/1372246610242265088.png" />
            </view>
            <view :class="['tag', !info.email ? 'disabled' : '']">电子邮箱
                <image src="https://pro-oss.pinkwallet.com/image/1372246610242265088.png" />

            </view>
            <view :class="['tag', info.shopStatus != 1 ? 'disabled' : '']" @click="LookReason(info.shopStatus)"> {{
                shopStatusLabel }}
                <image src="https://pro-oss.pinkwallet.com/image/1372246610242265088.png" v-if="info.shopStatus == 1" />
            </view>
        </view>

        <!-- 数据展示 -->
        <view class="data-panel">
            <view class="data-item" v-for="item in stats" :key="item.label">
                <view class="label">{{ item.label }}</view>
                <view class="value">{{ item.value }}</view>
            </view>
        </view>

        <!-- 操作列表 -->
        <view class="option-list" v-if="ismerchant">
            <view class="option-item" v-for="item in options" :key="item.label" @click="handleOptionClick(item)">
                <image class="icon" :src="item.icon" />
                <text class="text">{{ item.label }}</text>
                <text class="arrow">
                    <image src="https://pro-oss.pinkwallet.com/image/1372249936161759232.png" />
                </text>
            </view>
        </view>

        <view class="option-list" v-else>
            <view class="option-item" v-for="item in options1" :key="item.label" @click="handleOptionClick(item)">
                <image class="icon" :src="item.icon" />
                <text class="text">{{ item.label }}</text>
                <text class="arrow">
                    <image src="https://pro-oss.pinkwallet.com/image/1372249936161759232.png" />
                </text>
            </view>
        </view>

        <u-modal v-model="showModal" title="风险提示！" confirm-text="确定" :show-cancel-button="false" :content="modalContent"
            :close-on-click-overlay="false" :border-radius="24"
            :confirm-style="{ color: '#000', fontWeight: 'bold', background: '#ff' }" :title-style="{
                fontFamily: 'PingFang SC',
                fonWeight: '600',
                fontSize: '28rpx',
                lineHeight: '40rpx',
                color: '#000'
            }" />

        <!-- <tabbar :current="3" /> -->
    </view>
</template>

<script>
import tabbar from "./components/tabbar.vue"

export default {
    components: {
        tabbar,
    },
    data() {
        return {
            info: {},
            showModal: false,
            modalContent: '您的商家身份已被封禁，禁止出售交易',
            ismerchant: null,
            status: 1,
            avatar: "https://pro-oss.pinkwallet.com/image/1372244320160669696.png",
            email: '<EMAIL>',
            registerTime: 'China注册时间2025-01-09 15:56:45',
            stats: [
                { label: '成交单数', value: '' },
                { label: '成交率', value: '' },
                { label: '平均付款时间', value: '' },
                { label: '平均放币时间', value: '' }
            ],
            options1: [
                {
                    label: '收款管理方式',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250214525132800.png",
                    action: 'payway',
                },
                {
                    label: '申请商家身份',
                    icon: "https://pro-oss.pinkwallet.com/image/1372252581853224960.png",
                    action: 'bindMerchant',
                },
            ],
            options: [
                {
                    label: '收款管理方式',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250214525132800.png",
                    action: 'payway',
                },
                {
                    label: '发布出售信息',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250379617132544.png",
                    action: 'publish_sell',
                },
                {
                    label: '发布购买信息',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250502321496064.png",
                    action: 'publish_buy',

                },
                {
                    label: '解除商家身份',
                    icon: "https://pro-oss.pinkwallet.com/image/1372250622651883520.png",
                    action: 'unbind',
                },
            ]
        }
    },
    mounted() {
        this.getC2cuserInfo()
    },
    computed: {
        shopStatusLabel() {
            const map = {
                '-1': '不是商家',
                0: '(初始化)待审核',
                1: '商家',
                2: '未通过',
                3: '商家冻结中',
                4: '申诉解冻中',
                5: '冻结申诉退款中',
                6: '申请退款审核中',
                7: '停用'
            }
            return map[this.info.shopStatus] || ''
        },
    },
    methods: {

        async getC2cuserInfo() {
            let res = await this.$api.c2cuserInfo({});
            if (res.code == 200) {
                this.info = res.result
                this.stats[0].value = res.result.tradeCount // 总的-成交单数
                this.stats[1].value = (Number(res.result.tradeRate) * 100).toFixed(2) + '%' // 总的-成交占比
                // 把res.result.avgPayTime和res.result.avgPayAmount的秒时间格式转换为分钟小时秒的格式，注意0的情况
                //  this.stats[3].value = timeFormat(res.result.avgPayTime)

                // this.stats[2].value = res.result.avgPayTime  // 总的-平均付款时间
                // this.stats[3].value = res.result.avgReleaseTime // 总的-平均放币时间
                this.stats[2].value = this.formatSeconds(res.result.avgPayTime)     // 平均付款时间
                this.stats[3].value = this.formatSeconds(res.result.avgReleaseTime) // 平均放币时间
                this.ismerchant = res.result.shopStatus == 1
            }
        },
        formatSeconds(seconds) {
            seconds = Number(seconds)
            if (!seconds || seconds <= 0) return '0s'

            const h = Math.floor(seconds / 3600)
            const m = Math.floor((seconds % 3600) / 60)
            const s = Math.floor(seconds % 60)

            let result = ''
            if (h > 0) result += `${h}h`
            if (m > 0) result += `${m}min`
            if (s > 0) result += `${s}s`

            return result
        },
        LookReason(e) {
            if (e == 3) {
                this.$Router.push({
                    name: 'ban'
                })
            }

        },
        async toogleStatus() {
            if (this.status == 1) {
                this.status = 0
            } else {
                this.status = 1
            }
            let res = await this.$api.c2cBaseInfoOnLine({
                status: this.status
            })
            if (res.code == 200) {
                this.$u.toast('toogle success')
            }
        },
        handleOptionClick(item) {

            this.$Router.push({
                name: item.action
            })
            // 可在此处做页面跳转或打开弹窗等交互
        },
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000);
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        }
    }
}
</script>


<style lang="scss" scoped>
::v-deep .u-model {
    background: #fff !important;
}

::v-deep .u-modal__title {
    text-align: left !important;
    margin-left: 50rpx !important
}

::v-deep .u-modal__content {
    font-family: PingFang SC !important;
    font-weight: 400 !important;
    font-size: 24rpx !important;
    line-height: 34rpx !important;
    color: #000 !important;
}

::v-deep .u-modal__footer {
    border-top: 1rpx solid #eee;
}

::v-deep .u-model__footer__button {
    height: 86rpx !important;
    font-family: PingFang SC !important;
    font-weight: 600 !important;
    font-size: 28rpx !important;
    line-height: 86rpx !important;
    color: #000 !important;
}

.merchant-info-page {
    padding: 32rpx;

    .Right {
        margin-right: 32rpx;

        image {
            width: 46rpx;
            height: 46rpx;
        }
    }

    .top-section {
        display: flex;
        align-items: center;
        margin: 15rpx 0 24rpx 0;

        .avatar {
            width: 72rpx;
            height: 72rpx;
            border-radius: 50%;
            margin-right: 16rpx;
        }

        .user-info {
            flex: 1;

            .email {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
            }

            .register-time {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 34rpx;
                color: #808080;
                margin-top: 4rpx;
            }
        }

        .status-tag-off {
            background-color: #F2F2F2;
            color: #000;
            width: 140rpx;
            height: 58rpx;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;

            image {
                width: 18rpx;
                height: 18rpx;
                margin-left: 18rpx;
            }
        }

        .status-tag {
            background-color: #FF82A3;
            color: #fff;
            width: 140rpx;
            height: 58rpx;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            font-family: PingFang SC;
            font-weight: 600;
            font-size: 28rpx;

            image {
                width: 18rpx;
                height: 18rpx;
                margin-left: 18rpx;
            }
        }
    }

    .tags {
        display: flex;
        margin-bottom: 32rpx;

        .tag {
            background-color: #FFF2F5;
            color: #FF82A3;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            padding: 12rpx 24rpx;
            border-radius: 16rpx;
            margin-right: 12rpx;
            display: flex;
            align-items: center;

            image {
                width: 14rpx;
                height: 14rpx;
                margin-left: 14rpx;
            }
        }

        .disabled {
            background-color: #FF82A3;
            color: #fff;
        }
    }

    .data-panel {
        display: flex;
        justify-content: space-between;
        margin-bottom: 40rpx;
        border-top: 1rpx solid rgba(0, 0, 0, .1);
        border-bottom: 1rpx solid rgba(0, 0, 0, .1);
        padding: 32rpx 0;

        .data-item {
            text-align: center;
            // flex: 1;

            .value {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 34rpx;
                color: rgba(128, 128, 128, 1);
                margin-top: 10rpx;
            }

            .label {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 34rpx;
                color: #000;
            }
        }
    }

    .option-list {
        .option-item {
            display: flex;
            align-items: center;
            padding: 24rpx 0;
            // border-bottom: 1px solid #f2f2f2;
            transition: background-color 0.2s;

            &:first-child {
                padding: 0 0 24rpx 0;

            }

            &:active {
                background-color: #f8f8f8;
            }

            .icon {
                width: 40rpx;
                height: 42rpx;
                margin-right: 16rpx;
            }

            .text {
                flex: 1;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 34rpx;
                color: #000;
            }

            .arrow {

                image {
                    width: 36rpx;
                    height: 36rpx;
                }
            }
        }
    }
}
</style>