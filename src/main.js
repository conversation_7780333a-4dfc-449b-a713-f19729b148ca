import Vue from "vue";
import App from "./App";
import store from "@/store";
import api from "@/common/api/"; //数据请求
import router from "./router/index.js";
import jsrsasign from "jsrsasign";
import messages from "./lang";
import VueI18n from "vue-i18n";
// if (typeof window !== "undefined") {
//   window.wx =  {}; // 避免 wx 未定义
// }
Vue.use(VueI18n);
// #ifdef H5
import * as uni from "./utils/uni.webview.1.5.4.js";
// #endif

import uView from "@/uni_modules/uview-ui";
import CryptoJS from "crypto-js";

let i18nConfig = {
  locale: uni.getStorageSync("__language__"),
  messages,
  silentTranslationWarn: true,
};

const i18n = new VueI18n(i18nConfig);

import PriceSymbol from "@/components/public/priceSymbol.vue";
Vue.component("PriceSymbol", PriceSymbol);

const checkLogin = (callBack) => {
  try {
    const token = uni.getStorageSync("token");
    if (!token) return false;

    const payloadBase64 = token.split(".")[1];
    const payload = JSON.parse(
      decodeURIComponent(
        atob(payloadBase64)
          .split("")
          .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
          .join("")
      )
    );
    console.log(payload);

    uni.setStorageSync("uid", payload.uid);

    const nowAt = Date.now() / 1000; // 当前时间（秒）

    // console.log(payload.exp, nowAt, "时间");

    if (payload.exp && nowAt > payload.exp) {
      return false;
    }

    return true;
  } catch (e) {
    return false;
  }
};

Vue.prototype.$check = {
  checkLogin,
};

Vue.prototype.$api = api;

// Vue.component("popupBar", popupBar)
// 引入全局uView

//挂在原型
Vue.prototype.$store = store;

Vue.use(uView);
//判断是否是微信环境
Vue.prototype.$toast = function (title, state) {
  uni.showToast({
    title: title,
    icon: "none",
    duration: 2000,
    mask: state || false, //是否显示透明蒙层，防止触摸穿透
  });
};
Vue.prototype.$setUser = function (user) {
  let that = this;
  that.$store.state.user = user;
  that.$setStorageSync("user", user);
};
Vue.prototype.$getUser = async function () {
  let that = this;
  let user = that.$store.state.user;
  if (!user) {
    let res = await uni.getStorageSync("user");
    if (res) {
      //已注册过
      user = res;
    }
  }
  that.$setUser(user);
  return user;
};
// 设置缓存+store
Vue.prototype.$setStorageSync = function (key, value) {
  try {
    uni.setStorageSync(key, value);
  } catch (e) {
    console.log(`设置${key}缓存失败`);
  }
};
// 获取缓存
Vue.prototype.$getStorageSync = function (key, fn) {
  try {
    const value = uni.getStorageSync(key);
    if (value) {
      fn(value);
    }
  } catch (e) {
    // error
  }
};

//页面右上角文字点击，放在这里统一管理
Vue.prototype.$onTap = function () {
  console.log("点击le");
};

//获取系统信息
Vue.prototype.$getSystemInfo = function () {
  uni.getSystemInfo({
    success: function (res) {
      console.log("当前平台？？？？？", res);

      let lang = uni.getStorageSync("__language__");
      if (!lang) {
        if (res.appLanguage.includes("zh")) {
          uni.setStorageSync("__language__", "zh");
        } else {
          uni.setStorageSync("__language__", "en");
        }
      }

      // localStorage.setItem('platform', res.platform);
      let model = res.model;
      Vue.prototype.$isIphoneX =
        model.search("iPhoneX") != -1 || model.search("iPhone X") != -1;

      Vue.prototype.$isIphone = model.platform != "android";
      Vue.prototype.$screenH = res.screenHeight;
    },
  });
};

Vue.prototype.$getSystemInfo();
Vue.prototype.$CryptoJS = CryptoJS;
console.log("$isIphoneX", Vue.prototype.$isIphoneX);

// #ifdef H5
document.addEventListener("UniAppJSBridgeReady", function () {
  Vue.prototype.myUni = uni;
});

// #endif
App.mpType = "app";

Vue.prototype.$m = function (num, decimals = 2) {
  if (num === null || num === undefined || isNaN(num)) return '0.00';
  num = String(num);
  let [intPart, decPart = ''] = num.split('.');
  if (decimals === 0) {
    // 只要整数部分
    return Number(intPart).toLocaleString('en-US');
  }
  if (decPart.length === 0) {
    // 没有小数部分
    return Number(intPart).toLocaleString('en-US');
  }
  if (decPart.length <= decimals) {
    // 小数位不够，原样输出（不补0）
    return Number(intPart).toLocaleString('en-US') + '.' + decPart;
  }
  // 小数位超出，截取（向下取整，不四舍五入）
  decPart = decPart.slice(0, decimals);
  return Number(intPart).toLocaleString('en-US') + '.' + decPart;
};
Vue.prototype.$maxFontSize = function (num) {
  const len = num.length;
  if (len <= 7) return 80;
  if (len <= 9) return 66;
  if (len <= 11) return 56;
  if (len <= 15) return 40;
  return 40; // 如果更长，再缩小
}
const app = new Vue({
  i18n,
  store,
  router,
  ...App,
});
app.$mount();
