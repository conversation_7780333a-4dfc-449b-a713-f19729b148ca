<template>
	<view class="centent">
		<view style="height: 60rpx;"></view>
		<!-- <view class="back" @tap="nav_back()">
			<image
				src="https://cdn.yanjie.art/image/20240118/2c1a5e18aabbae604b5d16b8680993ce_200x200.png"
				mode="widthFix"></image>
		</view> -->
		<u-verification-code class="verification" :seconds="seconds" :change-text="changeText" @end="end" @start="start"
			ref="uCode" @change="codeChange"></u-verification-code>
		<view class="flex_view">
			<view>
				<view class="head_bg">
					<view class="back flex_all" @tap="nav_back()">
						<image
							src="https://pro-oss.pinkwallet.com/image/20250303/52e1ce7cdecd6d6ae3040a8ae821b0d6_21x36.png">
						</image>
					</view>
				</view>
				<!-- 
				<view class="tabbar_view">
					<u-tabs style="width: 100%;" name="cate_name" bg-color="transparent" :bar-style="barStyle"
						:list="tabList" bold inactive-color="#333333" :active-item-style="itemStyle"
						active-color="var(--default-color1)" :current="current" @change="change"></u-tabs>
					<view class="line"></view>
				</view> -->

				<view class="SignUp-title flex-column">
					<text class="SignUp-title-text">Sign Up</text>
					<text class="SignUp-title-text2">Enter below details to create a new account.</text>

				</view>

				<view class="login_view" v-if="current == 0">
					<view class="phone_inp def_input_bg">
						<view class="left">
							+<u-input v-model="region" id="regions" placeholder-style="{color:#A6A6A6;}"
								:disabled="true" :clearable="false" placeholder="" :maxlength="3" input-align="text">
								<!-- <template #right> -->
								<!-- <image
									src=
									mode="widthFix"></image> -->
								<!-- </template> -->
							</u-input>

						</view>
						<view class="right">
							<u-input v-model="phone" type="number" :placeholder='$t("register.PhoneNumber")'
								:maxlength="20" />
						</view>
					</view>
					<view class="code_inp">
						<view class="left">
							<u-input v-model="code" :clearable="false" type="number"
								:placeholder='$t("register.VerificationCode")' :maxlength="6" input-align="text" />
						</view>
						<view class="right" :class="[status == 0 ? ' inactive' : 'active']" @tap="getCode">
							{{ tips }}
						</view>
					</view>
					<!-- <view class="yqm_inp def_input_bg">
						<view class="right">
							<u-input v-model="invitationCode" placeholder="请输入邀请码（可不填）" :maxlength="11" />
						</view>
					</view> -->
				</view>
				<view class="login_view" v-if="current == 1">
					<view class="phone_inp def_input_bg">

						<view class="right">
							<u-input v-model="phone" :placeholder='$t("profile.Email")' :maxlength="30" />
						</view>
					</view>
					<view class="code_inp">
						<view class="left">
							<u-input v-model="code" :clearable="false" type="number"
								:placeholder='$t("register.VerificationCode")' :maxlength="6" input-align="text" />
						</view>
						<view class="right" :class="[status == 0 ? ' inactive' : 'active']" @tap="getCode">
							{{ tips }}
						</view>
					</view>
					<!-- <view class="yqm_inp def_input_bg">
						<view class="right">
							<u-input v-model="invitationCode" placeholder="请输入邀请码（可不填）" :maxlength="11" />
						</view>
					</view> -->
				</view>

				<view class="agreement">
					<view class="check">
						<image @tap="checkAgreement" v-if="isAgreement"
							src="https://cdn.yanjie.art/image/20241023/98b6f94570306e0b3796dd72613fa8f8_24x26.png"
							mode="widthFix">
						</image>
						<image @tap="checkAgreement" v-else
							src="https://cdn.yanjie.art/image/20241023/63ed1abbf0cd92dddc8e5d78de022152_24x24.png"
							mode="widthFix">
						</image>
					</view>
					<view class="text">
						<text class="wehit" @tap="checkAgreement">{{ $t('register.agree') }}</text>
						<text @tap="nav_link('Pink Wallet平台服务协议', 1)">{{ $t('register.registerAgree') }}</text>
						{{ $t('register.and') }}
						<text @tap="nav_link('Pink Wallet法律声明及隐私政策', 2)">{{ $t('register.IBan') }}</text>
					</view>
				</view>
				<view class="submit_login login_lang_input_bg" @tap="login">
					{{ $t('register.title') }}
				</view>
				<view style="margin: 44rpx auto;" class="submit_login_cancel login_lang_input_bg" @tap="Cancel">
					{{ $t('register.cancel') }}
				</view>
				<view class="otherLogin">
					<view @click="auth">
						<!-- <image src="https://cdn.yanjie.art/image/20241023/2128ffaa333939ecf6190e8fed9d5e21_60x60.png"
							mode="widthFix">
						</image>
						<text>手机号登录</text> -->
						<text>{{ $t('register.Google') }}</text>
					</view>
					<view @click="UserLogin">
						<!-- <image src="https://cdn.yanjie.art/image/20241023/102be49e9830adcff18c17220ac58921_120x120.png"
							mode="widthFix">
						</image> -->
						<text>{{ $t('register.PwdLogin') }}</text>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>
<script>
// import { GoogleSignin } from "@react-native-google-signin/google-signin";
// import firebase from "@/utils/firebase";
export default {
	data() {
		return {
			showCat: true,
			current: 0,
			barStyle: {
				'background': '#2b85e4',
				'width': '87rpx',
				'height': '4rpx',
				'border-radius': '0rpx',
				'bottom': '6rpx',
				'z-index': '1'
			},
			itemStyle: {
				'font-size': '32rpx',
				'min-width': '120rpx',
				'z-index': '2'
			},
			tabList: [
				{
					name: "手机号"
				},
				{
					name: "邮箱"
				}
			],
			region: '86',
			phone: '',
			code: '',
			tips: '',
			// refCode: null,
			seconds: 60,
			status: 1,
			changeText: '重新获取(x)',
			isAgreement: true,
			option1: {
				path: 'https://cdn.yanjie.art/h5/xCase/head/head.json',
				loop: false,
				autoplay: true
			},
			invitationCode: "",
			inviteType: ""
		}
	},
	onLoad(options) {
		// Firebase.auth.onAuthStateChanged(user => {
		// if (user) {
		// 	this.user.loggedIn = true;
		// 	this.user.data = user;
		// }
		// else {
		// 	this.user.loggedIn = false;
		// 	this.user.data = {};
		// }
		// })
		// console.log(uni.$u.config.v);
		// this.phone = options.phone
		// this.region = options.region ? options.region : "86"
		// this.invitationCode = options.code
		// this.inviteType = options.inviteType ? options.inviteType : null
		// if (options.url) {
		// 	this.returnUrl = options.url
		// 	console.log(this.returnUrl) 
		// 	if (/^#/.test(options.url)) {
		// 		this.returnUrl = `/h5/${options.url}`;
		// 		console.log(this.returnUrl)
		// 	}
		// }
		// console.log(this.invitationCode)
	},
	onShow() {
		// this.getUserInfo();
	},
	methods: {
		onSuccess() {
		},
		onFail() {
		},
		onRefresh() {
		},
		change(index) {
			this.current = index
		},
		Cancel() {
			this.$Router.pushTab({
				name: "index",
			})
		},
		// getUserInfo() {
		// 	const urlParams = new URLSearchParams(window.location.search);
		// 	const code = urlParams.get('code');
		// 	// console.log(code);
		// 	if (!code) {
		// 		return
		// 	}

		// 	// 用授权码交换访问令牌地址
		// 	const tokenEndpoint = 'https://oauth2.googleapis.com/token';
		// 	const requestBody = new URLSearchParams();
		// 	requestBody.append('code', code);
		// 	// 你的 Google OAuth 客户端 ID
		// 	requestBody.append('client_id',
		// 		'1018175067878-3amrbvbccaq5mdfaf82hn04v7u22j9jp.apps.googleusercontent.com');
		// 	// 你的Google OAuth 客户端密钥
		// 	requestBody.append('client_secret', '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');
		// 	requestBody.append('redirect_uri', 'http://localhost:8086');
		// 	requestBody.append('grant_type', 'authorization_code'); //这些参数在之前配置的有，看前面的代码
		// 	console.log(requestBody, 666);

		// 	fetch(tokenEndpoint, {
		// 		method: 'POST',
		// 		headers: {
		// 			'Content-Type': 'application/x-www-form-urlencoded',
		// 		},
		// 		body: requestBody
		// 	})
		// 		.then(response => response.json())
		// 		.then(data => {

		// 			// 获得token令牌的信息
		// 			const accessToken = data.access_token;
		// 			uni.showToast({
		// 				title: accessToken,
		// 				duration: 2000
		// 			});
		// 			// 调用获取用户信息接口
		// 			fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
		// 				headers: {
		// 					Authorization: `Bearer ${accessToken}`
		// 				}
		// 			})
		// 				.then(response => response.json())
		// 				.then(userInfo => {
		// 					// 获取用户信息
		// 					this.title = userInfo.name
		// 					this.pic = userInfo.picture
		// 					this.email = userInfo.email
		// 					console.log(userInfo)
		// 				})

		// 		})
		// },
		auth() {
			// 你的Google OAuth 客户端 ID
			window.clientId =
				'1018175067878-3amrbvbccaq5mdfaf82hn04v7u22j9jp.apps.googleusercontent.com';
			// 重定向 URI
			window.redirectUri = 'http://localhost:8086';
			// 请求的权限范围，可以根据需求修改
			window.scope = 'email profile';
			// 用于防止跨站请求伪造（CSRF）攻击，可以不设置，可以随心设置
			window.state = '';
			// 授权响应类型，表示要求返回授权码
			window.responseType = 'code';
			// 你的Google OAuth 客户端密钥
			window.clientSecret = '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';
			window.grantType = 'authorization_code';
			//&prompt=login 把它加到window.authUrl的末尾可以让用户每次都需要重新输入账号和密码
			window.authUrl =
				`https://accounts.google.com/o/oauth2/v2/auth?client_id=${window.clientId}&redirect_uri=${window.redirectUri}&scope=${window.scope}&state=${window.state}&response_type=${window.responseType}`;
			//点击Google登录  执行这个方法进行跳转
			window.location.href = window.authUrl
		},
		async googleLogin() {
			console.log(firebase);

			// 检测运行环境
			// if (uni.getSystemInfoSync().platform === "ios" || uni.getSystemInfoSync().platform === "android") {
			// await this.googleLoginApp();
			// } else {
			await firebase.login();
			// }
		},

		// H5 登录（Web 端）
		async googleLoginWeb() {
			console.log(firebase);

			const provider = new firebase.auth.GoogleAuthProvider();

			try {
				const result = await firebase.auth().signInWithPopup(provider);
				this.handleLoginSuccess(result.user);
			} catch (error) {
				console.error("Google 登录失败", error);
				uni.showToast({ title: "登录失败", icon: "none" });
			}
		},

		// App 端登录
		async googleLoginApp() {
			try {
				// 配置 Google Sign-In
				GoogleSignin.configure({
					webClientId: "YOUR_WEB_CLIENT_ID", // Firebase 中的 `client_id`
					offlineAccess: true
				});

				await GoogleSignin.hasPlayServices();
				const { idToken } = await GoogleSignin.signIn();

				// 使用 Google Token 登录 Firebase
				const credential = firebase.auth.GoogleAuthProvider.credential(idToken);
				const result = await firebase.auth().signInWithCredential(credential);

				this.handleLoginSuccess(result.user);
			} catch (error) {
				console.error("Google 登录失败", error);
				uni.showToast({ title: "登录失败", icon: "none" });
			}
		},

		// 处理登录成功
		handleLoginSuccess(user) {
			// uni.setStorageSync("userInfo", {
			// 	uid: user.uid,
			// 	name: user.displayName,
			// 	email: user.email,
			// 	photo: user.photoURL
			// });

			// uni.showToast({ title: `欢迎 ${user.displayName}`, icon: "success" });
			// uni.switchTab({ url: "/pages/index/index" });
		},
		PhoneLogin() {
			this.$Router.push({
				name: "login",
				params: {
					url: this.returnUrl || "",
					code: this.invitationCode,
					phone: this.phone,
				}
			})
		},
		UserLogin() {
			this.$Router.push({
				name: "login",
				params: {
					url: this.returnUrl || "",
					code: this.invitationCode,
					phone: this.phone,
				}
			})
		},
		codeChange(text) {
			this.tips = text;
		},
		getCode() {
			if (this.$refs.uCode.canGetCode) {
				// 模拟向后端请求验证码

				if (this.current == 0) {
					if (this.$u.test.mobile(this.phone)) {
						uni.showLoading({
							title: this.$t('register.GetingCode')
						})
						this.sendPhoneVerifyCode()
					} else {
						this.$u.toast(this.$t('register.PhonePlz'));
					}
				} else {
					if (this.$u.test.email(this.phone)) {
						uni.showLoading({
							title: this.$t('register.GetingCode')
						})
						this.sendEmailVerifyCode()
					} else {
						this.$u.toast(this.$t('register.PhonePlz'));
					}
				}



			} else {
				this.$u.toast(this.$t('register.Later'));
			}
		},
		end() {
			this.status = 0
		},
		start() {
			this.status = 1
		},
		checkAgreement() {
			this.isAgreement = !this.isAgreement
			console.log(this.isAgreement)
		},

		async sendEmailVerifyCode() {
			// let res = await this.$api.sendPhoneVerifyCode({
			let res = await this.$api.sendMailCaptcha({
				email: this.phone
			});
			if (res.code == 200) {
				this.$u.toast(this.$t("register.Send"));
				// 通知验证码组件内部开始倒计时
				this.$refs.uCode.start();
			} else {
				if (res.code == 110001) {
					this.$u.toast(res.msg);
					setTimeout(() => {
						this.$Router.push({
							name: 'login',
							params: {
								phone: this.phone,
								region: this.region,
								code: this.invitationCode,
								url: this.returnUrl || ""
							}
						})
					}, 2000)
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 3000
					});
				}
			}
		},
		async sendPhoneVerifyCode() {
			let res = await this.$api.sendPhoneVerifyCode({
				// let res = await this.$api.sendMailCaptcha({
				area: '+' + this.region,
				phoneNumber: this.phone
				// email: this.phone
			});
			if (res.code == 200) {
				this.$u.toast(this.$t("register.Send"));
				// 通知验证码组件内部开始倒计时
				this.$refs.uCode.start();
			} else {
				if (res.code == 110001) {
					this.$u.toast(res.msg);
					setTimeout(() => {
						this.$Router.push({
							name: 'login',
							params: {
								phone: this.phone,
								region: this.region,
								code: this.invitationCode,
								url: this.returnUrl || ""
							}
						})
					}, 2000)
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 3000
					});
				}
			}
		},
		async login() {
			if (this.phone == "") {
				this.$u.toast(this.$t('register.PhonePlz'));
			} else if (this.code == "") {
				this.$u.toast(this.$t('register.CodePlz'));
			} else if (!this.isAgreement) {
				this.$u.toast(this.$t('register.AgreementPlz'));
			} else {
				let res = await this.$api.register({
					registerType: this.current == 0 ? 'phone' : 'email',
					area: '+' + this.region,
					captcha: this.code,
					...(this.current == 1 ? { email: this.phone } : { phoneNumber: this.phone }),

				});
				if (res.code == 200) {
					uni.setStorageSync("token", res.result?.accessToken)
					this.$u.toast(this.$t('register.success'));
					// #ifdef APP-PLUS
					setTimeout(() => {
						this.$Router.push({
							name: 'login',
							params: {
								isRegister: true,
								code: this.invitationCode,
							},
							code
						})
					}, 300);
					// #endif
					// #ifdef H5
					if (this.returnUrl) {
						window.location.href = this.returnUrl
					} else {
						setTimeout(() => {
							this.$Router.push({
								name: 'login',
								// params: {
								// 	isRegister: true,
								// 	code: this.invitationCode,
								// },
							})
						}, 300);
					}
					// #endif
				} else {
					// if (res.status.code == 1005) {
					// 	this.$Router.push({
					// 		name: 'register',
					// 		params: {
					// 			phone: this.phone,
					// 			url: this.returnUrl || ""
					// 		}
					// 	})
					// } else
					// {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 3000
					});
					// }

				}
			}

		},
		nav_back() {
			this.$Router.back()
		},
		nav_link(title, index) {
			if (index === 1) {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com/link/#/pages/index/yanjiePlatformServicesAgreement"
					}
				})
			} else {
				this.$Router.push({
					name: "generalAgreement",
					params: {
						title: title,
						link: "https://www.nftcn.com/link/#/pages/index/yanjiePrivacyPolicy"
					}
				})
			}
		},
	}
}
</script>
<style lang="scss">
page {
	width: 100%;
	min-height: 100%;
	// background-image: url(https://cdn.yanjie.art/image/20241023/6debfead1c8d13f6d6223c7698cb225c_750x1624.png);
	background-size: 100%;
	background-repeat: no-repeat;

}

.SignUp-title {
	margin-top: 30rpx;
	.SignUp-title-text {
		font-family: Gilroy-Bold;
		font-weight: bold;
		font-size: 30*2rpx;
		line-height: 36*2rpx;
		color: #000;
	}

	.SignUp-title-text2 {
		margin-top: 20rpx;
		font-family: Gilroy-Medium;
		font-weight: 400;
		font-size: 16*2rpx;
		line-height: 19.2*2rpx;
		color: #686868;
	}
}

.tabbar_view {
	width: 100%;
	margin-top: 50rpx;

	.line {
		margin: -10rpx 40rpx 0 40rpx;
		height: 2rpx;
	}

	// border-bottom: 2rpx solid #EDEDED;
}

.centent {
	margin: 0 32rpx;
	min-height: 100vh;
}

.submit_login_cancel {
	height: 90rpx;
	width: 540rpx;
	margin: 80rpx auto 0 auto;
	font-size: 34rpx;
	font-weight: 400;
	font-size: 30rpx;
	color: #121221;
	display: flex;
	border-radius: 16rpx;
	align-items: center;
	justify-content: center;
	// background: rgba(176, 255, 47, 0.9);
	background: #e6f0ff;
}

::v-deep .u-scroll-box {
	display: flex !important;
}

::v-deep .u-tab-bar {
	margin-left: -20rpx !important;
}

::v-deep .u-input__input {
	font-weight: 400;
	font-size: 28rpx;
	color: #121212 !important;

	.uni-input-placeholder {
		color: rgba(0, 0, 0, 0.5) !important;
	}
}

#regions::after {
	content: '';
	// 定义元素位置
	margin: 30rpx 20rpx 0 5rpx;
	// 定义元素宽高
	width: 26rpx;
	height: 14rpx;
	background-image: url("https://cdn.yanjie.art/image/20241023/aa7d518cd34ba2f6ec9230c5eb5a4c42_52x28.png");
	background-size: 100% 100%;
}

.otherLogin {
	// position: fixed;
	// bottom: 60rpx;
	display: flex;
	// align-items: flex-end; /* 将子元素对齐到底部 */
	justify-content: space-between;
	width: 100%;
	margin-top: 100rpx;

	>view {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		// margin: 0 110rpx;
		margin: 0 47rpx;

		image {
			width: 60rpx;
			height: 60rpx;
		}

		text {
			display: block;
			width: 100%;
			font-weight: 400;
			font-size: 22rpx;
			// color: #D8B662;
			color: #121212;
			margin-top: 20rpx;
		}
	}
}


.head_bg {
	// height: 600rpx;
	// padding-top: 200rpx;
	// position: relative;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	z-index: 0;

	.back {
		// position: absolute;
		left: 40rpx;
		background: #F1F1F1;
		border-radius: 50%;
		width: 100rpx;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		/* #ifdef APP */
		top: 80rpx;
		/* #endif */
		/* #ifdef H5 */
		top: 40rpx;

		/* #endif */
		image {
			// padding: 25rpx;
			width: 15rpx;
			height: 25rpx;
		}
	}

	.logo {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 400rpx;
		border: 2rpx solid #ccc;

		image {
			height: 400rpx;
		}
	}
}

.login_view {
	padding: 0rpx 80rpx;
	margin-top: 50rpx;

	.yqm_inp {
		height: 90rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0rpx 40rpx;
		margin-top: 50rpx;

		.right {
			width: 100%;

			.verification {
				width: 100%;
				height: 90rpx;
			}

		}
	}

	.phone_inp {
		height: 90rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		padding: 0rpx 40rpx;
		background-color: #fff;
		border-radius: 10rpx;
		// box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;

		.left {
			color: #121212;
			width: 140rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			input {
				color: #000;
			}
		}

		.right {
			input {
				color: #000;
			}

			.verification {
				width: 100rpx;
				height: 90rpx;
			}

		}
	}

	.code_inp {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 50rpx;

		.left {
			width: 350rpx;
			height: 90rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			padding-left: 40rpx;
			background-color: #fff;
			border-radius: 10rpx;
			box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;

			// background-image: url(https://cdn.yanjie.art/image/20241023/3ac7836cf4e48ca67901b3a7170e616c_600x180.png);
			background-size: 100%;
		}

		.right {
			width: 220rpx;
			height: 90rpx;
			background-color: #fff;

			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			font-weight: 400;
			color: #e6f0ff;
			background: #2b85e4;
			border-radius: 10rpx;
			// &.active {
			// 	background-image: url(https://cdn.yanjie.art/image/20241023/440a49818a3ea497ed29d11f48fcdfbf_220x90.png);
			// 	background-size: 100%;
			// }

			// &.inactive {
			// 	background-image: url(https://cdn.yanjie.art/image/20241023/d6b3804438563c7aa609712ddbc026ec_220x90.png);
			// 	background-size: 100%;
			// }
		}
	}
}

.agreement {
	padding: 0rpx 80rpx;
	margin-top: 30rpx;
	display: flex;

	.check {
		width: 30rpx;
		margin-right: 20rpx;

		image {
			width: 30rpx;
		}
	}

	.text {
		font-size: 24rpx;
		// color: rgba(255, 255, 255, 0.4);
		// color: #000;
		color: rgba(0, 0, 0, 0.5);

		.wehit {
			// color: rgba(255, 255, 255, 0.4);
			color: rgba(0, 0, 0, 0.5);

		}

		text {
			color: rgba(216, 182, 98, 0.4);
			text-decoration: underline;
		}
	}
}

.submit_login {
	height: 90rpx;
	width: 540rpx;
	margin: 80rpx auto 0 auto;
	font-size: 34rpx;
	font-weight: 400;
	font-size: 30rpx;
	color: #e6f0ff;
	display: flex;
	border-radius: 16rpx;
	align-items: center;
	justify-content: center;
	// background: rgba(176, 255, 47, 0.9);
	background: #2b85e4;
}
</style>