<template>
	<view class="centent">
		<view class="barHeight"></view>
		<!-- <u-verification-code class="verification" :seconds="seconds" :change-text="changeText" @end="end" @start="start"
			ref="uCode" @change="codeChange"></u-verification-code> -->
		<view class="flex_view">
			<view>
				<view class="head_bg">
					<view class="language" @click.stop="toggleRotateLanguage">
						<image class="global"
							src="https://pro-oss.pinkwallet.com/image/********/ca536e196146966e840ad354eb75a102_64x65.png" />
						<text class="lang">{{ lang }}</text>
						<image class="down arrow" :class="{ rotated: isLangRotated }"
							src="https://pro-oss.pinkwallet.com/image/********/e2be0b1ab8b96b6009c5e1cb8459ccb2_56x57.png" />

						<transition name="expand-slide">
							<view class="helpoption" v-show="showLang">
								<view v-for="(item, index) in LangList" :key="index" class="Roptions"
									@click.stop="SetLang(item)">
									<text>{{ item.name }}</text>
								</view>
							</view>
						</transition>
					</view>
				</view>
				<view class="SignUp-title flex-column">
					<text class="SignUp-title-text">{{ $t("Auth.SignIn") }}</text>
					<text class="SignUp-title-text2">{{ $t("Auth.SignIn.Description") }}</text>

				</view>
			</view>
		</view>
		<view class="register-page">
			<view class="register-container">
				<view class="input-group">
					<view class="input-title">{{ $t("Auth.SignIn.PhoneNumber") }}</view>
					<view class="phone-input">

						<transition name="expand-slide">
							<view class="helpoption" v-show="showRegion">
								<view v-for="(item, index) in RightOption" :key="index" class="Roptions"
									@click="handleRight(item)">
									<image class="Rimg" :src="item.image"></image>
									<text>{{ item.label }}</text>
								</view>
							</view>
						</transition>
						<!-- <view class="country-code flex_all" @click="toggleRotate">
							<text>+{{ region }}</text>
							<image class="arrow" :class="{ rotated: isRotated }"
								src="https://pro-oss.pinkwallet.com/image/********/e12a5adca8ec7fc0c836cfb822f1b7ee_16x8.png" />
						</view> -->
						<!-- Enter your Email -->
						<u-input maxlength="30" type="text" v-model="phoneNumber" @input="onPhoneInput" placeholder=""
							height="102" class="phone-number-input" />
					</view>
				</view>
				<view class="switchpwd">
					<u-switch size="48" active-color="#008E28" v-model="ispwd" />
					<text>{{ $t("Auth.SignIn.VerificationCodeLogin") }}</text>
				</view>

				<view class="input-group" v-if="ispwd">
					<view class="input-title">{{ $t("Auth.SignUp.VerificationCode") }}</view>
					<view class="verification-input">
						<!-- Enter verification code -->
						<u-input maxlength="6" type="text" v-model="verificationCode" @input="onVerificationCodeInput"
							placeholder="" :clearable="false" height="102" class="verification-code-input" />
						<view class="get-code-btn flex_all" :disabled="isGettingCode" @click="getVerificationCode">
							{{ isGettingCode ? `${countdown}s` : $t("title.startText") }}
						</view>
					</view>
				</view>

				<view class="input-group" v-if="!ispwd">
					<view class="input-title">{{ $t("Auth.SignIn.Password") }}</view>
					<view class="verification-input">
						<!-- Enter Password -->
						<u-input maxlength="30" @keyup.enter="handleSignUp" @input="onPasswordInput" type="password"
							:clearable="false" v-model="Password" placeholder="" height="102"
							class="verification-code-input" />
						<!-- <view class="get-code-btn flex_all" :disabled="isGettingCode" @click="getVerificationCode">
							{{ isGettingCode ? `${countdown}s` : 'Get Code' }}
						</view> -->
					</view>
				</view>
				<view class="verification-code-section" @click="nav_to('forget', phoneNumber)">{{
					$t("Auth.SignIn.ForgotPassword") }}</view>
				<!-- <view class="agreement-section">
					<label class="agreement-checkbox">
						<input type="checkbox" v-model="agreementAccepted" />
						<text class="checkbox-custom"></text>
						<text class="agreement-text">
							I agree to the
							<text style="color: #008E28;margin: 0 2rpx;">"Registration Agreement"</text>
							and
							<text style="color: #008E28;margin: 0 2rpx;">"IBAN Account Opening Agreement"</text>
						</text>
					</label>
				</view> -->
				<!-- :disabled="!isFormValid" -->
				<u-button hover-class="none" class="signup-btn flex_all !rounded-button" @click="handleSignUp">
					{{ $t("Auth.SignIn.Button") }}
				</u-button>
				<view class="divider">
					<text> {{ $t("Auth.SignIn.SignInWith") }}</text>
				</view>
				<button class="google-btn flex_all !rounded-button" @click="auth">
					<image
						src="https://pro-oss.pinkwallet.com/image/********/5875ec4a914cddfd7dd4ab5abf3b9d28_26x26.png" />
					<text>Google</text>
				</button>



				<view class="form-item flex_x" style="margin-top: 60rpx;" @click="goChat">
					<image class="icon_serve"
						src="https://pro-oss.pinkwallet.com/image/********/6bdf5f1ed5d632b04ba56f8d89b3c81b_81x80.png" />
					<text class="label">{{ $t("title.LiveSupport") }}</text>
				</view>
				<!-- <button class="google-btn flex_all !rounded-button" style="margin-top: 32rpx;" @click="faceidLogin">
					<image
						src="https://pro-oss.pinkwallet.com/image/********/03c0c2673deadc0b4798266ba36416a8_88x88.png" />
					<text>Face ID</text>
				</button> -->
			</view>
		</view>

		<view class="bottom-text flex_all" :class="{ fixed: isFixedBottom }"
			:style="{ marginTop: isFixedBottom ? '0' : '194rpx' }">
			{{ $t("Auth.SignIn.NoAccount") }}
			<a href="#" style="color: #000;margin-left: 12rpx;" @click="nav_to('register')">{{ $t("Auth.SignIn.SignUp")
			}}</a>
		</view>
	</view>
</template>

<script>
import store from "@/store"
const { VUE_APP_URL, VUE_APP_CLIENT_ID, VUE_APP_CLIENT_SECRET } = process.env;
import { removeAllSpaces } from '@/utils/utils';
export default {
	data() {
		return {
			JYGoogleSignin: null,
			isIOSApp: false,
			returnUrl: "",
			showLang: false,
			LangList: [
				{
					name: "english",
					value: 'en'
				},
				{
					name: "简体中文",
					value: 'zh'
				},
				{
					name: "繁體中文",
					value: 'zhhant'
				}
			],
			Password: "",
			// lang: uni.getStorageSync('__language__'),
			lang: "",
			isLangRotated: false,
			ispwd: false,
			region: "86",
			showRegion: false,
			phoneNumber: '',
			verificationCode: '',
			agreementAccepted: true,
			isGettingCode: false,
			countdown: 59,
			timer: null,
			isRotated: false, // 控制旋转状态
			RightOption: [
				{
					value: '1',
					label: '1',
					image: 'https://flagcdn.com/16x12/us.png'
				},
				{
					value: '86',
					label: '86',
					image: 'https://flagcdn.com/16x12/cn.png'
				},
				{
					value: '44',
					label: '44',
					image: 'https://flagcdn.com/16x12/gb.png'
				},
				{
					value: '81',
					label: '81',
					image: 'https://flagcdn.com/16x12/jp.png'
				},
				{
					value: '91',
					label: '91',
					image: 'https://flagcdn.com/16x12/in.png'
				},
				{
					value: '33',
					label: '33',
					image: 'https://flagcdn.com/16x12/fr.png'
				},
				{
					value: '61',
					label: '61',
					image: 'https://flagcdn.com/16x12/au.png'
				}
			],
			currentIndex: null, // 当前打开的项
			isFixedBottom: true,
			link: "../../../static/serve.html",
		}
	},
	onReady() {
		this.$nextTick(() => {
			this.checkHeight()
		})
	},
	computed: {
		isFormValid() {
			return this.phoneNumber &&
				this.verificationCode &&
				this.agreementAccepted;
		}
	},
	onLoad(e) {
		this.lang = uni.getStorageSync('__language__') == 'zh' ? '简体中文' : uni.getStorageSync('__language__') == 'en' ? 'english' : '繁體中文'
		if (e.email || e.region) {
			this.phoneNumber = e.email
			this.region = e.region
		}
		uni.setNavigationBarTitle({
			title: this.$t("page.login") // 切换语言后重新设置标题
		})
		// this.JYGoogleSignin = uni.requireNativePlugin('JY-GoogleSignin');
		// console.log(this.JYGoogleSignin);

		// const systemInfo = uni.getSystemInfoSync();
		// const isApp = systemInfo.platform === 'ios' || systemInfo.platform === 'android';
		// const isIOS = systemInfo.platform === 'ios';
		// // 只有在 App + iOS 才为 true
		// this.isIOSApp = isApp && isIOS;
		// if(this.isIOSApp){
		// this.faceidLogin()
		// }
		// #ifdef APP-PLUS
		setTimeout(() => {
			// console.log('当前包名:', plus.runtime.identifier);
		}, 500); // 保证 plus 已加载
		// #endif
		// this.GoogleIos()
	},
	onShow() {
		// 获取当前登录用户信息
		// #ifdef H5
		this.getUserInfo();
		// #endif
	},
	methods: {
		removeAllSpaces,
		onPhoneInput(value) {
			this.phoneNumber = this.removeAllSpaces(value)
		},
		onVerificationCodeInput(value) {
			this.verificationCode = this.removeAllSpaces(value);
		},
		onPasswordInput(value) {
			this.Password = this.removeAllSpaces(value);
		},
		goChat() {
			this.$Router.push({
				name: 'webView',
				params: {
					url: this.link,

				}
			})
		},
		checkHeight() {
			// 获取整个页面容器高度
			const query = uni.createSelectorQuery().in(this)
			query.select('.centent').boundingClientRect()
			query.selectViewport().boundingClientRect()
			query.exec(res => {
				const pageHeight = res[0]?.height
				const screenHeight = res[1]?.height
				console.log(pageHeight, screenHeight);
				// this.isFixedBottom = pageHeight < screenHeight
				this.isFixedBottom = screenHeight >= 780
			})
		},
		GoogleIos() {
			console.log('this.GoogleIos');

			this.JYGoogleSignin.jy_init({
				//  安卓的client_id应该是谷歌开发者后台默认Web应用的；iOS的client_id应该是谷歌开发者后台iOS对应的
				client_id: '1018175067878-8snvhj6qvqu6rhvc6bpdmme66t87sikb.apps.googleusercontent.com'
			}, res => {
				//    这里不会有返回数据
			})

			this.JYGoogleSignin.jy_startLogin(res => {
				//  这里会返回登录的结果，如果errorCode = 1，代表错误，可检查msg返回的数据判断；如果errorCode = 0，代表成功，也会在data里面返回登录数据
				console.log(JSON.stringify(res));
				uni.showToast({
					icon: 'none',
					title: JSON.stringify(res)
				})
			})
		},
		faceidLogin() {
			uni.startSoterAuthentication({
				requestAuthModes: ['facial'],
				challenge: '123456',
				authContent: '请用FaceID解锁',
				success(res) {
					console.log(res);
				},
				fail(err) {
					console.log(err);
				},
				complete(res) {
					console.log(res);
				}
			})
			return
			// 判断设备是否支持 Face ID
			uni.checkIsSupportSoterAuthentication({
				success(res) {
					console.log(res);


					// if (res.supportMode.includes('FACIAL')) {
					// 	// 支持人脸识别，开始认证
					// 	uni.startSoterAuthentication({
					// 		requestAuthModes: ['FACIAL'],
					// 		challenge: 'login_challenge', // 随机字符串
					// 		authContent: '请验证您的面容以登录',
					// 		success: function (res) {
					// 			console.log('认证成功', res);
					// 			// 登录逻辑处理
					// 		},
					// 		fail: function (err) {
					// 			console.error('认证失败', err);
					// 		}
					// 	});
					// } else {
					// 	uni.showToast({ title: '不支持人脸识别', icon: 'none' });
					// }
				},
				fail(err) {
					console.error('检查支持失败', err);
				}
			})
		},
		auth() {
			const systemInfo = uni.getSystemInfoSync();
			const isIOS = systemInfo.platform === 'ios';
			// #ifdef APP-PLUS
			// if (isIOS) {
			this.GoogleIos()
			// #endif
			// } else {

			// #ifdef H5

			// 你的Google OAuth 客户端 ID
			window.clientId = VUE_APP_CLIENT_ID
			// 重定向 URI
			window.redirectUri = VUE_APP_URL;
			// 请求的权限范围，可以根据需求修改
			window.scope = 'email profile';
			// 用于防止跨站请求伪造（CSRF）攻击，可以不设置，可以随心设置
			window.state = '';
			// 授权响应类型，表示要求返回授权码
			window.responseType = 'code';
			// 你的Google OAuth 客户端密钥
			window.clientSecret = VUE_APP_CLIENT_SECRET;
			window.grantType = 'authorization_code';
			//&prompt=login 把它加到window.authUrl的末尾可以让用户每次都需要重新输入账号和密码
			window.authUrl =
				`https://accounts.google.com/o/oauth2/v2/auth?client_id=${window.clientId}&redirect_uri=${window.redirectUri}&scope=${window.scope}&state=${window.state}&response_type=${window.responseType}`;
			//点击Google登录  执行这个方法进行跳转
			window.location.href = window.authUrl
			// }
			// #endif


		},
		getUserInfo() {
			console.log(123);

			const urlParams = new URLSearchParams(window.location.search);
			const code = urlParams.get('code');
			console.log(code);

			// console.log(code);
			if (!code) {
				return
			}

			// 用授权码交换访问令牌地址
			const tokenEndpoint = 'https://oauth2.googleapis.com/token';
			const requestBody = new URLSearchParams();
			requestBody.append('code', code);
			// 你的 Google OAuth 客户端 ID
			requestBody.append('client_id', VUE_APP_CLIENT_ID);
			// 你的Google OAuth 客户端密钥
			requestBody.append('client_secret', VUE_APP_CLIENT_SECRET);
			requestBody.append('redirect_uri', VUE_APP_URL);
			requestBody.append('grant_type', 'authorization_code'); //这些参数在之前配置的有，看前面的代码
			console.log(requestBody, 666);

			fetch(tokenEndpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/x-www-form-urlencoded',
				},
				body: requestBody
			})
				.then(response => response.json())
				.then(data => {
					// 获得token令牌的信息
					const accessToken = data.access_token;
					this.$api.signUpAndSignIn({
						signType: 'oauth_google',
						token: data.id_token
					}).then(res => {
						if (res.code == 200) {
							uni.setStorageSync("token", res.result)
							this.$u.toast(res.msg);

							// #ifdef APP-PLUS
							setTimeout(() => {
								this.$Router.pushTab({
									name: 'index',
								})
							}, 300);
							// #endif
							// #ifdef H5
							// if (this.returnUrl) {
							// 	window.location.href = this.returnUrl
							// } else {

							setTimeout(() => {
								this.$Router.pushTab({
									name: 'index',
								})
							}, 300);
							// }
							// #endif
						}
					})


					// 调用获取用户信息接口
					fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
						headers: {
							Authorization: `Bearer ${accessToken}`
						}
					})
						.then(response => response.json())
						.then(userInfo => {
							// 获取用户信息
							this.title = userInfo.name
							this.pic = userInfo.picture
							this.email = userInfo.email
							console.log(userInfo)

						})

				})
		},
		SetLang(item) {
			this.$i18n.locale = item.value
			uni.setStorageSync("__language__", item.value)
			this.lang = item.name
			this.showLang = false
			setTimeout(() => {
				// #ifdef H5
				location.reload(); // H5 用浏览器刷新
				// #endif

				// #ifdef APP-PLUS
				// const pages = getCurrentPages();
				// const currentPage = pages[pages.length - 1];
				// const route = currentPage.route;
				// const options = currentPage.options || {};

				// const queryString = Object.entries(options).map(([k, v]) => `${k}=${v}`).join('&');
				// const url = `/${route}${queryString ? '?' + queryString : ''}`;

				// uni.reLaunch({
				// 	url
				// });
				// #endif
			}, 100);
			// this.setLgs(item.value)
		},
		nav_to(e, email) {
			this.$Router.push({
				name: e,
				params: {
					email
				}
			})
		},
		handleRight(item) {
			this.region = item.value
			this.showRegion = false // 切换显示状态
		},

		toggleRotate() {
			this.showRegion = !this.showRegion; // 切换显示状态
			this.isRotated = !this.isRotated; // 点击时切换状态
		},
		toggleRotateLanguage() {
			this.showLang = !this.showLang;
			this.isLangRotated = !this.isLangRotated; // 
		},
		nav_back() {
			this.$Router.back()
		},
		getVerificationCode() {
			if (!this.phoneNumber) {
				uni.showToast({
					title: this.$t("Please.email"),
					icon: 'none',
					duration: 2000
				});
				return
			} else if (!/^[\w.-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$/.test(this.phoneNumber)) {
				uni.showToast({
					title: this.$t("Please.erroremail"),
					icon: 'none',
					duration: 2000
				});
				return
			}
			if (this.isGettingCode) return;
			this.isGettingCode = true;
			this.countdown = 59;
			this.timer = setInterval(() => {
				if (this.countdown > 0) {
					this.countdown--;
				} else {
					this.isGettingCode = false;
					clearInterval(this.timer);
				}
			}, 1000);
			this.sendEmailVerifyCode()
		},
		async sendEmailVerifyCode() {
			// let res = await this.$api.sendPhoneVerifyCode({
			let res = await this.$api.sendMailCaptcha({
				email: this.phoneNumber,
				verificationCodeTypeEnum: "LOGIN",
			});
			if (res.code == 200) {
				this.$u.toast(this.$t("register.Send"));
				// 通知验证码组件内部开始倒计时
				this.$refs.uCode.start();
			} else {
				if (res.code == 110001) {
					this.$u.toast(res.msg);
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 3000
					});
				}
			}
		},
		async handleSignUp() {
			if (!this.phoneNumber) {
				return uni.showToast({
					title: this.$t("Please.email"),
					icon: 'none',
					duration: 3000
				});
				return
			}
			if (this.ispwd) {
				if (!this.verificationCode) {
					return uni.showToast({
						title: this.$t("Please.verification"),
						icon: 'none',
						duration: 3000
					});
					return
				}
			} else {
				if (!this.Password) {
					return uni.showToast({
						title: this.$t("Please.pwd"),
						icon: 'none',
						duration: 3000
					});
					return
				}
			}
			if (store.state.shouldVibrate) {
				uni.vibrateShort()
			}
			// if (!this.isFormValid) return;
			let res = await this.$api.login({
				// loginType: this.current == 0 ? 'phone' : 'email',
				loginType: 'email',
				verifyType: this.ispwd == true ? 1 : 0,
				area: '+' + this.region,
				...(this.ispwd == true ? { captcha: this.verificationCode } : { password: this.Password }),
				// ...(this.current == 1 ? { email: this.phone } : { phoneNumber: this.phone }),
				email: this.phoneNumber,
			});
			if (res.code == 200) {
				// uni.setStorageSync("uid", payload.uid);

				const payloadBase64 = res.result.split(".")[1];
				const payload = JSON.parse(
					decodeURIComponent(
						atob(payloadBase64)
							.split("")
							.map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
							.join("")
					)
				);
				uni.setStorageSync("uid", payload.uid)

				// console.log(payload);
				uni.setStorageSync("token", res.result)
				this.$u.toast(res.msg);
				// #ifdef APP-PLUS
				setTimeout(() => {
					this.$Router.pushTab({
						name: 'Home',
					})
				}, 300);
				// #endif
				// #ifdef H5
				if (this.returnUrl) {
					window.location.href = this.returnUrl
				} else {
					setTimeout(() => {
						this.$Router.pushTab({
							name: 'Home',
						})
					}, 300);
				}
				// #endif
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none',
					duration: 3000
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.centent {
	height: 100vh;
	padding-bottom: 168rpx; // 留出底部空隙避免内容被覆盖

	.fixed {
		position: fixed;
		bottom: 0;
	}

	.bottom-text {
		// margin-top: 194rpx;
		// position: fixed;
		// bottom: 0;
		height: 84*2rpx;
		width: 100vw;
		// opacity: 0.3;
		border-top-left-radius: 30*2rpx;
		border-top-right-radius: 30*2rpx;
		background: rgba(217, 214, 214, .3);

		font-family: Gilroy-SemiBold;
		font-weight: 400;
		font-size: 16*2rpx;
		line-height: 19.2*2rpx;
		color: #666666;
	}

	.register-page {
		padding: 24px 32rpx;

		.register-container {
			// max-width: 375px;
			// margin: 0 auto;
		}

		.switchpwd {
			display: flex;
			align-items: center;
			margin: 0 0 40rpx 0;
			font-family: Gilroy-Medium;
			font-weight: 400;
			font-size: 14*2rpx;
			line-height: 17*2rpx;
			color: #000;

			text {
				margin-left: 20rpx;
			}
		}

		.input-group {
			margin-bottom: 32rpx;

			.input-title {
				font-family: Gilroy-SemiBold;
				font-weight: 400;
				font-size: 16*2rpx;
				line-height: 19.2*2rpx;
				color: #000;
				margin-bottom: 20rpx;
			}

			.phone-input {
				display: flex;
				border-radius: 8px;
				// overflow: hidden;
				position: relative;

				.helpoption {
					width: 85*2rpx;
					transition: transform 0.3s ease, opacity 0.3s ease;
					transform-origin: top;
					/* 设置变换的起点为顶部 */
					z-index: 11;
					position: absolute;
					top: 122rpx;
					left: 0;
					box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

					// background-color: rgba(0, 0, 0, .5);
					background: #fff;
					border-radius: 16*2rpx;
					padding: 16*2rpx;
					opacity: 1;
					//padding: 100rpx;
					// height: 446rpx;
					display: flex;
					align-items: flex-start;
					flex-direction: column;

					&.collapse {
						transform: scaleY(0) translateY(-100%);
						/* 缩小至0，并向上移动 */
						opacity: 0;
					}

					&.expand {
						transform: scaleY(1) translateY(0%);
						/* 恢复到正常大小，并位置恢复 */
						opacity: 1;

					}

					>view {

						padding: 15rpx 0;
						display: flex;
						align-items: center;

						image {
							width: 40rpx;
							height: 30rpx;
						}

						text {
							margin-left: 20rpx;
							display: block;
							font-family: Gilroy-Bold;
							font-weight: 400;
							font-size: 16*2rpx;
							line-height: 19.2*2rpx;
							color: #000;
						}
					}
				}

				.country-code {
					// overflow: hidden;
					width: 85*2rpx;
					height: 51*2rpx;
					border-radius: 10*2rpx;
					border-width: 2rpx;
					// padding: 16px;
					border: 2rpx solid #999999;

					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 16*2rpx;


					display: flex;
					align-items: center;



					// padding: 12px 16px;
					// border-right: 1px solid #e5e7eb;
					// font-size: 14px;
					// color: #374151;
					// gap: 8px;


					.rotated {
						transform: rotate(180deg);
						/* 旋转180度 */
					}

					image {
						margin-left: 22rpx;
						width: 28rpx;
						height: 14rpx;
					}
				}

				.phone-number-input {
					border: 2rpx solid #999999 !important;
					height: 51*2rpx;
					border-radius: 10*2rpx;
					// margin-left: 20rpx;
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 16*2rpx;
					color: #000;
					padding: 0 32rpx !important;
				}
			}

			.verification-input {
				height: 51*2rpx;
				display: flex;
				border: 2rpx solid #999999 !important;
				border-radius: 10*2rpx;
				padding: 0 32rpx !important;
				overflow: hidden;
				position: relative;

				.verification-code-input {
					flex: 1;
					padding: 12px 16px;
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 16*2rpx;
					color: #000;
				}

				.get-code-btn {
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 14*2rpx;
					color: #fff;
					right: 14rpx;
					top: 8rpx;
					position: absolute;
					width: 110*2rpx;
					height: 42*2rpx;
					border-radius: 10*2rpx;
					background: #008E28;

					&:disabled {
						background: #FF82A3;
					}
				}
			}
		}

		.verification-code-section {
			margin-top: -20rpx;
			font-family: Gilroy-SemiBold;
			font-weight: 400;
			font-size: 14*2rpx;
			line-height: 22.4*2rpx;
			text-align: right;
			color: #666;
		}

		.agreement-section {
			margin: 24px 0;

			.agreement-checkbox {
				display: flex;
				align-items: flex-start;
				gap: 8px;
				cursor: pointer;

				input {
					display: none;
				}

				.checkbox-custom {
					width: 20px;
					height: 20px;
					border: 2px solid #e5e7eb;
					border-radius: 4px;
					position: relative;
				}

				input:checked+.checkbox-custom::after {
					content: '\2714';
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					color: #008E28;
				}

				.agreement-text {
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 14*2rpx;
					line-height: 22.4*2rpx;
					color: #333333;
				}
			}
		}

		.signup-btn {
			margin-top: 52rpx;
			width: 100%;
			background-color: #FF82A3;
			color: white;
			border: none;
			font-size: 16*2rpx;
			border-radius: 64*2rpx;
			height: 100rpx;
			font-family: Gilroy-Bold;
			font-weight: 400;

			&:disabled {
				// background-color: #e5e7eb;
			}
		}

		.divider {
			text-align: center;
			position: relative;
			margin: 42rpx 0;

			&::before,
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				width: calc(50% - 100rpx);
				height: 2rpx;
				background-color: #e5e7eb;
			}

			&::before {
				left: 0;
			}

			&::after {
				right: 0;
			}

			text {
				padding: 0 7rpx;
				color: #6b7280;
				font-family: Gilroy-SemiBold;
				font-weight: 400;
				font-size: 14*2rpx;
				line-height: 22.4*2rpx;
				text-align: center;

			}
		}

		.form-item {
			.label {
				margin-left: 20rpx;
				font-family: Gilroy-Medium;
				font-weight: 400;
				font-size: 16*2rpx;
				// line-height: 19.2*2rpx;
				letter-spacing: 0%;
				color: #FF82A3;
			}

			.icon_serve {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.google-btn {
			height: 50*2rpx;
			border-radius: 64*2rpx;
			border-width: 1*2rpx;

			width: 100%;
			// padding: 14px;
			background-color: white;
			border: 2rpx solid #e5e7eb;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 20rpx;
			color: #374151;
			font-family: Gilroy-SemiBold;
			font-weight: 400;
			font-size: 32rpx;

			image {
				width: 52rpx;
				height: 52rpx;
			}
		}


	}

	.flex_view {
		margin: 0 32rpx;

		.SignUp-title {
			margin-top: 30rpx;

			.SignUp-title-text {
				font-family: Gilroy-Bold;
				font-weight: 400;
				font-size: 30*2rpx;
				line-height: 36*2rpx;
				color: #000;
			}

			.SignUp-title-text2 {
				margin-top: 20rpx;
				font-family: Gilroy-Medium;
				font-weight: 400;
				font-size: 16*2rpx;
				line-height: 19.2*2rpx;
				color: #686868;
			}
		}


		.head_bg {
			// height: 600rpx;
			// padding-top: 200rpx;
			// position: relative;
			background-size: 100% 100%;
			background-repeat: no-repeat;
			z-index: 0;
			display: flex;
			justify-content: flex-end;

			.language {
				margin-top: 14rpx;
				width: 108*2rpx;
				height: 33*2rpx;
				background: #FF82A31A;
				border-radius: 18*2rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-family: Gilroy-SemiBold;
				font-weight: 400;
				font-size: 14*2rpx;
				color: #000;
				position: relative;

				.helpoption {
					width: 108*2rpx;

					transition: transform 0.3s ease, opacity 0.3s ease;
					transform-origin: top;
					/* 设置变换的起点为顶部 */
					z-index: 11;
					position: absolute;
					top: 92rpx;
					left: 0;
					box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

					// background-color: rgba(0, 0, 0, .5);
					background: #fff;
					border-radius: 16*2rpx;
					padding: 16*2rpx;
					opacity: 1;
					//padding: 100rpx;
					// height: 446rpx;
					display: flex;
					align-items: flex-start;
					flex-direction: column;

					&.collapse {
						transform: scaleY(0) translateY(-100%);
						/* 缩小至0，并向上移动 */
						opacity: 0;
					}

					&.expand {
						transform: scaleY(1) translateY(0%);
						/* 恢复到正常大小，并位置恢复 */
						opacity: 1;

					}

					>view {

						padding: 15rpx 0;
						display: flex;
						align-items: center;

						image {
							width: 40rpx;
							height: 30rpx;
						}

						text {
							margin-left: 20rpx;
							display: block;
							font-family: Gilroy-Bold;
							font-weight: 400;
							font-size: 16*2rpx;
							line-height: 19.2*2rpx;
							color: #000;
						}
					}
				}

				.lang {
					margin: 0 12rpx;
					// line-height: 17.15*2rpx;
				}

				.global {
					width: 16*2rpx;
					height: 16*2rpx;
				}

				.down {
					width: 14*2rpx;
					height: 14*2rpx;
				}
			}

			.back {
				// position: absolute;
				left: 40rpx;
				background: #F1F1F1;
				border-radius: 50%;
				width: 100rpx;
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				/* #ifdef APP */
				top: 80rpx;
				/* #endif */
				/* #ifdef H5 */
				top: 40rpx;

				/* #endif */
				image {
					// padding: 25rpx;
					width: 15rpx;
					height: 25rpx;
				}
			}

			.logo {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 400rpx;
				border: 2rpx solid #ccc;

				image {
					height: 400rpx;
				}
			}
		}

	}

	/* 动画效果 */
	.expand-slide-enter-active,
	.expand-slide-leave-active {
		transition: opacity 0.3s ease, transform 0.3s ease;
	}

	/* 打开时的初始状态，缩小并从右上角开始 */
	.expand-slide-enter {
		opacity: 0;
		// transform: scale(0.8) translate(50%, -50%);
		transform: scaleY(1) translateY(0%);

	}

	/* 关闭时的最终状态，收缩回到右上角 */
	.expand-slide-leave-to {

		opacity: 0;
		// transform: scale(0.5) translate(50%, -50%);
		transform: scaleY(0) translateY(-100%);

	}

}

::v-deep .u-switch__node {
	// scale: 0.9 !important;
	width: 40rpx !important;
	height: 40rpx !important;
	margin: 4rpx 0 0 6rpx !important;
}

.rotated {
	transform: rotate(180deg);
	/* 旋转180度 */
}

.arrow {
	/* 图片宽度 */
	/* 图片高度 */
	transition: transform 0.3s ease;
	/* 动画效果：0.3秒平滑旋转 */
}
</style>