<template>
	<view class="centent">
		<view style="height: 60rpx;"></view>
		<!-- <u-verification-code class="verification" :seconds="seconds" :change-text="changeText" @end="end" @start="start"
			ref="uCode" @change="codeChange"></u-verification-code> -->
		<view class="flex_view">
			<view>
				<view class="head_bg">
					<view class="back flex_all" @tap="nav_back()">
						<image
							src="https://pro-oss.pinkwallet.com/image/20250303/52e1ce7cdecd6d6ae3040a8ae821b0d6_21x36.png">
						</image>
					</view>
				</view>
				<view class="SignUp-title flex-column">
					<text class="SignUp-title-text">{{ $t("Auth.SignUp") }}</text>
					<text class="SignUp-title-text2">{{ $t("Auth.SignUp.Description") }}</text>

				</view>
			</view>
		</view>
		<view class="register-page">
			<view class="register-container">
				<view class="input-group">
					<view class="input-title">{{ $t("Auth.SignUp.PhoneNumber") }}</view>
					<view class="phone-input">

						<transition name="expand-slide">
							<view class="helpoption" v-show="showRegion">
								<view v-for="(item, index) in RightOption" :key="index" class="Roptions"
									@click="handleRight(item)">
									<image class="Rimg" :src="item.image"></image>
									<text>{{ item.label }}</text>
								</view>
								<!-- @click="nav_to('CurrencyCharge')" -->
							</view>
						</transition>
						<!-- <view class="country-code flex_all" @click="toggleRotate">
							<text>+{{ region }}</text>
							<image class="arrow" :class="{ rotated: isRotated }"
								src="https://pro-oss.pinkwallet.com/image/20250303/e12a5adca8ec7fc0c836cfb822f1b7ee_16x8.png" />
						</view> -->
						<!-- Enter your Email -->
						<u-input type="text" maxlength="30" @input="onPhoneInput" v-model="phoneNumber" placeholder="" height="102"
							class="phone-number-input" />
					</view>
				</view>
				<view class="input-group">
					<view class="input-title">{{ $t("Auth.SignUp.VerificationCode") }}</view>
					<view class="verification-input">
						<!-- Enter verification code -->
						<u-input maxlength="6" @input="onVerificationCodeInput" type="text" v-model="verificationCode" placeholder=""
							:clearable="false" height="102" class="verification-code-input" />
						<view v-if="!isGettingCode" class="get-code-btn flex_all" :disabled="isGettingCode"
							@click="getVerificationCode">
							{{ isGettingCode ? `${countdown}s` : $t("Auth.SignUp.GetOTP") }}
						</view>
						<view v-else class="get-code-btn2 flex_all">{{ `${countdown}s` }}</view>
					</view>
				</view>

				<view class="nocode" v-if="countdown < 21">{{ $t("Auth.ForgotPassword.NoOTP") }}
				</view>

				<view class="input-group">
					<view class="input-title">{{ $t("Auth.InvitationCode") }}</view>
					<view class="phone-input">

						<u-input maxlength="10" @input="onInviteCodeInput" type="text" v-model="inviteCode" placeholder="" height="102"
							class="phone-number-input" />
					</view>
				</view>

				<view class="agreement-section">
					<view class="agreement-checkbox">
						<image class="checkbox-image" :src="isChecked ? checkedImage : uncheckedImage"
							@click="toggleCheckbox" />
						<text class="agreement-text" @click.prevent="toggleCheckbox">
							{{ $t("Auth.SignUp.Agreement") }}
							<text style="color: #FF82A3;margin: 0 2rpx;" @tap="nav_link($t('Auth.SignUp.Terms'), 1)">{{
								$t("Auth.SignUp.Terms") }}</text>
							{{ $t("Auth.SignUp.and") }}
							<text style="color: #FF82A3;margin: 0 2rpx;" @tap="nav_link($t('Auth.SignUp.Privacy'),
								2)">{{ $t("Auth.SignUp.Privacy") }}</text>
						</text>
					</view>
				</view>
				<!-- :disabled="!isFormValid" -->
				<u-button hover-class="none" class="signup-btn flex_all !rounded-button" @click="handleSignUp">
					{{ $t("Auth.SignUp.Button") }}
				</u-button>
				<view class="divider">
					<text>{{ $t("Auth.SignUp.SignUpWith") }}</text>
				</view>
				<button class="google-btn flex_all !rounded-button" @click="auth">
					<image
						src="https://pro-oss.pinkwallet.com/image/20250303/5875ec4a914cddfd7dd4ab5abf3b9d28_26x26.png" />
					<text>Google</text>
				</button>

				<view class="form-item flex_x" style="margin-top: 60rpx;" @click="goChat">
					<image class="icon_serve"
						src="https://pro-oss.pinkwallet.com/image/********/6bdf5f1ed5d632b04ba56f8d89b3c81b_81x80.png" />
					<text class="label">{{ $t("title.LiveSupport") }}</text>
				</view>

			</view>
		</view>

		<view class="bottom-text flex_all" :class="{ fixed: isFixedBottom }"
			:style="{ marginTop: isFixedBottom ? '0' : '194rpx' }" @click="nav_to('login', '')">
			{{ $t("Auth.SignUp.AlreadyHaveAccount") }}
			<a href="#" style="color: #000;margin-left: 12rpx;">{{ $t("Auth.SignUp.SignIn") }}</a>
		</view>
	</view>
</template>

<script>
import store from "@/store/index.js"
const { VUE_APP_URL, VUE_APP_CLIENT_ID, VUE_APP_CLIENT_SECRET } = process.env;
import { removeAllSpaces } from '@/utils/utils';

export default {
	data() {
		return {
			isChecked: false, // 初始未选中
			uncheckedImage: "https://pro-oss.pinkwallet.com/image/********/406ccb4765d421b4a9d8cff5a66518d5_80x80.png", // 未勾选图片（空方框）
			checkedImage: "https://pro-oss.pinkwallet.com/image/1370356051609214976.png", // 勾选图片（绿色勾）
			region: "86",
			showRegion: false,
			phoneNumber: '',
			verificationCode: '',
			agreementAccepted: true,
			isGettingCode: false,
			countdown: 59,
			timer: null,
			isRotated: false, // 控制旋转状态
			inviteCode: null,
			RightOption: [
				{
					value: '1',
					label: '1',
					image: 'https://flagcdn.com/16x12/us.png'
				},
				{
					value: '86',
					label: '86',
					image: 'https://flagcdn.com/16x12/cn.png'
				},
				{
					value: '44',
					label: '44',
					image: 'https://flagcdn.com/16x12/gb.png'
				},
				{
					value: '81',
					label: '81',
					image: 'https://flagcdn.com/16x12/jp.png'
				},
				{
					value: '91',
					label: '91',
					image: 'https://flagcdn.com/16x12/in.png'
				},
				{
					value: '33',
					label: '33',
					image: 'https://flagcdn.com/16x12/fr.png'
				},
				{
					value: '61',
					label: '61',
					image: 'https://flagcdn.com/16x12/au.png'
				}
			],
			currentIndex: null, // 当前打开的项
			link: "../../../static/serve.html",
			isFixedBottom: true

		}
	},
	onReady() {
		this.$nextTick(() => {
			this.checkHeight()
		})
	},
	onLoad(e) {
		console.log(VUE_APP_URL);
		if (e.code) {
			this.inviteCode = e.code
		}
		uni.setNavigationBarTitle({
			title: this.$t("page.register") // 切换语言后重新设置标题
		})
	},
	computed: {
		isFormValid() {
			return this.phoneNumber &&
				this.verificationCode &&
				this.agreementAccepted;
		}
	},
	methods: {
		removeAllSpaces,
		onPhoneInput(value) {
			this.phoneNumber = this.removeAllSpaces(value)
		},
		onVerificationCodeInput(value) {
			this.verificationCode = this.removeAllSpaces(value);
		},
		onInviteCodeInput(value) {
			this.inviteCode = this.removeAllSpaces(value);
		},
		nav_link(title, index) {
			if (index === 1) {
				this.$Router.push({
					name: "webView",
					params: {
						title: title,
						url: "https://www.nftcn.com/link/#/pages/index/TermsofUse-Pinkwallet"
					}
				})
			} else {
				this.$Router.push({
					name: "webView",
					params: {
						title: title,
						url: "https://www.nftcn.com/link/#/pages/index/PrivacyPolicy"
					}
				})
			}
		},
		GoogleIos() { },
		auth() {
			const systemInfo = uni.getSystemInfoSync();
			const isIOS = systemInfo.platform === 'ios';
			// #ifdef APP-PLUS
			// if (isIOS) {
			this.GoogleIos()
			// #endif
			// } else {

			// #ifdef H5
			// 你的Google OAuth 客户端 ID
			window.clientId = VUE_APP_CLIENT_ID
			// 重定向 URI
			window.redirectUri = VUE_APP_URL;
			// 请求的权限范围，可以根据需求修改
			window.scope = 'email profile';
			// 用于防止跨站请求伪造（CSRF）攻击，可以不设置，可以随心设置
			window.state = '';
			// 授权响应类型，表示要求返回授权码
			window.responseType = 'code';
			// 你的Google OAuth 客户端密钥
			window.clientSecret = VUE_APP_CLIENT_SECRET;
			window.grantType = 'authorization_code';
			//&prompt=login 把它加到window.authUrl的末尾可以让用户每次都需要重新输入账号和密码
			window.authUrl =
				`https://accounts.google.com/o/oauth2/v2/auth?client_id=${window.clientId}&redirect_uri=${window.redirectUri}&scope=${window.scope}&state=${window.state}&response_type=${window.responseType}`;
			//点击Google登录  执行这个方法进行跳转
			window.location.href = window.authUrl
			// }
			// #endif
		},
		checkHeight() {
			// 获取整个页面容器高度
			const query = uni.createSelectorQuery().in(this)
			query.select('.centent').boundingClientRect()
			query.selectViewport().boundingClientRect()
			query.exec(res => {
				const pageHeight = res[0]?.height
				const screenHeight = res[1]?.height
				console.log(pageHeight, screenHeight);
				// this.isFixedBottom = pageHeight < screenHeight
				this.isFixedBottom = screenHeight >= 933
			})
		},
		goChat() {
			this.$Router.push({
				name: 'webView',
				params: {
					url: this.link,

				}
			})
		},
		nav_to(e, name) {
			this.$Router.push({
				name: e,
				params: {
					phoneNumber: name
				}
			})
		},
		toggleCheckbox() {
			this.isChecked = !this.isChecked; // 点击时切换状态
		},
		handleRight(item) {
			this.region = item.value
			this.showRegion = false // 切换显示状态

		},

		toggleRotate() {
			this.showRegion = !this.showRegion; // 切换显示状态
			this.isRotated = !this.isRotated; // 点击时切换状态
		},
		nav_back() {
			this.$Router.back()
		},
		getVerificationCode() {
			if (this.isGettingCode) return;
			if (!this.phoneNumber) {
				uni.showToast({
					title: this.$t("Please.email"),
					icon: 'none',
					duration: 2000
				});
				return
			} else if (!/^[\w.-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$/.test(this.phoneNumber)) {
				uni.showToast({
					title: this.$t("Please.erroremail"),
					icon: 'none',
					duration: 2000
				});
				return
			}
			this.isGettingCode = true;
			this.countdown = 59;
			this.timer = setInterval(() => {
				if (this.countdown > 0) {
					this.countdown--;
				} else {
					this.isGettingCode = false;
					clearInterval(this.timer);
				}
			}, 1000);
			this.sendEmailVerifyCode()
		},
		async sendEmailVerifyCode() {
			// let res = await this.$api.sendPhoneVerifyCode({
			let res = await this.$api.sendMailCaptcha({
				email: this.phoneNumber,
				verificationCodeTypeEnum: "REGISTER",
			});
			if (res.code == 200) {
				this.$u.toast(this.$t("register.Send"));
				// 通知验证码组件内部开始倒计时
				this.$refs.uCode.start();
			} else {
				if (res.code == 110001) {
					this.$u.toast(res.msg);
					setTimeout(() => {
						this.$Router.push({
							name: 'login',
						})
					}, 2000)
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 3000
					});
				}
			}
		},
		async handleSignUp() {
			if (!this.isChecked) {
				uni.showToast({
					title: this.$t("Please.agreements"),
					icon: 'none',
					duration: 2000
				});
				return
			}
			if (!this.verificationCode) { return }
			if (store.state.shouldVibrate) {
				uni.vibrateShort()
			}
			// if (!this.isFormValid) return;
			let res = await this.$api.register({
				registerType: 'email',
				area: '+' + this.region,
				captcha: this.verificationCode,
				email: this.phoneNumber,
				inviteCode: this.inviteCode,
			});
			if (res.code == 200) {
				// uni.setStorageSync("token", res.result?.accessToken)
				this.$u.toast(this.$t('register.success'));
				// #ifdef APP-PLUS
				setTimeout(() => {
					this.$Router.push({
						name: 'login',
						params: {
							phoneNumber: this.phoneNumber,
							region: this.region,
						},
					})
				}, 300);
				// #endif
				// #ifdef H5
				if (this.returnUrl) {
					window.location.href = this.returnUrl
				} else {
					setTimeout(() => {
						this.$Router.push({
							name: 'login',
							params: {
								phoneNumber: this.phoneNumber,
								region: this.region,
							},
						})
					}, 300);
				}
				// #endif
			} else {
				uni.showToast({
					title: res.msg,
					icon: 'none',
					duration: 3000
				});
			}
			// Handle sign up logic
		}
	}
}
</script>

<style lang="scss" scoped>
.centent {
	min-height: 100vh;
	position: relative;
	// padding-bottom: 80rpx;

	.fixed {
		position: fixed;
		bottom: 0;
	}

	.bottom-text {
		// position: fixed;
		// bottom: 0;
		height: 84*2rpx;
		width: 100vw;
		// opacity: 0.3;
		border-top-left-radius: 30*2rpx;
		border-top-right-radius: 30*2rpx;
		background: rgba(217, 214, 214, .3);

		font-family: Gilroy-SemiBold;
		font-weight: 400;
		font-size: 16*2rpx;
		line-height: 19.2*2rpx;
		color: #666666;
	}

	.register-page {
		padding: 60rpx 0 0 0;

		.register-container {
			padding: 0 32rpx;
		}

		.input-group {
			margin-bottom: 16px;

			.input-title {
				font-family: Gilroy-SemiBold;
				font-weight: 400;
				font-size: 16*2rpx;
				line-height: 19.2*2rpx;
				color: #000;
				margin-bottom: 20rpx;
			}

			.phone-input {
				display: flex;
				border-radius: 8px;
				// overflow: hidden;
				position: relative;

				.helpoption {
					width: 85*2rpx;
					box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

					transition: transform 0.3s ease, opacity 0.3s ease;
					transform-origin: top;
					/* 设置变换的起点为顶部 */
					z-index: 11;
					position: absolute;
					top: 122rpx;
					left: 0;

					// background-color: rgba(0, 0, 0, .5);
					background: #fff;
					border-radius: 16*2rpx;
					padding: 16*2rpx;
					opacity: 1;
					//padding: 100rpx;
					// height: 446rpx;
					display: flex;
					align-items: flex-start;
					flex-direction: column;

					&.collapse {
						transform: scaleY(0) translateY(-100%);
						/* 缩小至0，并向上移动 */
						opacity: 0;
					}

					&.expand {
						transform: scaleY(1) translateY(0%);
						/* 恢复到正常大小，并位置恢复 */
						opacity: 1;
					}

					>view {

						padding: 15rpx 0;
						display: flex;
						align-items: center;

						image {
							width: 40rpx;
							height: 30rpx;
						}

						text {
							margin-left: 20rpx;
							display: block;
							font-family: Gilroy-Bold;
							font-weight: 400;
							font-size: 16*2rpx;
							line-height: 19.2*2rpx;
							color: #000;
						}
					}
				}

				.country-code {
					// overflow: hidden;
					width: 85*2rpx;
					height: 51*2rpx;
					border-radius: 10*2rpx;
					border-width: 2rpx;
					// padding: 16px;
					border: 2rpx solid #999999;

					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 16*2rpx;


					display: flex;
					align-items: center;



					// padding: 12px 16px;
					// border-right: 1px solid #e5e7eb;
					// font-size: 14px;
					// color: #374151;
					// gap: 8px;
					.arrow {
						/* 图片宽度 */
						/* 图片高度 */
						transition: transform 0.3s ease;
						/* 动画效果：0.3秒平滑旋转 */
					}

					.rotated {
						transform: rotate(180deg);
						/* 旋转180度 */
					}

					image {
						margin-left: 22rpx;
						width: 28rpx;
						height: 14rpx;
					}
				}

				.phone-number-input {
					border: 2rpx solid #999999 !important;
					height: 51*2rpx;
					border-radius: 10*2rpx;
					// margin-left: 20rpx;
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 16*2rpx;
					color: #000;
					padding: 0 32rpx !important;
				}
			}

			.verification-input {
				height: 51*2rpx;
				display: flex;
				border: 2rpx solid #999999 !important;
				border-radius: 10*2rpx;
				padding: 0 32rpx !important;
				overflow: hidden;
				position: relative;

				.verification-code-input {
					flex: 1;
					padding: 12px 16px;
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 16*2rpx;
					color: #000;
				}

				.get-code-btn {
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 14*2rpx;
					color: #000;
					right: 14rpx;
					top: 8rpx;
					position: absolute;
					width: 110*2rpx;
					height: 42*2rpx;
					border-radius: 10*2rpx;
					// background: #008E28;

					text-decoration: underline;

					&:disabled {
						// background: #FF82A3;
					}
				}

				.get-code-btn2 {
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 16*2rpx;
					right: 14rpx;
					top: 8rpx;
					position: absolute;
					width: 110*2rpx;
					height: 42*2rpx;
					border-radius: 10*2rpx;
					background: rgba(255, 155, 181, .1);
					color: #FF82A3;

				}
			}
		}

		.nocode {
			font-family: Gilroy-SemiBold;
			font-weight: 400;
			font-size: 28rpx;
			line-height: 160%;
			letter-spacing: 0%;
			vertical-align: middle;
			color: #666;
		}

		.agreement-section {
			margin: 24px 0;

			.agreement-checkbox {
				display: flex;
				align-items: flex-start;
				// gap: 8px;
				cursor: pointer;

				.checkbox-image {
					margin-right: 20rpx;
					width: 40rpx !important;
					height: 40rpx;
					cursor: pointer;
				}

				input {
					display: none;
				}

				.checkbox-custom {
					width: 20px;
					height: 20px;
					border: 2px solid #e5e7eb;
					border-radius: 4px;
					position: relative;
				}

				input:checked+.checkbox-custom::after {
					content: '\2714';
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					color: #008E28;
				}

				.agreement-text {
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 14*2rpx;
					line-height: 22.4*2rpx;
					color: #333333;
				}
			}
		}

		.signup-btn {
			width: 100%;
			background-color: #FF82A3;
			color: white;
			border: none;
			font-size: 16*2rpx;
			border-radius: 64*2rpx;
			height: 100rpx;
			font-family: Gilroy-Bold;
			font-weight: 400;

			&:disabled {
				// background-color: #e5e7eb;
			}
		}

		.divider {
			text-align: center;
			position: relative;
			margin: 42rpx 0;

			&::before,
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				width: calc(50% - 100rpx);
				height: 2rpx;
				background-color: #e5e7eb;
			}

			&::before {
				left: 0;
			}

			&::after {
				right: 0;
			}

			text {
				padding: 0 7rpx;
				color: #6b7280;
				font-family: Gilroy-SemiBold;
				font-weight: 400;
				font-size: 14*2rpx;
				line-height: 22.4*2rpx;
				text-align: center;

			}
		}

		.form-item {
			.label {
				margin-left: 20rpx;
				font-family: Gilroy-Medium;
				font-weight: 400;
				font-size: 16*2rpx;
				// line-height: 19.2*2rpx;
				letter-spacing: 0%;
				color: #FF82A3;
			}

			.icon_serve {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.google-btn {
			height: 50*2rpx;
			border-radius: 64*2rpx;
			border-width: 1*2rpx;

			width: 100%;
			// padding: 14px;
			background-color: white;
			border: 2rpx solid #e5e7eb;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 20rpx;
			color: #374151;
			font-family: Gilroy-SemiBold;
			font-weight: 400;
			font-size: 32rpx;

			image {
				width: 52rpx;
				height: 52rpx;
			}
		}


	}

	.flex_view {
		margin: 0 32rpx;

		.SignUp-title {
			margin-top: 30rpx;

			.SignUp-title-text {
				font-family: Gilroy-Bold;
				font-weight: 400;
				font-size: 30*2rpx;
				line-height: 36*2rpx;
				color: #000;
			}

			.SignUp-title-text2 {
				margin-top: 20rpx;
				font-family: Gilroy-Medium;
				font-weight: 400;
				font-size: 16*2rpx;
				line-height: 19.2*2rpx;
				color: #686868;
			}
		}


		.head_bg {
			// height: 600rpx;
			// padding-top: 200rpx;
			// position: relative;
			background-size: 100% 100%;
			background-repeat: no-repeat;
			z-index: 0;

			.back {
				// position: absolute;
				left: 40rpx;
				background: #F1F1F1;
				border-radius: 50%;
				width: 100rpx;
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				/* #ifdef APP */
				top: 80rpx;
				/* #endif */
				/* #ifdef H5 */
				top: 40rpx;

				/* #endif */
				image {
					// padding: 25rpx;
					width: 15rpx;
					height: 25rpx;
				}
			}

			.logo {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 400rpx;
				border: 2rpx solid #ccc;

				image {
					height: 400rpx;
				}
			}
		}

	}

	/* 动画效果 */
	.expand-slide-enter-active,
	.expand-slide-leave-active {
		transition: opacity 0.3s ease, transform 0.3s ease;
	}

	/* 打开时的初始状态，缩小并从右上角开始 */
	.expand-slide-enter {
		opacity: 0;
		// transform: scale(0.8) translate(50%, -50%);
		transform: scaleY(1) translateY(0%);

	}

	/* 关闭时的最终状态，收缩回到右上角 */
	.expand-slide-leave-to {

		opacity: 0;
		// transform: scale(0.5) translate(50%, -50%);
		transform: scaleY(0) translateY(-100%);

	}

}
</style>