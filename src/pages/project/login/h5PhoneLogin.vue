<template>
  <view class="index_body">
    <u-navbar back-icon-color="var(--default-color1)" title-color="var(--main-text-color)" title-bold
              :border-bottom="false" :background="{backgroundColor: 'var(--main-bg-color)'}">
    </u-navbar>
    <u-verification-code class="verification" :seconds="seconds" :change-text="changeText" @end="end" @start="start"
                         ref="uCode" @change="codeChange"></u-verification-code>

    <view>
      <view class="index_body_img">
        <image src="@/static/login/logo.png" mode="widthFix"></image>
        <view class="index_body_text">给数藏一片净土</view>
      </view>
      <view class="index_body_box">
        <view class="index_body_content1">
          <!--    输入手机号-->
          <view class="top">
            <view class="left">
              <text>+86</text>
              <!-- <image src="@/static/login/dbx.png" mode="widthFix"></image> -->
            </view>
            <view class="right">
              <u-input class="modal-resale-input" v-model="phone" type="number" placeholder="请输入手机号"
                       :maxlength="11" @input="handlePhoneInput"/>
            </view>
          </view>
          <!--    输入验证码-->
        </view>
        <view class="index_body_content2">
          <view class="left">
            <u-input v-model="code" :clearable="false" type="number" placeholder="请输入验证码"
                     :maxlength="6"
                     input-align="text" class="modal-resale-input" @input="handleVerificationCodeInput"/>
          </view>
          <view class="right" :class="{'active':status==0}" @tap="getCode">
            {{ tips }}
          </view>
        </view>
      </view>
      <view class="agreement">
        <u-image mode="widthFix" width="28rpx" @click="isAgreement = !isAgreement"
                 :src="`../../../static/login/${isAgreement?'jxs2x':'jx'}.png`">
        </u-image>
        <view class="text">
          <text @click="isAgreement = !isAgreement">我已阅读并同意Bigverse</text>
          <span @tap="nav_link('Bigverse平台服务协议',1)">《用户协议》</span>与 <span
            @tap="nav_link('Bigverse法律声明及隐私政策',2)">《隐私协议》</span>
        </view>
      </view>
      <view class="submit_login" @tap="login">
        登录
      </view>
    </view>

  </view>
</template>

<script>
export default {
  name: "register",
  components: {},
  data() {
    return {
      // background: {
      // 	backgroundColor: '#35333E',
      // },
      region: '86',
      tips: '获取验证码',
      phone: '', //手机号码
      code: '', //验证码
      status: 0,
      isAgreement: false,

      seconds: 60,
      changeText: '重新获取(x)',

      return_url: ''
    }
  },

  onLoad(options) {
    if (options.url === undefined) {
      this.return_url = ""
    } else {
      if (/^#/.test(options.url)) {
        this.return_url = `/h5/${options.url}`;
      } else {
        this.return_url = options.url;
      }
    }
  },

  methods: {
    // 处理手机号输入，禁止空格
    handlePhoneInput(value) {
      this.phone = value.replace(/\s/g, '');
    },
    // 处理验证码输入，禁止空格
    handleVerificationCodeInput(value) {
      this.code = value.replace(/\s/g, '');
    },

    getCode() {
      if (this.$refs.uCode.canGetCode) {
        // 模拟向后端请求验证码
        if (this.$u.test.mobile(this.phone)) {
          uni.showLoading({
            title: '正在获取验证码'
          })
          this.sendPhoneVerifyCode()
        } else {
          this.$u.toast('请输入手机号再获取');
        }

      } else {
        // this.$u.toast('倒计时结束后再发送');
      }
    },
    async sendPhoneVerifyCode() {
      let res = await this.$api.java_sendAliYunSms({
        aliYumSmsType: "LOGIN",
        mobPhonePrefix: this.region,
        mobPhone: this.phone
      });
      if (res.status.code == 0) {
        this.$u.toast('验证码已发送');
        // 通知验证码组件内部开始倒计时
        this.$refs.uCode.start();
      } else {
        if (res.status.code == 110001) {
          this.$u.toast(res.status.msg);
          setTimeout(() => {
            this.$Router.push({
              name: 'login',
              params: {
                phone: this.phone,
                region: this.region
              }
            })
          }, 2000)
        } else {
          uni.showToast({
            title: res.status.msg,
            icon: 'none',
            duration: 3000
          });
        }
      }
    },
    async login() {
      if (this.phone == "") {
        this.$u.toast('请输入手机号');
      } else if (this.code == "") {
        this.$u.toast('请输入验证码');
      } else if (!this.isAgreement) {
        this.$u.toast('请先勾选协议');
      } else {
        let res = await this.$api.java_phoneVerifyCodeLogin({
          // type: "REGISTER",
          mobPhonePrefix: this.region,
          mobPhone: this.phone,
          code: this.code
        });
        if (res.status.code == 0) {
          console.log("登录token", res.result?.accessToken)
          if (res.result.code == 1005) {
            console.log("去注册")
            this.$Router.push({
              name: 'register',
              params: {
                phone: this.phone
              }
            })
            return false
          }
          uni.setStorageSync("token", res.result?.accessToken)
          // #ifdef APP
          this.bindDevice()
          // #endif
          this.$u.toast('登录成功');
          // #ifdef APP
          setTimeout(() => {
            this.$Router.pushTab({
              name: 'index'
            })
          }, 300);
          // #endif
          // #ifdef H5
          if (this.return_url) {
            window.location.href = this.return_url
          } else {
            setTimeout(() => {
              this.$Router.pushTab({
                name: 'index'
              })
            }, 300);
          }
          // #endif
        } else {
          uni.showToast({
            title: res.status.msg,
            icon: 'none',
            duration: 3000
          });
        }
      }

    },
    nav_link(title, index) {
      if (index === 1) {
        this.$Router.push({
          name: "generalAgreement",
          params: {
            title: title,
            link: "https://www.nftcn.com/link/#/pages/index/userAgreement"
          }
        })
      } else {
        this.$Router.push({
          name: "generalAgreement",
          params: {
            title: title,
            link: "https://www.nftcn.com/link/#/pages/index/PrivacyAgreement"
          }
        })
      }
    },
    async bindDevice() {
      let appType = uni.getSystemInfoSync().platform
      console.error(appType, uni.getStorageSync('deviceToken'))
      let res = await this.$api.bindDevice({
        appType: appType == 'ios' ? 2 : 1,
        deviceId: uni.getStorageSync('deviceToken')
      });
      if (res.status.code == 0) {
        console.log(res)
      } else {
        console.log(res)
      }
    },
    codeChange(text) {
      this.tips = text;
    },
  }
}
</script>

<style scoped lang="scss">
::v-deep .u-input__input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #fff inset !important;
  box-shadow: 0 0 0 1000px #fff inset !important;
  -webkit-text-fill-color: #000 !important;
}

.index_body {
  background: var(--login-bg-color);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;

  .index_body_img {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    width: auto;
    height: 108.14rpx;

    image {
      width: 340rpx;
      height: 59.65rpx;
    }

    .index_body_text {
      padding: 5rpx 0 0 18rpx;
      font-size: 32rpx;
      font-weight: 300;
      letter-spacing: 12.8rpx;
      line-height: 42rpx;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      font-family: HarmonyOS Sans SC;

    }

  }

  .index_body_box {
    margin-top: 100rpx;
    display: flex;
    align-items: center;
    flex-direction: column;

    .index_body_content1 {
      display: flex;
      justify-content: center;
    }

    .top {
      width: 540rpx;
      height: 90rpx;
      opacity: 1;
      border-radius: 50rpx;
      background: rgba(70, 69, 79, 1);
      display: flex;
      // justify-content: center;
      align-items: center;
      padding: 10rpx 40rpx;

      .left {
        display: flex;
        align-items: center;
        padding-right: 20rpx;

        text {
          font-size: 28rpx;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 37rpx;
          color: rgba(255, 255, 255, 1);
          text-align: left;
          vertical-align: top;
          padding-right: 4rpx;
        }

        image {
          width: 20rpx;
          height: 16rpx;
          margin-left: 10rpx;

        }

      }

      .right {
        input {
          font-size: 28rpx;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 37rpx;
          color: rgba(166, 166, 166, 1);
        }
      }
    }

    .index_body_content2 {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40rpx 0;

      .left {
        width: 300rpx;
        height: 90rpx;
        line-height: 90rpx;
        border-radius: 50rpx;
        background: rgba(70, 69, 79, 1);
        border: 1rpx solid rgba(0, 0, 0, 0);
        display: flex;
        align-items: center;
        padding-left: 40rpx;

        //input[type=text]:focus, input[type=password]:focus, textarea:focus {
        //
        //  outline: 0;
        //
        //}

        .pla_inp {
          text-align: center;
        }

      }

      .right {
        width: 220rpx;
        height: 90rpx;
        line-height: 90rpx;
        border-radius: 50rpx;
        background: rgba(99, 234, 238, 1);
        border: 1rpx solid rgba(0, 0, 0, 0);
        font-size: 24rpx;
        font-weight: 700;
        letter-spacing: 0px;
        color: rgba(20, 20, 20, 1);
        text-align: center;
        margin-left: 20rpx;

      }

    }

  }

  .agreement {
    padding: 0rpx 105rpx;
    margin-top: 80rpx;
    display: flex;

    .u-image {
      margin-right: 10rpx;
      margin-top: 4rpx;
    }

    .text {
      font-size: 24rpx;
      color: #A6A6A6;
      line-height: 32rpx;

      span {
        text-decoration: underline;
        color: var(--active-color1);
      }
    }
  }

  .submit_login {
    width: 540rpx;
    height: 100rpx;
    background: var(--primary-button-color);
    border-radius: 60rpx;
    font-size: 34rpx;
    color: #141414;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 206rpx auto 0rpx auto;

  }

  .head_bg_view {
    position: absolute;
    top: 0rpx;
    left: 0rpx;
    width: 100%;
    height: 500rpx;
    z-index: -1;
    background-color: #fff;
  }

}

::v-deep .uni-input-input {
  //text-align: center;
}

.modal-resale-input::v-deep {


  .u-input__input {
    outline:none !important;

    background:none !important;
    color: var(--default-color1) !important;

    input:-internal-autofill-selected{
      background-color:transparent !important;
      background-image: none;
    }
    input {
      background-color:transparent;
    }
  }

  .uni-input-placeholder {
    font-size: 28rpx !important;
  }
}
</style>
