<template>
    <view class="centent">
        <view style="height: 60rpx;"></view>
        <view class="flex_view">
            <view>
                <view class="head_bg">
                    <view class="back flex_all" @tap="nav_back()">
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250303/52e1ce7cdecd6d6ae3040a8ae821b0d6_21x36.png">
                        </image>
                    </view>
                </view>
                <view class="SignUp-title flex-column">
                    <text class="SignUp-title-text">{{ $t("Auth.ForgotPassword") }}</text>
                    <text class="SignUp-title-text2">{{ $t("Auth.ForgotPassword.Description") }}</text>

                </view>
            </view>
        </view>
        <view class="register-page">
            <view class="register-container">
                <view class="input-group">
                    <view class="input-title">{{ $t("Auth.ForgotPassword.PhoneNumber") }}</view>
                    <view class="phone-input">

                        <!-- <transition name="expand-slide">
                            <view class="helpoption" v-show="showRegion">
                                <view v-for="(item, index) in RightOption" :key="index" class="Roptions"
                                    @click="handleRight(item)">
                                    <image class="Rimg" :src="item.image"></image>
                                    <text>{{ item.label }}</text>
                                </view>
                            </view>
                        </transition> -->
                        <!-- <view class="country-code flex_all" @click="toggleRotate">
                            <text>+{{ region }}</text>
                            <image class="arrow" :class="{ rotated: isRotated }"
                                src="https://pro-oss.pinkwallet.com/image/20250303/e12a5adca8ec7fc0c836cfb822f1b7ee_16x8.png" />
                        </view> -->
                        <!-- Enter your Email -->
                        <u-input maxlength="30" type="text" v-model="phoneNumber" placeholder="" height="102"
                            class="phone-number-input" />
                    </view>
                </view>
                <view class="input-group">
                    <view class="input-title">{{ $t("Auth.ForgotPassword.VerificationCode") }}</view>
                    <view class="verification-input">
                        <!-- Enter verification code -->
                        <u-input type="text" :clearable="false" v-model="verificationCode" placeholder="" maxlength="6"
                            height="102" class="verification-code-input" />
                        <view v-if="!isGettingCode" class="get-code-btn flex_all" :disabled="isGettingCode"
                            @click="getVerificationCode">
                            {{ isGettingCode ? `${countdown}s` : $t("Auth.ForgotPassword.GetOTP") }}
                        </view>
                        <view v-else class="get-code-btn2 flex_all">{{ `${countdown}s` }}</view>
                    </view>
                </view>
                <view @click="goChat" class="nocode" v-if="countdown < 21">{{ $t("Auth.ForgotPassword.NoOTP") }}
                </view>

                <view class="input-group">
                    <view class="input-title">{{ $t("Auth.ForgotPassword.NewPassword") }}</view>
                    <view class="verification-input">
                        <!-- Enter Password -->
                        <u-input type="password" v-model="password" maxlength="30" placeholder="" height="102"
                            class="verification-code-input" />
                    </view>
                </view>

                <view class="rules">
                    <view class="rules-title">
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250304/0c7dd2a20404fa3784fe92b8620e92de_12x12.png" />
                        <text>{{ $t("Auth.ForgotPassword.PasswordRequirement1") }}</text>
                    </view>
                    <view class="rules-title">
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250304/0c7dd2a20404fa3784fe92b8620e92de_12x12.png" />
                        <text>{{ $t("Auth.ForgotPassword.PasswordRequirement2") }}</text>
                    </view>
                    <view class="rules-title">
                        <image
                            src="https://pro-oss.pinkwallet.com/image/20250304/0c7dd2a20404fa3784fe92b8620e92de_12x12.png" />
                        <text>{{ $t("Auth.ForgotPassword.PasswordRequirement3") }}</text>
                    </view>
                </view>
                <!-- :disabled="!isFormValid" -->
                <u-button hover-class="none" class="signup-btn flex_all !rounded-button" @click="handleSignUp">
                    {{ $t("Auth.ForgotPassword.Submit") }}
                </u-button>
            </view>
        </view>
    </view>
</template>

<script>
import store from '@/store/index.js'
export default {
    data() {
        return {
            link: "../../../static/serve.html",
            ispwd: false,
            region: "86",
            showRegion: false,
            phoneNumber: '',
            password: "",
            verificationCode: '',
            agreementAccepted: true,
            isGettingCode: false,
            countdown: 59,
            timer: null,
            isRotated: false, // 控制旋转状态
            RightOption: [
                {
                    value: '1',
                    label: '1',
                    image: 'https://flagcdn.com/16x12/us.png'
                },
                {
                    value: '86',
                    label: '86',
                    image: 'https://flagcdn.com/16x12/cn.png'
                },
                {
                    value: '44',
                    label: '44',
                    image: 'https://flagcdn.com/16x12/gb.png'
                },
                {
                    value: '81',
                    label: '81',
                    image: 'https://flagcdn.com/16x12/jp.png'
                },
                {
                    value: '91',
                    label: '91',
                    image: 'https://flagcdn.com/16x12/in.png'
                },
                {
                    value: '33',
                    label: '33',
                    image: 'https://flagcdn.com/16x12/fr.png'
                },
                {
                    value: '61',
                    label: '61',
                    image: 'https://flagcdn.com/16x12/au.png'
                }
            ],
            currentIndex: null, // 当前打开的项
        }
    },
    computed: {
        isFormValid() {
            return this.phoneNumber &&
                this.verificationCode &&
                this.password;
        }
    },
    onLoad(e) {
        if (e.email) {
            this.phoneNumber = e.email
        }
        uni.setNavigationBarTitle({
            title: this.$t("page.forget") // 切换语言后重新设置标题
        })
    },
    methods: {
        goChat() {
            this.$Router.push({
                name: 'webView',
                params: {
                    url: this.link,

                }
            })
        },
        nav_to(e) {
            this.$Router.push({
                name: e,
            })
        },
        handleRight(item) {
            this.region = item.value
            this.showRegion = false // 切换显示状态

        },

        toggleRotate() {
            this.showRegion = !this.showRegion; // 切换显示状态
            this.isRotated = !this.isRotated; // 点击时切换状态
        },
        nav_back() {
            this.$Router.back()
        },
        getVerificationCode() {
            if (!this.phoneNumber) {
                uni.showToast({
                    title: this.$t("Please.email"),
                    icon: 'none',
                    duration: 2000
                });
                return
            } else if (!/^[\w.-]+@[a-zA-Z\d.-]+\.[a-zA-Z]{2,}$/.test(this.phoneNumber)) {
                uni.showToast({
                    title: this.$t("Please.erroremail"),
                    icon: 'none',
                    duration: 2000
                });
                return
            }
            if (this.isGettingCode) return;
            this.isGettingCode = true;
            this.countdown = 59;
            this.timer = setInterval(() => {
                if (this.countdown > 0) {
                    this.countdown--;
                } else {
                    this.isGettingCode = false;
                    clearInterval(this.timer);
                }
            }, 1000);
            this.sendEmailVerifyCode()
        },
        async sendEmailVerifyCode() {
            // let res = await this.$api.sendPhoneVerifyCode({
            let res = await this.$api.sendMailCaptcha({
                email: this.phoneNumber
            });
            if (res.code == 200) {
                this.$u.toast(this.$t("register.Send"));
                // 通知验证码组件内部开始倒计时
                this.$refs.uCode.start();
            } else {
                if (res.code == 110001) {
                    this.$u.toast(res.msg);
                    setTimeout(() => {
                        this.$Router.push({
                            name: 'login',
                        })
                    }, 2000)
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none',
                        duration: 3000
                    });
                }
            }
        },
        validatePassword(password) {
            console.log(password);

            if (password.length < 8) {
                this.$u.toast(this.$t("Please.PasswordRequirement1"));
                return
            }
            if (!/[a-z]/.test(password)) {
                this.$u.toast(this.$t("Please.PasswordRequirement2"))
                return
            }
            if (!/[A-Z]/.test(password)) {
                this.$u.toast(this.$t("Please.PasswordRequirement3"))
                return
            }
            return true; // 验证通过
        },
        async handleSignUp() {
            if (!this.verificationCode || !this.phoneNumber) {
                uni.showToast({
                    title: this.$t("Please.full"),
                    icon: 'none'
                })
                return
            }

            const emailRegular = /^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*\.[a-z]{2,}$/
            if (!emailRegular.test(this.phoneNumber)) {
                uni.showToast({
                    title: this.$t("Please.email"),
                    icon: 'none',
                    duration: 3000
                });
                return
            }


            if (this.validatePassword(this.password)) {
                if (store.state.shouldVibrate) {
                    uni.vibrateShort()
                }
                let res = await this.$api.forgotPassByEmail({
                    email: this.phoneNumber,
                    captcha: this.verificationCode,
                    password: this.password,
                    passwordType: 'LOGIN'
                });
                if (res.code == 200) {

                    uni.showToast({
                        title: res.msg,
                        icon: 'none',
                        duration: 3000,
                    });
                    setTimeout(() => {
                        this.$Router.push({
                            name: 'login',
                            params: {
                                email: this.phoneNumber,
                            }
                        })
                    }, 1500);
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none',
                        duration: 3000,
                    });
                }
            }
            // Handle sign up logic
        }
    }
}
</script>

<style lang="scss" scoped>
.centent {
    min-height: 100vh;

    .bottom-text {
        position: fixed;
        bottom: 0;
        height: 84*2rpx;
        width: 100vw;
        // opacity: 0.3;
        border-top-left-radius: 30*2rpx;
        border-top-right-radius: 30*2rpx;
        background: rgba(217, 214, 214, .3);

        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 16*2rpx;
        line-height: 19.2*2rpx;
        color: #666666;
    }

    .register-page {
        padding: 60rpx 0;

        .register-container {
            padding: 0 32rpx;
        }

        .switchpwd {
            display: flex;
            align-items: center;
            margin: 0 0 40rpx 0;
            font-family: Gilroy-Medium;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 17*2rpx;
            color: #000;

            text {
                margin-left: 20rpx;
            }
        }

        .rules {
            height: 83*2rpx;
            border-bottom-right-radius: 10*2rpx;
            border-bottom-left-radius: 10*2rpx;
            background: #F2F2F2;
            padding: 20rpx;

            .rules-title {
                // gap: 14rpx;
                margin-bottom: 14rpx;
                display: flex;
                align-items: center;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }

                text {
                    margin-left: 22rpx;
                    font-family: Gilroy-Medium;
                    font-weight: 400;
                    font-size: 14*2rpx;
                    line-height: 16.8*2rpx;
                    color: #333;
                }
            }
        }

        .nocode {
            margin-top: -20rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 160%;
            letter-spacing: 0%;
            vertical-align: middle;
            color: #666;
        }

        .input-group {
            margin-bottom: 32rpx;

            .input-title {
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                color: #000;
                margin-bottom: 20rpx;
            }

            .phone-input {
                display: flex;
                border-radius: 8px;
                // overflow: hidden;
                position: relative;

                .helpoption {
                    width: 85*2rpx;
                    box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;

                    transition: transform 0.3s ease, opacity 0.3s ease;
                    transform-origin: top;
                    /* 设置变换的起点为顶部 */
                    z-index: 11;
                    position: absolute;
                    top: 122rpx;
                    left: 0;

                    // background-color: rgba(0, 0, 0, .5);
                    background: #fff;
                    border-radius: 16*2rpx;
                    padding: 16*2rpx;
                    opacity: 1;
                    //padding: 100rpx;
                    // height: 446rpx;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;

                    &.collapse {
                        transform: scaleY(0) translateY(-100%);
                        /* 缩小至0，并向上移动 */
                        opacity: 0;
                    }

                    &.expand {
                        transform: scaleY(1) translateY(0%);
                        /* 恢复到正常大小，并位置恢复 */
                        opacity: 1;
                    }

                    >view {

                        padding: 15rpx 0;
                        display: flex;
                        align-items: center;

                        image {
                            width: 40rpx;
                            height: 30rpx;
                        }

                        text {
                            margin-left: 20rpx;
                            display: block;
                            font-family: Gilroy-Bold;
                            font-weight: 400;
                            font-size: 16*2rpx;
                            line-height: 19.2*2rpx;
                            color: #000;
                        }
                    }
                }

                .country-code {
                    // overflow: hidden;
                    width: 85*2rpx;
                    height: 51*2rpx;
                    border-radius: 10*2rpx;
                    border-width: 2rpx;
                    // padding: 16px;
                    border: 2rpx solid #999999;

                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;


                    display: flex;
                    align-items: center;



                    // padding: 12px 16px;
                    // border-right: 1px solid #e5e7eb;
                    // font-size: 14px;
                    // color: #374151;
                    // gap: 8px;
                    .arrow {
                        /* 图片宽度 */
                        /* 图片高度 */
                        transition: transform 0.3s ease;
                        /* 动画效果：0.3秒平滑旋转 */
                    }

                    .rotated {
                        transform: rotate(180deg);
                        /* 旋转180度 */
                    }

                    image {
                        margin-left: 22rpx;
                        width: 28rpx;
                        height: 14rpx;
                    }
                }

                .phone-number-input {
                    border: 2rpx solid #999999 !important;
                    height: 51*2rpx;
                    border-radius: 10*2rpx;
                    // margin-left: 20rpx;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    color: #000;
                    padding: 0 32rpx !important;
                }
            }

            .verification-input {
                height: 51*2rpx;
                display: flex;
                border: 2rpx solid #999999 !important;
                border-radius: 10*2rpx;
                padding: 0 32rpx !important;
                overflow: hidden;
                position: relative;

                .verification-code-input {
                    flex: 1;
                    padding: 12px 16px;
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    color: #000;
                }

                .get-code-btn {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 14*2rpx;
                    color: #000;
                    right: 14rpx;
                    top: 8rpx;
                    position: absolute;
                    width: 110*2rpx;
                    height: 42*2rpx;
                    border-radius: 10*2rpx;
                    // background: #008E28;
                    text-decoration: underline;

                }

                .get-code-btn2 {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 16*2rpx;
                    right: 14rpx;
                    top: 8rpx;
                    position: absolute;
                    width: 110*2rpx;
                    height: 42*2rpx;
                    border-radius: 10*2rpx;
                    background: rgba(255, 155, 181, .1);
                    color: #FF82A3;

                }
            }
        }

        .verification-code-section {
            margin-top: -20rpx;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 14*2rpx;
            line-height: 22.4*2rpx;
            text-align: right;
            color: #666;
        }

        .agreement-section {
            margin: 24px 0;

            .agreement-checkbox {
                display: flex;
                align-items: flex-start;
                gap: 8px;
                cursor: pointer;

                input {
                    display: none;
                }

                .checkbox-custom {
                    width: 20px;
                    height: 20px;
                    border: 2px solid #e5e7eb;
                    border-radius: 4px;
                    position: relative;
                }

                input:checked+.checkbox-custom::after {
                    content: '\2714';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    color: #008E28;
                }

                .agreement-text {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 14*2rpx;
                    line-height: 22.4*2rpx;
                    color: #333333;
                }
            }
        }

        .signup-btn {
            margin-top: 52rpx;
            width: 100%;
            background-color: #FF82A3;
            color: white;
            border: none;
            font-size: 16*2rpx;
            border-radius: 64*2rpx;
            height: 100rpx;
            font-family: Gilroy-Bold;
            font-weight: 400;

            &:disabled {
                // background-color: #e5e7eb;
            }
        }

        .divider {
            text-align: center;
            position: relative;
            margin: 42rpx 0;

            &::before,
            &::after {
                content: '';
                position: absolute;
                top: 50%;
                width: calc(50% - 100rpx);
                height: 2rpx;
                background-color: #e5e7eb;
            }

            &::before {
                left: 0;
            }

            &::after {
                right: 0;
            }

            text {
                padding: 0 7rpx;
                color: #6b7280;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                line-height: 22.4*2rpx;
                text-align: center;

            }
        }

        .google-btn {
            height: 50*2rpx;
            border-radius: 64*2rpx;
            border-width: 1*2rpx;

            width: 100%;
            // padding: 14px;
            background-color: white;
            border: 2rpx solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20rpx;
            color: #374151;
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 32rpx;

            image {
                width: 52rpx;
                height: 52rpx;
            }
        }


    }

    .flex_view {
        margin: 0 32rpx;

        .SignUp-title {
            margin-top: 30rpx;

            .SignUp-title-text {
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 30*2rpx;
                line-height: 36*2rpx;
                color: #000;
            }

            .SignUp-title-text2 {
                margin-top: 20rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 16*2rpx;
                line-height: 19.2*2rpx;
                color: #686868;
            }
        }


        .head_bg {
            // height: 600rpx;
            // padding-top: 200rpx;
            // position: relative;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            z-index: 0;
            display: flex;
            // justify-content: flex-end;

            .language {
                width: 108*2rpx;
                height: 33*2rpx;
                background: #FF82A31A;
                border-radius: 18*2rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 14*2rpx;
                color: #000;

                .lang {
                    margin: 0 12rpx;
                    line-height: 17.15*2rpx;
                }

                .global {
                    width: 16*2rpx;
                    height: 16*2rpx;
                }

                .down {
                    width: 14*2rpx;
                    height: 14*2rpx;
                }
            }

            .back {
                // position: absolute;
                left: 40rpx;
                background: #F1F1F1;
                border-radius: 50%;
                width: 100rpx;
                height: 100rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                /* #ifdef APP */
                top: 80rpx;
                /* #endif */
                /* #ifdef H5 */
                top: 40rpx;

                /* #endif */
                image {
                    // padding: 25rpx;
                    width: 15rpx;
                    height: 25rpx;
                }
            }

            .logo {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 400rpx;
                border: 2rpx solid #ccc;

                image {
                    height: 400rpx;
                }
            }
        }

    }

    /* 动画效果 */
    .expand-slide-enter-active,
    .expand-slide-leave-active {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    /* 打开时的初始状态，缩小并从右上角开始 */
    .expand-slide-enter {
        opacity: 0;
        // transform: scale(0.8) translate(50%, -50%);
        transform: scaleY(1) translateY(0%);

    }

    /* 关闭时的最终状态，收缩回到右上角 */
    .expand-slide-leave-to {

        opacity: 0;
        // transform: scale(0.5) translate(50%, -50%);
        transform: scaleY(0) translateY(-100%);

    }

}

::v-deep .u-switch__node {
    // scale: 0.9 !important;
    width: 40rpx !important;
    height: 40rpx !important;
    margin: 4rpx 0 0 6rpx !important;
}
</style>