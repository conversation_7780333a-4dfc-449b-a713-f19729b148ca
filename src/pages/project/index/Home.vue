<template>
    <view class="page">
        <Index v-if="tabIndex == 0" />
        <market v-if="tabIndex == 1" />
        <trade v-if="tabIndex == 2" ref="trade" />
        <us v-if="tabIndex == 3" ref="ustab" />
        <Personal v-if="tabIndex == 4" />
        <!-- <tabbar :current="activeIndex" @changeTab="changeTab" /> -->
        <BottomTabBar v-model="tabIndex" @change="onTabChange" /> 

        <u-popup border-radius="40" mode="center" v-model="show" safe-area-inset-bottom round>
            <view class="alert-popup">
                <view class="icon-wrap">
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385641448773869568.png" />
                </view>
                <text class="question">是否设置登录密码？</text>
                <view class="btn-group">
                    <view class="btn need" @click="nav_to('setPwd')">需要</view>
                    <view class="btn cancel" @click="show = false">不需要</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import market from "../../../pagesA/project/market"
import Index from "./index"
import Personal from "../personal/index"
import trade from "../../../pagesA/project/trade/index"
import us from "../../../pagesA/project/us/index"
import BottomTabBar from "../../../components/public/BottomTabBar"
import { set } from 'vue'
export default {
    components: {
        Index,
        BottomTabBar,
        trade,
        us,
        Personal,
        market
    },
    onReachBottom() {
        if (this.tabIndex == 2) {
            this.onReachBottoms++
            this.$refs.trade.onBottom()
        }
    },
    watch: {
        tabIndex(newval) {
            if (newval == 3) {
                setTimeout(() => {
                    // this.$refs.ustab.getIndexData()
                    this.$refs.ustab.socketon()
                }, 100);
            }
        }
    },
    onHide() {
        if (this.tabIndex == 3) {
            setTimeout(() => {
                this.$refs.ustab.socketoff()
            }, 100);
        }
    },
    onLoad(option) {
        if (option) {
            if (option.coin) {
                this.nowCoins = JSON.parse(decodeURIComponent(option.coin));
            }
        }
        this.getUserInfos()
    },
    data() {
        return {
            show: false,
            tabIndex: 0,
            activeIndex: 1,
            onReachBottoms: 1,
            nowCoin: {},
        }
    },
    methods: {
        async getUserInfos() {
            let res = await this.$api.getUserInfo()
            if (res.code == 200) {
                if (!res.result.loginPassSet) {
                    this.show = true
                }
            }
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        },
        onTabChange(e) {
            this.tabIndex = e
        },
        changeTab(e) {
            this.activeIndex = e
        }
    },
}
</script>

<style lang="scss" scoped>
.alert-popup {
    width: 592rpx;
    padding: 66rpx 52rpx 68rpx 52rpx;
    background-color: #fff;
    text-align: center;

    .icon-wrap {
        margin-bottom: 50rpx;
        display: flex;
        justify-content: center;

        image {
            width: 128rpx;
            height: 128rpx;
        }
    }

    .question {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #000;
        margin-bottom: 34rpx;
        display: block;
    }

    .btn-group {
        .btn {
            height: 88rpx;
            line-height: 88rpx;
            border-radius: 20rpx;
            font-size: 28rpx;
            text-align: center;
            // margin-bottom: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
        }

        .need {
            background-color: #FF82A3;
            color: #fff;
        }

        .cancel {
            margin-top: 18rpx;
            background-color: #F2F2F2;
            color: #000;
        }
    }
}
</style>