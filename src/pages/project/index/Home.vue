<template>
    <view class="page">
        <Index v-if="tabIndex == 0" @nav_contract="nav_contract" />
        <market v-if="tabIndex == 1" @nav_contract="nav_contract" />
        <trade v-if="tabIndex == 2" ref="trade" />
        <us v-if="tabIndex == 3" ref="ustab" />
        <Personal v-if="tabIndex == 4" />
        <!-- <tabbar :current="activeIndex" @changeTab="changeTab" /> -->
        <BottomTabBar v-model="tabIndex" @change="onTabChange" :value="tabIndex" />

        <u-popup border-radius="40" mode="center" v-model="show" safe-area-inset-bottom round>
            <view class="alert-popup">
                <view class="icon-wrap">
                    <image src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385641448773869568.png" />
                </view>
                <text class="question">是否设置登录密码？</text>
                <view class="btn-group">
                    <view class="btn need" @click="nav_to('setPwd')">需要</view>
                    <view class="btn cancel" @click="show = false">不需要</view>
                </view>
            </view>
        </u-popup>
		<fpop ref="fpop"></fpop>
		<view class="dow_view" v-if="showDowApp&tabIndex==0">
			<view class="left">
				<u-icon name="https://pro-oss.pinkwallet.com/image/1388222318571511808.png" size="80rpx"></u-icon>
				<view class="title_view">
					<view class="title">PinkWallet APP</view>
					<view class="sub_title">随时随地，开启交易</view>
				</view>
			</view>
			<view class="right">
				<view class="but" @click="dowApp">
					下载
				</view>
				<u-icon @click="showDowApp=false" name="https://pro-oss.pinkwallet.com/image/1388222034092843008.png" size="40"></u-icon>
			</view>
		</view>
    </view>
</template>

<script>
import market from "../../../pagesA/project/market"
import Index from "./index"
import Personal from "../personal/index"
import trade from "../../../pagesA/project/trade/index"
import us from "../../../pagesA/project/us/index"
import BottomTabBar from "../../../components/public/BottomTabBar"
import fpop from '@/components/force-updates/force-updates.vue'
export default {
    components: {
        Index,
        BottomTabBar,
        trade,
        us,
        Personal,
        market,
		fpop
    },
    onReachBottom() {
        if (this.tabIndex == 2) {
            this.onReachBottoms++
            this.$refs.trade.onBottom()
        }
    },
    watch: {
        tabIndex(newval) {
            if (newval == 3) {
                setTimeout(() => {
                    // this.$refs.ustab.getIndexData()
                    this.$refs.ustab.socketon()
                }, 100);
            }
        }
    },
    onHide() {
        if (this.tabIndex == 3) {
            setTimeout(() => {
                this.$refs.ustab.socketoff()
            }, 100);
        }
    },
    onLoad(option) {
        if (option) {
            if (option.coin) {
                this.nowCoins = JSON.parse(decodeURIComponent(option.coin));
            }
            if (option.tabIndex) {
                this.tabIndex = option.tabIndex
            }
            console.log(this.nowCoins);
        }
        this.getUserInfos()
		// #ifdef H5
			this.showDowApp = true
		// #endif
    },
    data() {
        return {
            show: false,
            tabIndex: 0,
            activeIndex: 1,
            onReachBottoms: 1,
            nowCoin: {},
			showDowApp:false
        }
    },
    methods: {
        nav_contract(e) {
            if (e) {
                this.tabIndex = 2
                this.onTabChange(2)
            }
        },
        async getUserInfos() {
            let res = await this.$api.getUserInfo()
            if (res.code == 200) {
                if (!res.result.loginPassSet) {
                    this.show = true
                }
            }
        },
        nav_to(e) {
            this.$Router.push({
                name: e
            })
        },
        onTabChange(e) {
            console.log(e);

            this.tabIndex = e
        },
        changeTab(e) {
            this.activeIndex = e
        },
		dowApp(){
			let platform = uni.getSystemInfoSync().platform
			console.log(platform)
			if(platform == 'ios'){
				//ios下载地址
				window.location.href = 'https://apps.apple.com/us/app/bigverse/id1605702361'
			}else{
				//安卓下载地址
				window.location.href = 'https://pro-oss.pinkwallet.com/h5/pinkwallet/pinkwallet.apk'
				
			}
		}
    },
}
</script>

<style lang="scss" scoped>
.alert-popup {
    width: 592rpx;
    padding: 66rpx 52rpx 68rpx 52rpx;
    background-color: #fff;
    text-align: center;

    .icon-wrap {
        margin-bottom: 50rpx;
        display: flex;
        justify-content: center;

        image {
            width: 128rpx;
            height: 128rpx;
        }
    }

    .question {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        line-height: 40rpx;
        color: #000;
        margin-bottom: 34rpx;
        display: block;
    }

    .btn-group {
        .btn {
            height: 88rpx;
            line-height: 88rpx;
            border-radius: 20rpx;
            font-size: 28rpx;
            text-align: center;
            // margin-bottom: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
        }

        .need {
            background-color: #FF82A3;
            color: #fff;
        }

        .cancel {
            margin-top: 18rpx;
            background-color: #F2F2F2;
            color: #000;
        }
    }
}
.dow_view{
	display: flex;
	justify-content: space-between;
	align-items: center;
	width:690rpx;
	position: fixed;
	bottom:100rpx;
	left: 0;
	right: 0;
	height: 144rpx;
	z-index:1000;
	background-color:rgba(0, 0,0, 0.7);
	margin:0 auto;
	border-radius:72rpx;
	padding:34rpx 44rpx;
	font-family: PingFang SC;
	.left{
		display: flex;
		align-items: center;
		color:#fff;
		.title_view{
			margin-left:20rpx;
			.title{
				font-size:28rpx;
			}
			.sub_title{
				font-size:24rpx;
				color:rgba(255, 255,255, 0.4);
				margin-top: 10rpx;
			}
		}
	}
	.right{
		display: flex;
		align-items: center;
		.but{
			background-color:#FF82A3;
			width:132rpx;
			height:64rpx;
			border-radius:32rpx;
			color:#fff;
			font-size:24rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 24rpx;
			
		}
	}
}
</style>