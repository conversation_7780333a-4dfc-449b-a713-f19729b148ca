<template>
    <view class="profile-container">
        <u-navbar :border-bottom="false" :title="symbolNow.symbol">
        </u-navbar>
        <view class="assets flex-column">
            <text class="usd">{{ symbolNow.totalBalance }}</text>
            <text class="crypo" v-if="symbolNow.fiat == 'false' && symbolNow.price">{{ formatExchangeRate(symbolNow)
                }}USD</text>
        </view>
        <view class="function  flex_all" style="justify-content: space-around;" v-if="symbolNow.fiat == 'false'">
            <view class="item flex-column-all" @click="nav_to('buy', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250407/b3815b6aeff9e286a7fb348411230308_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.Buy") }} </view>
            </view>
            <view class="item flex-column-all" @click="nav_to('charge', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250407/3e7bf7815f65962d174c073234e95070_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.Receive") }}</view>
            </view>
            <!-- margin -->
            <view class="item flex-column-all " @click="nav_to('TransferOut', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250407/ffd4a9a45fd9a35fc0e10085021a7dd7_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.Send") }}</view>
            </view>
            <view class="item flex-column-all" @click="nav_to('swap', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250407/52949193833fcb7dcc7b161a31cc2092_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.Swap") }}</view>
            </view>
            <view class="item flex-column-all " @click="nav_to('sell', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250408/64d36204f33d1ac4b372c503df5b5302_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.Sell") }}</view>
            </view>
        </view>
        <view class="function flex_y" v-else style="justify-content: space-around;">
            <view class="item flex-column-all" @click="nav_to('CurrencyCharge', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250407/3e7bf7815f65962d174c073234e95070_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.Receive") }}</view>
            </view>
            <view class="item flex-column-all" @click="nav_to('TransferOut', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250407/ffd4a9a45fd9a35fc0e10085021a7dd7_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.Send") }}</view>
            </view>
            <view class="item flex-column-all" @click="nav_to('swap', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250407/52949193833fcb7dcc7b161a31cc2092_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.convert") }}</view>
            </view>
            <view class="item flex-column-all" @click="nav_to('withdraw', symbolNow.symbol)">
                <view class="bg flex_all">
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250414/3873d51678b98c3b8708cb06bcb4ed90_216x216.png" />
                </view>
                <view class="functionname">{{ $t("Types.Withdrawal") }}</view>
            </view>
        </view>
        <view class="CurrencyInfo">

            <!-- <view class="line"></view> -->
            <view style="height: 58rpx;" v-if="symbolNow.fiat == 'false'"></view>
            <view style="height: 1%;" v-if="symbolNow.fiat == 'true'"></view>

            <view class="chart" v-if="symbolNow.fiat == 'false' && isTradable">
                <view class="chart_box_title">{{ $t("title.chart") }}</view>
                <!-- <div class="chart-wrapper" ref="chart"></div> -->
                <view class="chart-wrapper">
                    <!-- @finished="init" -->
                    <l-echart @touchmove="touchMove" ref="chartRef"></l-echart>
                </view>
                <view class="time">
                    <text v-for="(item, index) in times" :key="index" @click="checks(item, index)"
                        :class="{ timeActive: nowTime == index }">{{ item.name }}
                    </text>
                </view>
            </view>
            <view class="trans">
                <translation :nowsymbol="symbolNow.symbol" :subpage="true" />
            </view>
        </view>
        <zero-loading type="sword" v-if="loading"></zero-loading>

    </view>
</template>

<script>
// import * as echarts from "echarts";
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min';

import translation from "./components/translation"
export default {
    components: {
        translation
    },
    data() {
        return {
            loading: false,
            isTradable: true,
            timeArray: [],
            priceArray: [],
            timeNode: "minute_div5_part",
            option: {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    confine: true
                },
                legend: {
                    data: ['热度', '正面', '负面']
                },
                grid: {
                    left: 20,
                    right: 20,
                    bottom: 15,
                    top: 40,
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'value',
                        axisLine: {
                            lineStyle: {
                                color: '#999999'
                            }
                        },
                        axisLabel: {
                            color: '#666666'
                        }
                    }
                ],
                yAxis: [
                    {
                        type: 'category',
                        axisTick: { show: false },
                        data: ['汽车之家', '今日头条', '百度贴吧', '一点资讯', '微信', '微博', '知乎'],
                        axisLine: {
                            lineStyle: {
                                color: '#999999'
                            }
                        },
                        axisLabel: {
                            color: '#666666'
                        }
                    }
                ],
                series: [
                    {
                        name: '热度',
                        type: 'bar',
                        label: {
                            normal: {
                                show: true,
                                position: 'inside'
                            }
                        },
                        data: [300, 270, 340, 344, 300, 320, 310],
                    },
                    {
                        name: '正面',
                        type: 'bar',
                        stack: '总量',
                        label: {
                            normal: {
                                show: true
                            }
                        },
                        data: [120, 102, 141, 174, 190, 250, 220]
                    },
                    {
                        name: '负面',
                        type: 'bar',
                        stack: '总量',
                        label: {
                            normal: {
                                show: true,
                                position: 'left'
                            }
                        },
                        data: [-20, -32, -21, -34, -90, -130, -110]
                    }
                ]
            },
            nowTime: 0,
            times: [
                {
                    name: '5min',
                    value: 'minute_div5_part'
                },
                {
                    name: "10min",
                    value: 'minute_div10_part',
                },
                {
                    name: "30min",
                    value: 'minute_div30_part',
                },
                {
                    name: "1H",
                    value: 'hour',
                },
                {
                    name: "1D",
                    value: 'day'
                },
                {
                    name: "1M",
                    value: 'month',
                },
                {
                    name: "1Y",
                    value: 'year',
                },
            ],
            title: "",
            veify: {
                name: false,
            },
            kybstatus: "",
            kycstatus: "",
            fetching: false,
            qrcodeUrl: '0x68a6ac174E3846035F2aEF4D1F91CB3b682ff331',
            kyclink: "",
            options: {
                useDynamicSize: false,
                errorCorrectLevel: 'Q',
                // margin: 10,
                areaColor: "#fff",
                // 指定二维码前景，一般可在中间放logo
                // foregroundImageSrc: require('static/image/logo.png')
            },
            NEW_ACCESS_TOKEN: "",
            verificationPopup: false,
            symbolNow: {}
        }
    },
    onLoad(options) {
        if (options.symbol) {
            this.symbolNow = options
            this.getexistCoinPair()
        }
        uni.setNavigationBarTitle({
            title: this.$t("page.detail") // 切换语言后重新设置标题
        })
        setTimeout(() => {
            this.init()
        }, 500);
    },
    methods: {
        touchMove(e) {
            // const moveX = e.touches[0].x;
            // if (Math.abs(moveX - this.startX) > 10) {
            // #ifdef APP-PLUS
            // plus.vibration.vibrate(50); // APP环境下触发50ms震动
            uni.vibrate({
                duration: 50, // 震动时长，单位毫秒
                success: function () {
                    console.log('震动成功');
                },
                fail: function () {
                    console.log('震动失败');
                }
            });
            // #endif
            // }
            // this.chart.scroll(e);
        },
        async getexistCoinPair() {
            let res = await this.$api.existCoinPair({
                coin0: this.symbolNow.symbol,
                coin1: 'USD',
            })
            if (res.code == 200 && res.result) {
                this.isTradable = res.result.isTradable == 'true' ? true : false
            } else {
                this.isTradable = false
            }
        },
        async init() {
            await this.getLine()
            // chart 图表实例不能存在data里
            const chart = await this.$refs.chartRef.init(echarts);
            const option = {
                dataZoom: [{
                    type: 'inside', //1平移 缩放
                    throttle: '50', //设置触发视图刷新的频率。单位为毫秒（ms）。
                    minValueSpan: 6, //用于限制窗口大小的最小值,在类目轴上可以设置为 5 表示 5 个类目
                    start: 1, //数据窗口范围的起始百分比 范围是：0 ~ 100。表示 0% ~ 100%。
                    end: 50, //数据窗口范围的结束百分比。范围是：0 ~ 100。
                    zoomLock: true, //如果设置为 true 则锁定选择区域的大小，也就是说，只能平移，不能缩放。
                }],
                title: {
                    show: false,
                },
                tooltip: {
                    trigger: 'axis',
                    // renderMode: 'html', // 强制使用 HTML 渲染
                    backgroundColor: "#F4F4F4",
                    borderColor: "#008E28",
                    borderWidth: 1,
                    textStyle: {
                        color: "#000000",
                        fontStyle: "normal",
                        fontWeight: "400",
                        fontFamily: "Gilroy-SemiBold"
                    },
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    },
                    formatter: (params) => {
                        const date = params[0].name;
                        const value = params[0].value;
                        return `${value} ${date}`;
                    },
                    extraCssText: `
                        border-radius: 10px !important;
                        padding: 10px 12px;
                        box-shadow: 0 2px 6px rgba(0,0,0,0.15);
                        color: "#000000",
                        fontStyle: "normal",
                        fontWeight: "400",
                        fontFamily: "Gilroy-SemiBold !important"
                    `
                },
                grid: {
                    left: '4%', // 距离左边的距离
                    right: '0%', // 距离右边的距离
                    top: '10%', // 距离顶部的距离
                    bottom: '22%' // 距离底部的距离
                },
                xAxis: {
                    type: 'category', // x轴类型为类目轴
                    boundaryGap: true, // 取消x轴两端空白
                    interval: 0,
                    // data: ['3:00', '4:00', '5:00', '6:00', '7:00', '8:00', '9:00', '10:00', "11:00", "12:00", "13:00", '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'], // x轴类目数据
                    data: this.timeArray,
                    axisTick: {
                        show: false, // 不显示刻度
                    },
                    axisLabel: { //x轴文字的配置
                        color: "#666666", //Y轴内容文字颜色
                        interval: 'auto', // 可以设置为具体的数字，如 5，表示显示每隔 5 个标签
                        fontSize: 12,//调整坐标轴字体大小
                        fontFamily: "Gilroy-SemiBold",
                        // margin: 
                    },
                    axisLine: {
                        lineStyle: {
                            // color: '#E0E7FF' // x轴线的颜色
                            color: 'transparent'
                        }
                    },
                    splitLine: {
                        // 纵向分割线
                        show: false,
                        lineStyle: {
                            // color: '#D2DAE3'
                        }
                    }
                },
                yAxis: {
                    show: false,
                    type: 'value', // y轴类型为数值轴
                    // name: '单位：斤/元', //单位
                    nameLocation: 'end', // (单位个也就是在在Y轴的最顶部)
                    //单位的样式设置
                    nameTextStyle: {
                        color: "#999", //颜色
                        padding: [0, 20, 0, 40], //间距分别是 上 右 下 左
                        fontSize: 14,
                    },
                    axisLabel: { //y轴文字的配置
                        color: "#777", //Y轴内容文字颜色
                    },
                    axisLine: { //y轴线的配置
                        show: true, //是否展示
                        lineStyle: {
                            color: "#E0E7FF", //y轴线的颜色（若只设置了y轴线的颜色，未设置y轴文字的颜色，则y轴文字会默认跟设置的y轴线颜色一致）
                            width: 1, //y轴线的宽度
                            //type: "solid" //y轴线为实线
                        },
                    },
                    axisTick: {
                        show: false // y轴上的小横线
                    },
                    // 横向分割线
                    splitLine: {
                        show: true, // 显示分割线。
                        lineStyle: {
                            // 分割线样式。
                            color: '#D2DAE3', // 分割线颜色。
                            type: 'dotted' // 分割线类型。 solid实线  dotted点型虚线  dashed线性虚线
                        }
                    },
                    splitNumber: 4, // 指定横向分割线的数量
                },
                //  图例
                // legend: {
                //     data: ['近7天价格变化'],
                //     left: 'center',
                //     top: 'bottom'
                // },
                series: [{
                    /* 
                    // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，
                    // 相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，
                    // 则该四个值是绝对的像素位置
                    */
                    type: 'line', // 图表类型为折线图
                    // datasetId: 'dataset_since_1950_of_germany',
                    showSymbol: false,
                    // data: [120, 180, 150, 80, 70, 110, 130, 70, 110, 130, 70, 110, 130, 2, 20, 50, 145, 200, 5], // 折线图数据
                    data: this.priceArray,
                    // smooth: true, // 平滑曲线
                    // 区域颜色渐变
                    // areaStyle: {
                    //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
                    //         [{
                    //             offset: 0,
                    //             color: "rgba(254, 235, 215, 1)",
                    //         },
                    //         {
                    //             offset: 1,
                    //             color: "rgba(254, 235, 215, 0)",
                    //         },
                    //         ], false
                    //     ),
                    // },

                    // 折线颜色
                    lineStyle: {
                        // 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
                        // color: {
                        //     type: 'linear',
                        //     x: 0,
                        //     y: 0,
                        //     x2: 0,
                        //     y2: 1,
                        //     colorStops: [{
                        //         offset: 0, color: 'red' // 0% 处的颜色
                        //     }, {
                        //         offset: 1, color: 'blue' // 100% 处的颜色
                        //     }],
                        //     global: false // 缺省为 false
                        // }
                        color: "#FF95B1",
                        width: '2',
                    },
                    color: "#008E28", //拐点颜色
                    // symbol: 'circle', // 设置拐点形状、
                    symbolSize: 10, // 设置拐点大小
                    // 拐点处显示值
                    itemStyle: {
                        symbol: 'none', // 隐藏所有节点
                        normal: {
                            node: { show: false },
                            label: { show: false }
                        }
                    },
                }],
            }
            console.log(this.timeArray, this.priceArray,);
            console.log('setoption');

            chart.setOption(option)
        },
        async getLine() {
            this.loading = true
            let res = await this.$api.priceLine({
                pageNum: 1,
                // instrument: this.fromCurrency ? this.fromCurrency + this.toCurrency + '.SPOT' : "",
                fromCoin: this.symbolNow.symbol,
                toCoin: 'USD',
                priceLineType: this.timeNode
            })
            if (res.code == 200) {
                this.timeArray = []
                this.priceArray = []
                // 获取当前时间节点类型
                const type = this.timeNode;
                this.loading = false

                // 格式化函数
                const formatTime = (timestamp) => {
                    const date = new Date(timestamp);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    const hour = String(date.getHours()).padStart(2, '0');
                    const minute = String(date.getMinutes()).padStart(2, '0');

                    if (type.includes('minute')) {
                        return `${month}/${day} ${hour}:${minute}`; // 04/11 14:30
                    } else if (type == 'hour' || type == 'day') {
                        return `${month}/${day}`; // 04/11
                    } else if (type == 'year' || type == 'month') {
                        return `${year}/${month}`; // 2025/04
                    } else {
                        return '--'// 默认 fallback
                    }
                };

                // this.timeArray = res.result.map(item => formatTime(item.crateAt)).reverse();
                this.timeArray = res.result.map(item => {
                    return this.formatTimeByNode(item.crateAt, type);
                }).reverse();
                this.priceArray = res.result.map(item => item.price).reverse();

            } else {
                this.loading = false
            }
        },
        formatTimeByNode(timestamp, type) {
            const date = new Date(timestamp * 1000);

            const pad = (n) => String(n).padStart(2, '0');

            const floorTo = (value, step) => Math.floor(value / step) * step;

            switch (type) {
                case 'minute_div5_part':
                    date.setMinutes(floorTo(date.getMinutes(), 5));
                    return `${pad(date.getHours())}.${pad(date.getMinutes())}`;
                case 'minute_div10_part':
                    date.setMinutes(floorTo(date.getMinutes(), 10));
                    return `${pad(date.getHours())}.${pad(date.getMinutes())}`;
                case 'minute_div30_part':
                    date.setMinutes(floorTo(date.getMinutes(), 30));
                    return `${pad(date.getHours())}.${pad(date.getMinutes())}`;
                case 'hour':
                    return `${pad(date.getHours())}:00`;
                case 'day':
                    return `${pad(date.getMonth() + 1)}/${pad(date.getDate())}`;
                case 'month':
                    return `${date.getFullYear()}/${pad(date.getMonth() + 1)}`;
                case 'year':
                    return `${date.getFullYear()}`;
                default:
                    return '--';
            }
        },
        nav_to(e, name) {
            // if (e == 'CurrencyCharge') {
            //     this.$u.toast('Coming Soon')
            //     return
            // }
            if (e == 'buy') {
                this.$u.toast('Coming Soon')
                return
            }
            this.$Router.push({
                name: e,
                params: {
                    fromCurrency: name
                }
            })
        },
        checks(item, index) {
            if (this.nowTime == index) return
            this.nowTime = index;
            this.timeNode = item.value
            this.init()
        },
        formatExchangeRate(crypto) {
            if (!crypto || !crypto.price || isNaN(crypto.price) || crypto.price === 0) {
                return '0';
            }
            if (crypto.symbol == 'USD') {
                return crypto.price
            }
            const rate = (crypto.price) * crypto.totalBalance;
            return `${rate.toFixed(4)}`;
        },
    }
};
</script>

<style scoped lang="scss">
.profile-container {
    background: radial-gradient(114.19% 35.15% at 50% 16.76%, #FFBDCE 0%, #FF8DAB 100%);
    /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */
    min-height: 100vh;

    .assets {
        margin: 90rpx 32rpx 36rpx 32rpx;

        .crypo {
            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 32rpx;
            line-height: 120%;
            letter-spacing: 0%;
            color: #000;

        }

        .usd {
            font-family: Gilroy-ExtraBold;
            font-weight: 400;
            font-size: 80rpx;
            line-height: 120%;
            letter-spacing: 0%;
            color: #000;
        }
    }

    .function {
        padding: 0 0 50rpx 0;

        .margin {
            margin: 0 84rpx;
        }

        .item {
            width: 146rpx;

            .functionname {
                margin-top: 10rpx;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 39rpx;
                color: #000;
                white-space: nowrap;
            }

            .bg {
                // border-radius: 50%;
                // width: 120rpx;
                // height: 120rpx;
                // background: #FFFFFF33;
                // border: 2rpx solid #00000033;

                image {
                    width: 108rpx;
                    height: 108rpx;
                }
            }
        }
    }

    .CurrencyInfo {
        height: calc(100vh - 240rpx);
        border-top-left-radius: 60rpx;
        border-top-right-radius: 60rpx;
        background: #fff;
        // padding: 0 32rpx;


        .line {
            height: 2rpx;
            background: #E0E0E0;
        }

        .chart {

            .chart-wrapper {
                z-index: 999;
                // width: 100vw;
                height: 140px;
                // height: 100%; /* 根据父容器大小自适应 */
                margin: 0 30rpx 0 0;
            }

            .time {
                padding: 16rpx 32rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                // color: #ffffff;
                font-family: Gilroy-SemiBold;
                font-weight: 400;
                font-size: 12*2rpx;
                line-height: 14.4*2rpx;
                margin-bottom: 32rpx;
                border-top: 2rpx solid #E0E0E0;
                border-bottom: 2rpx solid #E0E0E0;

                .timeActive {
                    // background: linear-gradient(180deg, #ef91fb 0%, #40f8ec 100%);
                    background: #008E28;


                    font-weight: 800;
                    font-size: 24rpx;
                    color: #fff;
                    line-height: 7rpx;
                    border-radius: 40rpx;
                    border: 1rpx solid #DFE2E4;
                }

                text {
                    //background-color: #2b2b2b;
                    padding: 19rpx 19rpx;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    //color: #FFFFFF;
                    line-height: 7rpx;
                }
            }

            .chart_box_title {
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 18*2rpx;
                line-height: 100%;
                color: #000;
                margin: 48rpx 0 0 32rpx;
            }
        }

        .trans {
            margin: 0 32rpx;
        }

        // .Transaction_History {
        //     margin: 40rpx 32rpx;

        //     .Transaction_title {
        //         .left {
        //             font-family: Gilroy-Bold;
        //             font-weight: 400;
        //             font-size: 18*2rpx;
        //             line-height: 100%;
        //             letter-spacing: 0%;
        //             color: #000;
        //         }

        //         .right {
        //             font-family: Gilroy-SemiBold;
        //             font-weight: 400;
        //             font-size: 28rpx;
        //             line-height: 160%;
        //             letter-spacing: 0%;
        //             text-align: center;
        //             text-decoration: underline;
        //             text-decoration-style: solid;
        //             text-decoration-offset: 15%;
        //             text-decoration-thickness: 1.5px;
        //             color: #FF82A3;
        //         }
        //     }

        //     .Transaction_list {
        //         margin-top: 30rpx;

        //         .Transaction_item {
        //             // height: 108*2rpx;
        //             // background: #FF82A326;
        //             // border: 2rpx solid #D9D6D6;
        //             // border-radius: 40rpx;
        //             padding-bottom: 40rpx;
        //             border-bottom: 2rpx solid #E0E0E0;
        //             margin-top: 20rpx;

        //             &:first-child {
        //                 margin-top: 0;
        //             }

        //             .tran_right {
        //                 .deposit {
        //                     color: #FF82A3;
        //                     background: #FFF3F6;
        //                 }

        //                 .send {
        //                     background: #E6F4EA;
        //                     color: #008E28;
        //                 }

        //                 .btn {

        //                     margin-left: 48rpx;
        //                     font-family: Gilroy-Bold;
        //                     font-weight: 400;
        //                     font-size: 28rpx;
        //                     line-height: 120%;
        //                     letter-spacing: 0%;
        //                     text-align: right;
        //                     vertical-align: middle;
        //                     padding: 14rpx 20rpx;
        //                     border-radius: 41*2rpx;
        //                 }

        //                 .info {
        //                     .plusoradd {
        //                         font-family: Gilroy-SemiBold;
        //                         font-weight: 400;
        //                         font-size: 16*2rpx;
        //                         line-height: 120%;
        //                         letter-spacing: 0%;
        //                         text-align: center;
        //                         vertical-align: middle;
        //                     }

        //                     .num {
        //                         font-family: Gilroy-SemiBold;
        //                         font-weight: 400;
        //                         font-size: 28rpx;
        //                         line-height: 160%;
        //                         letter-spacing: 0%;
        //                         text-align: center;
        //                         vertical-align: middle;
        //                         color: #333;
        //                     }
        //                 }
        //             }

        //             .tran_left {
        //                 .times {
        //                     font-family: Gilroy-SemiBold;
        //                     font-weight: 400;
        //                     font-size: 14*2rpx;
        //                     line-height: 160%;
        //                     letter-spacing: 0%;
        //                     vertical-align: middle;
        //                     color: #000;
        //                 }

        //                 .add {
        //                     margin-top: 12rpx;
        //                     font-family: Gilroy-SemiBold;
        //                     font-weight: 400;
        //                     font-size: 14*2rpx;
        //                     line-height: 160%;
        //                     letter-spacing: 0%;
        //                     vertical-align: middle;
        //                     color: #999999;
        //                 }
        //             }

        //         }
        //     }
        // }
    }



}
</style>