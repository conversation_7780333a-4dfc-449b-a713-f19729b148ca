<template>
	<coverIndex v-if="Firstopen" @closeCover="closeCover" />
	<view class="wallet-container" @click="currentIndex = null" v-else>
		<!-- <zero-loading type="sword" v-if="loading"></zero-loading> -->

		<view class="barHeight"></view>
		<view class="areapink"></view>
		<view class="topcontainer" ref="topcontainer">

			<!-- 顶部导航 -->
			<view class="header">
				<view class="left flex_y">
					<image class="icon-left"
						src="https://pro-oss.pinkwallet.com/image/1370098181554659328.png"
						@click="nav_to('user')"></image>
					<image class="logo"
						src="https://pro-oss.pinkwallet.com/image/1370105197085941760.png" />
				</view>

				<!-- <image mode="widthFix"
					src="https://pro-oss.pinkwallet.com/image/20250401/c998b2229618f6007a0d430fb7b3edea_202x59.png"
					class="logo" /> -->
				<view class="service flex_all" @click="nav_to('Notification')">
					<view class="dot" v-if="$store.state.isMsg"></view>
					<image src="https://pro-oss.pinkwallet.com/image/1370105797093711872.png" />
				</view>
			</view>

			<!-- 钱包信息 -->
			<view class="wallet-info">
				<view class="wallet-left flex-column">
					<view class="wallet-title">
						<!-- <view>{{ $t("index.Balance.YourBalance") }}</view> -->
						<view>Total Assets</view>
						<image @click="eyeoff = !eyeoff" v-if="eyeoff"
							src="https://pro-oss.pinkwallet.com/image/1370353025834115072.png" />
						<image @click="eyeoff = !eyeoff" v-else
							src="https://pro-oss.pinkwallet.com/image/1370353145610854400.png" />
					</view>

					<view class="wallet-money" @click="toggleRotate">
						<text class="wallet-balance" v-if="eyeoff">{{ '***' }}</text>
						<text class="wallet-balance" :style="{ fontSize: dynamicFontSize + 'rpx' }" v-else>${{
							Number(allassets).toFixed(2).toLocaleString('en-US') ||
							'0.00'
						}}</text>
						<view class="wallet-currency flex_y" @click="showcurrencyShow = !showcurrencyShow">
							{{ nowsymbol }}
							<image :style="{ transform: `rotate(${rotate}deg)` }"
								src="https://pro-oss.pinkwallet.com/image/20250401/378f682f03cd8682e54f32d68d2d57e7_56x56.png" />

							<transition name="expand-slide">
								<view class="helpoption" v-show="showcurrencyShow">
									<view v-for="(item, index) in userCoins" :key="index" class="Roptions"
										@click.stop="SetSymbol(item)">
										<text :style="{ color: nowsymbol == item.name ? '#008E28' : '' }">{{ item.name
										}}</text>
									</view>
								</view>
							</transition>
						</view>
					</view>
				</view>

				<view class="charts flex_all" @click="toogleChart">
					<transition name="slide">
						<image v-if="isChart"
							src="https://pro-oss.pinkwallet.com/image/20250402/11082ca6215d0cde28152efbd5070980_80x72.png"
							class="arrow" key="arrow" />
						<image v-else
							src="https://pro-oss.pinkwallet.com/image/1370354024200101888.png"
							class="chart" key="chart" />
					</transition>
				</view>
				<!-- <div ref="charts" @click="toggleChart" style="width: 240rpx; height: 240rpx;"></div> -->
			</view>

			<!-- 涨跌 -->
			<view class="rise" v-if="!showTransaction && cdObj.cdAmount">
				<!-- <text class="number">$47.10 (+2.21%)</text>1day 
				color: #008E28;
				 -->
				<text class="number" :style="{ color: cdObj.increaseRate < 0 ? '#FF82A3' : '#04A431' }">${{
					(cdObj.cdAmount - cdObj.ydAmount).toFixed(2) }} (<text v-if="cdObj.increaseRate > 0">+</text>{{
						(cdObj.increaseRate * 100).toFixed(2)
					}}%)</text>1{{ $t('index.day') }}
			</view>

			<!-- 金刚区 -->
			<menuGold />

			<!-- receive法币加密 -->
			<!-- <div class="receive-container" v-if="showTransaction">
				<div class="receive-back-button" @click="goBack">
					<image
						src="https://pro-oss.pinkwallet.com/image/20250402/822842d176de9a51b5f0ccd7aef8f5e4_80x80.png"
						alt="Back" class="receive-back-icon" />
				</div>

				<div class="receive-content">
					<div class="receive-currencies" @click="nav_to('CurrencyCharge')">
						<div class="receive-icons">
							<image
								src="https://pro-oss.pinkwallet.com/image/20250402/4b2f90b5636f0bfc0d6245edbf11958f_96x96.png"
								class="receive-icon" />
							<image
								src="https://pro-oss.pinkwallet.com/image/20250402/6e0051eb6280038c6a0ec5b2c265f4be_96x96.png"
								class="receive-icon" />
							<image
								src="https://pro-oss.pinkwallet.com/image/20250402/21d5979a0b51a2c0e6d4da53223be6e5_96x96.png"
								class="receive-icon" />
							<image
								src="https://pro-oss.pinkwallet.com/image/20250402/9413a6e0e9b963c74a5b914efad35670_96x96.png"
								class="receive-icon" />
						</div>
						<div class="receive-title">{{ $t('index.Balance.ReceiveCurrencies') }}</div>

					</div>

					<div class="receive-crypto" @click="nav_to('charge')">
						<div class="receive-icons">
							<image
								src="https://pro-oss.pinkwallet.com/image/20250402/dac99c54087b628b46a601ec5a032080_97x96.png"
								class="receive-icon" />
							<image
								src="https://pro-oss.pinkwallet.com/image/20250402/eea11fbee0b031010d68572cce8abc62_97x96.png"
								class="receive-icon" />
							<image
								src="https://pro-oss.pinkwallet.com/image/20250402/908be54e8ea3e9896475485bfefe63e6_97x96.png"
								class="receive-icon" />
							<image
								src="https://pro-oss.pinkwallet.com/image/20250402/c6c5d6c05fb98a72902ea6c2b108d0ae_97x96.png"
								class="receive-icon" />
						</div>
						<div class="receive-title">{{ $t('index.Balance.ReceiveCrypto') }}</div>

					</div>
				</div>
			</div> -->

			<transition name="fade">
				<view v-if="isChart && !showTransaction" class="chart-container">
					<div class="chart-wrapper">
						<l-echart ref="chartRef"></l-echart>
					</div>

					<view class="line"></view>
					<view class="time">
						<text v-for="(item, index) in times" :key="index" @click="checks(item, index)"
							:class="{ timeActive: nowTime == index }">{{ item.name }}
						</text>
					</view>
				</view>
			</transition>

			<!-- 操作按钮 -->
			<!-- <view class="bottom-actions flex_divide" v-if="!showTransaction">
				<view v-for="(item, index) in actions" @click="nav_to(item.path)" :key="index" class="action-item">
					<view class="icon">
						<image :src="item.icon" mode="widthFix" />
					</view>
					<text class="label">{{ item.label }}</text>
				</view>
			</view> -->
			<!-- 邀请奖励 -->
			<view class="invite-box">
				<u-swiper :list="swiperList" bg-color="transparent" :height="224" @click="bannerClick"
					:title-style="{ 'font-size': '24rpx' }" :border-radius="20" mode="none"></u-swiper>
			</view>

			<!-- 交易记录 -->
			<!-- <div v-if="showTransaction">
				<translation />
			</div> -->
		</view>

		<view class="bomcontainer resizable-box" @touchstart="startDrag" @touchend="stopDrag"
			@touchmove.prevent="onDragging"
			:style="{ height: boxHeight + 'px', transition: isDragging ? 'none' : '0.3s ease-out' }">
			<!-- v-if="isDragging"  ,-->
			<view class="drag-handle"></view>
			<bom @nav_contract="nav_contract" :hideZeroAsset="hideZeroAsset" @userCoin="userCoin" :count="count"
				@nav_stock="nav_stock" @getFiatList="getFiatList" />
		</view>

		<!-- <BottomTabBar v-model="tabIndex" @change="onTabChange" /> -->

	</view>
</template>

<script>
import store from "../../../store"
// import * as echarts from 'echarts';
import bom from "./components/indexbom"
import coverIndex from "./coverIndex"
import * as echarts from "echarts";
import translation from "./components/translation";
import menuGold from "./components/appmenu"
import BottomTabBar from "../../../components/public/BottomTabBar"
export default {
	components: {
		bom,
		translation,
		coverIndex,
		menuGold,
		BottomTabBar
	},
	data() {
		return {
			tabIndex: 0,
			loading: true,
			count: 1,
			Firstopen: false,
			timeArray: [],
			priceArray: [],
			topHeight: 0, // topcontainer 高度
			windowHeight: 0, // 视口高度
			isApp: false, // 是否为 App 环境
			showTransaction: false,
			isChart: false,
			nowTime: 0,
			timeNode: "day",
			times: [
				{
					name: "1D",
					value: 'day'
				},
				{
					name: "1W",
					value: 'week'
				},
				{
					name: "1M",
					value: 'month',
				},
				{
					name: "1Y",
					value: 'year',
				},
			],
			nowsymbol: '',
			userCoins: [
				// {
				// 	name: 'USD'
				// },
				// {
				// 	name: 'CNY'
				// }
			],
			showcurrencyShow: false,
			allassets: "",
			boxHeight: 0, // 初始最小高度
			startY: 0, // 触摸起始点
			startHeight: 0, // 触摸时 box 的初始高度
			// minHeight: 700, // 最小高度
			maxHeight: 0, // 最大高度
			isDragging: false, // 是否正在拖动
			rotate: 0,
			hideZeroAsset: false, // 隐藏零资产
			legal: false,
			RightOption: [
				{
					value: '0',
					label: '存入'
				},
				{
					value: '1',
					label: '转出'
				},
				{
					value: '2',
					label: '兑换'
				},
				{
					value: '3',
					label: '明细'
				}

			],
			currentIndex: null, // 当前打开的项
			eyeoff: false,
			chart: null,
			isPieChart: true,
			pieData: [
				{ value: 40, name: 'A' },
				{ value: 30, name: 'B' },
				{ value: 20, name: 'C' },
				{ value: 10, name: 'D' }
			],
			lineData: {
				categories: ['1月', '2月', '3月', '4月'],
				values: [10, 22, 28, 43]
			},
			swiperList: [],
			isFirstLoad: true,
			cdObj: {}
		};
	},
	mounted() {
		// #ifdef APP


		const hasOpened = uni.getStorageSync('isFirstOpen');
		if (!hasOpened) {
			this.Firstopen = true
			uni.setStorageSync('isFirstOpen', true);
		}
		// #endif

		let token = uni.getStorageSync('token')
		console.log(token);

		// #ifdef APP
		if (token) {
			this.BindDevices()
		} else {
			this.$Router.push({
				name: 'login'
			})
		}
		// #endif
		this.getBannerList()
		this.isApp = typeof uni !== 'undefined' && uni.getSystemInfo

		setTimeout(() => {
			this.getHeights()
		}, 2000);
		this.getLine()
		this.getamountPriceGn()
	},
	onShow() {
		this.count++
		this.isFirstLoad = true
		this.getamountPriceGn()
	},
	watch: {
		boxHeight(newval) {
			// console.log(newval); && this.showTransaction
			if (newval <= 200) {
				this.showTransaction = true
			} else {
				this.showTransaction = false
			}
		},
		"$store.state.indexRate"(val) {
			if (val) {
				this.cdObj = val
				if (val.msg) {
					if (store.state.shouldVibrate) {
						uni.vibrateShort()
					}
					store.commit('changeMsgBoolean', val.msg)
				}
			}
		},
	},
	computed: {
		actions() {
			return [
				{ label: this.$t('Types.Buy'), path: "QuickBuy", icon: "https://pro-oss.pinkwallet.com/image/20250414/a74cf2b99e7ecd269ebcfb88799b048c_216x216.png" },
				{ label: this.$t('Types.Receive'), path: "charge", icon: "https://pro-oss.pinkwallet.com/image/20250414/d6e24ba98c48ffb545bf4de319c54582_216x216.png" },
				{ label: this.$t('Types.Send'), path: "TransferOut", icon: "https://pro-oss.pinkwallet.com/image/20250414/fe4a68116436427a8e24259540da0d00_216x216.png" },
				{ label: this.$t('Types.Swap'), path: "swap", icon: "https://pro-oss.pinkwallet.com/image/20250414/0c93b1913c6de097375dc6b6677c2a14_216x216.png" },
				{ label: this.$t('Types.Sell'), path: "sell", icon: "https://pro-oss.pinkwallet.com/image/20250414/9292aec2b8668970dd15cbb51015dc3a_216x216.png" }
			]
		},
		minHeight() {
			// #ifdef H5
			return Math.max(100, uni.getSystemInfoSync().windowHeight * 0.53); // 20% 屏幕高度
			// #endif

			// #ifdef APP
			return Math.max(100, uni.getSystemInfoSync().windowHeight * 0.45); // 20% 屏幕高度
			// #endif
		},
		formattedAssets() {
			const num = Number(this.allassets || 0);
			return num.toFixed(2).toLocaleString('en-US');
		},
		dynamicFontSize() {
			const len = this.formattedAssets.length;
			if (len <= 7) return 80;
			if (len <= 9) return 66;
			if (len <= 11) return 56;
			return 48; // 如果更长，再缩小
		}
	},
	methods: {
		nav_contract(e) {
			if (e) {
				this.$emit('nav_contract', e)
			}
		},
		nav_stock(e) {
			if (e) {
				this.$emit('nav_stock', e)
			}
		},
		onTabChange(e) {
			console.log(e);

		},
		closeCover(e) {
			console.log(e);
			this.Firstopen = e
			setTimeout(() => {
				this.getHeights()
			}, 1000);
		},
		async getamountPriceGn() {
			let res = await this.$api.amountPriceGn({

			})
			if (res.code == 200) {
				this.cdObj = res.result
			}
		},
		getFiatList(e) {
			this.userCoins = e
			console.log(this.userCoins);
			this.nowsymbol = this.userCoins[0].name
			this.getAllAssetsFiat()

		},
		bannerClick(index) {
			// plus.vibration.vibrate(50); // APP环境下触发50ms震动
			const {
				needLogin,
				linkType,
				link
			} = this.swiperList[index]
			console.log(this.swiperList[index].link)
			if (linkType == 0) {
				return false
			} else {
				if (needLogin == 1) {
					if (uni.getStorageSync('token')) {
						// #ifdef APP
						this.$Router.push({
							name: "webView",
							params: {
								url: link,
							}
						})
						// #endif
						// #ifdef H5
						window.location.href = link
						// #endif
					} else {
						this.$Router.push({
							name: "login"
						})
					}
				} else {
					// #ifdef APP
					this.$Router.push({
						name: "webView",
						params: {
							url: link,
						}
					})
					// #endif
					// #ifdef H5
					window.location.href = link
					// #endif
				}
			}
		},
		async BindDevices() {
			const systemInfo = uni.getSystemInfoSync();
			const platform = systemInfo.platform;
			let res = await this.$api.BindDevice({
				deviceId: uni.getStorageSync('deviceToken') || '',
				appType: platform || ''
			})
			console.log(res);
		},
		async init() {
			await this.getLine()
			// chart 图表实例不能存在data里
			const chart = await this.$refs.chartRef.init(echarts);
			const option = {
				dataZoom: [{
					type: 'inside', //1平移 缩放
					throttle: '50', //设置触发视图刷新的频率。单位为毫秒（ms）。
					minValueSpan: 6, //用于限制窗口大小的最小值,在类目轴上可以设置为 5 表示 5 个类目
					start: 1, //数据窗口范围的起始百分比 范围是：0 ~ 100。表示 0% ~ 100%。
					end: 50, //数据窗口范围的结束百分比。范围是：0 ~ 100。
					zoomLock: true, //如果设置为 true 则锁定选择区域的大小，也就是说，只能平移，不能缩放。
				}],
				title: {
					show: false,
				},
				tooltip: {
					trigger: 'axis',
					backgroundColor: "#F4F4F4",
					borderColor: "#008E28",
					borderWidth: 1,
					borderRadius: 10,
					padding: 10,
					axisPointer: {
						type: 'cross',
						label: {
							backgroundColor: '#6a7985'
						}
					},
					textStyle: {
						color: "#000000",
						fontStyle: "normal",
						fontWeight: 400,
						fontFamily: "Gilroy-SemiBold",
						fontSize: 14
					},
					formatter: (params) => {
						const date = params[0].name;
						const value = (params[0].value).toFixed(2);
						return `Date:  ${date}\nPrice: $${value}`;
					}
				},
				grid: {
					left: '4%', // 距离左边的距离
					right: '0%', // 距离右边的距离
					top: '10%', // 距离顶部的距离
					bottom: '22%' // 距离底部的距离
				},
				xAxis: {
					type: 'category', // x轴类型为类目轴
					boundaryGap: true, // 取消x轴两端空白
					interval: 0,
					// data: ['3:00', '4:00', '5:00', '6:00', '7:00', '8:00', '9:00', '10:00', "11:00", "12:00", "13:00", '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'], // x轴类目数据
					data: this.timeArray,
					axisTick: {
						show: false, // 不显示刻度
					},
					axisLabel: { //x轴文字的配置
						color: "#666666", //Y轴内容文字颜色
						interval: 'auto', // 可以设置为具体的数字，如 5，表示显示每隔 5 个标签
						fontSize: 12,//调整坐标轴字体大小
						fontFamily: "Gilroy-SemiBold",
						// margin: 
					},
					axisLine: {
						lineStyle: {
							// color: '#E0E7FF' // x轴线的颜色
							color: 'transparent'
						}
					},
					splitLine: {
						// 纵向分割线
						show: false,
						lineStyle: {
							// color: '#D2DAE3'
						}
					}
				},
				yAxis: {
					show: false,
					type: 'value', // y轴类型为数值轴
					// name: '单位：斤/元', //单位
					nameLocation: 'end', // (单位个也就是在在Y轴的最顶部)
					//单位的样式设置
					nameTextStyle: {
						color: "#999", //颜色
						padding: [0, 20, 0, 40], //间距分别是 上 右 下 左
						fontSize: 14,
					},
					axisLabel: { //y轴文字的配置
						color: "#777", //Y轴内容文字颜色
					},
					axisLine: { //y轴线的配置
						show: true, //是否展示
						lineStyle: {
							color: "#E0E7FF", //y轴线的颜色（若只设置了y轴线的颜色，未设置y轴文字的颜色，则y轴文字会默认跟设置的y轴线颜色一致）
							width: 1, //y轴线的宽度
							//type: "solid" //y轴线为实线
						},
					},
					axisTick: {
						show: false // y轴上的小横线
					},
					// 横向分割线
					splitLine: {
						show: true, // 显示分割线。
						lineStyle: {
							// 分割线样式。
							color: '#D2DAE3', // 分割线颜色。
							type: 'dotted' // 分割线类型。 solid实线  dotted点型虚线  dashed线性虚线
						}
					},
					splitNumber: 4, // 指定横向分割线的数量
				},
				//  图例
				// legend: {
				//     data: ['近7天价格变化'],
				//     left: 'center',
				//     top: 'bottom'
				// },
				series: [{
					/* 
					// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，
					// 相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，
					// 则该四个值是绝对的像素位置
					*/
					type: 'line', // 图表类型为折线图
					// datasetId: 'dataset_since_1950_of_germany',
					showSymbol: false,
					// data: [120, 180, 150, 80, 70, 110, 130, 70, 110, 130, 70, 110, 130, 2, 20, 50, 145, 200, 5], // 折线图数据
					data: this.priceArray,
					// smooth: true, // 平滑曲线
					// 区域颜色渐变
					// areaStyle: {
					//     color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
					//         [{
					//             offset: 0,
					//             color: "rgba(254, 235, 215, 1)",
					//         },
					//         {
					//             offset: 1,
					//             color: "rgba(254, 235, 215, 0)",
					//         },
					//         ], false
					//     ),
					// },

					// 折线颜色
					lineStyle: {
						// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
						// color: {
						//     type: 'linear',
						//     x: 0,
						//     y: 0,
						//     x2: 0,
						//     y2: 1,
						//     colorStops: [{
						//         offset: 0, color: 'red' // 0% 处的颜色
						//     }, {
						//         offset: 1, color: 'blue' // 100% 处的颜色
						//     }],
						//     global: false // 缺省为 false
						// }
						color: "#FF95B1",
						width: '2',
					},
					color: "#008E28", //拐点颜色
					// symbol: 'circle', // 设置拐点形状、
					symbolSize: 11, // 设置拐点大小
					// 拐点处显示值
					itemStyle: {
						symbol: 'none', // 隐藏所有节点
						normal: {
							node: { show: false },
							label: { show: false }
						}
					},
				}],
			}
			console.log(this.timeArray, this.priceArray,);

			chart.setOption(option)
		},
		async getLine() {
			// this.timeArray = [];
			// this.priceArray = [];
			let res = await this.$api.userWalletSpotLine({
				toCoin: this.nowsymbol,
				type: this.timeNode
			});

			if (res.code === 200) {
				// 获取当前时间节点类型
				const type = this.timeNode;
				this.timeArray = res.result.map(item => {
					return this.formatTimeByNode(item.createAt, type);
				}).reverse();
				this.priceArray = res.result.map(item => item.amount || '0.00').reverse();;
			}
		},
		formatTimeByNode(timestamp, type) {
			const date = new Date(timestamp * 1000);
			const pad = (n) => String(n).padStart(2, '0');
			// console.log(type, 'type');

			switch (type) {

				case 'day':
					return `${pad(date.getMonth() + 1)}/${pad(date.getDate())}`;
				case 'week': {
					const day = date.getDay(); // 周日为 0
					const diffToMonday = day === 0 ? -6 : 1 - day;
					const monday = new Date(date);
					monday.setDate(date.getDate() + diffToMonday);
					const month = monday.getMonth() + 1;
					const dayOfMonth = monday.getDate();
					return `${month}.${dayOfMonth}`;
				}
				case 'month':
					return `${pad(date.getMonth() + 1)}/${pad(date.getDate())}`;
				case 'year':
					return `${date.getFullYear()}`;
				default:
					return '--';
			}
		},
		getSystemInfo() {
			return new Promise((resolve, reject) => {
				uni.getSystemInfo({
					success: res => resolve(res),
					fail: err => reject(err)
				})
			})
		},
		async getHeights() {
			try {
				// #ifdef APP-PLUS
				const systemInfo = await this.getSystemInfo()
				console.log(systemInfo);

				this.windowHeight = systemInfo.windowHeight
				this.maxHeight = systemInfo.windowHeight - 120
				const query = uni.createSelectorQuery().in(this)
				query.select('.topcontainer').boundingClientRect(data => {
					if (data) {
						console.log(data, 222);
						// this.$u.toast(data)
						this.topHeight = data.height
						this.setBoxHeight()
					}
				}).exec()
				console.log('app');
				// #endif

				// #ifdef H5
				console.log('ah5');

				this.windowHeight = window.innerHeight
				this.maxHeight = window.innerHeight + 10

				// this.topHeight = this.$refs.topcontainer.offsetHeight
				// this.topHeight = this.$refs.topcontainer.getBoundingClientRect().height
				uni.createSelectorQuery().select('.topcontainer').boundingClientRect(data => {
					// console.log(data)

					if (data) {
						console.log(data, 222);
						// this.$u.toast(data)
						this.topHeight = data.height
						this.setBoxHeight()
					}
					console.log(this.windowHeight, this.topHeight, 6666);

				}).exec()
				// this.setBoxHeight()
				// #endif
			} catch (error) {
				console.error('Failed to get heights:', error)
				// 回退方案
				this.windowHeight = window.innerHeight || 667 // 默认值
				this.topHeight = 100 // 默认值
				this.setBoxHeight()
			}
		},
		setBoxHeight() {
			// 计算 bomcontainer 高度：100vh - topcontainer 高度
			// this.boxHeight = (this.windowHeight - this.topHeight)
			// this.boxHeight = ((1 - (this.topHeight / this.windowHeight)) * this.windowHeight)

			// #ifdef H5
			this.boxHeight = Math.max(100, uni.getSystemInfoSync().windowHeight * 0.53); // 20% 屏幕高度
			// #endif

			// #ifdef APP
			this.boxHeight = Math.max(100, uni.getSystemInfoSync().windowHeight * 0.45); // 20% 屏幕高度
			// #endif

			// console.log(this.windowHeight, this.topHeight);
			console.log(this.boxHeight, '最终的');
			// 确保高度在合理范围内
			// this.boxHeight = Math.max(100, Math.min(this.boxHeight, this.windowHeight * 0.8))
		},
		async switchTab(index) {
			this.activeTab = index
			const type = this.tabs[index].toLowerCase() // 转换为小写
			await this.fetchTransactions(type)
		},
		async fetchTransactions(type) {
			// try {
			// 	// 模拟接口请求
			// 	const res = await this.$api.getTransactionHistory({ type })
			// 	if (res.code === 200) {
			// 		this.transactions = res.data
			// 	} else {
			// 		this.transactions = []
			// 	}
			// } catch (error) {
			// 	console.error('Fetch transactions failed:', error)
			// 	this.transactions = []
			// }
		},
		goBack() {
			this.isChart = false
			this.showTransaction = false

			setTimeout(() => {
				this.getHeights()
			}, 400);
		},
		toogleChart() {
			if (this.showTransaction) {
				return
			}
			this.isChart = !this.isChart;
			if (this.isChart) {
				setTimeout(() => {
					this.init()
				}, 10);
				// this.boxHeight = Math.max(100, uni.getSystemInfoSync().windowHeight * 0.5);
				this.getHeights()
				this.boxHeight = this.boxHeight - 200
			} else {
				// this.boxHeight = 0
				setTimeout(() => {
					this.getHeights()
				}, 400);
			}

		},
		checks(item, index) {
			// this.period = item.value
			this.nowTime = index;
			this.timeNode = item.value
			console.log(this.timeNode);

			this.init()
			// this.initChartLine()
			// this.selectedRight = 'VOL'
		},
		// 折线图
		initChartLine() {
			console.log(12);

			const chart = echarts.init(this.$refs.chart);
			const option = {
				dataZoom: [{
					type: 'inside', //1平移 缩放
					throttle: '50', //设置触发视图刷新的频率。单位为毫秒（ms）。
					minValueSpan: 6, //用于限制窗口大小的最小值,在类目轴上可以设置为 5 表示 5 个类目
					start: 1, //数据窗口范围的起始百分比 范围是：0 ~ 100。表示 0% ~ 100%。
					end: 50, //数据窗口范围的结束百分比。范围是：0 ~ 100。
					zoomLock: true, //如果设置为 true 则锁定选择区域的大小，也就是说，只能平移，不能缩放。
				}],
				title: {
					show: false,
				},
				tooltip: {
					formatter: (params) => {
						const date = params[0].name;  // 获取日期
						const value = params[0].value; // 获取汇率  
						// 格式化日期为易读格式
						// const formattedDate = new Date(date).toLocaleDateString();

						// 返回自定义内容：日期和汇率
						return `
                      <div>
                        ${value} ${date}
                      </div>
                    `;
					},
					formatter: (params) => { },
					backgroundColor: "#F4F4F4",
					borderColor: "#008E28",
					trigger: 'axis',
					borderRadius: "6",
					// 可以在这里添加更多 tooltip 配置
					axisPointer: {
						type: 'cross',
						label: {
							backgroundColor: '#6a7985'
						}
					},
					textStyle: {
						color: "#000000", // 文字的颜色
						fontStyle: "normal", // 文字字体的风格（'normal'，无样式；'italic'，斜体；'oblique'，倾斜字体）
						fontWeight: "400", // 文字字体的粗细（'normal'，无样式；'bold'，加粗；'bolder'，加粗的基础上再加粗；'lighter'，变细；数字定义粗细也可以，取值范围100至700）
						fontFamily: "Gilroy-SemiBold"
					},
				},
				grid: {
					left: '4%', // 距离左边的距离
					right: '0%', // 距离右边的距离
					top: '0%', // 距离顶部的距离
					bottom: '10%' // 距离底部的距离
				},
				xAxis: {
					type: 'category', // x轴类型为类目轴
					boundaryGap: true, // 取消x轴两端空白
					interval: 0,
					data: ['3:00', '4:00', '5:00', '6:00', '7:00', '8:00', '9:00', '10:00', "11:00", "12:00", "13:00", '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'], // x轴类目数据
					// data: this.timeArray,
					axisTick: {
						show: false, // 不显示刻度
					},
					axisLabel: { //x轴文字的配置
						color: "#666666", //Y轴内容文字颜色
						interval: 'auto', // 可以设置为具体的数字，如 5，表示显示每隔 5 个标签
						fontSize: 12,//调整坐标轴字体大小
						fontFamily: "Gilroy-SemiBold",
						// margin: 
					},
					axisLine: {
						lineStyle: {
							// color: '#E0E7FF' // x轴线的颜色
							color: 'transparent'
						}
					},
					splitLine: {
						// 纵向分割线
						show: false,
						lineStyle: {
							// color: '#D2DAE3'
						}
					}
				},
				yAxis: {
					show: false,

					type: 'value', // y轴类型为数值轴
					// name: '单位：斤/元', //单位
					nameLocation: 'end', // (单位个也就是在在Y轴的最顶部)
					//单位的样式设置
					nameTextStyle: {
						color: "#999", //颜色
						padding: [0, 20, 0, 40], //间距分别是 上 右 下 左
						fontSize: 14,
					},
					axisLabel: { //y轴文字的配置
						color: "#777", //Y轴内容文字颜色
					},
					axisLine: { //y轴线的配置
						show: true, //是否展示
						lineStyle: {
							color: "#E0E7FF", //y轴线的颜色（若只设置了y轴线的颜色，未设置y轴文字的颜色，则y轴文字会默认跟设置的y轴线颜色一致）
							width: 1, //y轴线的宽度
							//type: "solid" //y轴线为实线
						},
					},
					axisTick: {
						show: false // y轴上的小横线
					},
					// 横向分割线
					splitLine: {
						show: true, // 显示分割线。
						lineStyle: {
							// 分割线样式。
							color: '#D2DAE3', // 分割线颜色。
							type: 'dotted' // 分割线类型。 solid实线  dotted点型虚线  dashed线性虚线
						}
					},
					splitNumber: 4, // 指定横向分割线的数量
				},
				//  图例
				// legend: {
				//     data: ['近7天价格变化'],
				//     left: 'center',
				//     top: 'bottom'
				// },
				series: [{
					/* 
					// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，
					// 相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，
					// 则该四个值是绝对的像素位置
					*/
					type: 'line', // 图表类型为折线图
					// datasetId: 'dataset_since_1950_of_germany',
					showSymbol: false,
					name: '近7天价格变化',
					data: [120, 180, 150, 80, 70, 110, 130, 70, 110, 130, 70, 110, 130, 2, 20, 50, 145, 200, 5], // 折线图数据
					// data: this.priceArray,
					// smooth: true, // 平滑曲线
					// 区域颜色渐变
					// areaStyle: {
					//     color: new echarts.graphic.LinearGradient(0, 0, 0, 1,
					//         [{
					//             offset: 0,
					//             color: "rgba(254, 235, 215, 1)",
					//         },
					//         {
					//             offset: 1,
					//             color: "rgba(254, 235, 215, 0)",
					//         },
					//         ], false
					//     ),
					// },

					// 折线颜色
					lineStyle: {
						// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置
						// color: {
						//     type: 'linear',
						//     x: 0,
						//     y: 0,
						//     x2: 0,
						//     y2: 1,
						//     colorStops: [{
						//         offset: 0, color: 'red' // 0% 处的颜色
						//     }, {
						//         offset: 1, color: 'blue' // 100% 处的颜色
						//     }],
						//     global: false // 缺省为 false
						// }
						color: "#000000",
						width: '2',
					},
					color: "#FF95B1", //拐点颜色
					// symbol: 'circle', // 设置拐点形状、
					symbolSize: 2, // 设置拐点大小
					// 拐点处显示值
					itemStyle: {
						symbol: 'none', // 隐藏所有节点
						normal: {
							node: { show: false },
							label: { show: false }
						}
					},
				}],
			}
			chart.setOption(option)
		},
		SetSymbol(e) {
			console.log(e);
			this.nowsymbol = e.name
			this.showcurrencyShow = false
			this.getAllAssetsFiat()

			// this.getLine()
			this.init()
		},
		userCoin(e) {
			// 传过来的数组
			// this.userCoins = e
			// if (this.isFirstLoad && e.length > 0) {
			// 	this.nowsymbol = e[0].name
			// 	this.getAllAssetsFiat()
			// 	this.isFirstLoad = false
			// }
		},
		async getAllAssetsFiat() {
			let res = await this.$api.userAssetsFiat({
				coin: this.nowsymbol
			})
			if (res.code == 200) {
				this.allassets = res.result.amount
			}
		},
		// 开始拖拽
		startDrag(e) {
			e.stopPropagation(); // 阻止事件冒泡
			e.preventDefault();  // 阻止页面滚动
			this.isDragging = true;
			this.startY = e.touches[0].clientY; // 记录起始触摸位置
			this.startHeight = this.boxHeight; // 记录当前高度
		},

		// 拖拽中（优化频率）
		onDragging(e) {
			e.stopPropagation(); // 阻止事件冒泡
			e.preventDefault();  // 阻止页面滚动
			if (!this.isDragging) return;


			// requestAnimationFrame(() => {
			let deltaY = this.startY - e.touches[0].clientY; // 计算向上拖动的距离
			let newHeight = this.startHeight + deltaY; // 计算新高度
			e.stopPropagation(); // 阻止事件冒泡
			e.preventDefault();  // 阻止页面滚动

			// 限制高度范围
			this.boxHeight = Math.max(this.minHeight, Math.min(this.maxHeight, newHeight));
			// });
		},

		// 结束拖拽（带回弹效果）
		stopDrag() {
			this.isDragging = false;
		},
		toggleRotate() {
			this.rotate = this.rotate === 0 ? 180 : 0;
		},


		async getCoin() {
			let res = await this.$api.symbolListPaged()
		},
		async getBannerList() {
			let res = await this.$api.bannerList({
				type: 'banner'
			})
			if (res.code == 200) {
				this.swiperList = res.result
			}
			//  else if (res.code == 401) {
			// 	setTimeout(() => {
			// 		this.$Router.push({
			// 			name: 'login'
			// 		})
			// 	}, 500);
			// }
		},
		async fetchCoin() {
			let res = await this.$api.instrumentsAllcoin({
				all: 0
			})
			if (res.code == 200) {
				this.coinList = JSON.parse(res.result.body)
				console.log(this.coinList);

			}
		},
		handleRight(item) {
			if (item.label == '存入') {
				if (this.legal) {
					this.nav_to('CurrencyCharge')
				}
			}
			if (item.label == '转出') {
				this.nav_to('TransferOut')

			}

			if (item.label == '兑换') {
				this.nav_to('swap')

			}

			if (item.label == '明细') {
				this.nav_to('Record')
			}
		},
		toggleMoreOptions(index, item) {
			// 如果点击的项是当前显示的项，隐藏它；否则显示新项
			if (this.currentIndex === index) {
				this.currentIndex = null;
			} else {
				this.currentIndex = index;
			}
			this.legal = item.legal
		},

		nav_to(e, names) {
			console.log(e);
			if (e == 'Notification') {
				store.commit('changeMsgBoolean', false)
			}
			if (e == 'buy') {
				this.$u.toast('Coming Soon')
				return
			}
			this.$Router.push({
				name: e,
				params: {
					symbol: names
				}
			})
		},
		nav_to_tab(e) {
			this.$Router.pushTab({
				name: e
			})
		},
		renderChart() {
			console.log(123);
			const option = this.isPieChart ? this.getPieOption() : this.getLineOption();
			this.chart.setOption(option);
		},
		getPieOption() {
			return {
				tooltip: { trigger: 'item' },
				series: [{
					type: 'pie',
					radius: '70%',
					data: this.pieData
				}]
			};
		},
		getLineOption() {
			return {
				xAxis: { type: 'category', data: this.lineData.categories },
				yAxis: { type: 'value' },
				series: [{ type: 'line', data: this.lineData.values }]
			};
		},
		toggleChart() {
			this.isPieChart = !this.isPieChart;
			this.renderChart();
		}
	}
};
</script>

<style lang="scss" scoped>
::v-deep .u-swiper-wrap {
	margin: 0 32rpx !important;
}

.wallet-container {
	max-width: 100vw;
	min-height: 100vh;
	transition: all .8s;
	background: #FEFBFF;
	// background: radial-gradient(114.19% 33.28% at 50% 16.76%, #FFE0ED 0%, #FFBBCF 50%, #FF95B1 100%);
	overflow: hidden;
	position: relative;
	padding-bottom: 100rpx;


	.bomcontainer {
		// height: calc(100vh - 840rpx);
		// transition: height 0.1s ease-in-out;
		background: white;
		border-radius: 60rpx 60rpx 0 0;
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		z-index: 999;
		padding: 28rpx 32rpx;



		.drag-handle {
			position: absolute;
			width: 92rpx;
			height: 8rpx;
			left: 50%;
			/* 让左边对齐屏幕中心 */
			transform: translateX(-50%);
			/* 向左移动自身50%宽度，确保居中 */
			top: 16rpx;
			border-radius: 680rpx;
			background: #D9D6D6;

		}
	}

	.areapink {

		width: 545px;
		height: 145px;
		top: -96.63px;
		left: -84.63px;
		opacity: 0.2;
		background: #FF82A3;
		filter: blur(200px);
		// backdrop-filter: blur(200px);

		z-index: 0;
		position: absolute;
		// width: 442rpx;
		// height: 442rpx;
		// top: 80rpx;
		// right: -160rpx;
		// background: #FF99B4;
		// border-radius: 50%; // 如果想要更像光晕
		// box-shadow: 0 0 50rpx rgba(255, 153, 180, 0.6); // 模糊的粉色阴影
		// filter: blur(127rpx); // 让整个元素有模糊感
		// backdrop-filter: blur(127.80000305175781px)
	}

	.topcontainer {
		// #ifdef H5
		padding: 28rpx 0 28rpx 0;
		// #endif

		// #ifdef APP-PLUS
		padding: 28rpx 0 100rpx 0;

		// #endif
		.time {
			margin: 0 32rpx 0 32rpx;

			display: flex;
			justify-content: space-between;
			align-items: center;
			// color: #ffffff;
			font-family: Gilroy-SemiBold;
			font-weight: 400;
			font-size: 12*2rpx;
			line-height: 14.4*2rpx;

			// border-radius: 20rpx;
			// height: 26*2rpx;
			// margin: 0 22rpx 40rpx 22rpx;
			// margin-bottom: 32rpx;
			padding: 16rpx 0;
			// border-top: 2rpx solid #E0E0E0;
			// border-bottom: 2rpx solid #E0E0E0;

			.timeActive {
				// background: linear-gradient(180deg, #ef91fb 0%, #40f8ec 100%);
				background: #008E28;


				font-weight: 800;
				font-size: 24rpx;
				color: #fff;
				line-height: 7rpx;
				border-radius: 40rpx;
				// border: 1rpx solid #DFE2E4;
			}

			text {
				//background-color: #2b2b2b;
				padding: 19rpx 19rpx;
				font-family: PingFang SC;
				font-weight: 400;
				font-size: 24rpx;
				//color: #FFFFFF;
				line-height: 7rpx;
			}
		}

		/* 顶部导航 */
		.header {
			margin: 0 32rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.left {
				.logo {
					margin-left: 16rpx;
					width: 214rpx;
					height: 52rpx;
				}
			}

			.icon-left {
				width: 92rpx;
				height: 92rpx;

			}

			.service {
				width: 36*2rpx;
				height: 36*2rpx;
				border-radius: 50%;
				background: #FFFEFE;
				position: relative;

				.dot {
					position: absolute;
					top: 10rpx;
					right: 10rpx;
					width: 10rpx;
					height: 10rpx;
					border-radius: 50%;
					background: #FF2E2E;

				}

				image {
					width: 26*2rpx;
					height: 26*2rpx;
				}
			}

			.logo {
				width: 69*2rpx;
			}

		}

		/* 钱包信息 */
		.wallet-info {
			text-align: center;
			margin: 30rpx 32rpx 0 32rpx;

			display: flex;
			justify-content: space-between;
			align-items: center;

			.wallet-left {


				.wallet-title {
					display: flex;
					align-items: center;
					font-family: Gilroy-Bold;
					font-size: 28rpx;
					font-weight: 500;
					// line-height: 40rpx;
					color: rgba(0, 0, 0, .5);

					image {
						width: 36rpx;
						height: 36rpx;
						margin-left: 14rpx;
					}
				}

				.wallet-money {
					display: flex;
					align-items: center;

					.wallet-balance {
						font-family: Gilroy-ExtraBold;
						font-weight: 400;
						// font-size: 80rpx;
						line-height: 100rpx;
						color: #000;
					}

					.wallet-currency {
						z-index: 9;
						// border-radius: 36rpx;
						// padding: 16rpx 20rpx;
						// background: #FFB4C8;
						margin-top: 10rpx;
						margin-left: 5rpx;
						font-family: Gilroy-SemiBold;
						font-weight: 400;
						font-size: 32rpx;
						line-height: 28rpx;
						text-align: center;
						vertical-align: middle;
						color: #000;
						position: relative;

						.helpoption {
							width: 80*2rpx;
							transition: transform 0.3s ease, opacity 0.3s ease;
							transform-origin: top;
							/* 设置变换的起点为顶部 */
							z-index: 9999;
							position: absolute;
							top: 80rpx;
							left: 0;
							box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;
							max-height: 400rpx;
							overflow-y: auto;
							// background-color: rgba(0, 0, 0, .5);
							background: #fff;
							border-radius: 16*2rpx;
							padding: 16*2rpx;
							opacity: 1;
							//padding: 100rpx;
							// height: 446rpx;
							display: flex;
							align-items: flex-start;
							flex-direction: column;



							&.collapse {
								transform: scaleY(0) translateY(-100%);
								/* 缩小至0，并向上移动 */
								opacity: 0;
							}

							&.expand {
								transform: scaleY(1) translateY(0%);
								/* 恢复到正常大小，并位置恢复 */
								opacity: 1;

							}

							>view {
								width: 100%;
								padding: 15rpx 0;
								// display: flex;
								// align-items: flex-start;
								text-align: left;

								image {
									width: 40rpx;
									height: 30rpx;
								}

								text {
									// margin-left: 20rpx;
									// display: block;
									font-family: Gilroy-Bold;
									font-weight: 400;
									font-size: 16*2rpx;
									line-height: 19.2*2rpx;
									color: #000;
								}
							}
						}


						image {
							width: 28rpx;
							height: 28rpx;
							margin-left: 8rpx;
							transition: transform 0.4s ease-in-out;
						}
					}
				}
			}

			/* 图片的基础样式 */
			.arrow,
			.chart {
				display: block;
				/* 确保图片是块级元素 */
			}

			/* 滑动动画 */
			.slide-enter-active,
			.slide-leave-active {
				transition: all 0.3s ease-in-out;
				/* 动画时间和缓动函数 */
				position: absolute;
				/* 绝对定位以实现重叠效果 */
			}

			.slide-enter {
				transform: translateX(100%);
				/* 从右边进入 */
				opacity: 0;
			}

			.slide-enter-to {
				transform: translateX(0);
				/* 移动到正常位置 */
				opacity: 1;
			}

			.slide-leave {
				transform: translateX(0);
				/* 开始时在正常位置 */
				opacity: 1;
			}

			.slide-leave-to {
				transform: translateX(-100%);
				/* 向左退出 */
				opacity: 0;
			}

			.charts {
				margin-top: 10rpx;
				z-index: 9;
				width: 87*2rpx;
				height: 90rpx;
				// border-radius: 90rpx;
				// background: #FFCAD9;
				position: relative;
				/* 确保动画在容器内进行 */
				overflow: hidden;

				/* 防止图片溢出 */
				.arrow {
					width: 40rpx;
					height: 40rpx;
				}

				.chart {
					width: 200rpx;
					height: 60rpx;
				}
			}


		}

		.receive-container {
			margin-top: 14rpx;
			position: relative;
			width: 100%;
			// height: 200px;
			display: flex;
			align-items: center;
			// padding: 20px;
			box-sizing: border-box;

			.receive-back-button {
				position: absolute;
				left: 0;
				top: 50rpx;
				transform: translateY(-50%);

				width: 100rpx;
				height: 64rpx;
				border-radius: 90rpx;

				background: #FFB4C8;

				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;

				.receive-back-icon {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.receive-content {
				display: flex;
				justify-content: space-around;
				width: 100%;
				margin-top: 30rpx;

				.receive-currencies,
				.receive-crypto {
					display: flex;
					flex-direction: column;
					align-items: center;

					.receive-title {
						font-family: Gilroy-SemiBold;
						font-weight: 400;
						font-size: 28rpx;
						line-height: 40rpx;
						color: #000;
						margin-bottom: 28rpx;
					}

					.receive-icons {
						position: relative;
						width: 100px;
						/* 控制圆形区域大小 */
						height: 100px;

						.receive-icon {
							position: absolute;
							width: 48rpx;
							height: 48rpx;
							border-radius: 50%;
							padding: 24rpx;

							/* 4 个图标按圆形排列 */
							&:nth-child(1) {
								top: 0;
								left: 50%;
								transform: translateX(-50%);
							}

							&:nth-child(2) {
								top: 50%;
								right: 0;
								transform: translateY(-50%);
							}

							&:nth-child(3) {
								bottom: 0;
								left: 50%;
								transform: translateX(-50%);
							}

							&:nth-child(4) {
								top: 50%;
								left: 0;
								transform: translateY(-50%);
							}
						}
					}
				}
			}
		}

		.rise {
			font-family: Gilroy-SemiBold;
			font-weight: 400;
			margin-left: 32rpx;
			font-size: 24rpx;
			line-height: 160%;
			letter-spacing: 0%;
			color: #000;

			.number {
				margin-right: 8rpx;
			}
		}

		/* chart-container 的动画 */
		.fade-enter-active,
		.fade-leave-active {
			transition: all 0.4s ease-in-out;
		}

		.fade-enter,
		.fade-leave-to {
			max-height: 0;
			opacity: 0;
			overflow: hidden;
		}

		.fade-enter-to,
		.fade-leave {
			max-height: 500px;
			/* 调整为实际高度 */
			opacity: 1;
		}

		.chart-wrapper {
			z-index: 999;
			// width: 100vw;
			height: 300rpx;
			// height: 100%; /* 根据父容器大小自适应 */
			margin: 0 30rpx 0 -30rpx;
		}

		.line {
			margin: 20rpx 32rpx 0 32rpx;
			height: 2rpx;
			background: #666;
		}



		.bottom-actions {
			// margin: 27rpx 25rpx 20rpx 25rpx;
			margin: 27rpx 0 20rpx 0;

			transition: 0.3s ease-in-out;
			// transform: translateY(0);
			/* 默认位置 */


			.action-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 10rpx;
				z-index: 1;

				.icon {
					// width: 80rpx;
					// height: 80rpx;
					// border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}

				.label {
					margin-top: 10rpx;
					font-family: Gilroy-SemiBold;
					font-weight: 400;
					font-size: 28rpx;
					line-height: 40rpx;
					letter-spacing: 0px;
					text-align: center;
					color: #000;
				}
			}
		}

		/* 邀请奖励 */
		.invite-box {
			// display: flex;
			// align-items: center;
			margin-top: 18rpx;
			// border: 1px solid red;
			background: rgba(255, 255, 255, .9);
			border-radius: 48rpx;
			height: 224rpx;
			transition: 0.4s ease-in-out;

		}

		.transaction-history-container {
			margin-top: 48rpx;
			width: 100%;
			// padding: 20px;
			box-sizing: border-box;

			.titleTR {
				font-family: Gilroy-Bold;
				font-weight: 400;
				font-size: 36rpx;
				line-height: 44rpx;
				letter-spacing: 0%;
				color: #000;
				margin-bottom: 28rpx;
			}

			.tab-filter {
				display: flex;
				// padding: 10px;
				// justify-content: center;
				gap: 12rpx;
				// margin: 40rpx 32rpx;
				width: 100%;
				overflow-x: auto;

				.tab-item {
					padding: 8rpx 24rpx;
					border-radius: 40rpx;
					background: #FFB4C8;
					transition: all 0.3s;
					// min-width:120rpx;
					width: fit-content;
					display: flex;
					color: #000;
					align-items: center;
					justify-content: center;

					&:first-child {
						padding: 8rpx 40rpx;

					}

					text {
						display: block;
						// width: 90rpx;
						font-family: Gilroy-SemiBold;
						font-weight: 400;
						font-size: 14*2rpx;
						line-height: 160%;
						letter-spacing: 0%;
						text-align: center;

					}

					&.active {
						background: #FFE6ED;
						color: #000;
					}
				}
			}

			.transaction-history-content {
				.transaction-history-empty {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					margin-top: 92rpx;

					image {
						width: 178*2rpx;
						height: 190*2rpx;

					}

					.transaction-history-empty-icon {
						position: relative;
						width: 100px;
						height: 100px;

						.transaction-history-icon-btc,
						.transaction-history-icon-usd {
							position: absolute;
							width: 60px;
							height: 60px;
							border-radius: 50%;
							background: #fff;
							padding: 5px;
							box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
						}
					}

					.transaction-history-empty-text {
						margin-top: 56rpx;

						.t {
							font-family: Gilroy-Bold;
							font-weight: 400;
							font-size: 36rpx;
							line-height: 44rpx;
							letter-spacing: 0%;
							color: #000;
						}

						.b {
							margin-top: 20rpx;
							font-family: Gilroy-Medium;
							font-weight: 400;
							font-size: 32rpx;
							line-height: 38rpx;
							letter-spacing: 0%;
							color: #000;
						}
					}
				}

				.transaction-history-list {
					.transaction-history-item {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 10px 0;
						border-bottom: 1px solid rgba(0, 0, 0, 0.1);

						.transaction-history-item-left {
							display: flex;
							align-items: center;

							.transaction-history-item-type {
								display: flex;
								align-items: center;
								font-size: 14px;
								font-weight: bold;
								color: #333;

								.transaction-history-item-arrow {
									width: 84rpx;
									height: 84rpx;
								}
							}

							.transaction-history-item-address {
								margin-left: 28rpx;
								display: flex;
								flex-direction: column;
								font-size: 12px;
								color: #666;

								.type {
									font-family: Gilroy-SemiBold;
									font-weight: 400;
									font-size: 28rpx;
									line-height: 44rpx;
									color: #000;
								}

								.address {
									font-family: Gilroy-SemiBold;
									font-weight: 400;
									font-size: 28rpx;
									line-height: 44rpx;
									color: #333;
								}
							}
						}

						.transaction-history-item-right {
							text-align: right;

							.transaction-history-item-amount {
								font-family: Gilroy-SemiBold;
								font-weight: 400;
								font-size: 32rpx;
								line-height: 38rpx;
								letter-spacing: 0%;
								text-align: right;
								vertical-align: middle;
								color: #000;

								&.transaction-history-item-amount-positive {
									color: #02B632;
								}
							}

							.transaction-history-item-balance {
								font-family: Gilroy-SemiBold;
								font-weight: 400;
								font-size: 28rpx;
								line-height: 44rpx;
								letter-spacing: 0%;
								text-align: right;
								vertical-align: middle;

							}
						}
					}
				}
			}
		}

		/* 滑动动画 */
		.slide-up-enter-active,
		.slide-up-leave-active {
			transition: all 0.3s ease;
		}

		.slide-up-enter {
			transform: translateY(100%);
			opacity: 0;
		}

		.slide-up-leave-to {
			transform: translateY(-100%);
			opacity: 0;
		}

	}


}
</style>