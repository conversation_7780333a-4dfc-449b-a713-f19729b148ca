<template>
    <view class="market-panel">

        <view class="tabs_div">
            <view class="left">
                <view class="utab">
                    <view @click="changeTab(0)" :class="current == 0 ? 'utabact' : ''">加密市场</view>
                    <view @click="changeTab(1)" :class="current == 1 ? 'utabact' : ''">证券市场</view>
                    <view class="utabbar" :style="{ left: current == 0 ? '40rpx' : '200rpx' }"></view>
                </view>
            </view>
        </view>

        <!-- 卡片部分  websocket -->
        <view class="quote-cards" @touchstart.stop @touchmove.stop @touchend.stop v-if="current == 0">
            <view class="card" v-for="(item, index) in quoteList" :key="index"
                :style="{ background: item.chg > 0 ? 'rgba(4, 164, 49, 0.05)' : 'rgba(255, 130, 163, 0.05)' }">
                <view class="title">{{ item.symbol }}</view>

                <view class="prices" :style="{ color: item.color }">
                    <view class="price">{{ item.price }}</view>
                    <view class="change">{{ item.change }}</view>
                </view>

                <view class="lc">
                    <l-echart class="klink" ref="chartRefCrypto"></l-echart>
                </view>
            </view>
        </view>

        <view class="quote-cards" @touchstart.stop @touchmove.stop @touchend.stop v-else>
            <view class="card" v-for="(item, index) in stockMarketData" :key="index"
                :style="{ background: item.chg > 0 ? 'rgba(4, 164, 49, 0.05)' : 'rgba(255, 130, 163, 0.05)' }">
                <view class="title">{{ item.symbol == 'HSI' ? '恒生指数' : item.symbol == '.DJI' ? '道琼斯' : '纳斯达克' }}</view>

                <view class="prices" :style="{ color: item.chg > 0 ? '#00C853' : '#FF4D4F' }">
                    <view class="price">{{ item.lastPrice }}</view>
                    <view class="change"> {{ item.chg > 0 ? '+' : '' }}{{ item.chg + '%' }}</view>
                </view>

                <view class="lc">
                    <l-echart class="klink" ref="chartRefStock"></l-echart>
                </view>
            </view>
        </view>

        <!-- 二级 Tab -->
        <view class="sub-tabs" ref="subTabWrap">
            <view v-for="(tab, index) in subTabs" :key="index" class="sub-tab-item"
                :class="{ active: currentSubTab === index }" @click="switchSubTab(index)">
                {{ tab }}
            </view>
        </view>

        <!-- us hk切换 -->
        <view class="us" v-if="current == 1">
            <view class="us-tabs" :class="{ active: activeStockTab === 'us' }" @click="switchStockTab('us')">
                美股
            </view>
            <view class="us-tabs" :class="{ active: activeStockTab === 'hk' }" @click="switchStockTab('hk')">
                港股
            </view>
        </view>

        <!-- 表格头部 -->
        <view class="table-header">
            <view class="left">{{ current == 0 ? '名称' : '股票名称' }}</view>
            <view class="right">最新价</view>
            <view class="right"> {{ current == 0 ? '24h涨跌幅' : currentSubTab == 4 ? "成交额" : '涨跌幅' }} </view>
        </view>

        <!-- 币种列表容器 -->
        <view class="coin-list-container" @touchstart.stop @touchmove.stop @touchend.stop v-if="current == 0">
            <!-- 行内容 -->
            <view class="coin-row" v-for="(coin, i) in coinList" :key="coin.symbol">
                <view class="left name">
                    {{ coin.symbol }}
                    <image v-if="i < 3 && currentSubTab == 1 && coinList.length > 5"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385648408319582208.png"
                        class="hot-icon" />
                </view>
                <view class="right price">
                    <text>{{ coin.closePrice || '0.00' }}</text>
                    <text class="usd">${{ (coin.usdRate * coin.closePrice) || 0 || '0.00' }}</text>
                </view>
                <view class="rates">
                    <view class="rate" :class="getPriceChangeDirection(coin)">
                        {{ Number(calculatePriceChange(coin)) > 0 ? '+' : '' }}{{ calculatePriceChange(coin) }}%
                    </view>
                </view>
            </view>
            <nodata v-if="coinList.length == 0" />
        </view>
        <!-- 股票容器 -->
        <view class="coin-list-container-us" @touchstart.stop @touchmove.stop @touchend.stop
            v-if="current == 1 && currentSubTab != 4">
            <!-- 行内容 -->
            <view class="coin-row" v-for="(coin) in stockList" :key="coin.symbol">
                <view class="left">
                    <view class="name">{{ coin.name }}</view>
                    <!-- <image v-if="i < 3 && currentSubTab == 1 && coinList.length > 5"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385648408319582208.png" class="hot-icon" /> -->
                    <view class="symbol">
                        <view class="uss flex_all">{{ activeStockTab.toUpperCase() }}</view>
                        <text class="symbols">{{ coin.symbol }}</text>
                    </view>
                </view>
                <view class="right price">
                    <text>{{ coin.lastPrice || '0.00' }}</text>
                    <text class="usd">${{ (coin.usdRate * coin.closePrice) || 0 || '0.00' }}</text>
                </view>
                <view class="rates" :style="{ color: coin.changeRate > 0 ? '#04A431' : '#FF82A3' }">
                    <!-- :class="getPriceChangeDirection(coin)" -->
                    <view class="rate" v-if="currentSubTab != 0">
                        {{ Number((coin.changeRate)) > 0 ? '+' : '' }}{{ Number((coin.changeRate) * 100).toFixed(2) }}%
                    </view>
                    <view class="rate" v-if="currentSubTab == 0">
                        {{ Number((coin.lastPrice - coin.preClousPrice)) > 0 ? '+' : '' }}{{ Number((coin.lastPrice -
                            coin.preClousPrice) *
                        100).toFixed(2) }}%
                    </view>
                    <!-- preClousPrice -->
                </view>
            </view>
            <nodata v-if="stockList.length == 0" />
        </view>

        <view class="coin-list-container-us" @touchstart.stop @touchmove.stop @touchend.stop
            v-if="current == 1 && currentSubTab == 4">
            <!-- 行内容 -->
            <view class="coin-row" v-for="(coin) in stock24hChange" :key="coin.symbol">
                <view class="left">
                    <view class="name">{{ coin.name }}</view>
                    <!-- <image v-if="i < 3 && currentSubTab == 1 && coinList.length > 5"
                        src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1385648408319582208.png" class="hot-icon" /> -->
                    <view class="symbol">
                        <view class="uss flex_all">{{ activeStockTab.toUpperCase() }}</view>
                        <text class="symbols">{{ coin.symbol }}</text>
                    </view>
                </view>
                <view class="right price">
                    <text>{{ coin.latestPrice || '0.00' }}</text>
                    <text class="usd">${{ (coin.usdRate * coin.closePrice) || 0 || '0.00' }}</text>
                </view>
                <view class="rates" :style="{ color: coin.changeRate > 0 ? '#04A431' : '#FF82A3' }">
                    <!-- :class="getPriceChangeDirection(coin)"  {{ Number((coin.changeRate)) > 0 ? '+' : '' }}-->
                    <view class="rate">
                        {{ (coin.amount) }}
                        <!-- formatAmount -->
                    </view>
                </view>
            </view>
            <nodata v-if="stock24hChange.length == 0" />
        </view>


        <!-- 查看更多按钮 -->
        <view class="more-btn flex_all" v-if="coinList.length > 0 && hasnext" @click="loadMore">＋ 查看更多</view>
    </view>
</template>
<script>
import nodata from "./nodata"
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min';
export default {
    components: { nodata },
    data() {
        return {
            stockMarketData: [],
            current: 0,
            currentTab: 0,
            currentSubTab: 1,
            tabs: ['加密市场', '证券市场'],
            subTabs: ['自选', '热门', '涨幅榜', '跌幅榜', '24H成交额'],
            quoteList: [
                // { symbol: 'BTCUSDT', price: '41,859.09', change: '-0.00%', chg: -1, color: '#FF4D4F' },
                // { symbol: 'ETHUSDT', price: '18,925.73', change: '+0.28%', chg: 2.8, color: '#00C853' },
                // { symbol: 'SOLUSDT', price: '5,842.01', change: '-0.04%', chg: -0.04, color: '#FF4D4F' },
                // { symbol: 'SOLUSDT', price: '5,842.01', change: '-0.04%', chg: -0.04, color: '#FF4D4F' }
            ],
            coinList: [],
            mainTabLeft: 0,
            mainTabWidth: 0,
            subTabLeft: 0,
            subTabWidth: 0,
            globalOption: {
                animation: false,
                grid: {
                    borderWidth: 0, // 隐藏网格边界线
                    bottom: "15%", // 增加底部空间，防止标签遮挡
                    top: 0, // 增加顶部空间，防止标题遮挡
                    left: 0,
                    right: 0
                },
                xAxis: {
                    type: "category",
                    boundaryGap: false,
                    data: [],
                    axisLine: {
                        // 隐藏X轴线
                        show: false,
                    },
                    axisTick: {
                        // 隐藏X轴刻度
                        show: false,
                    },
                    axisLabel: {
                        // 隐藏X轴标签
                        show: false,
                    },
                },
                yAxis: {
                    type: "value",
                    splitLine: {
                        show: false,
                    },
                    axisLine: {
                        // 隐藏Y轴线
                        show: false,
                    },
                    axisTick: {
                        // 隐藏Y轴刻度
                        show: false,
                    },
                    axisLabel: {
                        // 隐藏Y轴标签
                        show: false,
                    },
                },
                series: [
                    {
                        data: [100, 222, 9999, 444, 888, 666],
                        type: "line",
                        smooth: true,
                        symbol: "none", // 隐藏折线上的点
                        lineStyle: {
                            color: "rgba(124, 228, 81, 1)", // 折线颜色
                            width: 2,
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: "rgba(124, 228, 81, 1)",
                                },
                                {
                                    offset: 1,
                                    color: "rgba(34, 161, 235, 0)",
                                },
                            ]),
                        },
                    },
                ],
            },
            pageNum: 1,
            pageSize: 6,
            stockSize: 6,
            stockNum: 1,
            hasnext: true,
            stockhasNext: true,
            stockList: [],
            // 美股港股切换状态
            activeStockTab: 'us', // 'us' 表示美股，'hk' 表示港股
            stock24hChange: []
        }
    },
    watch: {
        // count(val) {
        //     this.getUserCoinCrypto()
        //     this.getUserCoinFiat()
        // },
        "$store.state.indexBottom"(val) {
            // console.log(val);
            return
            if (this.CryptouserCoinPage.list.length) {
                this.CryptouserCoinPage.list = this.CryptouserCoinPage.list.map(item2 => {
                    const matchedItem = val.find(item1 => item1.baseCoin === item2.symbol);
                    if (matchedItem) {
                        return {
                            ...item2,
                            price: matchedItem.price,
                            percentageChange: matchedItem.percentageChange,
                            targetCoin: matchedItem.targetCoin
                        };
                    }
                    return { ...item2 };
                });
                this.$emit('userCoin', this.CryptouserCoinPage.list)
                // this.cryptobalance = this.CryptouserCoinPage.list.reduce((acc, cur) => {
                //     return acc + cur.totalBalance
                // }, 0)
                this.cryptobalance = (this.CryptouserCoinPage.list || []).reduce((acc, cur) => {
                    const price = Number(cur.price) || 0;
                    const totalBalance = Number(cur.totalBalance) || 0;
                    return acc + price * totalBalance;
                }, 0);
                // console.log(123, this.CryptouserCoinPage.list);
            }
        },
        "$store.state.market_ticker_data"(val) {
            // console.log(val, 'market_ticker_data');

            if (!val || typeof val !== 'object') return;

            // 转换数据格式
            const transformedQuoteList = Object.keys(val).map(key => {
                const data = val[key];

                // 解析symbol：e_btcusdt -> BTCUSDT
                const symbol = key.replace('e_', '').toUpperCase();

                // 计算涨跌幅
                const close = parseFloat(data.close || 0);
                const open = parseFloat(data.open || 0);
                const changeRate = open !== 0 ? (close - open) / open : 0;
                const changePercent = (changeRate * 100).toFixed(2);

                // 格式化价格（添加千分位）
                const formattedPrice = this.formatThousand(close);

                // 确定颜色
                const color = changeRate > 0 ? '#00C853' : (changeRate < 0 ? '#FF4D4F' : '#666666');

                // 格式化涨跌幅显示
                const changeDisplay = changeRate > 0 ? `+${changePercent}%` : `${changePercent}%`;

                return {
                    symbol: symbol,
                    price: formattedPrice,
                    change: changeDisplay,
                    chg: changeRate,
                    color: color
                };
            });

            // 更新quoteList
            this.quoteList = transformedQuoteList;
            // console.log('转换后的quoteList:', this.quoteList);
            setTimeout(() => {
                this.quoteList.forEach((item, index) => {
                    const raw = item.chg;
                    const change = raw == null ? 0 : Number(raw); // null/undefined → NaN

                    const isRed = raw < 0;
                    const trendData = this.generateMockTrend(1000, change);
                    if (trendData.length > 0) {
                        setTimeout(() => {
                            this.initcrypto(index, trendData, isRed);
                        }, 10);
                        console.warn(`图表数据bu 为空，跳过 index: ${index}`);

                    } else {
                        console.warn(`图表数据为空，跳过 index: ${index}`);
                    }
                });
            }, 600);
        },
        current(val) {
            if (val == 1) {
                this.getStockMarketData()
            } else {
                this.loadContractList()
            }
        }
    },
    mounted() {
        console.log('mounted');
        this.getUserCoinFiat()
        this.loadContractList()
    },
    computed: {
    },
    methods: {
        formatThousand(val) {
            if (isNaN(val)) return val
            // const precision = this.getPrecisionDigits(this.currentPrecision);
            let value = Number(val).toFixed(2)
            return (value).toLocaleString('en-US')
        },
        // 获取24H涨跌幅
        async get24hChange() {
            let res = await this.$api.dealAmountRank({
                market: this.activeStockTab === 'us' ? 'US' : 'HK'
            });
            if (res.code == 200) {
                // 转换数据结构
                const transformedData = res.result.map(item => {
                    // 从 baseDataList 中提取需要的数据
                    const baseData = item.baseDataList || [];

                    // 查找 latestPrice
                    const latestPriceItem = baseData.find(data => data.name === 'latestPrice');
                    const latestPrice = latestPriceItem ? latestPriceItem.value : '';

                    // 查找 amount
                    const amountItem = baseData.find(data => data.name === 'amount');
                    const amount = amountItem ? amountItem.value : '';

                    // 查找 curChangeRate
                    const curChangeRateItem = baseData.find(data => data.name === 'curChangeRate');
                    const changeRate = curChangeRateItem ? curChangeRateItem.value : 0;

                    // 返回转换后的数据结构
                    return {
                        symbol: item.symbol || '',
                        latestPrice: latestPrice,
                        amount: this.formatAmount(amount), // 格式化金额显示
                        rawAmount: amount, // 保留原始数值用于排序等操作
                        changeRate: changeRate,
                        market: item.market || '',
                        name: item.name || ''
                    };
                });

                this.stock24hChange = transformedData;
                // console.log('转换后的24H数据:', this.stock24hChange);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        // 涨幅-跌幅 从热们中自己筛选

        // 股票热门
        async getHotStock() {
            let res = await this.$api.getHotStock({
                // pageNum: this.stockNum,
                // pageSize: this.stockSize,
                market: this.activeStockTab === 'us' ? 'US' : 'HK'
            });
            if (res.code == 200) {
                this.stockhasNext = res.result.hasNext
                if (this.stockNum == 1) {
                    this.stockList = res.result
                } else {
                    this.stockList = this.stockList.concat(res.result)
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        // 股票自选
        async getstockself() {
            let res = await this.$api.selfStockList({
                pageNum: this.stockNum,
                pageSize: this.stockSize,
                market: this.activeStockTab === 'us' ? 'US' : 'HK'
            });
            if (res.code == 200) {
                this.stockhasNext = res.result.hasNext
                if (this.stockNum == 1) {
                    this.stockList = res.result.data
                } else {
                    this.stockList = this.stockList.concat(res.result.data)
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        async getStockMarketData() {
            // .DJI - 道琼斯
            // .IXIC - 纳斯达克
            // HSI - 恒生指数
            // 如下内容是获取股票数据的接口的res.reslut,把他的preClose，symbol，intraday里面的items的每一个的price整合成一个数组给我，还要一个items的最后一个对象的price,如下结构

            const stockSymbols = [
                { symbol: '.DJI', name: '道琼斯' },
                { symbol: '.IXIC', name: '纳斯达克' },
                { symbol: 'HSI', name: '恒生指数' }
            ];

            try {
                // 并发调用三个股票指数的API
                const promises = stockSymbols.map(stock =>
                    this.$api.stockmarkettimeline({
                        symbol: stock.symbol,
                        period: 'day',
                        tradeSession: 'Regular',
                    })
                );

                const results = await Promise.all(promises);

                // 汇总结果到一个数组
                const stockMarketArray = [];

                results.forEach((res, index) => {
                    if (res.code == 200) {
                        // 按照注释要求整合数据
                        const result = res.result[0];

                        // 提取 intraday.items 中每个对象的 price
                        const priceItems = [];
                        let lastPrice = "";

                        if (result.intraday && result.intraday.items && Array.isArray(result.intraday.items)) {
                            // 提取所有 price 到数组
                            priceItems.push(...result.intraday.items.map(item => item.price));

                            // 获取最后一个对象的 price
                            if (result.intraday.items.length > 0) {
                                lastPrice = result.intraday.items[result.intraday.items.length - 1].price;
                            }
                        }

                        // 按照要求的结构整合数据
                        stockMarketArray.push({
                            preClose: result.preClose || "",
                            symbol: result.symbol || stockSymbols[index].symbol,
                            items: priceItems,
                            lastPrice: lastPrice,
                            // chg为 ((lastPrice - result.preClose) / result.preClose).toFixed(4) * 100,
                            chg: Number(((lastPrice - result.preClose) / result.preClose) * 100).toFixed(2),
                        });
                    } else {
                        // 如果某个API调用失败，添加错误信息
                        stockMarketArray.push({
                            preClose: "",
                            symbol: stockSymbols[index].symbol,
                            items: [],
                            lastPrice: "",
                            error: res.msg || '获取数据失败'
                        });
                    }
                });

                this.stockMarketData = stockMarketArray;

                // console.log('股票市场数据汇总:', this.stockMarketData);

                setTimeout(() => {
                    this.stockMarketData.forEach((item, index) => {
                        const raw = item.chg;
                        const change = raw == null ? 0 : Number(raw); // null/undefined → NaN

                        const isRed = raw <= 0;
                        const trendData = item.items
                        // console.log(trendData);

                        // if (trendData.length > 0) {
                        // setTimeout(() => {
                        this.initStock(index, trendData, isRed);
                        // }, 10);
                        // console.warn(`图表数据bu 为空，跳过 index: ${index}`);

                        // } else {
                        // console.warn(`图表数据为空，跳过 index: ${index}`);
                        // }
                    });
                }, 1000);

            } catch (error) {
                console.error('获取股票市场数据出错:', error);
                uni.showToast({
                    title: '获取股票数据失败',
                    icon: 'none'
                });

                // 设置默认的空数据结构
                this.stockMarketData = stockSymbols.map(stock => ({
                    symbol: stock.symbol,
                    name: stock.name,
                    data: null,
                    error: '网络错误'
                }));
            }
        },
        // 获取涨幅
        async getContractHighRanks() {
            let res = await this.$api.getContractHighRank({
                pageNum: this.pageNum,
                pageSize: this.pageSize
            });
            if (res.code == 200) {
                this.hasnext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.coinList = res.result.data
                } else {
                    this.coinList = this.coinList.concat(res.result.data)
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        // 获取跌幅
        async getContractLowRanks() {
            let res = await this.$api.getContractLowRank({
                pageNum: this.pageNum,
                pageSize: this.pageSize
            });
            if (res.code == 200) {
                this.hasnext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.coinList = res.result.data
                } else {
                    this.coinList = this.coinList.concat(res.result.data)
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        // 获取24H
        async get24h() {
            let res = await this.$api.getContractAmountRank({
                pageNum: this.pageNum,
                pageSize: this.pageSize
            });
            if (res.code == 200) {
                this.hasnext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.coinList = res.result.data
                } else {
                    this.coinList = this.coinList.concat(res.result.data)
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },
        // 获取自选
        async getself() {
            let res = await this.$api.getContractSelfList({
                pageNum: this.pageNum,
                pageSize: this.pageSize
            });
            if (res.code == 200) {
                this.hasnext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.coinList = res.result.data
                } else {
                    this.coinList = this.coinList.concat(res.result.data)
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },

        // 获取热门
        async loadContractList() {
            let res = await this.$api.contractSymbolList({
                pageNum: this.pageNum,
                pageSize: this.pageSize
            });
            if (res.code == 200) {
                this.hasnext = res.result.hasNext
                if (this.pageNum == 1) {
                    this.coinList = res.result.data
                } else {
                    this.coinList = this.coinList.concat(res.result.data)
                }
                setTimeout(() => {
                    this.quoteList.forEach((item, index) => {
                        const raw = item.chg;
                        const change = raw == null ? 0 : Number(raw); // null/undefined → NaN

                        const trendData = this.generateMockTrend(100, change);

                        if (trendData.length > 0) {
                            setTimeout(() => {
                                const isRed = raw < 0;
                                this.initcrypto(index, trendData, isRed);
                            }, 10);
                            console.warn(`图表数据bu 为空，跳过 index: ${index}`);
                        } else {
                            console.warn(`图表数据为空，跳过 index: ${index}`);
                        }
                    });
                }, 100);
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        },

        async getUserCoinFiat() {
            let res = await this.$api.userCoinList({
                pageNum: 1,
                pageSize: 100,
                fiat: true, // 法币
                // hideZeroAsset: this.hideZeroAsset
                hideZeroAsset: false
            })
            if (res.code == 200) {
                // this.userCoinPage.list = res.result.data
                this.$emit('getFiatList', res.result.data)
                // this.fiatbalance = 累加this.userCoinPage.list的totalBalance
                // this.fiatbalance = this.userCoinPage.list.reduce((acc, cur) => {
                //     return acc + cur.totalBalance
                // }, 0)
            } else {
                this.$u.toast(res.msg)
            }
        },

        async initcrypto(index, globalOption, isRed) {
            let data = JSON.parse(JSON.stringify(this.globalOption)); // 深拷贝，避免修改原始数据
            // chart 图表实例不能存在data里
            // 计算最大值和最小值
            const minValue = Math.min(...globalOption);
            const maxValue = Math.max(...globalOption);

            // 设置 y 轴的最小值和最大值
            data.yAxis.min = minValue;
            data.yAxis.max = maxValue;
            if (isRed) {
                data.series[0].lineStyle.color = "rgba(236, 63, 103, 1)";
                data.series[0].areaStyle.color.colorStops[0].color =
                    "rgba(215, 57, 94, 1)";
                data.series[0].areaStyle.color.colorStops[1].color =
                    "rgba(255, 90, 117, 0)";
            }
            data.series[0].data = [];
            // 扩展数组并填充 null
            const extendedData = this.extendArrayWithNull(globalOption, 11);
            data.series[0].data = extendedData;
            const lEchart = await this.$refs.chartRefCrypto[index].init(echarts);
            lEchart.setOption(data);
        },

        async initStock(index, globalOption, isRed) {
            let data = JSON.parse(JSON.stringify(this.globalOption)); // 深拷贝，避免修改原始数据
            // chart 图表实例不能存在data里
            // 计算最大值和最小值
            const minValue = Math.min(...globalOption);
            const maxValue = Math.max(...globalOption);

            // 设置 y 轴的最小值和最大值
            data.yAxis.min = minValue;
            data.yAxis.max = maxValue;
            if (isRed) {
                data.series[0].lineStyle.color = "rgba(236, 63, 103, 1)";
                data.series[0].areaStyle.color.colorStops[0].color =
                    "rgba(215, 57, 94, 1)";
                data.series[0].areaStyle.color.colorStops[1].color =
                    "rgba(255, 90, 117, 0)";
            }
            data.series[0].data = [];
            // 扩展数组并填充 null
            const extendedData = this.extendArrayWithNull(globalOption, 11);
            data.series[0].data = extendedData;
            const lEchart = await this.$refs.chartRefStock[index].init(echarts);
            lEchart.setOption(data);
        },
        extendArrayWithNull(arr, targetLength) {
            // 确保目标长度大于等于当前数组长度
            const fillLength = Math.max(0, targetLength - arr.length);
            const extendedArray = arr.concat(Array(fillLength).fill(null));
            return extendedArray;
        },
        generateMockTrend(base = 100, changePercent = 0, count = 12) {
            base = Number(base);
            changePercent = Number(changePercent);
            count = Number(count);

            if (!isFinite(base) || !isFinite(changePercent) || !isFinite(count)) {
                console.warn('generateMockTrend 参数非法:', base, changePercent, count);
                return [];
            }

            const trend = [];
            let current = base;
            const isUp = changePercent > 0;
            const isDown = changePercent < 0;

            // 增加最大波动强度
            const amplitude = Math.min(Math.abs(changePercent), 30); // 控制整体波动
            const perStepRatio = 0.3 + Math.random() * 0.3; // 每步波动 30%~60% 的幅度

            for (let i = 0; i < count - 1; i++) {
                const noise = Math.random() * amplitude * perStepRatio;
                const direction = isUp ? 1 : isDown ? -1 : (Math.random() > 0.5 ? 1 : -1);
                current += direction * noise;
                trend.push(Number(current.toFixed(2)));
            }

            // 最后一个点为变化后的目标值
            const targetEnd = base * (1 + changePercent / 100);
            trend.push(Number(targetEnd.toFixed(2)));

            return trend;
        },
        changeTab(e) {
            if (this.current == e) return
            uni.setStorageSync('positionTab', e)
            this.stockMarketData = []
            this.quoteList = []
            console.log(e);
            this.current = e
            if (e == 1) {
                this.getHotStock()
            }
            // this.isLinkLoadding = true
            // setTimeout(() => {
            //     this.isLinkLoadding = false
            // }, 300);
            // if (e == 0) {
            //     this.tradedPageNum = 1
            //     this.entrustList = []
            //     this.fetchEntrust()
            // } else {
            //     this.pageNum = 1
            //     this.positionsList = []
            //     this.fetchPositions()
            // }

        },
        switchSubTab(index) {
            this.currentSubTab = index;
            this.pageNum = 1
            this.coinList = []
            this.stockList = []
            this.stock24hChange = []
            if (this.current == 0) {
                if (index == 0) {
                    this.getself()
                } else if (index == 1) {
                    this.loadContractList()
                } else if (index == 2) {
                    this.getContractHighRanks()
                } else if (index == 3) {
                    this.getContractLowRanks()
                } else if (index == 4) {
                    this.get24h()
                }
            } else {
                if (index == 0) {
                    this.getstockself()
                } else if (index == 1) {
                    // 热门
                    this.getHotStock()
                } else if (index == 2) {
                    // 涨幅榜 - 根据 changeRate 排序（从高到低）
                    this.sortStockDataByChangeRate('desc')
                } else if (index == 3) {
                    // 跌幅榜 - 根据 changeRate 排序（从低到高）
                    this.sortStockDataByChangeRate('asc')
                } else if (index == 4) {
                    this.get24hChange()
                }
            }


        },
        // 加载更多数据
        loadMore() {
            if (!this.hasnext) {
                uni.showToast({
                    title: '没有更多数据了',
                    icon: 'none'
                });
                return;
            }

            // 增加页码
            this.pageNum += 1;

            // 根据当前选中的子标签和市场类型调用对应的接口
            if (this.current == 0) {
                // 加密市场
                switch (this.currentSubTab) {
                    case 0:
                        // 自选
                        this.getself();
                        break;
                    case 1:
                        // 热门
                        this.loadContractList();
                        break;
                    case 2:
                        // 涨幅榜
                        this.getContractHighRanks();
                        break;
                    case 3:
                        // 跌幅榜
                        this.getContractLowRanks();
                        break;
                    case 4:
                        // 24H成交额
                        this.get24h();
                        break;
                    default:
                        console.error('未知的子标签索引:', this.currentSubTab);
                        break;
                }
            } else {
                // 证券市场
                switch (this.currentSubTab) {
                    case 0:
                        // 自选
                        this.getstockself();
                        break;
                    case 1:
                        // 热门
                        this.getHotStock();
                        break;
                    case 2:
                    case 3:
                        // 涨幅榜/跌幅榜 - 证券市场使用本地排序，不支持分页
                        uni.showToast({
                            title: '股票数据已全部加载',
                            icon: 'none'
                        });
                        return;
                    case 4:
                        // 24H成交额
                        this.get24hChange();
                        break;
                    default:
                        console.error('未知的子标签索引:', this.currentSubTab);
                        break;
                }
            }
        },
        // 计算价格变化百分比
        calculatePriceChange(coin) {
            const closePrice = coin.closePrice || 0;
            const openPrice = coin.openPrice || 0;
            const change = closePrice - openPrice;
            const percentage = openPrice != 0 ? (change / openPrice) * 100 : 0;
            return openPrice != 0 ? percentage.toFixed(2) : '0.00';
        },
        // 判断价格变化方向
        getPriceChangeDirection(coin) {
            const closePrice = coin.closePrice || 0;
            const openPrice = coin.openPrice || 0;
            const change = closePrice - openPrice;
            return {
                up: change > 0,
                down: change <= 0
            };
        },

        // 格式化金额显示
        formatAmount(amount) {
            if (!amount || isNaN(amount)) return '0';
            const num = Number(amount);
            console.log(num);

            if (num >= 1000000000) {
                // 十亿以上显示为 B
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                // 百万以上显示为 M
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                // 千以上显示为 K
                return (num / 1000).toFixed(1) + 'K';
            } else {
                // 小于千的直接显示
                return num.toFixed(0);
            }
        },

        // 切换美股港股标签
        switchStockTab(tab) {
            if (this.activeStockTab === tab) return; // 如果已经是当前标签，不执行切换
            this.activeStockTab = tab;
            // this.switchSubTab()
            if (this.currentSubTab == 0) {
                this.getstockself()
            } else if (this.currentSubTab == 1) {
                this.getHotStock()
            } else if (this.currentSubTab == 2) {
                // 涨幅榜
                this.sortStockDataByChangeRate('desc')
            } else if (this.currentSubTab == 3) {
                // 跌幅榜

                this.sortStockDataByChangeRate('asc')
            } else if (this.currentSubTab == 4) {
                this.get24hChange()
            }

            // 这里可以添加切换后的逻辑，比如重新获取数据
            if (tab === 'us') {
                // 切换到美股时的逻辑
                // this.getStockMarketData(); // 获取美股数据
            } else if (tab === 'hk') {
                // 切换到港股时的逻辑
                // this.getHKStockData(); // 获取港股数据（如果有的话）
            }

            // // 显示切换提示
            // uni.showToast({
            //     title: `已切换到${tab === 'us' ? '美股' : '港股'}`,
            //     icon: 'none',
            //     duration: 1000
            // });
        },

        // 根据 changeRate 对股票数据进行排序
        async sortStockDataByChangeRate(order = 'desc') {
            await this.getHotStock()
            if (!this.stockList || this.stockList.length === 0) {
                console.warn('股票数据为空，无法排序');
                // uni.showToast({
                //     title: '暂无股票数据',
                //     icon: 'none'
                // });
                return;
            }

            // 创建数据副本进行排序
            const sortedData = [...this.stockList].sort((a, b) => {
                const changeRateA = parseFloat(a.changeRate) || 0;
                const changeRateB = parseFloat(b.changeRate) || 0;

                if (order === 'desc') {
                    // 降序：涨幅榜（从高到低）
                    return changeRateB - changeRateA;
                } else {
                    // 升序：跌幅榜（从低到高）
                    return changeRateA - changeRateB;
                }
            });

            // 更新股票列表
            this.stockList = sortedData;

            console.log(`股票数据已按 changeRate ${order === 'desc' ? '降序' : '升序'} 排序:`, this.stockList);

            // // 显示排序提示
            // uni.showToast({
            //     title: `已按${order === 'desc' ? '涨幅' : '跌幅'}排序`,
            //     icon: 'none',
            //     duration: 1000
            // });
        },
    }
};
</script>
<style lang="scss" scoped>
.market-panel {

    .tabs_div {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            .utab {
                display: flex;
                font-weight: 400;
                font-size: 28rpx;
                color: rgba(0, 0, 0, .4);
                position: relative;
                margin: 30rpx 0;

                font-family: PingFang SC;
                font-weight: 600;
                line-height: 40rpx;



                .utabbar {

                    background: #FF82A3;
                    width: 32rpx;
                    height: 6rpx;
                    border-radius: 2rpx;

                    position: absolute;
                    bottom: -10rpx;
                    // left: 67rpx;
                    transition: all 0.3s ease-in-out;
                }


                view {
                    &:nth-of-type(1) {
                        margin: 0 52rpx 0 0;
                    }
                }
            }

            .utabact {
                color: #000;
            }

        }

        .right {
            image {
                width: 36rpx;
                margin-top: 14rpx;
            }
        }
    }


    .quote-cards {
        display: flex;
        flex-wrap: nowrap; // ✅ 禁止换行
        gap: 6rpx;
        margin-bottom: 36rpx;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        white-space: nowrap; // ✅ 兼容处理（对 text 也有帮助）
        height: 170rpx;

        // 隐藏滚动条（可选）
        &::-webkit-scrollbar {
            display: none;
        }

        .card {
            // width: 210rpx;
            // flex: 1;
            // flex: 0 0 auto;
            // background: rgba(255, 130, 163, 0.05);
            border-radius: 10rpx;
            padding: 6rpx 14rpx 10rpx 14rpx;

            .lc {
                margin-top: 9rpx;
                width: 220rpx;
                height: 70rpx;
            }

            .prices {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .title {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                line-height: 40rpx;
                color: #000000;
            }

            .price {
                font-size: 28rpx;
                font-weight: bold;
            }

            .change {
                font-size: 22rpx;
            }
        }
    }

    .sub-tabs {
        display: flex;
        position: relative;
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 22rpx;
        line-height: 44rpx;
        color: rgba(0, 0, 0, .4);
        gap: 16rpx;

        .sub-tab-item {
            padding: 2rpx 12rpx;
            color: #999;
            transition: all 0.3s ease; // ✅ 加在这里，生效于状态切换

            &.active {
                background: #FEF5FA;
                border-radius: 6rpx;
                color: #000;
                font-weight: 500;
            }
        }

        .sub-tab-bar {
            position: absolute;
            bottom: -4rpx;
            height: 4rpx;
            background-color: #ff77a2;
            border-radius: 4rpx;
            transition: all 0.3s ease;
        }
    }

    .us {
        margin: 20rpx 0;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 14rpx;

        .us-tabs {
            display: flex;
            align-items: center;
            height: 36rpx;
            padding: 0 12rpx;
            // background: #FEF5FA;
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 22rpx;
            color: #000;
            line-height: 40rpx;
        }

        .active {
            background: #FED3E1;
            border-radius: 6rpx;

        }
    }

    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 24rpx 0 0 0;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 22rpx;
        line-height: 40rpx;
        color: #999899;

        .left {
            flex: 1;
            text-align: left;
        }

        .right {
            flex: 1;
            text-align: right;
        }
    }

    .coin-list-container {
        // max-height: 800rpx;
        max-height: 1000rpx;

        overflow-y: auto;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
            width: 6rpx;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3rpx;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 3rpx;

            &:hover {
                background: rgba(0, 0, 0, 0.5);
            }
        }

        .coin-row {
            display: flex;
            align-items: center;
            padding: 26rpx 0;

            &:nth-of-type(1) {
                padding: 20rpx 0 26rpx 0;
            }

            .left {
                flex: 1;
                text-align: left;
                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;


                .hot-icon {
                    width: 20rpx;
                    height: 24rpx;
                    margin-left: 12rpx;
                }
            }

            .right {
                flex: 1;
                text-align: right;

                &.price {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 28rpx;
                    line-height: 40rpx;
                    color: #000;

                    .usd {
                        display: block;
                        font-family: PingFang SC;
                        font-weight: 500;
                        font-size: 24rpx;
                        line-height: 34rpx;
                        text-align: right;
                        color: rgba(0, 0, 0, .4);
                    }
                }


            }

            .rates {
                width: 33%;
                display: flex;
                justify-content: flex-end;

                .rate {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    // padding: 6rpx 16rpx;
                    color: #fff;
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 24rpx;
                    width: 142rpx;
                    height: 48rpx;
                    border-radius: 10rpx;



                    &.up {
                        background-color: #08B819;
                    }

                    &.down {
                        background-color: #FD0042;
                    }
                }
            }


        }
    }

    .coin-list-container-us {
        max-height: 1000rpx;
        overflow-y: auto;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
            width: 6rpx;
        }

        &::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3rpx;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 3rpx;

            &:hover {
                background: rgba(0, 0, 0, 0.5);
            }
        }

        .coin-row {
            display: flex;
            align-items: center;
            padding: 26rpx 0;

            &:nth-of-type(1) {
                padding: 20rpx 0 26rpx 0;
            }

            .left {
                // flex: 1;
                width: 362rpx;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                text-align: left;
                display: flex;
                align-items: center;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 40rpx;
                color: #000;
                display: flex;
                flex-direction: column;
                align-items: flex-start;

                .name {
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .symbol {
                    display: flex;
                    align-items: center;
                    gap: 10rpx;

                    .uss {
                        background: #FEF5FB;
                        border-radius: 4rpx;
                        width: 32rpx;
                        height: 24rpx;
                        font-family: PingFang SC;
                        font-weight: 500;
                        font-size: 20rpx;
                        color: #FF82A3;
                    }

                    .symbols {
                        font-family: PingFang SC;
                        font-weight: 500;
                        font-size: 20rpx;
                        line-height: 40rpx;
                        color: #929292;
                    }
                }

                .hot-icon {
                    width: 20rpx;
                    height: 24rpx;
                    margin-left: 12rpx;
                }
            }

            .right {
                flex: 1;
                text-align: right;

                &.price {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 28rpx;
                    line-height: 40rpx;
                    color: #000;

                    .usd {
                        display: block;
                        font-family: PingFang SC;
                        font-weight: 500;
                        font-size: 24rpx;
                        line-height: 34rpx;
                        text-align: right;
                        color: rgba(0, 0, 0, .4);
                    }
                }


            }

            .rates {
                width: 33%;
                display: flex;
                justify-content: flex-end;

                .rate {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    // padding: 6rpx 16rpx;
                    // color: #fff;
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 24rpx;
                    width: 142rpx;
                    height: 48rpx;
                    border-radius: 10rpx;



                    &.up {
                        background-color: #08B819;
                    }

                    &.down {
                        background-color: #FD0042;
                    }
                }
            }


        }
    }



    .more-btn {
        height: 96rpx;
        margin-top: 26rpx;
        background: #FFFFFF0D;
        border: 2rpx solid #0000001A;
        border-radius: 16rpx;
        font-size: 28rpx;
        color: #000;
        font-family: PingFang SC;
        font-weight: 500;

    }
}
</style>