<template>
    <view class="menu">
        <view v-for="(item, index) in menuList" :key="index" class="menu-item" @click="handleClick(item.key)">
            <view class="bg flex_all">
                <image :src="item.icon" class="menu-icon" />
            </view>
            <text class="menu-text">{{ item.label }}</text>
        </view>
    </view>
</template>

<script>
export default {
    name: "menu",
    data() {
        return {
            menuList: [
                {
                    key: 'Main',
                    label: 'C2C',
                    icon: "https://pro-oss.pinkwallet.com/image/1382412180111581184.png"
                },
                {
                    key: 'charge',
                    label: '充值',
                    icon: "https://pro-oss.pinkwallet.com/image/1382412578817925120.png"
                },
                {
                    key: 'trade',
                    label: '合约',
                    icon: "https://pro-oss.pinkwallet.com/image/1382416452047953920.png"
                },
                {
                    key: 'invite',
                    label: '邀请',
                    icon: "https://pro-oss.pinkwallet.com/image/1382412834930515968.png"
                },
                {
                    key: 'more',
                    label: '更多',
                    icon: "https://pro-oss.pinkwallet.com/image/1382413585136312320.png"
                }
            ]
        }
    },
    methods: {
        handleClick(key) {
            if (key == 'trade') {
                this.$emit('nav_contract', true);
                return
            }
            if (key == 'invite') {
                let token = uni.getStorageSync('token')
                if (token) {
                    let url = getApp().globalData.active_url
                    console.log(url);
                    this.$Router.push({
                        name: 'webView',
                        params: {
                            url,
                            token: uni.getStorageSync('token')
                        }
                    })
                }
                return
            }
            if (key == 'charge') {
                this.$Router.push({
                    name: 'symbol',
                    params: {
                        targetPage: 'addCapital'
                    }
                })
                return
            }
            this.$Router.push({
                name: key,
                params: {

                }
            })

            // this.$emit('menuClick', key)
        }
    }
}
</script>

<style lang="scss" scoped>
.menu {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 38rpx 0;
    background: #fff;
    gap: 50rpx;
    margin-top: 32rpx;
    // border-radius: 20rpx 20rpx 0 0;

    .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .bg {
            background: #FEFAFE;
            width: 84rpx;
            height: 84rpx;
            border-radius: 24rpx;

            .menu-icon {
                width: 44rpx;
                height: 44rpx;
            }
        }



        .menu-text {
            margin-top: 18rpx;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 100%;
            color: #000;
        }
    }
}
</style>