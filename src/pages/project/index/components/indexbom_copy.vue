<template>
    <view>
        <!-- 法币标题 v-if="fiatbalance"-->
        <view class="nowcurrency" @click="nav_to('Main')">{{ $t("index.Currencies.Title") }} : ${{
            Number(fiatbalance).toLocaleString('en-US')
            || '0.00' }}
        </view>
        <!-- 横向滚动 -->
        <view class="currency-container">
            <!-- scroll-x="true" :scroll-with-animation="true" -->
            <view class="scroll-wrapper">
                <scroll-view scroll-x="true" :scroll-with-animation="true" @touchstart.stop @touchmove.stop
                    @touchend.stop class="currency-card"
                    @click="nav_to_fiat('CurrentCryptocurrencyDetails', currency, true)"
                    v-for="(currency, index) in userCoinPage.list" :key="index">
                    <view class="info">
                        <image class="currency-icon" :src="currency.icon" />
                        <view class="currency-info flex-column">
                            <text class="name">{{ currency.name }}</text>
                            <text class="code">{{ currency.symbol }}</text>
                        </view>
                    </view>
                    <view class="value">${{ currency.totalBalance }}</view>

                    <view class="currency-actions">
                        <view class="btn flex_all">
                            <image
                                src="https://pro-oss.pinkwallet.com/image/20250401/55e5f7564835672f5a364b00c5d98dbe_60x60.png" />
                        </view>
                        <view class="btn flex_all">
                            <image
                                src="https://pro-oss.pinkwallet.com/image/20250401/796429bb17cdc4bd07853c4c49956022_80x80.png" />
                        </view>
                        <view class="btn flex_all">
                            <image
                                src="https://pro-oss.pinkwallet.com/image/20250401/73aa35a168ad28bb7978086c68d8eae9_80x80.png" />
                        </view>
                    </view>
                </scroll-view>

            </view>
            <!--  -->
            <nodata v-if="userCoinPage.list.length == 0" />
        </view>

        <!-- 加密标题 v-if="cryptobalance"-->
        <view class="nowcurrency">{{ $t("index.Crypto.Title") }}: ${{ Number(cryptobalance).toLocaleString('en-US') ||
            '0.00' }}
            <view class="hide" @click="hidezero">
                <view class="cir" v-if="!showimg">
                </view>
                <image v-else
                    src="https://pro-oss.pinkwallet.com/image/20250415/e4ded842c15a38d6646d85b39a64fa6b_97x96.png" />
                <text>{{ $t("index.Crypto.HideZero") }}</text>
            </view>
        </view>
        <!-- 加密货币信息 -->
        <view class="crypto-list">
            <view class="crypto-item" v-for="(crypto, index) in CryptouserCoinPage.list.slice(0, 5)" :key="index"
                @click="nav_to('CurrentCryptocurrencyDetails', crypto, false)">
                <view class="crypto-info">
                    <!-- <image :src="crypto.icon" class="crypto-icon"></image> -->
                    <view class="crypto-info-right">
                        <text class="crypto-name">{{ crypto.name }}</text>
                        <text class="crypto-symbol">{{ formatPriceAndChange(crypto) }}</text>
                    </view>
                </view>

                <view class="trend">
                    <view class="lc">
                        <l-echart class="klink" ref="chartRefCrypto"></l-echart>
                    </view>
                    <text :style="{ color: crypto.percentageChange > 0 ? '#02B632' : '#D72D4A' }">{{
                        formatPercentageChange(crypto.percentageChange) }}</text>
                </view>

                <view class="crypto-price">
                    <view class="rightrate">
                        <text class="crypto-value"> {{ formatExchangeRate(crypto) }}</text>

                        <text class="crypto-rate">{{ crypto.totalBalance.toFixed(3) + crypto.name }}</text>
                    </view>
                    <image
                        src="https://pro-oss.pinkwallet.com/image/20250401/880341bbfbfb0e5dfacb477dd44cf66d_80x81.png" />
                </view>
            </view>
            <nodata v-if="CryptouserCoinPage.list.length == 0" />

        </view>
    </view>
</template>

<script>
import bomMixin from "../mixins/bom";
import * as echarts from '@/uni_modules/lime-echart/static/echarts.min';
import nodata from "../../../../components/public/nodata"
export default {
    props: ["hideZeroAsset", 'count'],
    mixins: [bomMixin],
    components: {
        nodata
    },
    data() {
        return {
            showimg: false,
        }
    },

    mounted() {
        this.getUserCoinCrypto()
        this.getUserCoinFiat()
    },
    watch: {
        count(val) {
            this.getUserCoinCrypto()
            this.getUserCoinFiat()
        },
        "$store.state.indexBottom"(val) {
            // console.log(this.CryptouserCoinPage.list, val);
            if (this.CryptouserCoinPage.list.length) {
                this.CryptouserCoinPage.list = this.CryptouserCoinPage.list.map(item2 => {
                    const matchedItem = val.find(item1 => item1.baseCoin === item2.symbol);
                    if (matchedItem) {
                        return {
                            ...item2,
                            price: matchedItem.price,
                            percentageChange: matchedItem.percentageChange,
                            targetCoin: matchedItem.targetCoin
                        };
                    }
                    return { ...item2 };
                });
                this.$emit('userCoin', this.CryptouserCoinPage.list)
                // this.cryptobalance = this.CryptouserCoinPage.list.reduce((acc, cur) => {
                //     return acc + cur.totalBalance
                // }, 0)
                this.cryptobalance = (this.CryptouserCoinPage.list || []).reduce((acc, cur) => {
                    const price = Number(cur.price) || 0;
                    const totalBalance = Number(cur.totalBalance) || 0;
                    return acc + price * totalBalance;
                }, 0);
                // console.log(123, this.CryptouserCoinPage.list);
            }
        }
    },
    methods: {
        formatExchangeRate(crypto) {
            if (!crypto || !crypto.price || isNaN(crypto.price) || crypto.price === 0) {
                return '--';
            }
            const rate = (crypto.price) * crypto.totalBalance;
            return `$${rate.toFixed(4)}`;
        },
        nav_to_fiat(e, names, fiat) {
            this.$Router.push({
                name: e,
                params: {
                    symbol: names,
                    fiat: fiat,
                },
            });
        },
        hidezero() {
            this.showimg = !this.showimg
            if (this.showimg) {
                // 过滤掉CryptouserCoinPage.list中totalBalance为0 的
                this.CryptouserCoinPage.list = this.CryptouserCoinPage.list.filter(item => {
                    return item.totalBalance != 0
                })
            } else {
                this.getUserCoinCrypto()
                this.getUserCoinFiat()
            }


        },
        async getUserCoinFiat() {
            let res = await this.$api.userCoinList({
                pageNum: this.userCoinPage.pageNum,
                pageSize: this.userCoinPage.pageSize,
                fiat: true, // 法币
                hideZeroAsset: this.hideZeroAsset
            })
            if (res.code == 200) {
                this.userCoinPage.list = res.result.data
                this.$emit('getFiatList', this.userCoinPage.list)
                // this.fiatbalance = 累加this.userCoinPage.list的totalBalance
                this.fiatbalance = this.userCoinPage.list.reduce((acc, cur) => {
                    return acc + cur.totalBalance
                }, 0)
            } else {
                this.$u.toast(res.msg)
            }
        },
        generateMockTrend(base = 100, changePercent = 0, count = 12) {
            base = Number(base);
            changePercent = Number(changePercent);
            count = Number(count);

            if (!isFinite(base) || !isFinite(changePercent) || !isFinite(count)) {
                console.warn('generateMockTrend 参数非法:', base, changePercent, count);
                return [];
            }

            const trend = [];
            let current = base;
            const isUp = changePercent > 0;
            const isDown = changePercent < 0;

            // 增加最大波动强度
            const amplitude = Math.min(Math.abs(changePercent), 30); // 控制整体波动
            const perStepRatio = 0.3 + Math.random() * 0.3; // 每步波动 30%~60% 的幅度

            for (let i = 0; i < count - 1; i++) {
                const noise = Math.random() * amplitude * perStepRatio;
                const direction = isUp ? 1 : isDown ? -1 : (Math.random() > 0.5 ? 1 : -1);
                current += direction * noise;
                trend.push(Number(current.toFixed(2)));
            }

            // 最后一个点为变化后的目标值
            const targetEnd = base * (1 + changePercent / 100);
            trend.push(Number(targetEnd.toFixed(2)));

            return trend;
        }

        ,
        async getUserCoinCrypto() {
            let res = await this.$api.userCoinList({
                pageNum: this.CryptouserCoinPage.pageNum,
                pageSize: this.CryptouserCoinPage.pageSize,
                fiat: false, // 不是法币
                hideZeroAsset: this.hideZeroAsset
            })
            if (res.code == 200) {
                this.CryptouserCoinPage.list = res.result.data

                // setTimeout(() => {
                //     this.CryptouserCoinPage.list.forEach((item, index) => {
                //         let isRed = Number(item.percentageChange) < 0;
                //         this.initcrypto(index, [95, 96, 97, 98, 96, 97, 99, 80, 100, 70, 20], isRed)
                //     })
                // }, 100);

                setTimeout(() => {
                    this.CryptouserCoinPage.list.forEach((item, index) => {
                        const raw = item.percentageChange;
                        const change = raw == null ? 0 : Number(raw); // null/undefined → NaN

                        const isRed = raw < 0;
                        const trendData = this.generateMockTrend(100, change);
                        if (trendData.length > 0) {
                            setTimeout(() => {
                                this.initcrypto(index, trendData, isRed);
                            }, 10);
                        } else {
                            console.warn(`图表数据为空，跳过 index: ${index}`);
                        }
                    });
                }, 600);
            } else {
                this.$u.toast(res.msg)
            }
        },
        async initcrypto(index, globalOption, isRed) {
            let data = JSON.parse(JSON.stringify(this.globalOption)); // 深拷贝，避免修改原始数据
            // chart 图表实例不能存在data里
            // 计算最大值和最小值
            const minValue = Math.min(...globalOption);
            const maxValue = Math.max(...globalOption);

            // 设置 y 轴的最小值和最大值
            data.yAxis.min = minValue;
            data.yAxis.max = maxValue;
            if (isRed) {
                data.series[0].lineStyle.color = "rgba(236, 63, 103, 1)";
                data.series[0].areaStyle.color.colorStops[0].color =
                    "rgba(215, 57, 94, 1)";
                data.series[0].areaStyle.color.colorStops[1].color =
                    "rgba(255, 90, 117, 0)";
            }
            data.series[0].data = [];
            // 扩展数组并填充 null
            const extendedData = this.extendArrayWithNull(globalOption, 11);
            data.series[0].data = extendedData;
            const lEchart = await this.$refs.chartRefCrypto[index].init(echarts);
            lEchart.setOption(data);
        },
    }
}
</script>

<style lang="scss" scoped>
/* 加密货币信息 */
.crypto-list {
    // padding-bottom: 100rpx;

    .crypto-item {
        display: flex;
        align-items: center;
        padding: 10px 0;
        justify-content: space-between;

        .trend {
            .lc {
                width: 150rpx;
                height: 56rpx;
            }

            display: flex;
            flex-direction: column;
            align-items: center;

            font-family: Gilroy-SemiBold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 160%;

            .klink {
                height: 56rpx;

            }
        }

        .crypto-info {
            display: flex;
            align-items: center;
            width: 220rpx;

            .crypto-info-right {
                display: flex;
                flex-direction: column;
                margin-left: 10px;

            }

            .crypto-name {
                font-family: Gilroy-Bold;
                font-weight: 400;
                font-size: 28rpx;
                line-height: 34rpx;
                letter-spacing: 0%;
                color: #000;
            }

            .crypto-symbol {
                white-space: nowrap;
                margin-top: 4rpx;
                font-family: Gilroy-Medium;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 120%;
                letter-spacing: 0%;
                color: #333333;
            }
        }

        .crypto-price {
            display: flex;
            align-items: center;

            .rightrate {
                text-align: right;
                display: flex;
                flex-direction: column;

                .crypto-value {
                    font-family: Gilroy-Bold;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 34rpx;
                    letter-spacing: 0%;
                    text-align: right;
                    color: #000;
                }

                .crypto-rate {
                    margin-top: 4rpx;
                    font-family: Gilroy-Bold;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 28rpx;
                    letter-spacing: 0%;
                    text-align: right;
                    vertical-align: middle;
                    color: #999;
                }
            }

            image {
                width: 40rpx;
                height: 40rpx;
                margin-left: 20rpx;
            }
        }
    }
}

.currency-container {
    margin: 20rpx 0;
    // width: 100%;
    // overflow: hidden;
    background: #fff;
    // padding: 20rpx 0;

    .scroll-wrapper {
        // width: 400px;
        display: flex;
        align-items: center;
        gap: 20rpx;
        // padding: 0 20rpx;
        overflow-x: auto;
        white-space: nowrap; // 确保卡片不会换行
        // -webkit-overflow-scrolling: touch; // 让 iOS 端滑动更丝滑
    }

    .currency-card {
        min-width: 280rpx;
        height: 230rpx;
        border-radius: 32rpx;
        padding: 32rpx;
        background: #FFFFFF;
        border: 2rpx solid #D9D6D6;
        display: flex;
        flex-direction: column;
        // align-items: center;

        .info {
            margin-left: 4rpx;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .currency-icon {
                width: 80rpx;
                height: 80rpx;
                border-radius: 50%;
                // background: #ff99b4;
            }

            .currency-info {
                text-align: center;
                margin-left: 20rpx;

                .name {
                    font-family: Gilroy-Bold;
                    font-weight: 400;
                    font-size: 28rpx;
                    line-height: 34rpx;
                    letter-spacing: 0%;
                    color: #000;
                }

                .code {
                    font-family: Gilroy-SemiBold;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 30rpx;
                    letter-spacing: 0%;
                    color: #666;
                }


            }
        }

        .value {
            font-family: Gilroy-Bold;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 34rpx;
            margin: 20rpx 0;
        }

        .currency-actions {
            display: flex;
            justify-content: space-between;
            width: 100%;

            .btn {
                width: 64rpx;
                height: 64rpx;
                border-radius: 54rpx;
                border: 2rpx solid #D9D6D6;
            }

            image {
                width: 40rpx;
                height: 40rpx;
            }
        }
    }
}

.nowcurrency {
    font-family: Gilroy-Bold;
    font-weight: 400;
    font-size: 36rpx;
    line-height: 44rpx;
    color: #000;
    display: flex;
    align-items: center;
    justify-content: space-between;


    .hide {
        padding: 0 16rpx;
        // width: 304rpx;
        height: 60rpx;
        border-radius: 94rpx;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        // just
        border: 2rpx solid #D9D7D7;
        font-family: Gilroy-SemiBold;
        font-weight: 400;
        font-size: 28rpx;

        text {
            line-height: 34rpx;
        }

        image {
            width: 36rpx;
            margin-right: 16rpx;
            height: 36rpx;
        }

        .cir {
            width: 36rpx;
            height: 36rpx;
            border-radius: 60rpx;
            border: 2rpx solid #B4B1B1;
            background: #FFFFFF;
            margin-right: 16rpx;


        }
    }
}
</style>