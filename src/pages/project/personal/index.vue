<template>
	<view class="personal_bg">
		<!-- 顶部资产卡片浮层 -->
		<view class="asset_card">
			<view class="right_icon">
				<image src="https://pro-oss.pinkwallet.com/image/1387826972485836800.png" mode="widthFix"></image>
			</view>
			<view class="asset_card_top">
				<view class="asset_card_left">
					<view class="asset_card_label">
						<text class="asset_card_label_text">预计总资产</text>
						<u-icon name="info-circle" size="18" color="#BDBDBD" class="asset_card_info_icon" />
					</view>
					<view class="asset_card_amount_row">
						<text class="asset_card_amount">$ 3,211.59</text>
						<text class="asset_card_unit">USD</text>
						<u-icon name="arrow-down" size="14" color="#BDBDBD" class="asset_card_arrow_icon" />
					</view>
					<view class="asset_card_rate">1,99999 USD(100%) 昨日</view>
				</view>
				<view class="asset_card_chart">
					<view class="chart_placeholder">
						<image src="https://pro-oss.pinkwallet.com/image/1387826531387662336.png" mode="widthFix">
						</image>
					</view>
				</view>
			</view>
			<view class="asset_card_account_row">
				<view class="asset_card_account_left">
					<text class="asset_card_account_label">账户</text>
					<text class="asset_card_account_date">5,22,2025</text>
				</view>
				<view class="asset_card_account_right">
					<text class="asset_card_account_value">999,999.00 USD</text>
					<text class="asset_card_account_percent">100%</text>
					<u-icon name="arrow-right" size="14" color="#BDBDBD" class="asset_card_account_arrow" />
				</view>
			</view>
		</view>

		<!-- 操作按钮区块 -->
		<view class="action_btns_wrap">
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="openBottomPopup()">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386806647358840832.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">添加资金</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="onPopupOption({title:'转账'})">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807020060499968.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">转账</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="nav_to('transfer')">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807124683218944.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">划转</text>
			</view>
		</view>

		<!-- 交易记录区块 -->
		<view class="trade_record_card">
			<view class="trade_record_title">交易记录</view>
			<view class="trade_record_list">
				<view class="trade_record_item">
					<text class="trade_record_type">加密货币提现 9USD T</text>
					<view class="">
						<text class="trade_record_status">已完成</text>
						<u-icon name="arrow-right" size="20" color="#BDBDBD" />
					</view>
				</view>
			</view>
		</view>

		<!-- 资产信息区块 -->
		<view class="asset_info_card">
			<view class="asset_info_header">
				<text class="asset_info_title">资产信息</text>
				<view class="asset_info_search_filter">
					<template v-if="!searchActive">
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386810093399007232.png" size="36"
							color="#121212" @click="searchActive = true" />
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386809693031718912.png" size="36"
							color="#121212" style="margin-left: 24rpx;" />
					</template>
					<template v-else>
						<view class="asset_info_searchbox">
							<u-icon name="https://pro-oss.pinkwallet.com/image/1386810093399007232.png" size="32"
								color="#999" />
							<input class="asset_info_searchinput" v-model="searchText" placeholder="Search" />
						</view>
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386809693031718912.png" size="36"
							color="#121212" style="margin-left: 24rpx;" />
					</template>
				</view>
			</view>
			<view class="asset_info_list">
				<view class="asset_info_item">
					<view class="asset_info_logo_wrap btc_bg">
						<image class="asset_info_logo" src="/static/imgs/public/BTC.png" />
					</view>
					<view class="asset_info_main">
						<text class="asset_info_name">BTC</text>
						<text class="asset_info_rate">--< -0.01%</text>
					</view>
					<view class="asset_info_value">
						<text class="asset_info_num">0.1232221</text>
						<text class="asset_info_usd">$9.93</text>
					</view>
				</view>
				<view class="asset_info_item">
					<view class="asset_info_logo_wrap eth_bg">
						<image class="asset_info_logo" src="/static/imgs/public/ETH.png" />
					</view>
					<view class="asset_info_main">
						<text class="asset_info_name">ETH</text>
						<text class="asset_info_rate">--< -0.01%</text>
					</view>
					<view class="asset_info_value">
						<text class="asset_info_num">0.1232221</text>
						<text class="asset_info_usd">$9.93</text>
					</view>
				</view>
				<view class="asset_info_item">
					<view class="asset_info_logo_wrap usdt_bg">
						<image class="asset_info_logo" src="/static/imgs/public/BTC.png" />
					</view>
					<view class="asset_info_main">
						<text class="asset_info_name">USDT</text>
						<text class="asset_info_rate">--< -0.01%</text>
					</view>
					<view class="asset_info_value">
						<text class="asset_info_num">0.1232221</text>
						<text class="asset_info_usd">$9.93</text>
					</view>
				</view>
				<view class="asset_info_item">
					<view class="asset_info_logo_wrap bnb_bg">
						<image class="asset_info_logo" src="/static/imgs/public/BTC.png" />
					</view>
					<view class="asset_info_main">
						<text class="asset_info_name">BNB</text>
						<text class="asset_info_rate">--< 0.01%</text>
					</view>
					<view class="asset_info_value">
						<text class="asset_info_num">0.1232221</text>
						<text class="asset_info_usd">$9.93</text>
					</view>
				</view>
			</view>
			<view class="asset_info_more">
				<view class="asset_info_more_btn">+ View More</view>
			</view>
		</view>
		<!-- 底部弹窗 -->
		<u-popup v-model="showBottomPopup" mode="bottom" border-radius="32" height="auto">
			<view class="bottom-popup-content">
				<view class="popup-title-row">
					<text class="popup-title">选择充值方式</text>
					<u-icon name="close" size="36" color="#999" @click="showBottomPopup = false" />
				</view>
				<view class="popup-option" v-for="item in popupOptions" :key="item.title" @click="onPopupOption(item)">
					<image :src="item.icon" class="popup-option-icon" />
					<view class="popup-option-info">
						<text class="popup-option-title">{{ item.title }}</text>
						<text class="popup-option-desc">{{ item.desc }}</text>
					</view>
					<u-icon name="https://pro-oss.pinkwallet.com/image/1387134736018268160.png" size="12" />
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchActive: false,
				searchText: '',
				showBottomPopup: false,
				popupOptions: [{
						title: '链上充值',
						desc: '将其他交易平台/钱包的加密货币存入PinkWallet账户',
						icon: 'https://pro-oss.pinkwallet.com/image/1387135206002614272.png'
					},
					{
						title: '快捷买币',
						desc: '法币快速购买数字货币，购买成功后存入PinkWallet账户',
						icon: 'https://pro-oss.pinkwallet.com/image/1387135325519306752.png'
					},
					{
						title: 'C2C交易',
						desc: '点对点交易，价格从优，支持多种本地支付方式',
						icon: 'https://pro-oss.pinkwallet.com/image/1387135386655481856.png'
					}
				],
			}
		},
		mounted() {
			this.getInfo()
		},
		methods: {
			onPopupOption(item) {
				// 这里可以根据item.title或其他字段做跳转
				this.showBottomPopup = false
				this.$u.toast('你选择了：' + item.title)
				switch (item.title) {
					case '链上充值':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case '快捷买币':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case 'C2C交易':
						this.nav_to('symbol')
						break
					case '转账':
						this.nav_to('symbol', {
							targetPage: 'withdraw'
						})
						break
					default:
						break
				}
			},
			nav_to(name, params) {
				this.$Router.push({
					name,
					params
				})
			},
			openBottomPopup() {
				this.showBottomPopup = true
			},
			async getInfo() {
				let res = await this.$api.amount({
					assetType: 0,
					assetTypeEnum: "ASSET_COIN",
					symbol: "USD",
					convertCoin: "USD"
				});
				if (res.status.code == 0) {
					console.log(res)
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.personal_bg {
		min-height: 100vh;
		background: #FEFBFF;
		padding: 0 0 40rpx 0;
		font-family: 'PingFang SC';
		padding-bottom: 100rpx;
	}

	.asset_card {
		margin: 0;
		background: #fff;
		border-radius: 0 0 48rpx 48rpx;
		padding: 48rpx 30rpx 48rpx 30rpx;
		position: relative;
		z-index: 2;
		font-family: 'PingFang SC';
		position: relative;

		.right_icon {
			position: absolute;
			right: 30rpx;
			top: 40rpx;

			image {
				width: 40rpx;
			}
		}

		.asset_card_top {
			display: flex;
			justify-content: space-between;
			align-items: flex-end;

			.asset_card_left {
				flex: 1;

				.asset_card_label {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #BDBDBD;
					font-weight: 400;
					margin-bottom: 12rpx;
				}

				.asset_card_amount_row {
					display: flex;
					align-items: flex-end;
					margin-bottom: 8rpx;

					.asset_card_amount {
						font-size: 60rpx;
						font-weight: 800;
						color: #121212;
						line-height: 1;
					}

					.asset_card_unit {
						font-size: 28rpx;
						color: #121212;
						font-weight: 500;
						margin-left: 10rpx;
					}

					.asset_card_arrow_icon {
						margin-left: 6rpx;
						font-size: 22rpx;
						color: #BDBDBD;
					}
				}

				.asset_card_rate {
					font-size: 22rpx;
					color: #BDBDBD;
					font-weight: 400;
					/* margin-bottom: 16rpx; */
				}
			}

			.asset_card_chart {
				width: 154rpx;
				height: 74rpx;
				margin-left: 16rpx;

				.chart_placeholder {
					width: 100%;
					height: 100%;

					image {
						width: 100%;
					}
				}
			}
		}

		.asset_card_account_row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 24rpx;

			.asset_card_account_left {
				display: flex;
				flex-direction: column;

				.asset_card_account_label {
					font-size: 28rpx;
					color: #121212;
					font-weight: 700;
				}

				.asset_card_account_date {
					font-size: 20rpx;
					color: #BDBDBD;
					font-weight: 400;
					margin-top: 2rpx;
				}
			}

			.asset_card_account_right {
				display: flex;
				align-items: center;

				.asset_card_account_value {
					font-size: 28rpx;
					color: #121212;
					font-weight: 700;
				}

				.asset_card_account_percent {
					font-size: 20rpx;
					color: #BDBDBD;
					margin-left: 8rpx;
				}

				.asset_card_account_arrow {
					margin-left: 8rpx;
					font-size: 22rpx;
					color: #BDBDBD;
				}
			}
		}
	}

	.action_btns_wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 48rpx;
		background: #fff;
		padding: 24rpx 100rpx;
		margin: 20rpx 0rpx;

		.action_btn_item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.action_btn_icon_wrap {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 12rpx;

				.u-icon {
					font-size: 44rpx !important;
				}
			}

			.action_btn_text {
				font-size: 24rpx;
				color: #121212;
				margin-top: 4rpx;
				font-weight: 500;
			}
		}

		.action_btn_item+.action_btn_item {
			margin-left: 24rpx;
		}
	}

	.trade_record_card {
		background: #fff;
		border-radius: 48rpx;
		padding: 32rpx 32rpx 12rpx 32rpx;

		.trade_record_title {
			font-size: 28rpx;
			font-weight: bold;
			color: #121212;
			margin-bottom: 20rpx;
		}

		.trade_record_list {
			.trade_record_item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;

				.trade_record_type {
					font-size: 24rpx;
					color: #121212;
				}

				.trade_record_status {
					font-size: 22rpx;
					color: #BDBDBD;
				}
			}

			.trade_record_item:last-child {
				border-bottom: none;
			}
		}
	}

	.asset_info_card {
		background: #fff;
		border-radius: 48rpx;
		padding: 48rpx 32rpx 48rpx 32rpx;
		margin-top: 20rpx;

		.asset_info_header {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.asset_info_title {
				font-size: 28rpx;
				font-weight: bold;
				color: #121212;
			}

			.asset_info_search_filter {
				display: flex;
				align-items: center;

				.asset_info_searchbox {
					display: flex;
					align-items: center;
					background: #f5f5f5;
					border-radius: 24rpx;
					padding: 0 24rpx;
					height: 56rpx;

					.asset_info_searchinput {
						border: none;
						background: transparent;
						font-size: 26rpx;
						color: #121212;
						margin-left: 12rpx;
						width: 180rpx;
					}
				}
			}
		}

		.asset_info_list {
			.asset_info_item {
				display: flex;
				align-items: center;
				padding: 20rpx 0;

				.asset_info_logo_wrap {
					width: 56rpx;
					height: 56rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;

					.asset_info_logo {
						width: 38rpx;
						height: 38rpx;
					}
				}

				.btc_bg {
					background: #F7931A;
				}

				.eth_bg {
					background: #627EEA;
				}

				.usdt_bg {
					background: #26A17B;
				}

				.bnb_bg {
					background: #F3BA2F;
				}

				.asset_info_main {
					flex: 1;
					display: flex;
					flex-direction: column;

					.asset_info_name {
						font-size: 26rpx;
						color: #121212;
					}

					.asset_info_rate {
						font-size: 22rpx;
						color: #BDBDBD;
					}
				}

				.asset_info_value {
					display: flex;
					flex-direction: column;
					align-items: flex-end;

					.asset_info_num {
						font-size: 26rpx;
						color: #121212;
					}

					.asset_info_usd {
						font-size: 22rpx;
						color: #BDBDBD;
					}
				}
			}

			.asset_info_item:last-child {
				border-bottom: none;
			}
		}

		.asset_info_more {
			display: flex;
			justify-content: center;
			margin: 20rpx 0 0 0;

			.asset_info_more_btn {
				width: 92%;
				background: #fff;
				color: #121212;
				border: 1rpx solid rgba(0, 0, 0, 0.05);
				border-radius: 18rpx;
				font-size: 24rpx;
				text-align: center;
				font-weight: 500;
				height: 88rpx;
				line-height: 88rpx;
			}
		}
	}

	.bottom-popup-content {
		padding: 40rpx 0 32rpx 0;

		.popup-title-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 32rpx 32rpx 32rpx;

			.popup-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #121212;
			}
		}

		.popup-option {
			display: flex;
			align-items: center;
			background: #fff;
			border-radius: 24rpx;
			margin: 0 32rpx 24rpx 32rpx;
			padding: 32rpx 24rpx;
			border: 1rpx solid rgba(0, 0, 0, 0.05);

			.popup-option-icon {
				width: 60rpx;
				height: 60rpx;
				margin-right: 24rpx;
			}

			.popup-option-info {
				flex: 1;
				display: flex;
				flex-direction: column;

				.popup-option-title {
					font-size: 28rpx;
					color: #121212;
					font-weight: 600;
					margin-bottom: 8rpx;
				}

				.popup-option-desc {
					font-size: 20rpx;
					color: rgba(0, 0, 0, 0.50);
				}
			}
		}
	}
</style>