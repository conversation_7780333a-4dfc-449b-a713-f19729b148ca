<template>
	<view class="personal_bg">
		<!-- 顶部资产卡片浮层 -->
		<view class="asset_card">
			<view class="right_icon" @click="showWalletPopup = true">
				<image src="https://pro-oss.pinkwallet.com/image/1387826972485836800.png" mode="widthFix"></image>
			</view>
			<view class="asset_card_top">
				<view class="asset_card_left">
					<view class="asset_card_label">
						<text class="asset_card_label_text">预计总资产</text>
						<u-icon v-if="eyeoff" @click="eyeoff = !eyeoff"
							name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370353025834115072.png"
							size="36" class="asset_card_info_icon" />
						<u-icon v-else @click="eyeoff = !eyeoff"
							name="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1370353145610854400.png"
							size="36" class="asset_card_info_icon" />

					</view>
					<view class="asset_card_amount_row">
						<text class="asset_card_amount" v-if="eyeoff">***</text>
						<text class="asset_card_amount" v-else>$ {{Number(totalNum).toFixed(2).toLocaleString('en-US')
							||
							'0.00'}} </text>
						<view>
							<text class="asset_card_unit"
								@click="toggleRotate(),showcurrencyShow = !showcurrencyShow">{{nowsymbol}}</text>
							<u-icon name="arrow-down" size="24" color="#000000"
								:style="{ transform: `rotate(${rotate}deg)` }" class="asset_card_arrow_icon"
								@click="showcurrencyShow = !showcurrencyShow" />
						</view>
						<transition name="expand-slide">
							<view class="helpoption" v-show="showcurrencyShow">
								<view v-for="(item, index) in userCoins" :key="index" class="Roptions"
									@click.stop="SetSymbol(item)">
									<text :style="{ color: nowsymbol == item.name ? '#008E28' : '' }">{{ item.name
										}}</text>
								</view>
							</view>
						</transition>
					</view>
					<view class="asset_card_rate">{{increaseInfo.nowDay}} {{nowsymbol}}
						<text>({{increaseInfo.increaseRate*1000>0?`+${increaseInfo.increaseRate}`:increaseInfo.increaseRate}}%)  昨日</text>
					</view>
				</view>
				<view class="asset_card_chart">
					<view class="chart_placeholder">
						<image src="https://pro-oss.pinkwallet.com/image/1387826531387662336.png" mode="widthFix">
						</image>
					</view>
				</view>
			</view>
			<view class="asset_card_account_row">
				<view class="asset_card_account_left">
					<text class="asset_card_account_label">账户</text>
					<text class="asset_card_account_date">实时</text>
				</view>
				<view class="asset_card_account_right">
					<text class="asset_card_account_value" v-if="eyeoff">***</text>

					<text class="asset_card_account_value"
						v-else>${{Number(totalNum).toFixed(2).toLocaleString('en-US')||'0.00'}} {{nowsymbol}}</text>
					<u-icon name="arrow-right" size="22" color="#000" class="asset_card_account_arrow" />
				</view>
			</view>
		</view>

		<!-- 操作按钮区块 -->
		<view class="action_btns_wrap">
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="openBottomPopup()">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386806647358840832.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">添加资金</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="onPopupOption({title:'转账'})">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807020060499968.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">转账</text>
			</view>
			<view class="action_btn_item">
				<view class="action_btn_icon_wrap" @click="nav_to('transfer')">
					<u-icon name="https://pro-oss.pinkwallet.com/image/1386807124683218944.png" size="80"
						color="#EC5CF4" />
				</view>
				<text class="action_btn_text">划转</text>
			</view>
		</view>

		<!-- 交易记录区块 -->
		<view class="trade_record_card">
			<view class="trade_record_title">交易记录</view>
			<view class="trade_record_list">
				<view class="trade_record_item" v-for="(item,index) in transactions" v-if="index<4">
					<view class="trade_record_type">
						<text class="history_desc" v-if="item.type == 'deposit'">充值</text>
						<text class="history_desc" v-if="item.type == 'withdraw'">提现</text>
						<text class="history_desc" v-if="item.type == 'swap'">闪兑</text>
						&nbsp;&nbsp;{{item.amount}}&nbsp;&nbsp;{{item.symbol}}
					</view>
					<view class="">
						<text class="trade_record_status">{{getStatusLabel(item.status)}}</text>
						<u-icon name="arrow-right" size="20" color="#BDBDBD" />
					</view>
				</view>
			</view>
			<noata v-if="transactions.length ==0"></noata>
		</view>

		<!-- 资产信息区块 -->
		<view class="asset_info_card">
			<view class="asset_info_header">
				<text class="asset_info_title">资产信息</text>
				<view class="asset_info_search_filter">
					<template v-if="!searchActive">
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386810093399007232.png" size="36"
							color="#121212" @click="searchActive = true" />
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386809693031718912.png" size="36"
							color="#121212" style="margin-left: 24rpx;" />
					</template>
					<template v-else>
						<view class="asset_info_searchbox">
							<u-icon name="https://pro-oss.pinkwallet.com/image/1386810093399007232.png" size="32"
								color="#999" />
							<input class="asset_info_searchinput" v-model="searchText" placeholder="Search" />
						</view>
						<u-icon name="https://pro-oss.pinkwallet.com/image/1386809693031718912.png" size="36"
							color="#121212" style="margin-left: 24rpx;" />
					</template>
				</view>
			</view>
			<view class="asset_info_list">
				<view class="asset_info_item" v-for="(item,index) in [0,0,0]">
					<view class="asset_info_logo_wrap btc_bg">
						<image class="asset_info_logo" src="/static/imgs/public/BTC.png" />
					</view>
					<view class="asset_info_main">
						<text class="asset_info_name">BTC</text>
						<text class="asset_info_rate">--< -0.01%</text>
					</view>
					<view class="asset_info_value">
						<text class="asset_info_num">0.1232221</text>
						<text class="asset_info_usd">$9.93</text>
					</view>
				</view>
			</view>
			<view class="asset_info_more">
				<view class="asset_info_more_btn">+ View More</view>
			</view>
		</view>
		<!-- 底部弹窗 -->
		<u-popup v-model="showBottomPopup" mode="bottom" border-radius="32" height="auto">
			<view class="bottom-popup-content">
				<view class="popup-title-row">
					<text class="popup-title">选择充值方式</text>
					<u-icon name="close" size="36" color="#999" @click="showBottomPopup = false" />
				</view>
				<view class="popup-option" v-for="item in popupOptions" :key="item.title" @click="onPopupOption(item)">
					<image :src="item.icon" class="popup-option-icon" />
					<view class="popup-option-info">
						<text class="popup-option-title">{{ item.title }}</text>
						<text class="popup-option-desc">{{ item.desc }}</text>
					</view>
					<u-icon name="https://pro-oss.pinkwallet.com/image/1387134736018268160.png" size="12" />
				</view>
			</view>
		</u-popup>
		<u-popup v-model="showWalletPopup" mode="bottom" border-radius="32" height="auto">
			<view class="wallet_popup_content">
				<view class="wallet_popup_title_row">
					<text class="wallet_popup_title">选择钱包</text>
					<u-icon name="close" size="36" color="#999" @click="showWalletPopup = false" />
				</view>
				<view class="wallet_popup_list">
					<view class="wallet_popup_item" v-for="(item, idx) in walletList" :key="idx"
						@click="onSelectWallet(item)">
						<!-- <image :src="item.icon" class="wallet_popup_icon" /> -->
						<u-icon :name="item.icon" size="68" style="margin-right: 20rpx;" />
						<view class="wallet_popup_info">
							<text class="wallet_popup_item_title">{{item.title}}</text>
							<text class="wallet_popup_item_desc">{{item.desc}}</text>
						</view>
						<u-icon name="arrow-right" size="28" color="#BDBDBD" />
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import noata from '../../../components/public/noata2.vue'
	export default {
		data() {
			return {
				searchActive: false,
				searchText: '',
				showBottomPopup: false,
				popupOptions: [{
					title: '链上充值',
					desc: '将其他交易平台/钱包的加密货币存入PinkWallet账户',
					icon: 'https://pro-oss.pinkwallet.com/image/1387135206002614272.png'
				},
				{
					title: '快捷买币',
					desc: '法币快速购买数字货币，购买成功后存入PinkWallet账户',
					icon: 'https://pro-oss.pinkwallet.com/image/1387135325519306752.png'
				},
				{
					title: 'C2C交易',
					desc: '点对点交易，价格从优，支持多种本地支付方式',
					icon: 'https://pro-oss.pinkwallet.com/image/1387135386655481856.png'
				}
				],
				eyeoff: false,
				userCoins: [],
				showcurrencyShow: false,
				nowsymbol: '',
				rotate: 0,
				totalNum: 0,
				transactions: [],
				showWalletPopup: false,
				walletList: [
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187064594948096.png',
						title: '资金账户（加密货币）',
						desc: '≈40,999.12345678 USD（划转到U本位合约账户）',
						tag: '加密货币'
					},
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187124351197184.png',
						title: 'U本位合约账户（加密货币）',
						desc: '≈40,999.12345678 USD（划转到资金账户）',
						tag: '加密货币'
					},
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187172954791936.png',
						title: '资金账户（法币）',
						desc: '≈40,999.12345678 USD（仅限法币账户内转）',
						tag: '法币'
					},
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187222795706368.png',
						title: '美股股票账户（法币）',
						desc: '40,999.12345678 USD（划转到港股资金账户）',
						tag: '法币'
					},
					{
						icon: 'https://pro-oss.pinkwallet.com/image/1388187284028350464.png',
						title: '港股股票账户（法币）',
						desc: '40,999.12345678 HKD（划转到美股资金账户）',
						tag: '法币'
					}
				],
				increaseInfo:{}
			}
		},
		computed: {
			statusOptions() {
				return [
					{ value: 1, label: this.$t("Record.Status.Init") },
					{ value: 2, label: this.$t("Record.Status.InProgress") },
					{ value: 3, label: this.$t("Record.Status.Completed") },
					{ value: 4, label: this.$t("Record.Status.Failed") }
				]
			}
		},
		components: {
			noata
		},
		mounted() {

			this.getUserCoinFiat()
			this.getList()
			// this.balanceList()
		},
		methods: {
			onPopupOption(item) {
				// 这里可以根据item.title或其他字段做跳转
				this.showBottomPopup = false
				this.$u.toast('你选择了：' + item.title)
				switch (item.title) {
					case '链上充值':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case '快捷买币':
						this.nav_to('symbol', {
							targetPage: 'addCapital'
						})
						break
					case 'C2C交易':
						this.nav_to('symbol')
						break
					case '转账':
						this.nav_to('symbol', {
							targetPage: 'withdraw'
						})
						break
					default:
						break
				}
			},
			nav_to(name, params) {
				this.$Router.push({
					name,
					params
				})
			},
			openBottomPopup() {
				this.showBottomPopup = true
			},

			async getInfo() {
				let res = await this.$api.amount({
					assetType: 0,
					convertCoin: this.nowsymbol
					// assetTypeEnum: "ASSET_COIN",
					// symbol: "USD",
				});
				if (res.code == 200) {
					console.log(res)
					this.totalNum = res.result.amount
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			async getAllAssetsFiat() {
				let res = await this.$api.userAssetsFiat({
					coin: this.nowsymbol
				})
				if (res.code == 200) {
					this.allassets = res.result.amount
				}
			},
			SetSymbol(e) {
				console.log(e);
				this.nowsymbol = e.name
				this.showcurrencyShow = false
				// this.getAllAssetsFiat()
				// this.getLine()
				// this.init()
				this.getIncreaseRate()
				this.getInfo()
			},
			toggleRotate() {
				this.rotate = this.rotate === 0 ? 180 : 0;
			},
			async getUserCoinFiat() {
				let res = await this.$api.userCoinList({
					pageNum: 1,
					pageSize: 100,
					fiat: true, // 法币
					hideZeroAsset: false
				})
				if (res.code == 200) {
					this.nowsymbol = res.result.data[0].symbol
					this.userCoins = res.result.data
					this.getInfo()
					this.getIncreaseRate()
				} else {
					this.$u.toast(res.msg)
				}
			},
			async getIncreaseRate() {
				let res = await this.$api.increaseRate({
					assetType: 0,
					convertCoin: this.nowsymbol
				})
				if (res.code == 200) {
					this.increaseInfo = res.result
				}
			},
			async getList() {
				this.loading = true
				let res = await this.$api.getTransactionPaged({
					pageNum: 1,
					pageSize: 20,
					symbol: '',
					type: 'all'
				})
				if (res.code == 200) {
					this.loading = false
					this.transactions = res.result.data
					// if (this.pageNum == 1) {
					//     this.transactions = res.result.data
					// } else { 
					//     this.transactions = this.transactions.concat(res.result.data)
					// }
				} else {
					this.loading = false
					this.$u.toast(res.msg)
				}
			},
			openWalletPopup() {
				this.showWalletPopup = true
			},
			onSelectWallet(item) {
				this.showWalletPopup = false
				this.$u.toast('你选择了：' + item.title)
			},
			getStatusLabel(status) {
				const match = this.statusOptions.find(opt => opt.value === status)
				return match ? match.label : '--'
			},
			async balanceList() {
				this.loading = true
				let res = await this.$api.symbolBalanceList({
					fiat: 1,
					pageNum: 1,
					pageSize: 20,
				})
				if (res.code == 200) {
					console.log(res)
				} else {
					this.loading = false
					this.$u.toast(res.msg)
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.personal_bg {
		min-height: 100vh;
		background: #FEFBFF;
		padding: 0 0 40rpx 0;
		font-family: 'PingFang SC';
		padding-bottom: 100rpx;
	}

	.asset_card {
		margin: 0;
		background: #fff;
		border-radius: 0 0 48rpx 48rpx;
		padding: 48rpx 30rpx 48rpx 30rpx;
		position: relative;
		z-index: 2;
		font-family: 'PingFang SC';
		position: relative;

		.right_icon {
			position: absolute;
			right: 30rpx;
			top: 40rpx;

			image {
				width: 40rpx;
			}
		}

		.asset_card_top {
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			margin: 20rpx 0rpx;

			.asset_card_left {
				.asset_card_labe l {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #BDBDBD;
					font-weight: 400;
					margin-bottom: 12rpx;
				}

				.asset_card_amount_row {
					display: flex;
					align-items: flex-end;
					margin-bottom: 8rpx;
					position: relative;

					.asset_card_amount {
						font-size: 60rpx;
						font-weight: 800;
						color: #121212;
						line-height: 1;
					}

					.asset_card_unit {
						font-size: 28rpx;
						color: #121212;
						font-weight: 500;
						margin-left: 10rpx;
					}

					.asset_card_arrow_icon {
						margin-left: 6rpx;
						font-size: 22rpx;
						color: #BDBDBD;
						margin-bottom: 5rpx;
					}
				}

				.asset_card_rate {
					font-size: 22rpx;
					color: #BDBDBD;
					font-weight: 400;
					/* margin-bottom: 16rpx; */
				}
			}

			.asset_card_chart {
				width: 154rpx;
				height: 74rpx;
				margin-left: 16rpx;

				.chart_placeholder {
					width: 100%;
					height: 100%;

					image {
						width: 100%;
					}
				}
			}
		}

		.asset_card_account_row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 40rpx;

			.asset_card_account_left {
				display: flex;
				flex-direction: column;

				.asset_card_account_label {
					font-size: 28rpx;
					color: #121212;
					font-weight: 700;
				}

				.asset_card_account_date {
					font-size: 20rpx;
					color: #BDBDBD;
					font-weight: 400;
					margin-top: 2rpx;
				}
			}

			.asset_card_account_right {
				display: flex;
				align-items: center;

				.asset_card_account_value {
					font-size: 28rpx;
					color: #121212;
					font-weight: 700;
				}

				.asset_card_account_percent {
					font-size: 20rpx;
					color: #BDBDBD;
					margin-left: 8rpx;
				}

				.asset_card_account_arrow {
					margin-left: 8rpx;
					font-size: 22rpx;
					color: #BDBDBD;
				}
			}
		}
	}

	.action_btns_wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 48rpx;
		background: #fff;
		padding: 24rpx 100rpx;
		margin: 20rpx 0rpx;

		.action_btn_item {
			display: flex;
			flex-direction: column;
			align-items: center;

			.action_btn_icon_wrap {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 12rpx;

				.u-icon {
					font-size: 44rpx !important;
				}
			}

			.action_btn_text {
				font-size: 24rpx;
				color: #121212;
				margin-top: 4rpx;
				font-weight: 500;
			}
		}

		/* .action_btn_item+.action_btn_item {
			margin-left: 24rpx;
		} */
	}

	.trade_record_card {
		background: #fff;
		border-radius: 48rpx;
		padding: 32rpx 32rpx 12rpx 32rpx;

		.trade_record_title {
			font-size: 28rpx;
			font-weight: bold;
			color: #121212;
			margin-bottom: 20rpx;
		}

		.trade_record_list {
			.trade_record_item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;

				.trade_record_type {
					font-size: 24rpx;
					color: #121212;
				}

				.trade_record_status {
					font-size: 22rpx;
					color: #BDBDBD;
				}
			}

			.trade_record_item:last-child {
				border-bottom: none;
			}
		}
	}

	.asset_info_card {
		background: #fff;
		border-radius: 48rpx;
		padding: 48rpx 32rpx 48rpx 32rpx;
		margin-top: 20rpx;

		.asset_info_header {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.asset_info_title {
				font-size: 28rpx;
				font-weight: bold;
				color: #121212;
			}

			.asset_info_search_filter {
				display: flex;
				align-items: center;

				.asset_info_searchbox {
					display: flex;
					align-items: center;
					background: #f5f5f5;
					border-radius: 24rpx;
					padding: 0 24rpx;
					height: 56rpx;

					.asset_info_searchinput {
						border: none;
						background: transparent;
						font-size: 26rpx;
						color: #121212;
						margin-left: 12rpx;
						width: 180rpx;
					}
				}
			}
		}

		.asset_info_list {
			padding: 20rpx 0rpx;

			.asset_info_item {
				display: flex;
				align-items: center;
				/* padding: 20rpx 0; */
				margin-bottom: 60rpx;

				.asset_info_logo_wrap {
					width: 56rpx;
					height: 56rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;

					.asset_info_logo {
						width: 38rpx;
						height: 38rpx;
					}
				}

				.btc_bg {
					background: #F7931A;
				}

				.eth_bg {
					background: #627EEA;
				}

				.usdt_bg {
					background: #26A17B;
				}

				.bnb_bg {
					background: #F3BA2F;
				}

				.asset_info_main {
					flex: 1;
					display: flex;
					flex-direction: column;

					.asset_info_name {
						font-size: 26rpx;
						color: #121212;
					}

					.asset_info_rate {
						font-size: 22rpx;
						color: #BDBDBD;
					}
				}

				.asset_info_value {
					display: flex;
					flex-direction: column;
					align-items: flex-end;

					.asset_info_num {
						font-size: 26rpx;
						color: #121212;
					}

					.asset_info_usd {
						font-size: 22rpx;
						color: #BDBDBD;
					}
				}
			}

			.asset_info_item:last-child {
				border-bottom: none;
			}
		}

		.asset_info_more {
			display: flex;
			justify-content: center;
			margin: 20rpx 0 0 0;

			.asset_info_more_btn {
				width: 92%;
				background: #fff;
				color: #121212;
				border: 1rpx solid rgba(0, 0, 0, 0.05);
				border-radius: 18rpx;
				font-size: 24rpx;
				text-align: center;
				font-weight: 500;
				height: 88rpx;
				line-height: 88rpx;
			}
		}
	}

	.bottom-popup-content {
		padding: 40rpx 0 32rpx 0;

		.popup-title-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 32rpx 32rpx 32rpx;

			.popup-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #121212;
			}
		}

		.popup-option {
			display: flex;
			align-items: center;
			background: #fff;
			border-radius: 24rpx;
			margin: 0 32rpx 24rpx 32rpx;
			padding: 32rpx 24rpx;
			border: 1rpx solid rgba(0, 0, 0, 0.05);

			.popup-option-icon {
				width: 60rpx;
				height: 60rpx;
				margin-right: 24rpx;
			}

			.popup-option-info {
				flex: 1;
				display: flex;
				flex-direction: column;

				.popup-option-title {
					font-size: 28rpx;
					color: #121212;
					font-weight: 600;
					margin-bottom: 8rpx;
				}

				.popup-option-desc {
					font-size: 20rpx;
					color: rgba(0, 0, 0, 0.50);
				}
			}
		}
	}

	.helpoption {
		width: 80*2rpx;
		transition: transform 0.3s ease, opacity 0.3s ease;
		transform-origin: top;
		/* 设置变换的起点为顶部 */
		z-index: 9999;
		position: absolute;
		top: 80rpx;
		right: -50rpx;
		box-shadow: 14rpx 20rpx 100.3*2rpx 2rpx #00000026;
		max-height: 400rpx;
		overflow-y: auto;
		background: #fff;
		border-radius: 16*2rpx;
		padding: 16*2rpx;
		opacity: 1;
		display: flex;
		align-items: flex-start;
		flex-direction: column;



		&.collapse {
			transform: scaleY(0) translateY(-100%);
			/* 缩小至0，并向上移动 */
			opacity: 0;
		}

		&.expand {
			transform: scaleY(1) translateY(0%);
			/* 恢复到正常大小，并位置恢复 */
			opacity: 1;

		}

		>view {
			width: 100%;
			padding: 15rpx 0;
			text-align: left;

			image {
				width: 40rpx;
				height: 30rpx;
			}

			text {
				font-family: Gilroy-Bold;
				font-weight: 400;
				font-size: 16*2rpx;
				line-height: 19.2*2rpx;
				color: #000;
			}
		}
	}

	.wallet_popup_content {
		padding: 40rpx 0 32rpx 0;

		.wallet_popup_title_row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 32rpx 32rpx 32rpx;

			.wallet_popup_title {
				font-size: 32rpx;
				font-weight: 600;
				color: #121212;
			}
		}

		.wallet_popup_list {
			.wallet_popup_item {
				display: flex;
				align-items: center;
				background: #fff;
				border-radius: 24rpx;
				margin: 0 32rpx 24rpx 32rpx;
				padding: 32rpx 24rpx;
				border: 1rpx solid rgba(0, 0, 0, 0.05);

				.wallet_popup_icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 24rpx;
				}

				.wallet_popup_info {
					flex: 1;
					display: flex;
					flex-direction: column;

					.wallet_popup_item_title {
						font-size: 28rpx;
						color: #121212;
						font-weight: 600;
						margin-bottom: 8rpx;
					}

					.wallet_popup_item_desc {
						font-size: 20rpx;
						color: #BDBDBD;
					}
				}
			}
		}
	}
</style>