<template>
    <div class="about_container">
        <!-- 顶部Logo -->
        <div class="about_logo mt_110">
            <img src="https://pro-oss.pinkwallet.com/image/1377693913115615232.png" alt="PinkWallet.com" />
        </div>

        <!-- 关于我们 -->
        <div class="about_section">
            <div class="title-container-card">
                <span class="title">{{ $t('about_title') }}</span>
            </div>
            <div class="about_desc">
                {{ $t('about_desc1') }}
            </div>
            <div class="about_desc">
                {{ $t('about_desc2') }}
            </div>
        </div>

        <!-- 核心优势 -->
        <div class="core_section">
            <div class="title-container-card">
                <span class="title">{{ $t('about_core_title') }}</span>
            </div>
            <div class="core_cards">
                <div class="core_card">
                    <img class="core_card_img" src="https://pro-oss.pinkwallet.com/image/1377697978679648256.png"
                        :alt="$t('about_core_card1_title')">
                    <div class="view">
                        <div class="core_card_title">{{ $t('about_core_card1_title') }}</div>
                        <div class="core_card_desc">{{ $t('about_core_card1_desc') }}</div>
                    </div>
                </div>
                <div class="core_card">
                    <img class="core_card_img" src="https://pro-oss.pinkwallet.com/image/1379888648680988672.png"
                        :alt="$t('about_core_card2_title')">
                    <div class="view">
                        <div class="core_card_title">{{ $t('about_core_card2_title') }}</div>
                        <div class="core_card_desc">{{ $t('about_core_card2_desc') }}</div>
                    </div>
                </div>
                <div class="core_card">
                    <img class="core_card_img" src="https://pro-oss.pinkwallet.com/image/1377697978679648256.png"
                        alt="">
                    <div class="view">
                        <div class="core_card_title">{{ $t('about_core_card3_title') }}</div>
                        <div class="core_card_desc">{{ $t('about_core_card3_desc') }}</div>
                    </div>
                </div>
                <div class="core_card">
                    <img class="core_card_img" src="https://pro-oss.pinkwallet.com/image/1379888753194655744.png"
                        :alt="$t('about_core_card4_title')">
                    <div class="view">
                        <div class="core_card_title">{{ $t('about_core_card4_title') }}</div>
                        <div class="core_card_desc">{{ $t('about_core_card4_desc') }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 我们的使命 -->
        <div class="mission_section">
            <div class="max_width_1400">
                <div class="mission_title">{{ $t('about_mission_title') }}</div>
                <div class="mission_desc">
                    {{ $t('about_mission_desc1') }}<br>
                    {{ $t('about_mission_desc2') }}<br>
                    {{ $t('about_mission_desc3') }}
                </div>
            </div>

            <div class="mission_img  mb_96 mt_96">
                <img src="https://pro-oss.pinkwallet.com/image/1377696426527121408.png" alt="">
            </div>
        </div>

        <!-- 我们的愿景 -->
        <div class="vision_section">
            <div class="max_width_1400">
                <div class="vision_title">{{ $t('about_vision_title') }}</div>
                <div class="vision_desc">
                    {{ $t('about_vision_desc') }}
                </div>
            </div>
            <div class="vision_img mb_96 mt_96">
                <img src="https://pro-oss.pinkwallet.com/image/1377696490658029568.png" alt="">
            </div>
        </div>
    </div>
</template>

<script setup>
    import { watchEffect } from 'vue'
    import { useI18n } from "vue-i18n";
    const { locale } = useI18n();
    watchEffect(() => {
        localStorage.setItem('lang', locale.value)
    });
</script>

<style lang="scss" scoped>
    @import "../pxto.scss";

    .title-container-card {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        opacity: 1;
        margin-bottom: 79px;
        transition: all 0.8s;

        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }

        .title {
            font-weight: bold;
            position: relative;

            font-family: MiSans-bold;
            font-weight: 700;
            font-size: px(36);
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px;
                height: 2px;
                background-color: rgba(255, 255, 255, 0.2);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }
    }

    .about_container {
        width: 100%;
        margin: 0 auto;
        padding: 40px 0 0 0;
        background: #111;
        font-family: 'MiSans-regular', Arial, sans-serif;
        color: #fff;
    }

    .about_logo {
        text-align: center;
        margin-bottom: 32px;

        img {
            width: 340px;
            max-width: 90vw;
        }
    }

    .about_section {
        text-align: center;
        margin-bottom: 60px;
        max-width: 1000px;
        margin: 0 auto;
        .about_title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 18px;
            position: relative;
            
            &::before,
            &::after {
                content: '';
                display: inline-block;
                width: 60px;
                height: 2px;
                background: rgba(255, 255, 255, 0.2);
                vertical-align: middle;
                margin: 0 16px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }

        .about_desc {
            color: #A7A7A7;
            font-size: 18px;
            margin-bottom: 18px;
        }

        .about_subtitle {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #fff;
        }

        .about_text {
            color: #A7A7A7;
            font-size: 15px;
            line-height: 1.7;
            max-width: 900px;
            margin: 0 auto;
        }
    }

    .core_section {
        margin: 60px auto 0 auto;
        max-width: 1100px;

        .core_title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 32px;
            position: relative;
            
            &::before,
            &::after {
                content: '';
                display: inline-block;
                width: 60px;
                height: 2px;
                background: rgba(255, 255, 255, 0.2);
                vertical-align: middle;
                margin: 0 16px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }

        .core_cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 32px 32px;
            justify-content: center;
            align-items: stretch;
            margin: 40px auto 0 auto;
            max-width: 1200px;

            .core_card {
                display: flex;
                flex-direction: row;
                align-items: center;
                background: #222;
                border-radius: 16px;
                border: none;
                min-height: 220px;
                padding: 32px 32px 32px 32px;
                box-sizing: border-box;
                transition: box-shadow 0.3s, border-color 0.3s;
                cursor: pointer;
                display: flex;
                justify-content: flex-start;
                align-items: center;

                .core_card_img {
                    width: px(176);
                    height: px(176);
                    object-fit: contain;
                    margin-right: px(100);
                }

                &:hover {
                    box-shadow: 0 4px 20px 0 rgba(239, 136, 163, 0.18);
                }

                .view {
                    width: px(400);
                    text-align: center;

                    .core_card_title {
                        font-size: px(24);
                        color: #fff;
                        text-align: left;
                        margin-bottom: px(14);
                        font-family: 'MiSans';
                    }

                    .core_card_desc {
                        font-size: 16px;
                        color: #A7A7A7;
                        line-height: 1.5;
                        text-align: left;
                        word-break: break-all;
                    }
                }

            }
        }
    }

    .mission_section {
        margin: 80px auto 0 auto;

        .mission_title {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 18px;
            color: #fff;
            font-family: 'MiSans';
        }

        .mission_desc {
            color: #EF88A3;
            font-size: 16px;
            margin-bottom: 32px;
            line-height: 1.7;
        }

        .mission_img {
            width: 100%;
            text-align: center;
            height: 360px;
            overflow: hidden;

            img {
                width: 100%;
                margin: 0 auto;
                display: block;
            }
        }
    }

    .vision_section {
        margin: 80px auto 0 auto;

        .vision_title {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 18px;
            color: #fff;
            font-family: 'MiSans';
        }

        .vision_desc {
            color: #EF88A3;
            font-size: 16px;
            margin-bottom: 32px;
            line-height: 1.7;
        }

        .vision_img {
            width: 100%;
            text-align: center;
            height: 360px;
            overflow: hidden;

            img {
                width: 100%;
                margin: 0 auto;
                display: block;
            }
        }
    }
    .max_width_1400{
        width: 1200px;
        text-align: left;
        margin: 0 auto;
    }
</style>