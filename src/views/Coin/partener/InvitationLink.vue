<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">

            <!-- action -->
            <template #action="scope">
                <!-- <el-button type="text" @click="openPopup(scope.row)">详情 <span
                        v-if="scope.row.approvalStatus == 'PENDING'">/审批</span> </el-button> -->
                <el-button size="mini" type="primary" @click="onDetail(scope.row)">复制链接</el-button>
            </template>

            <!-- influenceProof -->
            <!-- <template #influenceProof="scope">
                <el-button type="text" @click="openPopup(scope.row)">查看</el-button>
            </template> -->
            <!-- approvalStatus -->
            <template #userType="scope">
                {{ formatType(scope.row.userType) }}
            </template>

            <template #contract="scope">
                {{ (scope.row.contractRebateRate.toFixed(2) + '/' + scope.row.inviteeContractRebateRate.toFixed(2)) }}
            </template>

            <template #mg="scope">
                {{ (scope.row.usStockRebateRate.toFixed(2) + '/' + scope.row.inviteeUsStockRebateRate.toFixed(2)) }}
            </template>

            <template #hk="scope">
                {{ (scope.row.hkStockRebateRate.toFixed(2) + '/' + scope.row.inviteeHkStockRebateRate.toFixed(2)) }}
            </template>

            <template #api="scope">
                {{ (scope.row.apiRebateRate.toFixed(2) + '/' + scope.row.inviteeApiRebateRate.toFixed(2)) }}
            </template>

            <!-- rebateType -->
            <template #rebateType="scope">
                {{ formatsubStatus(scope.row.rebateType) }}
            </template>

            <!-- subjectType -->
            <template #subjectType="scope">
                {{ formatStatus(scope.row.subjectType) }}
            </template>

            <template #rebateTime="scope">
                {{ formatTimestamp(scope.row.rebateTime) }}
            </template>
            <template #tradeTime="scope">
                {{ formatTimestamp(scope.row.tradeTime) }}
            </template>
        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[10, 20, 30, 50]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>

        <el-dialog title="审批合伙人/详情" :visible.sync="dialogVisible">

            <el-form label-position="top" label-width="100px" class="form-container">
                <div class="section-title">基本信息</div>

                <div class="form-row">
                    <el-form-item label="申请人">
                        <el-input size="mini" disabled value="<EMAIL>" />
                    </el-form-item>
                    <el-form-item label="UID">
                        <el-input size="mini" disabled value="121111111" />
                    </el-form-item>
                </div>

                <div class="form-row">
                    <el-form-item label="联系方式">
                        <el-input size="mini" disabled value="张三" />
                        <el-input size="mini" disabled value="@1234" style="margin-top: 8px;" />
                    </el-form-item>
                    <el-form-item label="邮箱">
                        <el-input size="mini" disabled value="AZ***@gmail.com" />
                    </el-form-item>
                </div>

                <div class="form-row">
                    <el-form-item label="您的居住地">
                        <el-input size="mini" disabled value="泰国" />
                    </el-form-item>
                    <el-form-item label="BD 信息（UID）">
                        <el-input size="mini" disabled value="12341" />
                    </el-form-item>
                </div>

                <div class="form-row">
                    <el-form-item label="影响力证明" class="full-width">
                        <div class="influence-box">
                            <div class="influence-item">
                                <div v-if="influences.x" disabled>X</div>
                                <el-input size="mini" disabled value="https://1111" />
                                <el-input size="mini" disabled value="500" />
                            </div>
                            <!-- <div class="influence-item">
                                <el-checkbox v-model="influences.youtube" disabled>✔ Youtube</el-checkbox>
                                <el-input size="mini" disabled value="https://1111" />
                                <el-input size="mini" disabled value="500" />
                            </div> -->
                        </div>
                    </el-form-item>
                </div>
            </el-form>

            <div class="section-title">审批处理</div>
            <common-form :is-edit="true" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
                <!-- <template #timing="scope"></template> -->
            </common-form>
        </el-dialog>


    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
export default {
    name: "EarningsSummaryQuery",
    components: {
        CommonQuery,
        CommonTable,
        CommonForm,
    },
    data() {
        return {
            formSchema: [
                {
                    type: 'input',
                    label: '合约返佣比例%：',
                    placeholder: '请输入合约返佣比例%',
                    field: 'contractLevelRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入合约返佣比例%',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '美股返佣比例%：',
                    placeholder: '请输入美股返佣比例%',
                    field: 'usStockLevelRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入美股返佣比例%',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '港股返佣比例：',
                    placeholder: '请输入港股返佣比例',
                    field: 'hkStockLevelRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入港股返佣比例',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: 'API返佣比例%：',
                    placeholder: '请输入API返佣比例%',
                    field: 'apiStockLevelRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入API返佣比例%',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'select',
                    label: '审批结果：',
                    placeholder: '请选择审批结果',
                    field: 'approvalStatus',
                    options: [
                        {
                            value: 'REJECTED',
                            label: '拒绝'
                        },
                        {
                            value: 'APPROVED',
                            label: '通过'
                        }
                    ],
                    rules: [
                        {
                            required: true,
                            message: '请输入审批结果',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'action'
                }
            ],
            influences: {
                x: true,
                youtube: true
            },
            dialogVisible: false,
            query1: {
                // tradeTimeBegin: "",
                // tradeTimeEnd: ""
            },
            Substatus: [
                { value: 1, label: "初始化" },
                { value: 2, label: "进行中" },
                { value: 3, label: "完成" },
                { value: 4, label: "失败" },
                { value: 5, label: "审批" }
            ],
            status: [
                { value: 1, label: "初始化" },
                { value: 2, label: "进行中" },
                { value: 3, label: "完成" },
                { value: 4, label: "失败" }
            ],
            types: [
                {
                    value: 'COMMON',
                    label: '普通'
                },
                {
                    value: 'AGENT',
                    label: '代理商'
                },
                {
                    value: 'BD',
                    label: '商务'
                }
            ],

            formData: {},
            loading: false,
            tableData: [],
            tableSchema: [
                { label: '用户UID', field: 'uid' },
                { label: '被邀请人 UID', field: 'inviteeUid' },
                { label: '用户身份', slot: 'userType' }, // 枚举: COMMON, AGENT, BD
                { label: '邀请码', field: 'inviteCode' },

                { label: '合约（合伙人返佣比例/直接下属返佣比例）', slot: 'contract' },
                { label: '美股（合伙人返佣比例/直接下属返佣比例）', slot: 'mg' },
                { label: '港股（合伙人返佣比例/直接下属返佣比例）', slot: 'hk' },
                { label: 'API（合伙人返佣比例/直接下属返佣比例）', slot: 'api' },


                // { label: '邀请人的合约返佣比例', field: 'contractRebateRate' },
                // { label: '直接下级的合约返佣比例', field: 'inviteeContractRebateRate' },

                // { label: '邀请人的美股返佣比例', field: 'usStockRebateRate' },
                // { label: '直接下级的美股返佣比例', field: 'inviteeUsStockRebateRate' },

                // { label: '邀请人的港股返佣比例', field: 'hkStockRebateRate' },
                // { label: '直接下级的港股返佣比例', field: 'inviteeHkStockRebateRate' },

                // { label: '邀请人的 API 返佣比例', field: 'apiRebateRate' },
                // { label: '直接下级的 API 返佣比例', field: 'inviteeApiRebateRate' },

                { label: '邀请人数', field: 'invitePersonCount' },

                {
                    label: '操作',
                    slot: 'action',
                    width: 180
                }
            ],
            query: {},


            page: {
                pageNum: 1,
                pageSize: 10,
                totalCount: 0
            },
            nowrow: {},
            coinPair: [],
            totalAsset: {},
            shen: [
                {
                    value: 'REJECTED',
                    label: '拒绝'
                },
                {
                    value: 'APPROVED',
                    label: '通过'
                },
                {
                    value: 'PENDING',
                    label: '审核中'
                }
            ]

        }
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                {
                    type: 'input',
                    label: '用户id：',
                    placeholder: '',
                    field: 'uid',
                },
                {
                    type: 'input',
                    label: '被邀请人 uid：',
                    placeholder: '',
                    field: 'inviteeUid',
                },
                {
                    type: 'input',
                    label: '邀请码：',
                    placeholder: '',
                    field: 'inviteCode',
                },
                {
                    type: 'select',
                    label: '用户身份：',
                    placeholder: '',
                    field: 'userType',
                    options: [
                        {
                            value: "COMMON",
                            label: "普通用户"
                        },
                        {
                            value: "AGENT",
                            label: "代理商"
                        },
                        {
                            value: "BD",
                            label: "商务"
                        }
                    ]
                },
            ]
        }

    },
    mounted() {
        this.GetqueryCoinTransactionPage();
        // let net = localStorage.getItem('Networks')
        let coin = localStorage.getItem('coinPair')
        // this.networkList = JSON.parse(net)
        this.coinPair = JSON.parse(coin)
        console.log(new Date().getTimezoneOffset() / 60);
    },
    methods: {
        async submit() {
            let res = await this.$api.agentApplyApproval({
                uid: this.nowrow.uid,
                ...this.formData
            })
            if (res.code == 200) {
                this.$message.success(res.msg)
                this.dialogVisible = false
                this.GetqueryCoinTransactionPage();

            }
        },
        openPopup(row) {
            this.nowrow = row;
            this.dialogVisible = true;
        },
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000); // 转成毫秒时间戳
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        formatUtcTime(utcDateStr) {
            // 截取需要的部分，假设输入总是有效的ISO 8601格式
            const date = utcDateStr.substr(0, 10); // 获取日期部分 '2025-03-07'
            const time = utcDateStr.substr(11, 8); // 获取时间部分 '07:29:04'

            // 合并日期和时间部分
            return `${date} ${time}`;
        },
        formatsubStatus(value) {
            let arr = [
                // SELF, DIRECT, INDIRECT
                {
                    value: 'SELF',
                    text: '自返佣'
                },
                {
                    value: 'DIRECT',
                    text: '直接返佣'
                },
                {
                    value: 'INDIRECT',
                    text: '间接返佣'
                }

            ]
            const item = arr.find(i => i.value == value)
            return item ? item.label : value
        },
        formatStatus(value) {
            let arr = [
                {
                    value: 'API',
                    text: 'API'
                },
                {
                    value: 'HUMAN',
                    text: '人工'
                },

            ]
            const item = arr.find(i => i.value === value)
            return item ? item.label : value
        },
        formatType(value) {
            let arr = [
                {
                    value: "COMMON",
                    label: "普通用户"
                },
                {
                    value: "AGENT",
                    label: "代理商"
                },
                {
                    value: "BD",
                    label: "商务"
                }
            ]
            const item = arr.find(i => i.value === value)
            return item ? item.label : value
        },


        onDetail(item) {
            console.log(process.env.VUE_APP_URL + '/#/pages/project/login/register?code=' + item.inviteCode);
            this.copyText(process.env.VUE_APP_URL + '/#/pages/project/login/register?code=' + item.inviteCode)
            // this.queryDetail(item)
        },
        copyText(text) {
            const textarea = document.createElement('textarea')
            textarea.value = text
            textarea.setAttribute('readonly', '')
            textarea.style.position = 'absolute'
            textarea.style.left = '-9999px'
            document.body.appendChild(textarea)
            textarea.select()

            try {
                const success = document.execCommand('copy')
                if (success) {
                    this.$message.success('复制成功')
                } else {
                    this.$message.error('复制失败')
                }
            } catch (err) {
                this.$message.error('复制异常')
            }

            document.body.removeChild(textarea)
        },
        async queryDetail(item) {
            let res = await this.$api.queryCoinTransactionDetail({
                idStr: item.id
            })
        },
        onQueryChange(data) {
            this.query1 = data
            this.GetqueryCoinTransactionPage(true)
        },
        onRefresh(data) {
            this.query1 = data
            this.GetqueryCoinTransactionPage()
        },
        async GetqueryCoinTransactionPage() {
            if (this.query1.tradeTimeBegin && this.query1.tradeTimeEnd) {
                this.query1.tradeTimeBegin = Math.floor(new Date(this.query1.tradeTimeBegin).getTime() / 1000);
                this.query1.tradeTimeEnd = Math.floor(new Date(this.query1.tradeTimeEnd).getTime() / 1000);
            }
            let res = await this.$api.queryInviteLink({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                ...this.query1
            });
            if (res.code == 200) {
                this.tableData = res.result.data
                this.page.totalCount = res.result.total
            }
        },
        currentChange(value) {
            this.page.pageNum = value
            this.GetqueryCoinTransactionPage()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.GetqueryCoinTransactionPage()
        },
    }
}
</script>

<style lang="scss" scoped>
// ::v-deep .el-form--label-top .el-form-item__label {
//     padding: 0 !important;
// }

// // 300px
// ::v-deep .el-input__inner {
//     width: 300px !important;
// }

// .form-container {
//     max-width: 650px;
//     // margin: 0 auto;
// }

// .section-title {
//     font-weight: bold;
//     font-size: 16px;
//     // margin: 20px 0 12px;
// }

// .form-row {
//     display: flex;
//     justify-content: space-between;
//     gap: 20px;
//     margin-bottom: 16px;
// }

// .el-form-item {
//     flex: 1;
// }

// .influence-box {
//     display: flex;
//     flex-direction: column;
//     gap: 12px;
// }

// .influence-item {
//     display: flex;
//     align-items: center;
//     gap: 12px;
// }

// .full-width {
//     width: 100%;
// }</style>