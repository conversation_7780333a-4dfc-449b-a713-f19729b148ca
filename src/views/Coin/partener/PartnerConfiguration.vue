<template>
    <d2-container class="page" ref="returnTop">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>
        <!-- <el-button style="margin-bottom: 20px;" type="primary" size="mini" @click="openPopup()">添加代理商配置</el-button> -->

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
            <!-- assetStatus -->
            <template #assetStatus="scope">
                {{ scope.row.assetStatus == 1 ? '正常' : scope.row.assetStatus == 2 ? '冻结' : '' }}
            </template>
            <!-- action -->
            <template #action="scope">
                <el-button type="primary" size="mini" @click="EditPopup(scope.row)">操作</el-button>
            </template>
            <!-- ctime  -->
            <template #ctime="scope">
                {{ formatDate(scope.row.ctime) }}
            </template>
            <!-- mtime  -->
            <template #mtime="scope">
                {{ formatDate(scope.row.mtime) }}
            </template>

            <!-- <contractRebateRate> -->
            <template #contractRebateRate="scope">
                {{ scope.row.contractRebateRate + '%' }}
            </template>

            <!-- <usStockRebateRate> -->
            <template #usStockRebateRate="scope">
                {{ scope.row.usStockRebateRate + '%' }}
            </template>

            <!-- <hkStockRebateRate> -->
            <template #hkStockRebateRate="scope">
                {{ scope.row.hkStockRebateRate + '%' }}
            </template>

            <!-- <apiRebateRate> -->
            <template #apiRebateRate="scope">
                {{ scope.row.apiRebateRate + '%' }}
            </template>


        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.total"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[10, 20, 30, 50]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>

        <!-- 修改 -->
        <el-dialog title="编辑代理商" :visible.sync="dialogVisibleAddorSub">
            <common-form :is-edit="true" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
                <!-- <template #timing="scope"></template> -->
            </common-form>
        </el-dialog>

        <!-- 新增 -->
        <el-dialog title="新增代理商" :visible.sync="dialogVisibleAdd">
            <common-form :is-edit="true" :submit="submitAdd" :data="formDataAdd" :schema="formSchemaAdd"
                label-width="300px">
                <!-- <template #timing="scope"></template> -->
            </common-form>
        </el-dialog>
    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
export default {
    components: {
        CommonQuery,
        CommonTable,
        CommonForm,
    },
    data() {
        return {
            dialogVisibleAdd: false,
            formDataAdd: {},
            formData: {
                email: "",
                uidStr: "",
                symbol: "",
                assetStatus: "",
                changeType: "",
                amount: "",
                remark: "",
                name: "",
                phone:"",
                note:"",
                contractRebateRate:"",
                hkStockRebateRate:"",
                usStockRebateRate:"",
                apiRebateRate:"",
            },
            formSchemaAdd: [
                {
                    type: 'switch',
                    label: '是否法币：',
                    // placeholder: '请输入退市涨幅',
                    field: 'fiat',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请选择是否法币',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
            ],
            formSchema: [
                {
                    type: 'input',
                    label: '用户UID：',
                    placeholder: '请输入uid',
                    field: 'uid',
                    disabled: true,
                },
                {
                    type: 'input',
                    label: '合约返佣比例%：',
                    placeholder: '请输入 合约返佣比例%',
                    field: 'contractRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入 合约返佣比例%',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '美股返佣比例：',
                    placeholder: '请输入美股返佣比例',
                    field: 'usStockRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入美股返佣比例',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '港股返佣比例：',
                    placeholder: '请输入港股返佣比例',
                    field: 'hkStockRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入港股返佣比例',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: 'API 返佣比例：',
                    placeholder: '请输入API 返佣比例',
                    field: 'apiRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入API 返佣比例',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '登录名：',
                    placeholder: '',
                    field: 'name',
                    disabled: true,
                },
                {
                    type: 'input',
                    label: '手机号：',
                    placeholder: '请输入手机号',
                    field: 'phone',
                },
                {
                    type: 'input',
                    label: '邮箱：',
                    placeholder: '请输入邮箱',
                    field: 'email',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入备注',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '备注：',
                    placeholder: '请输入备注',
                    field: 'note',
                },

                {
                    type: 'action',
                }
                // {
                //     type: 'input',
                //     label: '资产来源值：',
                //     placeholder: '请输入资产来源值',
                //     field: 'fromValue',
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入资产来源值',
                //             trigger: 'blur',
                //         },
                //     ],
                // },

            ],
            dialogVisibleAddorSub: false,
            nowrow: {},
            loading: false,
            page: {
                pageSize: 10,
                pageNum: 1,
                total: ""
            },
            query: {},

            tableData: [],
            tableSchema: [
                { label: '下属UID', field: 'uid' },
                { label: "直接归属上级", field: "inviterUid" },
                // countryCode
                { label: '国籍', field: 'address' },
                { label: '合约（直接下属返佣比例）', slot: 'contractRebateRate' },
                { label: '美股（直接下属返佣比例）', slot: 'usStockRebateRate' },
                { label: '港股（直接下属返佣比例）', slot: 'hkStockRebateRate' },
                { label: 'API（直接下属返佣比例）', slot: 'apiRebateRate' },
                { label: '备注', field: 'note' },

                { label: '登录名', field: 'loginName' },
                { label: '手机号', field: 'phone' },

                // { label: '手机号区号', field: 'countryCode' },
                { label: '邮箱', field: 'email' },

                {
                    label: '操作',
                    slot: 'action',
                    width: 180
                }
            ],
            coinPair: []
        }
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                {
                    type: 'input',
                    label: '用户uid：',
                    placeholder: '',
                    field: 'uid',
                },
                {
                    type: 'input',
                    label: '邀请码：',
                    placeholder: '',
                    field: 'inviteCode',
                },
                {
                    type: 'select',
                    label: '用户身份：',
                    placeholder: '',
                    field: 'userType',
                    options: [
                        {
                            value: 'COMMON',
                            label: '普通'
                        },
                        {
                            value: 'AGENT',
                            label: '代理商'
                        },
                        {
                            value: 'BD',
                            label: '商务'
                        }
                    ]
                },

            ]
        },
    },
    mounted() {
        this.getPageUser()
        let coin = localStorage.getItem('coinPair')
        this.coinPair = JSON.parse(coin)
    },
    methods: {
        submitAdd() { },
        openPopup() {
            this.formData = {
                uidStr: "",
                symbol: "",
                assetStatus: "",
                changeType: "",
                amount: "",
                remark: "",
            }
            // this.dialogVisibleAddorSub = true
        },
        async submit() {
            let res = await this.$api.updateAgentConfig({
                uid: this.formData.uid,
                contractRebateRate: this.formData.contractRebateRate,
                usStockRebateRate: this.formData.usStockRebateRate,
                hkStockRebateRate: this.formData.hkStockRebateRate,
                apiRebateRate: this.formData.apiRebateRate,
                note: this.formData.note,
                phone: this.formData.phone,
                email: this.formData.email,
            })
            if (res.code == 200) {
                this.dialogVisibleAddorSub = false
                this.$message({
                    message: '操作成功',
                    type: 'success'
                })
            }
        },
        async EditPopup(item) {
            await this.generateAgentAccounts(item)
            this.formData.apiRebateRate = item.apiRebateRate
            this.formData.contractRebateRate = item.contractRebateRate
            this.formData.email = item.email
            this.formData.hkStockRebateRate = item.hkStockRebateRate
            this.formData.note = item.note
            this.formData.phone = item.phone
            this.formData.uid = item.uid
            this.formData.usStockRebateRate = item.usStockRebateRate
            this.nowrow = item
            this.dialogVisibleAddorSub = true
        },
        async generateAgentAccounts(e) {
            let res = await this.$api.generateAgentAccount({
                uid: e.uid,
                language: 'zh_CN'
            })
            if (res.code == 200) {
                this.formData.name = res.result.loginName
            }
        },
        async getPageUser() {
            // this.loading = true
            let res = await this.$api.queryAgentConfig({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                ...this.query
            })
            if (res.code == 200) {
                this.loading = false
                this.tableData = res.result.data
                this.page.total = res.result.total
            } else {
                this.loading = false
            }
        },
        onQueryChange(data) {
            this.query = data
            this.getPageUser(true)
        },
        onRefresh(data) {
            this.query = data
            this.getPageUser()
        },
        formatDate(isoString) {
            if (!isoString) return
            const [date, time] = isoString.split('T'); // 分割日期和时间
            const formattedTime = time.split('.')[0];  // 去掉毫秒部分
            return `${date} ${formattedTime}`;
        },
        currentChange(value) {
            this.page.pageNum = value
            this.getPageUser()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.getPageUser()
        },
    }
}
</script>

<style lang="scss" scoped></style>