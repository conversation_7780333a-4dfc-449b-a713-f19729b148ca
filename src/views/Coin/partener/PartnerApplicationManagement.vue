<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">

            <!-- action -->
            <template #action="scope">
                <!-- v-if="scope.row.approvalStatus == 'PENDING'"、 -->
                <el-button type="text" @click="openPopup(scope.row)">详情</el-button>
                <el-button type="text" @click="openPopupshen(scope.row)" v-if="scope.row.approvalStatus == 'PENDING'">审批
                </el-button>

                <!-- <el-button size="mini" type="primary" @click="onDetail(scope.row)">交易明细</el-button> -->
            </template>

            <!-- approvalStatus -->
            <template #approvalStatus="scope">
                {{ formatType(scope.row.approvalStatus) }}
            </template>

            <!-- rebateType -->
            <template #rebateType="scope">
                {{ formatsubStatus(scope.row.rebateType) }}
            </template>

            <!-- subjectType -->
            <template #subjectType="scope">
                {{ formatStatus(scope.row.subjectType) }}
            </template>

            <template #submitApplicationTime="scope">
                {{ formatTimestamp(scope.row.submitApplicationTime) }}
            </template>
            <template #registerTime="scope">
                {{ formatTimestamp(scope.row.registerTime) }}
            </template>
            <!-- approvalTime -->
            <template #approvalTime="scope">
                {{ formatTimestamp(scope.row.approvalTime) }}
            </template>
        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[10, 20, 30, 50]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>
        <el-dialog title="详情" :visible.sync="dialogVisible">

            <el-form label-position="top" label-width="100px" class="form-container">
                <div class="section-title">基本信息</div>

                <div class="form-row">
                    <el-form-item label="申请人">
                        <el-input size="mini" disabled :value="nowrow.email" />
                    </el-form-item>
                    <el-form-item label="UID">
                        <el-input size="mini" disabled :value="nowrow.uid" />
                    </el-form-item>
                </div>

                <div class="form-row">
                    <el-form-item label="联系方式">
                        <el-input size="mini" disabled :value="nowrow.name" />
                        <el-input size="mini" disabled :value="nowrow.phone" style="margin-top: 8px;" />
                    </el-form-item>
                    <el-form-item label="邮箱">
                        <el-input size="mini" disabled :value="nowrow.email" />
                    </el-form-item>
                </div>

                <div class="form-row">
                    <el-form-item label="您的居住地">
                        <el-input size="mini" disabled :value="nowrow.address" />
                    </el-form-item>
                    <el-form-item label="BD 信息（UID）">
                        <el-input size="mini" disabled :value="nowrow.bdUid" />
                    </el-form-item>
                </div>

                <div class="form-row">
                    <!-- {{ influenceProof }} -->
                    <el-form-item label="影响力证明" class="full-width">
                        <div class="influence-box" v-if="influenceProof.Xlink">
                            <div class="influence-item">
                                <div disabled class="bold">X</div>
                                <div class="input-group">
                                    <span class="input-label">主页链接</span>
                                    <el-input size="mini" disabled :value="influenceProof.Xlink" />
                                </div>

                                <div class="input-group">
                                    <span class="input-label">粉丝数量</span>
                                    <el-input size="mini" disabled :value="influenceProof.Xfans" />
                                </div>
                            </div>
                        </div>

                        <div class="influence-box" v-if="influenceProof.YoutubeLink">
                            <div class="influence-item">
                                <div disabled class="bold">Youtube</div>
                                <div class="input-group">
                                    <span class="input-label">主页链接</span>
                                    <el-input size="mini" disabled :value="influenceProof.YoutubeLink" />
                                </div>

                                <div class="input-group">
                                    <span class="input-label">粉丝数量</span>
                                    <el-input size="mini" disabled :value="influenceProof.YoutubeFans" />
                                </div>
                            </div>
                        </div>

                        <div class="influence-box" v-if="influenceProof.FacebookLink">
                            <div class="influence-item">
                                <div disabled class="bold">Facebook</div>
                                <div class="input-group">
                                    <span class="input-label">主页链接</span>
                                    <el-input size="mini" disabled :value="influenceProof.FacebookLink" />
                                </div>

                                <div class="input-group">
                                    <span class="input-label">粉丝数量</span>
                                    <el-input size="mini" disabled :value="influenceProof.FacebookFans" />
                                </div>
                            </div>
                        </div>

                        <div class="influence-box" v-if="influenceProof.TikTokLink">
                            <div class="influence-item">
                                <div disabled class="bold">TikTok</div>
                                <div class="input-group">
                                    <span class="input-label">主页链接</span>
                                    <el-input size="mini" disabled :value="influenceProof.TikTokLink" />
                                </div>

                                <div class="input-group">
                                    <span class="input-label">粉丝数量</span>
                                    <el-input size="mini" disabled :value="influenceProof.TikTokFans" />
                                </div>
                            </div>
                        </div>


                        <div class="influence-box" v-if="influenceProof.TelegramLink">
                            <div class="influence-item">
                                <div disabled class="bold">Telegram</div>
                                <div class="input-group">
                                    <span class="input-label">社区/频道链接 </span>
                                    <el-input size="mini" disabled :value="influenceProof.TelegramLink" />
                                </div>


                                <div class="input-group">
                                    <span class="input-label">个人telegram链接</span>
                                    <el-input size="mini" disabled :value="influenceProof.TelegramPersonalLink" />
                                </div>

                                <div class="input-group">
                                    <span class="input-label">社群人数/频道订阅人数</span>
                                    <el-input size="mini" disabled :value="influenceProof.TelegramChannelNum" />
                                </div>

                            </div>
                        </div>

                        <div class="influence-box" v-if="influenceProof.OthersName">
                            <div class="influence-item">
                                <div disabled class="bold">其他平台</div>
                                <div class="input-group">
                                    <span class="input-label">其他平台名称</span>
                                    <el-input size="mini" disabled :value="influenceProof.OthersName" />
                                </div>


                                <div class="input-group">
                                    <span class="input-label">其他平台链接</span>
                                    <el-input size="mini" disabled :value="influenceProof.OthersLink" />
                                </div>

                                <div class="input-group">
                                    <span class="input-label">粉丝数量/群成员人数</span>
                                    <el-input size="mini" disabled :value="influenceProof.OthersFans" />
                                </div>

                            </div>
                        </div>
                    </el-form-item>
                </div>
            </el-form>

            <!-- <div class="section-title">审批处理</div> -->

        </el-dialog>

        <el-dialog title="审批处理" :visible.sync="dialogVisibleshen">
            <common-form :is-edit="true" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
                <!-- <template #timing="scope"></template> -->
            </common-form>
        </el-dialog>


    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
export default {
    name: "EarningsSummaryQuery",
    components: {
        CommonQuery,
        CommonTable,
        CommonForm,
    },
    data() {
        return {
            influenceProof: {},
            dialogVisibleshen: false,
            formSchema: [
                {
                    type: 'input',
                    label: '合约返佣比例%：',
                    placeholder: '请输入合约返佣比例%',
                    field: 'contractLevelRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入合约返佣比例%',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '美股返佣比例%：',
                    placeholder: '请输入美股返佣比例%',
                    field: 'usStockLevelRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入美股返佣比例%',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '港股返佣比例：',
                    placeholder: '请输入港股返佣比例',
                    field: 'hkStockLevelRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入港股返佣比例',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: 'API返佣比例%：',
                    placeholder: '请输入API返佣比例%',
                    field: 'apiStockLevelRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入API返佣比例%',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'select',
                    label: '审批结果：',
                    placeholder: '请选择审批结果',
                    field: 'approvalStatus',
                    options: [
                        {
                            value: 'REJECTED',
                            label: '拒绝'
                        },
                        {
                            value: 'APPROVED',
                            label: '通过'
                        }
                    ],
                    rules: [
                        {
                            required: true,
                            message: '请输入审批结果',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'action'
                }
            ],
            influences: {
                x: true,
                youtube: true
            },
            dialogVisible: false,
            query1: {
                // tradeTimeBegin: "",
                // tradeTimeEnd: ""
            },
            Substatus: [
                { value: 1, label: "初始化" },
                { value: 2, label: "进行中" },
                { value: 3, label: "完成" },
                { value: 4, label: "失败" },
                { value: 5, label: "审批" }
            ],
            status: [
                { value: 1, label: "初始化" },
                { value: 2, label: "进行中" },
                { value: 3, label: "完成" },
                { value: 4, label: "失败" }
            ],
            types: [
                {
                    value: 'COMMON',
                    label: '普通'
                },
                {
                    value: 'AGENT',
                    label: '代理商'
                },
                {
                    value: 'BD',
                    label: '商务'
                }
            ],

            formData: {},
            loading: false,
            tableData: [],
            tableSchema: [
                { label: '用户 ID', field: 'uid' },
                { label: '手机号', field: 'phone' },
                { label: '邮箱', field: 'email' },
                { label: '商务', field: 'bdUid' },
                // { label: '名字', field: 'name' },
                // { label: '手机号区号', field: 'countryCode' },
                                            //    contractRebateRate
                { label: '合约返佣比例', field: 'contractRebateRate' },
                { label: '美股返佣比例', field: 'usStockRebateRate' },
                { label: '港股返佣比例', field: 'hkStockRebateRate' },
                { label: 'API 返佣比例', field: 'apiRebateRate' },
                { label: '审批结果', slot: 'approvalStatus' }, // 枚举: REJECTED, APPROVED, PENDING
                { label: '审批时间', slot: 'approvalTime' },
                { label: '申请时间', slot: 'submitApplicationTime' },
                { label: '注册时间', slot: 'registerTime' },

                // { label: '居住地', field: 'address' },
                // { label: '影响力证明', slot: 'influenceProof' },
                {
                    label: '操作',
                    slot: 'action',
                    width: 180
                }
            ],
            query: {},


            page: {
                pageNum: 1,
                pageSize: 10,
                totalCount: 0
            },
            nowrow: {},
            coinPair: [],
            totalAsset: {},
            shen: [
                {
                    value: 'REJECTED',
                    label: '拒绝'
                },
                {
                    value: 'APPROVED',
                    label: '通过'
                },
                {
                    value: 'PENDING',
                    label: '审核中'
                }
            ]

        }
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                {
                    type: 'input',
                    label: '用户id：',
                    placeholder: '',
                    field: 'uid',
                },
                {
                    type: 'input',
                    label: '手机号：',
                    placeholder: '',
                    field: 'phone',
                },
                {
                    type: 'input',
                    label: '邮箱：',
                    placeholder: '',
                    field: 'email',
                },
                {
                    type: 'select',
                    label: '审批状态：',
                    placeholder: '',
                    field: 'approvalStatus',
                    options: this.shen
                },
            ]
        }

    },
    mounted() {
        this.GetqueryCoinTransactionPage();
        // let net = localStorage.getItem('Networks')
        let coin = localStorage.getItem('coinPair')
        // this.networkList = JSON.parse(net)
        this.coinPair = JSON.parse(coin)
        console.log(new Date().getTimezoneOffset() / 60);
    },
    methods: {
        async submit() {
            let res = await this.$api.agentApplyApproval({
                id: this.formData.id,
                contractLevelRebateRate: this.formData.contractLevelRebateRate,
                usStockLevelRebateRate: this.formData.usStockLevelRebateRate,
                hkStockLevelRebateRate: this.formData.hkStockLevelRebateRate,
                apiStockLevelRebateRate: this.formData.apiStockLevelRebateRate,
                approvalStatus: this.formData.approvalStatus,
                // ...this.formData
            })
            if (res.code == 200) {
                this.$message.success(res.msg)
                this.dialogVisibleshen = false
                this.GetqueryCoinTransactionPage();
            } else {
                this.GetqueryCoinTransactionPage();
            }
        },
        openPopup(row) {
            try {
                // 检查是否需要解析
                if (typeof row.influenceProof === 'string') {
                    // 去除首尾空格后检查是否为 JSON 对象格式
                    const trimmed = row.influenceProof.trim();
                    if ((trimmed.startsWith('{') && trimmed.endsWith('}')) ||
                        (trimmed.startsWith('[') && trimmed.endsWith(']'))) {
                        // 尝试解析
                        this.influenceProof = JSON.parse(row.influenceProof);
                    } else {
                        // 不是 JSON 格式，直接赋值原始字符串
                        this.influenceProof = row.influenceProof;
                    }
                } else {
                    // 已经是对象或其他类型，直接赋值
                    this.influenceProof = row.influenceProof;
                }

                console.log(this.influenceProof);
                this.nowrow = row;
                this.dialogVisible = true;
            } catch (error) {
                console.error('解析 influenceProof 失败:', error);
                // 解析失败时的默认处理
                this.influenceProof = row.influenceProof;
                this.nowrow = row;
                this.dialogVisible = true;
            }
        },
        openPopupshen(row) {
            this.formData = row;
            this.dialogVisibleshen = true;
        },
        formatTimestamp(seconds) {
            if (!seconds) {
                return '--'
            }
            const date = new Date(seconds * 1000); // 转成毫秒时间戳
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        formatUtcTime(utcDateStr) {
            // 截取需要的部分，假设输入总是有效的ISO 8601格式
            const date = utcDateStr.substr(0, 10); // 获取日期部分 '2025-03-07'
            const time = utcDateStr.substr(11, 8); // 获取时间部分 '07:29:04'

            // 合并日期和时间部分
            return `${date} ${time}`;
        },
        formatsubStatus(value) {
            let arr = [
                // SELF, DIRECT, INDIRECT
                {
                    value: 'SELF',
                    text: '自返佣'
                },
                {
                    value: 'DIRECT',
                    text: '直接返佣'
                },
                {
                    value: 'INDIRECT',
                    text: '间接返佣'
                }

            ]
            const item = arr.find(i => i.value == value)
            return item ? item.label : value
        },
        formatStatus(value) {
            let arr = [
                {
                    value: 'API',
                    text: 'API'
                },
                {
                    value: 'HUMAN',
                    text: '人工'
                },

            ]
            const item = arr.find(i => i.value === value)
            return item ? item.label : value
        },
        formatType(value) {
            const item = this.shen.find(i => i.value === value)
            return item ? item.label : value
        },


        onDetail(item) {
            this.queryDetail(item)
        },
        async queryDetail(item) {
            let res = await this.$api.queryCoinTransactionDetail({
                idStr: item.id
            })
        },
        onQueryChange(data) {
            this.query1 = data
            this.GetqueryCoinTransactionPage(true)
        },
        onRefresh(data) {
            this.query1 = data
            this.GetqueryCoinTransactionPage()
        },
        async GetqueryCoinTransactionPage() {
            if (this.query1.tradeTimeBegin && this.query1.tradeTimeEnd) {
                this.query1.tradeTimeBegin = Math.floor(new Date(this.query1.tradeTimeBegin).getTime() / 1000);
                this.query1.tradeTimeEnd = Math.floor(new Date(this.query1.tradeTimeEnd).getTime() / 1000);
            }
            let res = await this.$api.queryAgentApply({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                ...this.query1
            });
            if (res.code == 200) {
                this.tableData = res.result.data
                this.page.totalCount = res.result.total
            }
        },
        currentChange(value) {
            this.page.pageNum = value
            this.GetqueryCoinTransactionPage()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.GetqueryCoinTransactionPage()
        },
    }
}
</script>

<style lang="scss" scoped>
.influence-item {
    // display: flex;
    // align-items: center;
    // margin-bottom: 16rpx;

    .platform-name {
        width: 60rpx;
        font-weight: bold;
        margin-right: 20rpx;
        font-size: 14px;
    }

    .input-group {
        display: flex;
        flex-direction: column;

        .input-label {
            font-size: 12px !important;
            color: #888;
        }
    }

    .bold {
        font-weight: bold;
        font-size: 18px;

    }
}
</style>
// ::v-deep .el-form--label-top .el-form-item__label {
// padding: 0 !important;
// }

// // 300px
// ::v-deep .el-input__inner {
// width: 300px !important;
// }

// .form-container {
// max-width: 650px;
// // margin: 0 auto;
// }

// .section-title {
// font-weight: bold;
// font-size: 16px;
// // margin: 20px 0 12px;
// }

// .form-row {
// display: flex;
// justify-content: space-between;
// gap: 20px;
// margin-bottom: 16px;
// }

// .el-form-item {
// flex: 1;
// }

// .influence-box {
// display: flex;
// flex-direction: column;
// gap: 12px;
// }

// .influence-item {
// display: flex;
// align-items: center;
// gap: 12px;
// }

// .full-width {
// width: 100%;
// }