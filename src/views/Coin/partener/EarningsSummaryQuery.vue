<template>
    <d2-container class="page">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>

        <div class="income-cards">
            <div class="card">
                <div class="label">我获得的收益</div>
                <div class="value">{{ totalAsset?.rebateTotalAmount || 0 }} <span class="unit">USDT</span></div>
            </div>
            <div class="card">
                <div class="label">自成交的收益（只有总代有）</div>
                <div class="value">{{ totalAsset?.selfRebateTotalAmount || 0 }} <span class="unit">USDT</span></div>
            </div>
            <div class="card">
                <div class="label">直属下级带来的收益</div>
                <div class="value">{{ totalAsset?.directRebateTotalAmount || 0 }} <span class="unit">USDT</span></div>
            </div>
            <div class="card">
                <div class="label">全部下级带来的收益</div>
                <div class="value">{{ totalAsset?.allInviteeRebateTotalAmount || 0 }} <span class="unit">USDT</span></div>
            </div>
            <div class="card">
                <div class="label">全部下级的交易量</div>
                <div class="value">{{ totalAsset?.allTradeTotalAmount || 0 }}<span class="unit">USDT</span></div>
            </div>
        </div>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">

            <!-- action -->
            <!-- <template #action="scope">
                <el-button size="mini" type="primary" @click="onDetail(scope.row)">交易明细</el-button>
            </template> -->

            <!-- userType -->
            <template #userType="scope">
                {{ formatType(scope.row.userType) }}
            </template>

            <!-- rebateType -->
            <template #rebateType="scope">
                {{ formatsubStatus(scope.row.rebateType) }}
            </template>

            <!-- subjectType -->
            <template #subjectType="scope">
                {{ formatStatus(scope.row.subjectType) }}
            </template>

            <template #rebateTime="scope">
                {{ formatTimestamp(scope.row.rebateTime) }}
            </template>
            <template #tradeTime="scope">
                {{ formatTimestamp(scope.row.tradeTime) }}
            </template>

            <!-- actualRebateRate -->
            <template #actualRebateRate="scope">
                {{ scope.row.actualRebateRate  + '%' }}
            </template>

        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[10, 20, 30, 50]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>

    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
export default {
    name: "EarningsSummaryQuery",
    components: {
        CommonQuery,
        CommonTable,
        CommonForm,
    },
    data() {
        return {
            query1: {
                // tradeTimeBegin: "",
                // tradeTimeEnd: ""
            },
            Substatus: [
                { value: 1, label: "初始化" },
                { value: 2, label: "进行中" },
                { value: 3, label: "完成" },
                { value: 4, label: "失败" },
                { value: 5, label: "审批" }
            ],
            status: [
                { value: 1, label: "初始化" },
                { value: 2, label: "进行中" },
                { value: 3, label: "完成" },
                { value: 4, label: "失败" }
            ],
            types: [
                {
                    value: 'COMMON',
                    label: '普通'
                },
                {
                    value: 'AGENT',
                    label: '代理商'
                },
                {
                    value: 'BD',
                    label: '商务'
                }
            ],

            formData: {},
            loading: false,
            tableData: [],
            tableSchema: [
                { label: '返佣对象 UID', field: 'uid' },
                { label: '交易人 UID', field: 'tradeUid' },
                { label: '用户类型', slot: 'userType' }, // 枚举: COMMON, AGENT, BD
                { label: '返佣类型', slot: 'rebateType' }, // 枚举: SELF, DIRECT, INDIRECT
                { label: '我的返佣收益', field: 'rebateAmount' },
                { label: '主体类型', slot: 'subjectType' }, // 枚举: API, HUMAN
                { label: '返佣时间', slot: 'rebateTime' },
                { label: '交易时间', slot: 'tradeTime' },
                { label: '备注', field: 'note' },
                { label: '实际返佣率', slot: 'actualRebateRate' },
                { label: '团队交易总额', field: 'agentTeamTradeTotalAmount' },
                { label: '当天邀请人交易金额', field: 'inviteeTradeAmountInDay' },
                { label: '团队返佣总额', field: 'agentTeamRebateTotalAmount' }, // 枚举: SELF, DIRECT, INDIRECT
                { label: '当天邀请人数', field: 'inviteeCountInDay' },
                // {
                //     label: '操作',
                //     slot: 'action',
                //     width: 180
                // }
            ],
            query: {},


            page: {
                pageNum: 1,
                pageSize: 10,
                totalCount: 0
            },
            nowrow: {},
            coinPair: [],
            totalAsset: {}

        }
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                {
                    type: 'input',
                    label: '下级用户 id：',
                    placeholder: '',
                    field: 'inviteeUid',
                },
                {
                    type: 'select',
                    label: '业务类型：',
                    placeholder: '',
                    field: 'tradeType',
                    options: [
                        {
                            value: 'CONTRACT',
                            label: '合约'
                        },
                        {
                            value: 'US_STOCK',
                            label: '美股'
                        },
                        {
                            value: 'HK_STOCK',
                            label: '港股'
                        }
                    ]
                },
                {
                    type: 'input',
                    label: '受益人备注：',
                    placeholder: '',
                    field: 'note',
                },
                {
                    type: 'select',
                    label: '用户类型：',
                    placeholder: '',
                    field: 'userType',
                    options: [
                        {
                            value: null,
                            label: '全部'
                        },
                        {
                            value: 'COMMON',
                            label: '普通'
                        },
                        {
                            value: 'AGENT',
                            label: '代理商'
                        },
                        {
                            value: 'BD',
                            label: '商务'
                        }
                    ]
                },
                // {
                //     type: 'input',
                //     label: 'symbolConfigId：',
                //     placeholder: '',
                //     field: 'symbolConfigIdStr',
                // },
                // 是否API
                // {
                //     type: 'select',
                //     label: '币种：',
                //     placeholder: '',
                //     field: 'symbol',
                //     options: this.coinPair
                // },
                {
                    type: 'select',
                    label: '是否API：',
                    placeholder: '',
                    field: 'subjectType',
                    options: [
                        {
                            value: 'API',
                            label: 'API'
                        },
                        {
                            value: 'HUMAN',
                            label: '人工'
                        }
                    ]
                },
                {
                    type: 'datetimerange',
                    label: '交易时间日期',
                    field: 'tradeTimeBegin',
                    field2: 'tradeTimeEnd',
                    placeholder: '请选择交易时间日期',
                    options: []
                },
            ]
        }

    },
    mounted() {
        this.GetqueryCoinTransactionPage();
        this.queryRebateSummaryAlls()
        // let net = localStorage.getItem('Networks')
        let coin = localStorage.getItem('coinPair')
        // this.networkList = JSON.parse(net)
        this.coinPair = JSON.parse(coin)
        console.log(new Date().getTimezoneOffset() / 60);
    },
    methods: {
        formatTimestamp(seconds) {
            if (!seconds) {
                return '--'
            }
            const date = new Date(seconds * 1000); // 转成毫秒时间戳
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        formatUtcTime(utcDateStr) {
            // 截取需要的部分，假设输入总是有效的ISO 8601格式
            const date = utcDateStr.substr(0, 10); // 获取日期部分 '2025-03-07'
            const time = utcDateStr.substr(11, 8); // 获取时间部分 '07:29:04'

            // 合并日期和时间部分
            return `${date} ${time}`;
        },
        formatsubStatus(value) {
            let arr = [
                // SELF, DIRECT, INDIRECT
                {
                    value: 'SELF',
                    text: '自返佣'
                },
                {
                    value: 'DIRECT',
                    text: '直接返佣'
                },
                {
                    value: 'INDIRECT',
                    text: '间接返佣'
                }
            ]
            const item = arr.find(i => i.value == value)
            console.log(item);
            
            return item ? item.text : value
        },
        formatStatus(value) {
            let arr = [
                {
                    value: 'API',
                    text: 'API'
                },
                {
                    value: 'HUMAN',
                    text: '人工'
                },

            ]
            const item = arr.find(i => i.value === value)
            return item ? item.text : value
        },
        formatType(value) {
            const item = this.types.find(i => i.value === value)
            return item ? item.label : value
        },


        onDetail(item) {
            this.queryDetail(item)
        },
        async queryDetail(item) {
            let res = await this.$api.queryCoinTransactionDetail({
                idStr: item.id
            })
        },
        onQueryChange(data) {
            this.query1 = data
            this.GetqueryCoinTransactionPage(true)
        },
        onRefresh(data) {
            this.query1 = data
            this.GetqueryCoinTransactionPage()
        },
        async queryRebateSummaryAlls() {
            let res = await this.$api.queryRebateSummaryAll()
            if (res.code == 200) {
                this.totalAsset = res.result
            }
        },
        async GetqueryCoinTransactionPage() {
            if (this.query1.tradeTimeBegin && this.query1.tradeTimeEnd) {
                this.query1.tradeTimeBegin = Math.floor(new Date(this.query1.tradeTimeBegin).getTime() / 1000);
                this.query1.tradeTimeEnd = Math.floor(new Date(this.query1.tradeTimeEnd).getTime() / 1000);
            }
            let res = await this.$api.queryRebateSummary({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                ...this.query1
            });
            if (res.code == 200) {
                this.queryRebateSummaryAlls()
                this.tableData = res.result.data
                this.page.totalCount = res.result.total
            }
        },
        currentChange(value) {
            this.page.pageNum = value
            this.GetqueryCoinTransactionPage()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.GetqueryCoinTransactionPage()
        },
    }
}
</script>

<style lang="scss" scoped>
.income-cards {
    display: flex;
    // justify-content: space-between;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 12px;
    margin: 20px 0;

    .card {
        background: #f5f5f5;
        padding: 16px 20px;
        border-radius: 4px;
        // width: calc(20% - 10px); // 每行 5 列
        min-width: 160px;

        .label {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
        }

        .value {
            font-size: 20px;
            font-weight: bold;
            color: #000;

            .unit {
                font-size: 14px;
                margin-left: 4px;
                color: #666;
            }
        }
    }
}
</style>