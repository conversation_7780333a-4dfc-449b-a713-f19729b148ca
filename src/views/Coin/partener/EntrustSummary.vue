<template>
    <d2-container class="page" ref="returnTop">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>
        <!-- <el-button style="margin-bottom: 20px;" type="primary" size="mini" @click="openPopup()">添加代理商配置</el-button> -->

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
            <!-- assetStatus -->
            <template #assetStatus="scope">
                {{ scope.row.assetStatus == 1 ? '正常' : scope.row.assetStatus == 2 ? '冻结' : '' }}
            </template>
            <!-- action -->
            <template #action="scope">
                <el-button type="primary" size="mini" @click="EditPopup(scope.row)">操作</el-button>
            </template>
            <!-- positionOpenTime  -->
            <template #positionOpenTime="scope">
                {{ formatTimestamp(scope.row.positionOpenTime) }}
            </template>
            <!-- positionCloseTime  -->
            <template #positionCloseTime="scope">
                {{ formatTimestamp(scope.row.positionCloseTime) }}
            </template>

            <!-- orderStatus -->
            <template #orderStatus="scope">
                {{ formatsubStatus(scope.row.orderStatus) }}
            </template>

            <!-- direction -->
            <template #direction="scope">
                {{ formatOrderKind(scope.row.direction) }}
            </template>
            <!-- orderKind -->
            <template #orderKind="scope">
                {{ (scope.row.orderKind == 'OPEN' ? '开仓' : scope.row.orderKind == 'CLOSE' ? '平仓' : '--') }}
            </template>

            <!-- orderSide -->
            <template #orderSide="scope">
                {{ (scope.row.orderSide == 'BUY' ? '买入' : scope.row.orderSide == 'SELL' ? '卖出' : '--') }}
            </template>

            <!-- positionType -->
            <template #positionType="scope">
                {{ (scope.row.positionType == 'TOTAL' ? '全仓' : scope.row.positionType == "PART" ? '逐仓' : '--') }}
            </template>

            <!-- positionMode -->
            <template #positionMode="scope">
                {{ (scope.row.positionMode == 'MERGE' ? '合仓' : scope.row.positionMode == "SPLIT" ? '分仓' : '--') }}
            </template>



        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.total"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[10, 20, 30, 50]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>

        <!-- 修改 -->
        <el-dialog title="编辑代理商" :visible.sync="dialogVisibleAddorSub">
            <common-form :is-edit="true" :submit="submit" :data="formData" :schema="formSchema" label-width="300px">
                <!-- <template #timing="scope"></template> -->
            </common-form>
        </el-dialog>

        <!-- 新增 -->
        <el-dialog title="新增代理商" :visible.sync="dialogVisibleAdd">
            <common-form :is-edit="true" :submit="submitAdd" :data="formDataAdd" :schema="formSchemaAdd"
                label-width="300px">
                <!-- <template #timing="scope"></template> -->
            </common-form>
        </el-dialog>
    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
export default {
    components: {
        CommonQuery,
        CommonTable,
        CommonForm,
    },
    data() {
        return {
            query1: {},
            Substatus: [
                { value: 'INIT', label: '初始化' },
                { value: 'NEW', label: '进行中' },
                { value: 'FILLED', label: '完成' },
                { value: 'PART_FILLED', label: '部分成交' },
                { value: 'CANCELED', label: '已取消' },
                { value: 'PENDING_CANCEL', label: '取消中' },
                { value: 'EXPIRED', label: '已过期' }
            ],
            dialogVisibleAdd: false,
            formDataAdd: {},
            formData: {
                uidStr: "",
                symbol: "",
                assetStatus: "",
                changeType: "",
                amount: "",
                remark: "",
            },
            formSchemaAdd: [
                {
                    type: 'switch',
                    label: '是否法币：',
                    // placeholder: '请输入退市涨幅',
                    field: 'fiat',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请选择是否法币',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
            ],
            formSchema: [
                {
                    type: 'input',
                    label: '用户UID：',
                    placeholder: '请输入uid',
                    field: 'uid',
                    rules: [
                        {
                            required: true,
                            message: '请输入uid',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '合约返佣比例%：',
                    placeholder: '请输入 合约返佣比例%',
                    field: 'contractRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入 合约返佣比例%',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '美股返佣比例：',
                    placeholder: '请输入美股返佣比例',
                    field: 'usStockRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入美股返佣比例',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '港股返佣比例：',
                    placeholder: '请输入港股返佣比例',
                    field: 'hkStockRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入港股返佣比例',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: 'API 返佣比例：',
                    placeholder: '请输入API 返佣比例',
                    field: 'apiRebateRate',
                    rules: [
                        {
                            required: true,
                            message: '请输入API 返佣比例',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '登录名：',
                    placeholder: '',
                    field: 'loginName',
                },
                {
                    type: 'input',
                    label: '手机号：',
                    placeholder: '请输入手机号',
                    field: 'phone',
                },
                {
                    type: 'input',
                    label: '邮箱：',
                    placeholder: '请输入邮箱',
                    field: 'email',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入备注',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '备注：',
                    placeholder: '请输入备注',
                    field: 'note',
                },

                {
                    type: 'action',
                }
                // {
                //     type: 'input',
                //     label: '资产来源值：',
                //     placeholder: '请输入资产来源值',
                //     field: 'fromValue',
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入资产来源值',
                //             trigger: 'blur',
                //         },
                //     ],
                // },

            ],
            dialogVisibleAddorSub: false,
            nowrow: {},
            loading: false,
            page: {
                pageSize: 10,
                pageNum: 1,
                total: ""
            },
            query: {},

            tableData: [],
            tableSchema: [
                { label: '下属 UID', field: 'uid' },
                { label: '直属上级 UID', field: 'inviterUid' },
                { label: '归属总代 UID', field: 'topAgent' },
                { label: '交易币对', field: 'symbol' },
                { label: '杠杆倍数', field: 'leverageRatio' },

                { label: '仓位保证金', field: 'positionMargin' },
                { label: '委托价格', field: 'orderPrice' },

                { label: '成交价格', field: 'filledPrice' },
                { label: '成交均价', field: 'avgFilledPrice' },

                { label: '成交数量', field: 'filledQuantity' },

                // OPEN_BUY
                // OPEN_SELL
                // CLOSE_BUY
                // CLOSE_SELL
                { label: '综合方向', slot: 'direction' },

                { label: '合约类别', slot: 'orderKind' }, // OPEN CLOSE
                { label: '订单方向', slot: 'orderSide' }, // BUY SELL
                { label: '委托状态', slot: 'orderStatus' }, // 完成

                { label: '仓位类型', slot: 'positionType' }, // TOTAL PART
                { label: '仓位模式', slot: 'positionMode' }, // MERGE SPLIT

                { label: '仓位 ID', field: 'positionId' },
                { label: '委托编号', field: 'orderId' },
                { label: '开仓时间', slot: 'positionOpenTime' },
                { label: '平仓时间', slot: 'positionCloseTime' }
            ],
            coinPair: []
        }
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                {
                    type: 'select',
                    label: '业务类型：',
                    placeholder: '',
                    field: 'tradeType',
                    options: [
                        {
                            value: 'CONTRACT',
                            label: '合约'
                        },
                        {
                            value: 'US_STOCK',
                            label: '美股'
                        },
                        {
                            value: 'HK_STOCK',
                            label: '港股'
                        }
                    ]
                },
                {
                    type: 'input',
                    label: '用户uid：',
                    placeholder: '',
                    field: 'uid',
                },
                {
                    type: 'input',
                    label: '仓位 id：',
                    placeholder: '',
                    field: 'positionId',
                },
                // {
                //     type: 'select',
                //     label: '交易方向：',
                //     placeholder: '',
                //     field: 'direction',
                //     options: [
                //         {
                //             label: '开仓',
                //             value: '1'
                //         },
                //         {
                //             label: '平仓',
                //             value: '2'
                //         }
                //     ]
                // },
                {
                    type: 'input',
                    label: '交易币对：',
                    placeholder: '',
                    field: 'tradeSymbol',
                },
                {
                    type: 'select',
                    label: '委托状态：',
                    placeholder: '',
                    field: 'orderStatus',
                    options: this.Substatus
                },
                {
                    type: 'datetimerange',
                    label: '开仓时间日期',
                    field: 'positionOpenTimeBegin',
                    field2: 'positionOpenTimeEnd',
                    placeholder: '请选择开仓时间日期',
                    options: []
                },


            ]
        },
    },
    mounted() {
        this.getPageUser()
        let coin = localStorage.getItem('coinPair')
        this.coinPair = JSON.parse(coin)
    },
    methods: {
        formatOrderKind(value) {
            // OPEN_BUY
            // OPEN_SELL
            // CLOSE_BUY
            // CLOSE_SELL
            // 翻译成中文

            // return {
            //     OPEN_BUY: 'OPEN_BUY',
            //     OPEN_SELL: 'OPEN_SELL',
            //     CLOSE_BUY: 'CLOSE_BUY',
            //     CLOSE_SELL: 'CLOSE_SELL'
            // }

            let map = {
                OPEN_BUY: '开多',
                OPEN_SELL: '开空',
                CLOSE_BUY: '平多',
                CLOSE_SELL: '平空'
            }
            return map[value]

        },
        formatsubStatus(value) {
            const item = this.Substatus.find(i => i.value === value)
            return item ? item.label : value
        },
        formatTimestamp(seconds) {
            const date = new Date(seconds * 1000); // 转成毫秒时间戳
            const Y = date.getFullYear();
            const M = String(date.getMonth() + 1).padStart(2, '0');
            const D = String(date.getDate()).padStart(2, '0');
            const h = String(date.getHours()).padStart(2, '0');
            const m = String(date.getMinutes()).padStart(2, '0');
            const s = String(date.getSeconds()).padStart(2, '0');
            return `${Y}-${M}-${D} ${h}:${m}:${s}`;
        },
        submitAdd() { },
        openPopup() {
            this.formData = {
                uidStr: "",
                symbol: "",
                assetStatus: "",
                changeType: "",
                amount: "",
                remark: "",
            }
            // this.dialogVisibleAddorSub = true
        },
        async submit() {
            let res = await this.$api.updateUserBalance(this.formData)
            if (res.code == 200) {
                this.dialogVisibleAddorSub = false
                this.$message({
                    message: '操作成功',
                    type: 'success'
                })
            }
        },
        EditPopup(item) {
            this.nowrow = item
            // this.formData = item
            this.formData.uidStr = item.uid

            this.formData.symbol = item.symbol
            this.formData.assetStatus = item.assetStatus

            this.dialogVisibleAddorSub = true
        },
        async getPageUser() {
            if (this.query1.positionOpenTimeBegin && this.query1.positionOpenTimeEnd) {
                this.query1.positionOpenTimeBegin = Math.floor(new Date(this.query1.positionOpenTimeBegin).getTime() / 1000);
                this.query1.positionOpenTimeEnd = Math.floor(new Date(this.query1.positionOpenTimeEnd).getTime() / 1000);
            }
            // this.loading = true
            let res = await this.$api.queryEntrustStatistics({
                pageNum: this.page.pageNum,
                pageSize: this.page.pageSize,
                ...this.query1
            })
            if (res.code == 200) {
                this.loading = false
                this.tableData = res.result.data
                this.page.total = res.result.total
            } else {
                this.loading = false
            }
        },
        onQueryChange(data) {
            this.query1 = data
            this.getPageUser(true)
        },
        onRefresh(data) {
            this.query1 = data
            this.getPageUser()
        },
        formatDate(isoString) {
            if (!isoString) return
            const [date, time] = isoString.split('T'); // 分割日期和时间
            const formattedTime = time.split('.')[0];  // 去掉毫秒部分
            return `${date} ${formattedTime}`;
        },
        currentChange(value) {
            this.page.pageNum = value
            this.getPageUser()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.getPageUser()
        },
    }
}
</script>

<style lang="scss" scoped></style>