<template>
    <d2-container class="page" ref="returnTop">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>
        <el-button style="margin-bottom: 20px;" type="primary" size="mini" @click="nav_add()">创建币对</el-button>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
            <!-- <template #action-header>
          <el-button type="primary" size="mini" @click="nav_add()">创建批量上架</el-button>
        </template> -->
            <template #ctime="scope">
                {{ formatUtcTime(scope.row.ctime) }}
            </template>
            <template #mtime="scope">
                {{ formatUtcTime(scope.row.mtime) }}
            </template>
            <template #action="scope">
                <el-button type="primary" @click="EditPopup(scope.row)">配置明细</el-button>
                <el-button type="primary" @click="Update(scope.row)">编辑</el-button>

            </template>
        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>
        <!-- 增加币对 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisibleAddorUpdate">
            <common-form :is-edit="true" :submit="submit" :data="formDataAddorUpdate" :schema="formSchema"
                label-width="300px">
                <template #timing="scope"></template>
            </common-form>
        </el-dialog>

        <!-- 增加明细 -->
        <el-dialog :title="edittitle" :visible.sync="editPopup">
        </el-dialog>
    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'

import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: 'CoinSymbolConfig',
    components: {
        CommonQuery,
        CommonTable,
        CommonForm,
    },
    data() {
        return {
            edittitle: "增加币对明细",
            tempCoin: {},
            editPopup: false,
            formSchema: [
                // {
                //     type: 'select',
                //     label: '资产来源：',
                //     placeholder: '请选择资产来源',
                //     field: 'fromType',
                //     options: [
                //         {
                //             label: 'fireblocks',
                //             value: 0
                //         },
                //         {
                //             label: 'uqpay',
                //             value: 1
                //         },
                //     ],
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入资产来源',
                //             trigger: 'blur',
                //         },
                //     ],
                // },
                // {
                //     type: 'input',
                //     label: '资产来源值：',
                //     placeholder: '请输入资产来源值',
                //     field: 'fromValue',
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入资产来源值',
                //             trigger: 'blur',
                //         },
                //     ],
                // },
                // defaultOrder
                {
                    type: 'input',
                    label: '默认排序：',
                    placeholder: '请输入默认排序',
                    field: 'defaultOrder',
                    rules: [
                        {
                            required: true,
                            message: '请输入默认排序',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '资产符号：',
                    placeholder: '请选择资产符号',
                    field: 'symbol',
                    rules: [
                        {
                            required: true,
                            message: '请输入资产符号',
                            trigger: 'blur',
                        },
                    ],
                },
                // {
                //     type: 'select',
                //     label: '资产符号：',
                //     placeholder: '请选择资产符号',
                //     field: 'symbol',
                //     options: [
                //         {
                //             label: 'ETH',
                //             value: 'ETH'
                //         }
                //     ],
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入资产符号',
                //             trigger: 'blur',
                //         },
                //     ],
                // },
                // fiat
                {
                    type: 'switch',
                    label: '是否法币：',
                    // placeholder: '请输入退市涨幅',
                    field: 'fiat',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请选择是否法币',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'switch',
                    label: '是否开启：',
                    // placeholder: '请输入退市涨幅',
                    field: 'openStatus',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请选择是否开启',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'switch',
                    label: '是否显示：',
                    // placeholder: '请输入退市涨幅',
                    field: 'showStatus',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请选择是否显示',
                    //         trigger: 'blur',
                    //     },
                    // ],
                }, {
                    type: 'switch',
                    label: '是否支持提现：',
                    // placeholder: '请输入退市涨幅',
                    field: 'withdrawStatus',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请选择是否支持提现',
                    //         trigger: 'blur',
                    //     },
                    // ],
                }, {
                    type: 'switch',
                    label: '是否支持充值：',
                    // placeholder: '请输入退市涨幅',
                    field: 'depositStatus',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请选择是否支持充值',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '最大提现金额：',
                    placeholder: '请输入最大提现金额',
                    field: 'maxWithdrawAmount',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入最大提现金额',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '最小提现金额：',
                    placeholder: '请输入最小提现金额',
                    field: 'minWithdrawAmount',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入最小提现金额',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                // {
                //     type: 'input',
                //     label: '最大提现日限额：',
                //     placeholder: '请输入最大提现日限额',
                //     field: 'withdrawMaxDay',
                //     // rules: [
                //     //     {
                //     //         required: true,
                //     //         message: '请输入最大提现日限额',
                //     //         trigger: 'blur',
                //     //     },
                //     // ],
                // },
                {
                    type: 'input',
                    label: '显示名称：',
                    placeholder: '请输入显示名称',
                    field: 'name',
                    rules: [
                        {
                            required: true,
                            message: '请输入显示名称',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    label: '图标：',
                    type: 'img',
                    field: 'image',
                    rules: [
                        {
                            required: true,
                            message: '请选择图标',
                            trigger: 'blur',
                        },
                    ],

                },
                {
                    type: 'action'
                }
            ],
            formDataAddorUpdate: {
                // fromType: 0,
                openStatus: 1,
                image: ""
            },
            dialogTitle: "增加币对",
            dialogVisibleAddorUpdate: false,
            page: {
                totalCount: 0,
                pageSize: 10,
                pageNum: 1
            }, // 分页数据
            query: {},
            tableSchema: [ // 表格架构
                {
                    label: 'id',
                    field: 'idStr',
                    width: 80
                },
                {
                    label: '币种',
                    field: 'symbol',
                },
                {
                    label: '是否法币',
                    field: 'fiat',
                    type: 'tag',
                    tagMap: {
                        false: {
                            label: '否',
                        },
                        true: {
                            label: '是',
                            tagType: 'success'

                        }
                    },
                },
                {
                    label: '精度',
                    field: 'precision',
                },
                // {
                //     label: '任务状态',
                //     field: 'dutyStatus',
                //     type: 'tag',
                //     tagMap: {
                //         INIT: {
                //             label: '准备中',
                //             tagType: 'info'
                //         },
                //         DOING: {
                //             label: '进行中',
                //             tagType: 'success'
                //         },
                //         STOPPING: {
                //             label: '终止中',
                //             tagType: 'danger'
                //         },
                //         STOP: {
                //             label: '已终止',
                //             tagType: 'danger'
                //         },
                //         DONE: {
                //             label: '已完成',
                //             tagType: 'success'
                //         },
                //         FAIL: {
                //             label: '执行失败',
                //             tagType: 'danger'
                //         }
                //     },
                //     width: '80px',
                // },
                {
                    label: '是否开启',
                    field: 'openStatus',
                    type: 'tag',
                    tagMap: {
                        false: {
                            label: '否',
                        },
                        true: {
                            label: '是',
                            tagType: 'success'

                        }
                    },
                },
                {
                    label: '是否展示',
                    field: 'showStatus',
                    type: 'tag',
                    tagMap: {
                        false: {
                            label: '否',
                        },
                        true: {
                            label: '是',
                            tagType: 'success'

                        }
                    },
                },
                // {
                //     label: '排序方式',
                //     field: 'priceSortType',
                //     type: 'tag',
                //     tagMap: {
                //         "1": {
                //             label: '价格从高往低',
                //             tagType: 'info'
                //         },
                //         "2": {
                //             label: '价格从低往高',
                //             tagType: 'info'
                //         },
                //     },
                // },
                // {
                //     label: '寄售价格范围',
                //     slot: 'jishouPrice'
                // },
                {
                    label: '是否开启提现',
                    field: 'withdrawStatus',
                    type: 'tag',
                    tagMap: {
                        false: {
                            label: '否',
                        },
                        true: {
                            label: '是',
                            tagType: 'success'

                        }
                    },
                },
                {
                    label: '是否开启充值',
                    field: 'depositStatus',
                    type: 'tag',
                    tagMap: {
                        false: {
                            label: '否',
                        },
                        true: {
                            label: '是',
                            tagType: 'success'

                        }
                    },
                }, {
                    label: '单次提现最大值',
                    field: 'maxWithdrawAmount',
                }, {
                    label: '单次提现最小值',
                    field: 'minWithdrawAmount',
                },
                //  {
                //     label: '单日提现最大值',
                //     field: 'withdrawMaxDay',
                // },
                {
                    field: "defaultOrder",
                    label: "默认排序"
                },
                {
                    label: '显示名称',
                    field: 'name',
                }, {
                    type: 'img',
                    label: '展示图片',
                    field: 'image',
                }, {
                    label: '创建时间',
                    slot: 'ctime',
                }, {
                    label: '更新时间',
                    slot: 'mtime',
                },
                //  {
                //     label: '来源类型',
                //     field: 'fromType',
                //     type: 'tag',
                //     tagMap: {
                //         0: {
                //             label: 'fireblocks',
                //         },
                //         1: {
                //             label: 'uqpay',
                //         },
                //     },
                // },
                //  {
                //     label: '来源值',
                //     field: 'fromValue',
                // },
                // {
                //     label: '备注',
                //     field: 'remark',
                //     showOverflowTooltip: true,
                //     width: '140px',
                // },
                {
                    label: '操作',
                    slot: 'action',
                    headerSlot: 'action-header',
                    width: '240px',
                    fixed: 'right'
                },
            ],
            tableData: [],
            coinPair: [],
            loading: false,
            query: {
                dutyStatus: 'DOING'
            }
        }
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                // {
                //     type: 'search',
                //     label: '系列名/系列ID：',
                //     placeholder: '请输入系列名/系列ID：',
                //     field: 'ctid'
                // },
                // {
                //     type: 'select',
                //     label: '资产来源：',
                //     placeholder: '',
                //     field: 'fromType',
                //     options: [{
                //         label: 'fireblocks',
                //         value: 0
                //     },
                //     {
                //         label: 'uqpay',
                //         value: 1
                //     },
                //     ],
                //     rules: [{
                //         required: true,
                //     }]
                // },
                // {
                //     type: 'select',
                //     label: '资产来源值：',
                //     placeholder: '',
                //     field: 'fromValue',
                //     options: [{
                //         label: 'ETH_TESTS',
                //         value: 0
                //     }
                //     ],
                //     rules: [{
                //         required: true,
                //     }]
                // },
                {
                    type: 'select',
                    label: '资产符号：',
                    placeholder: '',
                    field: 'symbol',
                    options: this.coinPair,
                    rules: [{
                        required: true,
                    }]
                }
            ]
        }
    },
    mounted() {
        this.getList()
        let coin = localStorage.getItem('coinPair')
        this.coinPair = JSON.parse(coin)
    },
    methods: {
        async EditPopup(item) {
            if (!item.fiat) {
                this.$router.push({
                    name: 'CoinDetails',
                    query: {
                        item: JSON.stringify(item)
                    }
                })

            } else {
                this.$message.warning('该币种为法币，暂不支持编辑')
            }
            return
            console.log(item);

            let result = await this.$api.getFireblocksAssetPaged({

            })

            let res = await this.$api.getCoinSymbolConfigDetailPaged({
                // fromType: item.fromType,
                symbolConfigIdStr: item.idStr,
                symbol: item.symbol
            })
            if (res.code == 200) {

            }
            this.tempCoin = item
            this.editPopup = true
        },
        Update(row) {
            this.dialogVisibleAddorUpdate = true
            this.formDataAddorUpdate = row
        },
        formatUtcTime(utcDateStr) {
            // 截取需要的部分，假设输入总是有效的ISO 8601格式
            const date = utcDateStr.substr(0, 10); // 获取日期部分 '2025-03-07'
            const time = utcDateStr.substr(11, 8); // 获取时间部分 '07:29:04'

            // 合并日期和时间部分
            return `${date} ${time}`;
        },
        async submit() {
            console.log(this.formDataAddorUpdate);
            let res = await this.$api.addOrUpdateCoinSymbolConfig({
                ...this.formDataAddorUpdate,
                // openStatus: this.formDataAddorUpdate.openStatus == 1 ? true : false,
                // showStatus: this.formDataAddorUpdate.showStatus == 1 ? true : false,
                // withdrawStatus: this.formDataAddorUpdate.withdrawStatus == 1 ? true : false,
                // depositStatus: this.formDataAddorUpdate.depositStatus == 1 ? true : false,
            })


            if (res.code == 200) {
                this.$message.success(res.msg)
                this.dialogVisibleAddorUpdate = false
                this.formDataAddorUpdate = {}
                this.getList()
            }
        },
        toTemplatePage(item = {}) {
            // this.$store.commit('SAVE_INFO', item)
            // mapActions('save_stateInfo', item)
            if (item.businessType && item.businessType != 0) {
                this.$router.push({
                    name: 'NoticeEdit',
                    query: {
                        templateId: item.id,
                        businessType: item.businessType
                    }
                })
            } else {
                localStorage.setItem('noticeInfo', JSON.stringify(item))
                this.$router.push({
                    name: 'platformPublish',
                    query: {
                        templateId: item.id
                    }
                })
            }

        },
        nav_add() {
            this.dialogVisibleAddorUpdate = true
        },
        // 过滤查询
        onQueryChange(data) {
            this.query = data
            this.getList(true)
        },
        onRefresh(data) {
            this.query = data
            this.getList()
        },
        // 分页改变
        currentChange(value) {
            this.page.pageNum = value
            this.getList()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.getList()
        },
        getList() {

            const params = {
                ...this.query,
                ...this.page,
                // pageNum: this.page.pageNum,
                // pageSize: this.page.pageSize,
                // fromType: 0,
                // fromValue: 'ETH_TESTS',
                // symbol: 'ETH'
            }
            let dataList = []
            this.$api.getAllPushConfig(params).then(res => {
                const data = res.result.data
                // data.forEach((item) => {
                //     dataList.push({
                //         ...item.onSaleExtra,
                //         ctid: item.ctid,
                //         startTime: item.startTime,
                //         endTime: item.endTime,
                //         dutyId: item.dutyId,
                //         createAt: item.createAt,
                //         dutyStatus: item.dutyStatus,
                //         remark: item.remark
                //     })
                // })
                this.tableData = data
                console.log(this.tableData, '')
                this.page.totalCount = res.result.total
                this.page.pageSize = res.result.pageSize
                // this.page.pageCount = res.result.pageCount
            })
        },
        // 发表/下架
        tradeStop(id) {
            // this.$confirm(`确定终止该任务吗？`, '', {
            //   confirmButtonText: '确定',
            //   cancelButtonText: '取消',
            //   type: 'warning'
            // }).then(() => {

            // })
            tradeStop({
                dutyId: id,
                dutyType: 'ON_SALE'
            }).then((res) => {
                if (res.status.code == 0) {
                    this.$message.success(res.status.msg)
                    this.getList()
                }
            })
        },
        async onExport(title, id) {
            this.loadingText = "正在导出"
            this.loading = true
            const params = {
                dutyId: id,
                dutyType: 'ON_SALE'
            }
            const res = await this.$api.tradeRecordExport(params)
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.loading = false
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, title + Date.now() + '.csv')
                this.$message.success('明细导出成功')
                this.loading = false
                this.getList()
            }
        },
        openText(text) {
            console.log(text)
            this.$alert(text, '备注详情', {
                confirmButtonText: '确定',
                callback: action => {

                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
}
</style>
