<template>
    <d2-container class="page" ref="returnTop">
        <common-query :query-schema="querySchema" :data="query" @onRefresh="onRefresh" @onSubmit="onQueryChange"
            :showRefresh="true"></common-query>
        <el-button style="margin-bottom: 20px;" type="primary" size="mini" @click="nav_add()">新增</el-button>

        <common-table :table-schema="tableSchema" :showIndex="false" :table-data="tableData" :loading="loading">
            <!-- <template #action-header>
          <el-button type="primary" size="mini" @click="nav_add()">创建批量上架</el-button>
        </template> -->
            <template #createAt="scope">
                {{ removeMilliseconds(scope.row.createAt) }}
            </template>
            <template #updateAt="scope">
                {{ removeMilliseconds(scope.row.updateAt) }}
            </template>
            <template #action="scope">
                <el-button size="mini" type="primary" @click="EditPopup(scope.row)">编辑</el-button>
                <!-- <el-button type="primary" @click="Update(scope.row)">编辑</el-button> -->
                <!-- <el-button size="mini" type="danger" @click="Delete(scope.row)">删除</el-button> -->
                <el-button size="mini" type="text" @click="changeStatus(scope.row)">{{ scope.row.status == 1 ? '不启用'
                    : '启用'
                    }}</el-button>

            </template>

            <!-- status -->
             <template #status="scope">
                <el-tag v-if="scope.row.status == 1" type="success">启用</el-tag>
                <el-tag v-if="scope.row.status == 0" type="warning">不启用</el-tag>
             </template>
        </common-table>
        <div class="" style="display: flex; justify-content: center; background-color: #ffffff">
            <el-pagination background layout="total, sizes, prev, pager, next, jumper" :total="page.totalCount"
                :page-size="page.pageSize" :current-page="page.pageNum" :page-sizes="[10, 20, 30, 40, 50]"
                style="padding: 20px; background-color: #ffffff" @current-change="currentChange"
                @size-change="currentChangeSize">
            </el-pagination>
        </div>
        <!-- 增加币对 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisibleAddorUpdate">
            <common-form :is-edit="true" :submit="submit" :data="formDataAddorUpdate" :schema="formSchema"
                label-width="300px">
                <template #timing="scope"></template>
            </common-form>
        </el-dialog>

        <!-- 增加明细 -->
        <el-dialog :title="edittitle" :visible.sync="editPopup">
        </el-dialog>
    </d2-container>
</template>

<script>
import CommonQuery from '@/components/CommonQuery_h'
import CommonTable from '@/components/CommonTable'
import CommonForm from '@/components/CommonForm'
import { removeMilliseconds } from '@/utils/helper'
import {
    downloadBlob
} from '@/utils/helper'
export default {
    name: 'CoinSymbolConfig',
    components: {
        CommonQuery,
        CommonTable,
        CommonForm,
    },
    data() {
        return {
            symbolList: [],
            edittitle: "增加币对明细",
            tempCoin: {},
            editPopup: false,
            formDataAddorUpdate: {
                // fromType: 0,
                openStatus: 1,
                image: ""
            },
            dialogTitle: "增加配置",
            dialogVisibleAddorUpdate: false,
            page: {
                totalCount: 0,
                pageSize: 10,
                pageNum: 1
            }, // 分页数据
            query: {},
            tableSchema: [ // 表格架构
                {
                    label: 'id',
                    field: 'id',
                    width: 80
                },
                {
                    label: '币对',
                    field: 'symbol'
                },
                {
                    label: '用户uid',
                    field: 'uid'
                },
                {
                    label: '合约名称',
                    field: 'contractName'
                },
                {
                    label: '铺单类型',
                    field: 'contractType'
                },
                {
                    label: '启用状态',
                    slot: 'status'
                },
                {
                    label: '最大持仓(进远端)',
                    field: 'maxPosition'
                },
                {
                    label: '盘口价差',
                    field: 'levelInterval'
                },
                {
                    label: '首档挂单系数',
                    field: 'levelFirstPosition'
                },
                {
                    label: '基础订单数量',
                    field: 'baseOrder'
                },
                {
                    label: '档位数量',
                    field: 'n'
                },
                {
                    label: '超时撤单重挂时间间隔',
                    field: 'timeCancel'
                },
                {
                    label: '更新时间',
                    slot: 'updateAt'
                },
                {
                    label: '创建时间',
                    slot: 'createAt'
                },
                {
                    label: '外放的tick数(近端)',
                    field: 'spread'
                },
                {
                    label: 'tick（价值的0.1%）(近端)',
                    field: 'tick'
                },
                {
                    label: '订单编号',
                    field: 'orderNumber'
                },
                {
                    label: '近端盘口一定时间内最大成交金额（分钟，金额）(近端)',
                    field: 'maxTradePosition'
                },
                {
                    label: '下单最小间隔(近端)',
                    field: 'rateInterval'
                },
                {
                    label: 'n档挂单系数，随档位增加',
                    field: 'nposition'
                },
                {
                    label: '操作',
                    slot: 'action',
                    headerSlot: 'action-header',
                    width: '240px',
                    fixed: 'right'
                },
            ],
            tableData: [],
            coinPair: [],
            loading: false,
            query: {
                dutyStatus: 'DOING'
            },
            coinlist: []
        }
    },
    computed: {
        querySchema() {
            return [ // 搜索组件架构
                // {
                //     type: 'search',
                //     label: '系列名/系列ID：',
                //     placeholder: '请输入系列名/系列ID：',
                //     field: 'ctid'
                // },
                // {
                //     type: 'select',
                //     label: '资产来源：',
                //     placeholder: '',
                //     field: 'fromType',
                //     options: [{
                //         label: 'fireblocks',
                //         value: 0
                //     },
                //     {
                //         label: 'uqpay',
                //         value: 1
                //     },
                //     ],
                //     rules: [{
                //         required: true,
                //     }]
                // },
                // {
                //     type: 'input',
                //     label: '订单id：',
                //     placeholder: '',
                //     field: 'id'
                // },
                {
                    type: 'select',
                    label: '合约名称：',
                    placeholder: '',
                    field: 'contractName',
                    options: this.coinlist,
                    rules: [{
                        required: true,
                    }]
                },
                // {
                //     type: 'select',
                //     label: '币对：',
                //     placeholder: '',
                //     field: 'symbol',
                //     options: this.symbolList,
                //     rules: [{
                //         required: true,
                //     }]
                // },
                // symbolList
                {
                    type: 'select',
                    label: '铺单类型：',
                    placeholder: '请选择铺单类型',
                    field: 'contractType',
                    options: [
                        {
                            label: 'farPort',
                            value: 'farPort'
                        },
                        {
                            label: 'nearPort',
                            value: 'nearPort'
                        },
                    ],
                    rules: [
                        {
                            required: true,
                            message: '请输入合约类型',
                            trigger: 'blur',
                        },
                    ],
                },
            ]
        },
        formSchema() {
            return [
                {
                    type: 'input',
                    label: '订单id：',
                    placeholder: '请输入订单id',
                    field: 'id',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入订单id',
                    //         trigger: 'blur',
                    //     },
                    // ],
                    disabled: true,
                },
                {
                    type: 'input',
                    label: '币对：',
                    placeholder: '请输入币对',
                    field: 'symbol',
                    rules: [
                        {
                            required: true,
                            message: '请输入币对',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'select',
                    label: '合约名称：',
                    placeholder: '请选择合约名称',
                    field: 'contractName',
                    options: this.coinlist,
                    rules: [{
                        required: true,
                    }]
                },
                {
                    type: 'select',
                    label: '合约类型：',
                    placeholder: '请选择合约类型',
                    field: 'contractType',
                    options: [
                        {
                            label: '远端',
                            value: 'farPort'
                        },
                        {
                            label: '近端',
                            value: 'nearPort'
                        },
                    ],
                    rules: [
                        {
                            required: true,
                            message: '请输入合约类型',
                            trigger: 'blur',
                        },
                    ],
                },
                // {
                //     type: 'select',
                //     label: '是否显示：',
                //     field: 'showStatus',
                //     options: [
                //         {
                //             label: '显示',
                //             value: 1
                //         },
                //         {
                //             label: '隐藏',
                //             value: 0
                //         },
                //     ],
                //     rules: [
                //         {
                //             required: true,
                //             message: '请选择是否显示',
                //             trigger: 'blur',
                //         },
                //     ],
                // },
                {
                    type: 'input',
                    label: '最大持仓(进远端)：',
                    placeholder: '请选择最大持仓(进远端)',
                    field: 'maxPosition',
                    rules: [
                        {
                            required: true,
                            message: '请输入最大持仓(进远端)',
                            trigger: 'blur',
                        },
                    ],
                },
                {
                    type: 'input',
                    label: '盘口价差（远端）：',
                    placeholder: '请选择盘口价差',
                    field: 'levelInterval',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入盘口价差',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '首档挂单系数（远端）：',
                    placeholder: '请选择首档挂单系数',
                    field: 'levelFirstPosition',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入首档挂单系数',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '基础订单数量（远端）：',
                    placeholder: '请选择基础订单数量',
                    field: 'baseOrder',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入基础订单数量',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '档位数量（远端）：',
                    placeholder: '请选择档位数量',
                    field: 'n',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入档位数量',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '超时撤单重挂时间间隔（远端）：',
                    placeholder: '请选择超时撤单重挂时间间隔',
                    field: 'timeCancel',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入超时撤单重挂时间间隔',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '外放的tick数(近端)：',
                    placeholder: '请选择外放的tick数(近端)',
                    field: 'spread',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入外放的tick数(近端)',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: 'tick（价值的0.1%）(近端)：',
                    placeholder: '请输入tick（价值的0.1%）(近端)',
                    field: 'tick',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入tick（价值的0.1%）(近端)',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '近端盘口一定时间内最大成交金额（分钟，金额）(近端)：',
                    placeholder: '请输入近端盘口一定时间内最大成交金额（分钟，金额）(近端)',
                    field: 'maxTradePosition',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入近端盘口一定时间内最大成交金额（分钟，金额）(近端)',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '挂单基础份数(近端)：',
                    placeholder: '请输入挂单基础份数(近端)',
                    field: 'orderNumber',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入挂单基础份数(近端)',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: '下单最小间隔(近端)：',
                    placeholder: '请输入下单最小间隔(近端)',
                    field: 'rateInterval',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入下单最小间隔(近端)',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                {
                    type: 'input',
                    label: 'n档挂单系数，随档位增加：(远端)',
                    placeholder: '请输入n档挂单系数',
                    field: 'nposition',
                    // rules: [
                    //     {
                    //         required: true,
                    //         message: '请输入n档挂单系数，随档位增加',
                    //         trigger: 'blur',
                    //     },
                    // ],
                },
                // nposition
                // rateInterval
                // {
                //     type: 'select',
                //     label: '资产符号：',
                //     placeholder: '请选择资产符号',
                //     field: 'symbol',
                //     options: [
                //         {
                //             label: 'ETH',
                //             value: 'ETH'
                //         }
                //     ],
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入资产符号',
                //             trigger: 'blur',
                //         },
                //     ],
                // },
                // fiat
                // {
                //     type: 'switch',
                //     label: '是否法币：',
                //     // placeholder: '请输入退市涨幅',
                //     field: 'fiat',
                //     // rules: [
                //     //     {
                //     //         required: true,
                //     //         message: '请选择是否法币',
                //     //         trigger: 'blur',
                //     //     },
                //     // ],
                // },
                // {
                //     type: 'switch',
                //     label: '是否开启：',
                //     // placeholder: '请输入退市涨幅',
                //     field: 'openStatus',
                //     // rules: [
                //     //     {
                //     //         required: true,
                //     //         message: '请选择是否开启',
                //     //         trigger: 'blur',
                //     //     },
                //     // ],
                // },
                // {
                //     type: 'switch',
                //     label: '是否显示：',
                //     // placeholder: '请输入退市涨幅',
                //     field: 'showStatus',
                //     // rules: [
                //     //     {
                //     //         required: true,
                //     //         message: '请选择是否显示',
                //     //         trigger: 'blur',
                //     //     },
                //     // ],
                // }, {
                //     type: 'switch',
                //     label: '是否支持提现：',
                //     // placeholder: '请输入退市涨幅',
                //     field: 'withdrawStatus',
                //     // rules: [
                //     //     {
                //     //         required: true,
                //     //         message: '请选择是否支持提现',
                //     //         trigger: 'blur',
                //     //     },
                //     // ],
                // }, {
                //     type: 'switch',
                //     label: '是否支持充值：',
                //     // placeholder: '请输入退市涨幅',
                //     field: 'depositStatus',
                //     // rules: [
                //     //     {
                //     //         required: true,
                //     //         message: '请选择是否支持充值',
                //     //         trigger: 'blur',
                //     //     },
                //     // ],
                // },
                // {
                //     type: 'input',
                //     label: '最大提现金额：',
                //     placeholder: '请输入最大提现金额',
                //     field: 'maxWithdrawAmount',
                //     // rules: [
                //     //     {
                //     //         required: true,
                //     //         message: '请输入最大提现金额',
                //     //         trigger: 'blur',
                //     //     },
                //     // ],
                // },
                // {
                //     type: 'input',
                //     label: '最小提现金额：',
                //     placeholder: '请输入最小提现金额',
                //     field: 'minWithdrawAmount',
                //     // rules: [
                //     //     {
                //     //         required: true,
                //     //         message: '请输入最小提现金额',
                //     //         trigger: 'blur',
                //     //     },
                //     // ],
                // },
                // // {
                // //     type: 'input',
                // //     label: '最大提现日限额：',
                // //     placeholder: '请输入最大提现日限额',
                // //     field: 'withdrawMaxDay',
                // //     // rules: [
                // //     //     {
                // //     //         required: true,
                // //     //         message: '请输入最大提现日限额',
                // //     //         trigger: 'blur',
                // //     //     },
                // //     // ],
                // // },
                // {
                //     type: 'input',
                //     label: '显示名称：',
                //     placeholder: '请输入显示名称',
                //     field: 'name',
                //     rules: [
                //         {
                //             required: true,
                //             message: '请输入显示名称',
                //             trigger: 'blur',
                //         },
                //     ],
                // },
                // {
                //     label: '图标：',
                //     type: 'img',
                //     field: 'image',
                //     rules: [
                //         {
                //             required: true,
                //             message: '请选择图标',
                //             trigger: 'blur',
                //         },
                //     ],

                // },
                {
                    type: 'action'
                }
            ]
        }
    },
    async mounted() {
        await this.fetchcoin()
        this.getList()
        let coin = localStorage.getItem('coinPair')
        this.coinPair = JSON.parse(coin)
    },
    methods: {
        removeMilliseconds,
        async Delete(row) {
            this.$confirm('是否确认删除？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                this.sub(row)
            })
        },
        async changeStatus(row) {
            this.$confirm(`是否确认${row.status == 1 ? '不启用' : '启用'}？`, `确认${row.status == 1 ? '不启用' : '启用'}`, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                this.sub(row)
            })
        },
        
        async sub(row) {
            let res = await this.$api.updatePushConfigStatus({
                id: row.id,
            })
            if (res.code == 200) {
                this.$message.success('操作成功')
                this.getList()
            }
        },
        async fetchcoin() {
            const res = await this.$api.queryCoinPairConfig({})
            if (res.code == 200) {
                // this.coinlist = res.result
                this.coinlist = res.result.data.map(item => ({
                    label: item.contractName,
                    value: item.contractName,
                }))
                // this.symbolList 为item.contractName去掉E-
                this.symbolList = res.result.data.map(item => ({
                    label: item.contractName.replace('E-', ''),
                    value: item.contractName.replace('E-', ''),
                }))

                // res.result.data.map(item => item.contractName.replace('E-', ''))
                // this.coinlist.push({
                //   label: 'E-BIT-USDT',
                //   value: 'E-BIT-USDT',
                // })
            }
        },
        async EditPopup(item) {
            // if (!item.fiat) {
            //     this.$router.push({
            //         name: 'CoinDetails',
            //         query: {
            //             item: JSON.stringify(item)
            //         }
            //     })

            // } else {
            //     this.$message.warning('该币种为法币，暂不支持编辑')
            // }
            // return
            // console.log(item);

            // let result = await this.$api.getFireblocksAssetPaged({

            // })

            // let res = await this.$api.getCoinSymbolConfigDetailPaged({
            //     // fromType: item.fromType,
            //     symbolConfigIdStr: item.idStr,
            //     symbol: item.symbol
            // })
            // if (res.code == 200) {

            // }
            this.dialogVisibleAddorUpdate = true
            this.dialogTitle = '编辑配置'
            this.tempCoin = item
            this.formDataAddorUpdate = item
            // this.editPopup = true
        },
        Update(row) {
            this.dialogVisibleAddorUpdate = true
            this.formDataAddorUpdate = row
        },
        formatUtcTime(utcDateStr) {
            // 截取需要的部分，假设输入总是有效的ISO 8601格式
            const date = utcDateStr.substr(0, 10); // 获取日期部分 '2025-03-07'
            const time = utcDateStr.substr(11, 8); // 获取时间部分 '07:29:04'

            // 合并日期和时间部分
            return `${date} ${time}`;
        },
        async submit() {
            console.log(this.formDataAddorUpdate);
            if (this.dialogTitle == '增加配置') {
                let res = await this.$api.addOrUpdatePushConfig({
                    ...this.formDataAddorUpdate,
                })
                if (res.code == 200) {
                    this.$message.success(res.msg)
                    this.dialogVisibleAddorUpdate = false
                    this.formDataAddorUpdate = {}
                    this.getList()
                }
            } else {
                let res = await this.$api.addOrUpdatePushConfig({
                    ...this.formDataAddorUpdate,
                })
                if (res.code == 200) {
                    this.$message.success(res.msg)
                    this.dialogVisibleAddorUpdate = false
                    this.formDataAddorUpdate = {}
                    this.getList()
                }
            }

        },
        toTemplatePage(item = {}) {
            // this.$store.commit('SAVE_INFO', item)
            // mapActions('save_stateInfo', item)
            if (item.businessType && item.businessType != 0) {
                this.$router.push({
                    name: 'NoticeEdit',
                    query: {
                        templateId: item.id,
                        businessType: item.businessType
                    }
                })
            } else {
                localStorage.setItem('noticeInfo', JSON.stringify(item))
                this.$router.push({
                    name: 'platformPublish',
                    query: {
                        templateId: item.id
                    }
                })
            }

        },
        nav_add() {
            this.dialogVisibleAddorUpdate = true
            this.dialogTitle = "增加配置"
            this.formDataAddorUpdate = {}

        },
        // 过滤查询
        onQueryChange(data) {
            this.query = data
            // if (!data.contractName && !data.symbol && !data.id && !data.contractType) {
            this.getList()
            // } else {
            // this.getpudan(true)
            // }
        },
        async getpudan(data) {
            let res = await this.$api.getOrderConfigByName({
                ...this.query
            })
            if (res.code == 200) {
                this.tableData = res.result.pageData.data
                // this.page.totalCount = res.result.total
            }
        },
        onRefresh(data) {
            this.query = data
            // this.getpudan(true)
            // if (!data.contractName) {
            this.getList()
            // } else {
            // this.getpudan(true)
            // }

        },
        // 分页改变
        currentChange(value) {
            this.page.pageNum = value
            this.getList()
        },
        // 分页改变
        currentChangeSize(value) {
            this.page.pageSize = value
            this.getList()
        },
        getList() {

            const params = {
                ...this.query,
                ...this.page,
                // pageNum: this.page.pageNum,
                // pageSize: this.page.pageSize,
                // fromType: 0,
                // fromValue: 'ETH_TESTS',
                // symbol: 'ETH'
            }
            let dataList = []
            this.$api.getAllPushConfig(params).then(res => {
                const data = res.result.data
                // data.forEach((item) => {
                //     dataList.push({
                //         ...item.onSaleExtra,
                //         ctid: item.ctid,
                //         startTime: item.startTime,
                //         endTime: item.endTime,
                //         dutyId: item.dutyId,
                //         createAt: item.createAt,
                //         dutyStatus: item.dutyStatus,
                //         remark: item.remark
                //     })
                // })
                this.tableData = data
                console.log(this.tableData, '')
                this.page.totalCount = res.result.total
                this.page.pageSize = res.result.pageSize
                this.page.pageCount = res.result.pages
            })
        },
        // 发表/下架
        tradeStop(id) {
            // this.$confirm(`确定终止该任务吗？`, '', {
            //   confirmButtonText: '确定',
            //   cancelButtonText: '取消',
            //   type: 'warning'
            // }).then(() => {

            // })
            tradeStop({
                dutyId: id,
                dutyType: 'ON_SALE'
            }).then((res) => {
                if (res.status.code == 0) {
                    this.$message.success(res.status.msg)
                    this.getList()
                }
            })
        },
        async onExport(title, id) {
            this.loadingText = "正在导出"
            this.loading = true
            const params = {
                dutyId: id,
                dutyType: 'ON_SALE'
            }
            const res = await this.$api.tradeRecordExport(params)
            if (res.retCode === 500) {
                this.$message.error(res.retMsg)
                this.loading = false
                this.getList()
            } else if (res.type === 'application/json') {
                // blob 转 JSON
                const enc = new TextDecoder('utf-8')
                res.arrayBuffer().then((buffer) => {
                    const data = JSON.parse(enc.decode(new Uint8Array(buffer))) || {}
                    this.$message.error(data.status?.msg)
                })
            } else {
                downloadBlob(res, title + Date.now() + '.csv')
                this.$message.success('明细导出成功')
                this.loading = false
                this.getList()
            }
        },
        openText(text) {
            console.log(text)
            this.$alert(text, '备注详情', {
                confirmButtonText: '确定',
                callback: action => {

                }
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.oneOver {
    display: inline-block;
    /*超出部分隐藏*/
    white-space: nowrap;
    overflow: hidden;
    /*不换行*/
    text-overflow: ellipsis;
    width: 120px;
    cursor: pointer;
    font-size: 12px;
    /*超出部分文字以...显示*/
}
</style>
