<template>
    <div v-if="localVisible" class="modal-mask">
        <div class="modal-wrapper">
            <div class="modal-header">
                <div class="left">
                    <img class="logo"
                        src="https://pro-oss.pinkwallet.com/image/1379519159477035008.png"
                        alt="logo" />
                    <span class="brand">PinkWallet</span>
                </div>
                <img class="close" @click="closeModal"
                    src="https://pro-oss.pinkwallet.com/image/1379224833215782912.png" alt="">
            </div>

            <div class="modal-content">
                <div class="invite-code-label">{{ $t('broker.invite_codes') }}</div>
                <div class="invite-code">{{ inviteCode }}</div>

                <div class="qrcode-wrapper">
                    <img v-if="qrImage" :src="qrImage"  />
                </div>

                <div class="footer-text">{{ $t('broker.apply_now_and_earn_coins') }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import QRCode from 'qrcode'
import { useI18n } from "vue-i18n";
const { t } = useI18n();
// 接收 props
const props = defineProps({
    inviteCode: {
        type: String,
        required: true
    },
    visible: {
        type: Boolean,
        default: false
    }
})

// 向父组件发出事件
const emit = defineEmits(['update:visible'])

const localVisible = ref(props.visible)
const qrImage = ref('')

const generateQRCode = async () => {
    const qrUrl = import.meta.env.VITE_APP_URL + '?code=' + props.inviteCode
    // ?code=${props.inviteCode}`
    qrImage.value = await QRCode.toDataURL(qrUrl, {
        width: 200,
        margin: 2
    })
}

watch(() => props.visible, (val) => {
    localVisible.value = val
    if (val) generateQRCode()
})

const closeModal = () => {
    emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.modal-mask {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.65);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-wrapper {
    background: #151515;
    border-radius: 20px;
    width: 394px;
    text-align: center;
    color: #fff;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 18px 24px 10px;
    border-bottom: 0.5px solid rgba(255, 255, 255, .1);

    .left {
        display: flex;
        align-items: center;

        .logo {
            width: 26px;
            height: 26px;
            margin-right: 14px;
        }

        .brand {
            font-family: MiSans;
            font-weight: 400;
            font-size: 16px;
            line-height: 20px;
            color: #fff;
        }

    }


    .close {
        cursor: pointer;
        width: 24px;
        height: 24px;
    }
}

.modal-content {
    margin-top: 38px;

    .invite-code-label {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: rgba(255, 255, 255, .5);
    }

    .invite-code {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: #fff;
        margin: 4px 0 6px 0;
    }

    .qrcode-wrapper {
        padding: 9px 12px;
        background: rgba(255, 255, 255, .1);
        display: inline-block;
        border-radius: 20px;
        margin-bottom: 12px;

        img {
            width: 171px;
            height: 176px;
        }
    }

    .footer-text {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: #FFFFFF;
        margin-bottom: 92px;
    }
}
</style>