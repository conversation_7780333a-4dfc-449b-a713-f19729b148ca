<template>
    <div class="partner-program">
        <!-- 顶部介绍 -->
        <section class="hero">
            <div class="hero-left">
                <div class="title">{{ $t('broker.join_title') }}</div>
                <div class="desc">
                    {{ $t('broker.join_desc') }}
                </div>
                <div class="gobe" @click="postshow = true">
                    {{ $t('broker.invite_commission') }}
                    <img src="https://pro-oss.pinkwallet.com/image/1377952497078460416.png"
                        alt="">
                </div>
                <div class="gopar">
                    <view class="join-btn" @click="visible = true">{{ $t('broker.become_broker') }}</view>
                    <div class="share" @click="postshow = true">
                        <img src="https://pro-oss.pinkwallet.com/image/1377954629240315904.png"
                            alt="">
                    </div>
                </div>
            </div>
            <div class="hero-right">
                <img src="https://pro-oss.pinkwallet.com/image/1377745403372986368.png"
                    alt="hero-img" />
            </div>
        </section>

        <!-- 代理商权益 -->
        <section class="benefits">
            <div class="title-container-card">
                <span class="title">{{ $t('broker.benefits_title') }}</span>
            </div>
            <div class="cards">
                <div v-for="(item, i) in benefits" :key="i" class="card">
                    <img :src="item.icon" class="icon" />
                    <span class="text">{{ item.text }}</span>
                </div>
            </div>
        </section>

        <!-- swiper 代理商如何运营 -->
        <section class="swiper-section">
            <div class="title-container-card">
                <span class="title">{{ $t('broker.operation_title') }}</span>
            </div>
            <div class="swiper" v-for="(item, index) in swiperList" :key="index">
                <div class="view_grid" v-if="currentIndex == index" :class="[`slide_${slideDirection}`]">
                    <div class="view_container">
                        <div class="left_view">
                            <div class="text">
                                <span class="title">{{ item.title }}</span>
                                <span class="desc">{{ item.desc }}</span>
                            </div>
                            <div class="carousel-controls">
                                <div class="arrows">
                                    <div class="arrow-btn" @click="prevPage(index)">
                                        <img
                                            src="https://pro-oss.pinkwallet.com/image/1378076261296463872.png" />
                                    </div>
                                    <div class="arrow-btn rotate" @click="nextPage(index)">
                                        <img
                                            src="https://pro-oss.pinkwallet.com/image/1378076261296463872.png" />
                                    </div>
                                </div>
                                <div class="dots">
                                    <div v-for="(item, i) in swiperList" :key="i"
                                        :class="['dot', { active: currentIndex == i }]"></div>
                                </div>
                            </div>
                        </div>
                        <div class="right_view">
                            <img :src="item.img" :style="{ width: item.w + 'px', height: item.h + 'px' }" alt="" />
                        </div>
                    </div>
                    <!-- <div class="view_bottom right mt_48" @click="nextPage(1)">
                        <div class="right_icon">
                            <img src="https://pro-oss.pinkwallet.com/image/1377356567308492800.png" alt="" srcset="">
                        </div>
                    </div> -->
                </div>
            </div>
        </section>

        <!-- 品类展示 -->
        <section class="categories">
            <div class="title-container-card">
                <span class="title">{{ $t('broker.categories_title') }}</span>
            </div>
            <div class="cards">
                <div v-for="(item, i) in categories" :key="i" class="card">
                    <img :src="item.icon" class="icon" />
                    <p class="text">{{ item.text }}</p>
                </div>
            </div>
        </section>

        <!-- 合规牌照 -->
        <section class="compliance">
            <div class="title-container-card">
                <span class="title">{{ $t('broker.compliance_title') }}</span>
            </div>
            <div class="cards-compliance">
                <div v-for="(item, i) in licenses" :key="i" class="card-compliance">
                    <div class="img">
                        <img :src="item.icon" class="icon" mode="heightFixed" />
                        <img class="checked"
                            src="https://pro-oss.pinkwallet.com/image/1377973040368410624.png"
                            alt="">
                    </div>
                    <p class="text">{{ item.text }}</p>
                </div>
            </div>
        </section>

        <!-- FAQ -->
        <section class="faq">
            <div class="title-container-card">
                <span class="title">FAQ</span>
            </div>

            <div v-for="(item, index) in faqListZh" :key="index" class="faq-item">
                <div class="faq-question" @click="toggle(index)">
                    <span>{{ item.question }}</span>
                    <img class="arrow-icon" :class="{ rotated: activeIndex === index }"
                        src="https://pro-oss.pinkwallet.com/image/1377683340055371776.png"
                        alt="arrow" />
                </div>

                <transition name="faq-slide">
                    <div class="faq-answer" v-show="activeIndex === index">
                        {{ item.answer }}
                    </div>
                </transition>
            </div>

        </section>


        <!-- 底部按钮 -->
        <footer class="footer">
            <!-- <button class="footer-btn"></button>
            <button class="login-btn">登录</button> -->

            <div class="inner">
                <span>{{ $t("broker.apply_step1") }}</span>
                <div class="btns">
                    <div class="login-btn" @click="visible = true">{{ $t("broker.apply_now") }}</div>
                    <div class="contact-btn" @click="openlink">{{ $t("broker.contact_specialist") }}</div>
                </div>
            </div>
        </footer>
    </div>

    <div v-show="visible" class="modal-mask">
        <transition name="fade-zoom">
            <div class="modal-wrapper">
                <div class="modal-container">
                    <!-- Header -->
                    <div class="modal-header">
                        <div class="title">{{ $t('broker.apply_agent_content') }}</div>

                        <img class="close-btn" @click="visible = false"
                            src="https://pro-oss.pinkwallet.com/image/1379224833215782912.png"
                            alt="">
                    </div>

                    <!-- Form -->
                    <div class="modal-body">

                        <div class="sub-title">{{ $t('broker.apply_agent_content_1') }}</div>
                        <div class="email-box">
                            <div class="email">
                                <img src="https://pro-oss.pinkwallet.com/image/1379228054437060608.png"
                                    alt="">
                                {{ userInfo.email || '--' }}
                            </div>
                            <div class="uid">UID: {{ uid }}</div>
                        </div>

                        <div class="form-group">
                            <div class="required label">{{ $t('broker.apply_agent_content_2') }}</div>
                            <div class="form-row">
                                <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_5')"
                                    v-model="form.name" />
                                <input type="text" maxlength="30" disabled :value="userInfo.email" />
                            </div>

                            <div class="form-row">
                                <input type="text" maxlength="30" class="full"
                                    :placeholder="$t('broker.apply_agent_content_6')" v-model="form.telegram" />
                                <div></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="required label">{{ $t('broker.apply_agent_content_7') }}</div>
                            <div class="form-row">
                                <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_7')"
                                    v-model="form.address" />
                                <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_8')"
                                    v-model="form.BDcode" />
                            </div>


                        </div>

                        <div class="form-group" style="margin-bottom: 0;">
                            <div class="required label">{{ $t('broker.apply_agent_content_3') }}</div>

                            <div class="proof" v-if="hasX">
                                <div class="labelX">
                                    <img src="https://pro-oss.pinkwallet.com/image/1379492820443291648.png"
                                        alt="">
                                    X
                                </div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_9')"
                                        v-model="formPl.Xlink" />
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_10')"
                                        v-model="formPl.Xfans" />
                                </div>
                            </div>

                            <div class="proof" v-if="hasYoutube">
                                <div class="labelX">
                                    <img src="https://pro-oss.pinkwallet.com/image/1379492820443291648.png"
                                        alt="">
                                    Youtube
                                </div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_9')"
                                        v-model="formPl.YoutubeLink" />
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_10')"
                                        v-model="formPl.YoutubeFans" />
                                </div>
                            </div>

                            <div class="proof" v-if="hasFacebook">
                                <div class="labelX">
                                    <img src="https://pro-oss.pinkwallet.com/image/1379492820443291648.png"
                                        alt="">
                                    Facebook
                                </div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_9')"
                                        v-model="formPl.FacebookLink" />
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_10')"
                                        v-model="formPl.FacebookFans" />
                                </div>
                            </div>

                            <div class="proof" v-if="hasTikTok">
                                <div class="labelX">
                                    <img src="https://pro-oss.pinkwallet.com/image/1379492820443291648.png"
                                        alt="">
                                    TikTok
                                </div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_9')"
                                        v-model="formPl.TikTokLink" />
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_10')"
                                        v-model="formPl.TikTokFans" />
                                </div>
                            </div>

                            <div class="proof" v-if="hasTelegram">
                                <div class="labelX">
                                    <img src="https://pro-oss.pinkwallet.com/image/1379492820443291648.png"
                                        alt="">
                                    Telegram
                                </div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_14')"
                                        v-model="formPl.TelegramLink" />
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_15')"
                                        v-model="formPl.TelegramPersonalLink" />
                                </div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" class="full"
                                        :placeholder="$t('broker.apply_agent_content_16')"
                                        v-model="formPl.TelegramChannelNum" />
                                    <div></div>
                                </div>
                            </div>

                            <div class="proof" v-if="hasOthers">
                                <div class="labelX">
                                    <img src="https://pro-oss.pinkwallet.com/image/1379492820443291648.png"
                                        alt="">
                                    Others
                                </div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_11')"
                                        v-model="formPl.OthersName" />
                                    <input type="text" maxlength="30" :placeholder="$t('broker.apply_agent_content_12')"
                                        v-model="formPl.OthersLink" />
                                </div>
                                <div class="form-row">
                                    <input type="text" maxlength="30" class="full"
                                        :placeholder="$t('broker.apply_agent_content_13')"
                                        v-model="formPl.OthersFans" />
                                    <div></div>
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <label v-for="(item, i) in platforms" :key="i">
                                    <input type="checkbox" v-model="form.selectedPlatforms" :value="item" />
                                    {{ item }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="modal-footer">
                        <button class="submit-btn" :disabled="!form.name" @click="submitForm">{{
                            $t('broker.apply_agent_submit') }}</button>
                    </div>
                </div>
            </div>
        </transition>
    </div>


    <SharePosterModal v-model:visible="postshow" mode="2" :link="url" />
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { getInviteCode, submitAgentApply } from '../../api/pinkexchange'
import SharePosterModal from "./component/SharePosterModal.vue"
import { GetUserInfo } from "../../api/pinkwallet"

import { useI18n } from "vue-i18n";
const postshow = ref(false)
const inviteCodes = ref({})

const { t } = useI18n();
const activeIndex = ref(null)
const currentSwiperIndex = ref(0);
const currentIndex = ref(0);
const slideDirection = ref('right');
const visible = ref(false);
const userInfo = ref({})
const uid = localStorage.getItem("uid") || ''

const form = reactive({
    name: '',
    telegram: '',
    influence: [],
    selectedPlatforms: [],
    platformData: {} // 存储每个平台的三个字段
});

const formPl = reactive({})

const platforms = ['X', 'Youtube', 'Facebook', 'TikTok', 'Telegram', 'Others'];

const hasX = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'x')
})

const hasYoutube = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'youtube')
})

const hasFacebook = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'facebook')
})

const hasTikTok = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'tiktok')
})

const hasTelegram = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'telegram')
})

const hasOthers = computed(() => {
    return form.selectedPlatforms.length > 0 && form.selectedPlatforms.some(item => item.toLowerCase() == 'others')
})

const fetchGetUserInfo = async () => {
    let res = await GetUserInfo()
    if (res.code == 200) {
        userInfo.value = res.result
    }
}
fetchGetUserInfo()

const submitForm = async () => {
    let data = {
        // name: form.name,
        // // phone: form.telegram,
        // email: userInfo.email,
        // otherContracts: "",
        // address: form.address,
        // bdCode: form.BDcode,
        // influenceProof: JSON.stringify(formPl),
        name: form.name,
        // phone: form.telegram,
        email: userInfo.value.email,
        // form.telegram
        otherContracts: "",
        address: form.address,
        bdCode: form.BDcode,
        influenceProof: JSON.stringify(formPl),
    }
    let res = await submitAgentApply(data)
    if (res.code == 200) {
        visible.value = false;
    }
    // console.log('提交信息：', form.value);
    // 提交逻辑
};



const url = ref('')

const getInviteCodes = async () => {
    let res = await getInviteCode()
    if (res.code == 200) {
        url.value = import.meta.env.VITE_APP_URL + '?code=' + res.result.inviteCode
        inviteCodes.value = res.result
    }
}
getInviteCodes()

const handleClick = (index) => {
    activeIndex.value = index
}

const swiperList = computed(() => [
    {
        title: t("broker.apply_step1"),
        desc: t("broker.apply_step1_desc"),
        img: "https://pro-oss.pinkwallet.com/image/1378070623594045440.png",
        w: 507,
        h: 292
    },
    {
        title: t("broker.apply_step2"),
        desc: t("broker.apply_step2_desc"),
        img: "https://pro-oss.pinkwallet.com/image/1378070928142458880.png",
        w: 488,
        h: 315
    },
    {
        title: t("broker.apply_step3"),
        desc: t("broker.apply_step3_desc"),
        img: "https://pro-oss.pinkwallet.com/image/1378071309429858304.png",
        w: 452,
        h: 374
    },
    {
        title: t("broker.apply_step4"),
        desc: t("broker.apply_step4_desc"),
        img: "https://pro-oss.pinkwallet.com/image/1378071424118906880.png",
        w: 490,
        h: 240
    }
])

const prevPage = (index) => {
    console.log(index, swiperList.value.length);

    if (index <= 0) {
        currentIndex.value = swiperList.value.length - 1;
        return
    }

    if (index > currentIndex.value) {
        slideDirection.value = 'right';
    } else {
        slideDirection.value = 'left';
    }


    currentIndex.value--;
}

const nextPage = (index) => {
    console.log(index, swiperList.value.length);
    if (index >= swiperList.value.length - 1) {
        currentIndex.value = 0;
        return
    }

    if (index > currentIndex.value) {
        slideDirection.value = 'right';
    } else {
        slideDirection.value = 'left';
    }
    currentIndex.value++;

}

const openlink = () => {
    window.open('https://t.me/+U5S8KFdz7_AzMDM0')
}



const toggle = (index) => {
    activeIndex.value = activeIndex.value === index ? null : index
}



const faqListZh = computed(() => [
    {
        question: t("broker.faq_question1"),
        answer: t("broker.faq_answer1")
    },
    {
        question: t("broker.faq_question2"),
        answer: t("broker.faq_answer2")
    },
    {
        question: t("broker.faq_question3"),
        answer: t("broker.faq_answer3")
    },
    {
        question: t("broker.faq_question4"),
        answer: t("broker.faq_answer4")
    },
    {
        question: t("broker.faq_question5"),
        answer: t("broker.faq_answer5")
    }]);

const benefits = computed(() => [
    { icon: "https://pro-oss.pinkwallet.com/image/1377745577424019456.png", text: t("broker.benefit1") },
    { icon: "https://pro-oss.pinkwallet.com/image/1377745683565076480.png", text: t("broker.benefit2") },
    { icon: "https://pro-oss.pinkwallet.com/image/1377745790314307584.png", text: t("broker.benefit3") }
])

const categories = computed(() => [
    { icon: "https://pro-oss.pinkwallet.com/image/1377746092459384832.png", text: t("broker.category1") },
    { icon: "https://pro-oss.pinkwallet.com/image/1377746272919314432.png", text: t("broker.category2") },
    { icon: "https://pro-oss.pinkwallet.com/image/1377746394361192448.png", text: t("broker.category3") }
])

const licenses = computed(() => [
    { icon: "https://pro-oss.pinkwallet.com/image/1377746683847860224.png", text: t("broker.license1") },
    { icon: "https://pro-oss.pinkwallet.com/image/1377746807646937088.png", text: t("broker.license2") },
    { icon: "https://pro-oss.pinkwallet.com/image/1377746807646937088.png", text: t("broker.license3") }
])

</script>

<style lang="scss" scoped>
.modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.65);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;

    .modal-wrapper {
        width: 910px;
        background: #151515;

        border-radius: 20px;
        color: #fff;
        position: relative;

        .modal-container {
            display: flex;
            flex-direction: column;

            .modal-header {
                // padding: 22px 18px 48px 20px;
                padding: 22px 18px 12.5px 20px;

                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 0.5px solid rgba(255, 255, 255, .1);

                .title {
                    font-family: PingFang SC;
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 20px;
                    letter-spacing: 0px;
                    color: #fff;
                }


                .close-btn {
                    cursor: pointer;
                    width: 24px;
                    height: 24px;
                }
            }

            .modal-body {
                padding: 10.5px 18px 0 20px;
                max-height: 600px;
                overflow-y: auto;

                .sub-title {
                    text-align: left;
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 20px;
                    color: #8A8A8A;
                    // margin-top: 6px;
                }

                .email-box {
                    background: #212121;
                    border: 1px solid #3E3E3E;
                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 100%;
                    color: rgba(255, 255, 255, .5);
                    border-radius: 12px;
                    margin-top: 16.5px;
                    padding: 9px 22px 8px 12px;
                    display: flex;
                    justify-content: space-between;

                    .uid {

                        display: flex;
                        align-items: center;
                    }

                    .email {
                        display: flex;
                        align-items: center;
                        margin-right: 4px;

                        img {
                            width: 34px;
                            height: 34px;
                        }
                    }
                }

                .form-group {
                    // margin-bottom: 20px;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;

                    .proof {
                        .labelX {
                            text-align: left;
                            margin: 4px 0 13px 0;
                            font-size: 14px;
                            // margin-bottom: 8px;
                            // display: inline-block;
                            color: #fff;
                            font-family: PingFang SC;
                            font-weight: 600;
                            font-size: 16px;
                            line-height: 20px;
                            display: flex;
                            align-items: center;

                            img {
                                margin-right: 8px;
                                width: 12px;
                                height: 12px;
                            }
                        }
                    }


                    .label {
                        text-align: left;
                        margin: 24px 0 13px 0;
                        font-size: 14px;
                        // margin-bottom: 8px;
                        // display: inline-block;
                        color: #fff;
                        font-family: PingFang SC;
                        font-weight: 600;
                        font-size: 16px;
                        line-height: 20px;
                    }

                    .required::before {
                        content: "* ";
                        color: #EF88A3;
                    }

                    .form-row {
                        display: flex;
                        gap: 14px;
                        margin-bottom: 14px;

                        div {
                            flex: 1;
                            padding: 16px 22px;

                        }

                        input {
                            flex: 1;
                            background: #212121;
                            border: 1px solid #3E3E3E;

                            font-family: MiSans;
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 100%;
                            color: #fff;
                            border-radius: 12px;
                            padding: 16px 22px;

                            &:focus {
                                outline: none;
                                // border: none;
                                box-shadow: none;
                            }
                        }

                        input[disabled] {
                            opacity: 0.5;
                        }
                    }

                    .full {
                        flex: 1;
                        background: #212121;
                        border: 1px solid #3E3E3E;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 100%;
                        color: #fff;
                        border-radius: 12px;
                        padding: 16px 22px;

                        // margin-left: 14px;
                        &:focus {
                            outline: none;
                            // border: none;
                            box-shadow: none;
                        }
                    }

                    .checkbox-group {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 24px;
                        // margin-top: 17px;
                        cursor: pointer;

                        label {
                            display: flex;
                            align-items: center;
                            gap: 6px;
                            font-size: 14px;
                            cursor: pointer;

                            input {
                                accent-color: #fff;
                            }
                        }
                    }
                }
            }

            .modal-footer {
                text-align: right;
                margin: 12px 18px 48px 0;
                display: flex;
                justify-content: flex-end;

                .submit-btn {
                    width: 130px;
                    height: 49px;
                    border-radius: 12px;
                    background: #EF88A3;
                    color: #fff;
                    border: none;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: MiSans;
                    font-weight: 500;
                    font-size: 14px;


                    &:not(:disabled) {
                        opacity: 1;
                    }

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }

                    &:focus,
                    &:active {
                        outline: none;
                        border: none;
                        box-shadow: none;
                    }
                }
            }

            .fade-zoom-enter-active,
            .fade-zoom-leave-active {
                transition: opacity 0.3s ease, transform 0.3s ease;
            }

            .fade-zoom-enter-from,
            .fade-zoom-leave-to {
                opacity: 0;
                transform: scale(0.95);
            }

            .fade-zoom-enter-to,
            .fade-zoom-leave-from {
                opacity: 1;
                transform: scale(1);
            }
        }

    }

}

.partner-program {
    .section-title {
        font-size: 22px;
        text-align: center;
        margin: 40px 0 20px;
    }

    .title-container-card {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        // opacity: 0;
        // margin-bottom: 120px;
        // padding-top: -60px;

        transition: all 0.8s;

        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            color: white;
            position: relative;
            // padding: 0 16px;

            font-family: MiSans-bold;
            font-weight: 700;
            font-size: 36px;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px; // 横线宽度
                height: 2px; // 横线高度
                background-color: rgba(255, 255, 255, 0.2);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }
    }

    .hero {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 111px;
        max-width: 1280px;
        margin: 100px auto 0 auto;

        .hero-left {
            .title {
                width: 378px;
                white-space: wrap;
                font-family: MiSans;
                font-weight: 700;
                font-size: 48px;
                line-height: 100%;
                color: #fff;
                text-align: left;
            }

            .desc {
                width: 490px;
                white-space: wrap;
                font-family: MiSans;
                font-weight: 400;
                font-size: 18px;
                line-height: 137%;
                color: #fff;
                margin: 24px 0 10px 0;
                opacity: 0.7;
                text-align: left;
            }

            .gobe {
                cursor: pointer;
                font-family: MiSans;
                font-weight: 500;
                font-size: 16px;
                line-height: 150%;
                text-align: left;
                color: #EF88A3;
                display: flex;
                align-items: center;
                gap: 8px;

                img {
                    width: 24px;
                    height: 19px;
                }
            }

            .gopar {
                margin-top: 44px;
                display: flex;
                align-items: center;
                justify-content: flex-start;
                gap: 12px;

                .join-btn {
                    white-space: nowrap;
                    cursor: pointer;
                    // width: 178px;
                    padding: 0 20px;
                    height: 54px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #EF88A3;
                    color: #fff;
                    border: none;
                    border-radius: 12px;
                    font-family: MiSans;
                    font-weight: 600;
                    font-size: 20px;

                }

                .share {
                    width: 54px;
                    height: 54px;
                    border-radius: 18.13px;
                    background: #141414;
                    border: 1.13px solid #363636;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    img {
                        width: 24px;
                        height: 24px;
                    }
                }
            }

        }


        .hero-right {
            img {
                width: 422px;
                height: 397px;
            }
        }
    }

    .benefits {
        max-width: 1280px;
        margin: 110px auto 0 auto;
    }

    .cards {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // flex-wrap: wrap;
        gap: 9px;
        margin-top: 47px;

        .card {
            cursor: pointer;
            min-width: 421px;
            min-height: 202px;
            border-radius: 18.13px;
            background: #141414;
            border: 1.13px solid #363636;
            padding: 0;
            // padding: 20px;
            flex: 1;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;

            .icon {
                width: 59px;
                height: 59px;
            }

            .text {
                font-family: MiSans;
                font-weight: 600;
                font-size: 24px;
                line-height: 150%;
                text-align: center;
                color: #fff;
                margin-top: 3px;
            }
        }
    }

    .swiper-section {
        // margin: 60px 0;
        max-width: 1280px;
        height: 400px;
        margin: 140px auto 0 auto;

        .swiper {
            margin-top: 47px;

            .view_grid {
                cursor: pointer;
                // width: 884px;
                // max-width: 884px;
                // background-color: #222;
                // border-radius: px;
                // padding: px(50) px(48);
                margin: 0 auto;
                // border: 1px solid #4B4B4B;
                position: relative;
                overflow: hidden;
                transition: all 0.5s ease-in-out;
                opacity: 0;

                &.slide_right {
                    animation: slideRight 0.5s forwards;
                }

                &.slide_left {
                    animation: slideLeft 0.5s forwards;
                }

                .view_container {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .left_view {
                        // width: 300px;
                        // background-color: #222;
                        // border-radius: 16px;
                        // padding: px(14) 0;

                        .text {
                            text-align: left;
                            display: flex;
                            flex-direction: column;

                            .title {
                                width: 430px;
                                white-space: wrap;
                                font-family: MiSans;
                                font-weight: 700;
                                font-size: 48px;
                                line-height: 100%;
                                color: #fff;
                            }

                            .desc {
                                width: 416px;
                                white-space: wrap;
                                margin-top: 14px;
                                font-family: MiSans;
                                font-weight: 400;
                                font-size: 18px;
                                line-height: 137%;
                                color: rgba(255, 255, 255, .7);
                            }
                        }

                        .carousel-controls {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-top: 122px;

                            .arrows {
                                display: flex;
                                gap: 20px;
                                align-items: flex-end;

                                .arrow-btn {
                                    cursor: pointer;
                                    transition: background 0.3s;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    width: 52px;
                                    height: 52px;
                                    border-radius: 50%;
                                    background: #141414;
                                    border: 1.13px solid #363636;

                                    img {
                                        width: 16px;
                                        height: 11px;
                                    }
                                }

                                .rotate {
                                    transform: rotate(180deg);
                                }

                                .arrow-btn:hover {
                                    background: rgba(255, 255, 255, 0.1);
                                }
                            }


                            .dots {
                                display: flex;
                                gap: 10px;

                                .dot {
                                    height: 10px;
                                    border-radius: 12px;
                                    background: rgba(255, 255, 255, .5);
                                    transition: all 0.3s ease;
                                    width: 10px;
                                }

                                .dot.active {
                                    background: #fff;
                                    width: 54px;
                                }
                            }


                        }



                    }

                    .right_view {
                        // width: 324px;
                        rotate: 1.95 deg;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                }

            }

            @keyframes slideRight {
                0% {
                    transform: translateX(100%);
                    opacity: 0;
                }

                100% {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideLeft {
                0% {
                    transform: translateX(-100%);
                    opacity: 0;
                }

                100% {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        }

        .my-swiper {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;

            .slide-content {
                text-align: center;

                .slide-title {
                    font-size: 18px;
                    margin-bottom: 10px;
                }

                .slide-img {
                    width: 100%;
                    max-width: 300px;
                    border-radius: 12px;
                }
            }
        }
    }

    .categories {
        margin-top: 157px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        height: 495px;
        background: #FF95B2;

        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;

            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-size: 18px;
                font-weight: bold;
                position: relative;
                // padding: 0 16px;

                font-family: MiSans-bold;
                font-weight: 700;
                font-size: 36px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: #000;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: 76px; // 横线宽度
                    height: 2px; // 横线高度
                    background-color: rgba(0, 0, 0, 0.2);
                }

                &::before {
                    left: -100px;
                }

                &::after {
                    right: -100px;
                }
            }
        }

        .card {
            background: #0A0A0A;
            border: 1.13px solid #0A0A0A
        }
    }

    .compliance {
        max-width: 1213px;
        margin: 183px auto 0 auto;

        .cards-compliance {
            display: flex;
            justify-content: space-between;
            align-items: center;
            // flex-wrap: wrap;
            gap: 9px;
            margin-top: 48px;

            .card-compliance {
                cursor: pointer;
                min-width: 421px;
                min-height: 202px;
                border-radius: 18.13px;
                // background: #141414;
                // border: 1.13px solid #363636;
                padding: 0;
                // padding: 20px;
                flex: 1;
                text-align: center;
                display: flex;
                // justify-content: center;
                align-items: center;
                flex-direction: column;

                .img {
                    position: relative;

                    .checked {
                        position: absolute;
                        width: 26px;
                        height: 26px;
                        right: -8px;
                        bottom: 4px;
                    }
                }

                .icon {
                    // width: 59px;
                    // 
                    height: 59px;
                }

                .text {
                    font-family: MiSans;
                    font-weight: 600;
                    font-size: 24px;
                    line-height: 150%;
                    text-align: center;
                    color: #fff;
                    margin-top: 3px;
                }
            }
        }

    }

    .faq {
        margin: 117px auto 0 auto;
        max-width: 1280px;

        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;
            margin-bottom: 8px;
            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-size: 18px;
                font-weight: bold;
                color: white;
                position: relative;
                // padding: 0 16px;

                font-family: MiSans-bold;
                font-weight: 700;
                font-size: 36px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: #fff;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: 76px; // 横线宽度
                    height: 2px; // 横线高度
                    background-color: rgba(255, 255, 255, 0.2);
                }

                &::before {
                    left: -100px;
                }

                &::after {
                    right: -100px;
                }
            }
        }


        .faq-item {
            border-bottom: 1px solid rgba(255, 255, 255, .1);
            padding: 40px 0 32px 0;
        }

        .faq-question {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s ease;

            span {
                font-family: MiSans;
                font-weight: 600;
                font-size: 24px;
                line-height: 32px;
                color: #fff;
            }
        }



        .arrow-icon {
            width: 29px;
            height: 29px;
            transition: transform 0.3s ease;
        }

        .arrow-icon.rotated {
            transform: rotate(180deg);
        }

        .faq-answer {
            overflow: hidden;
            padding-top: 24px;
            color: rgba(255, 255, 255, .8);
            font-family: MiSans;
            font-weight: 400;
            font-size: 16px;
            // line-height: 100%;
            text-align: left;

        }

        /* 动效过渡 */
        .faq-slide-enter-active,
        .faq-slide-leave-active {
            transition: all 0.3s ease;
        }

        .faq-slide-enter-from,
        .faq-slide-leave-to {
            opacity: 0;
            max-height: 0;
        }

        .faq-slide-enter-to,
        .faq-slide-leave-from {
            opacity: 1;
            max-height: 200px;
        }

        // .faq-list {
        //     margin-top: 8px;
        //     .faq-item {
        //         background-color: #111;
        //         border-radius: 10px;
        //         padding: 16px;
        //         margin-bottom: 12px;

        //         .question {
        //             font-size: 14px;
        //             color: #fff;
        //         }
        //     }
        // }
    }

    .footer {
        margin-top: 120px;
        text-align: center;
        // background-image: url("https://pro-oss.pinkwallet.com/image/1377684264547082240.png");
        // background-size: 100% 125%;
        background: #151515;
        height: 338px;
        color: #fff;

        .inner {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 21px;
        }

        span {
            font-family: MiSans;
            font-weight: 700;
            font-size: 40px;
            line-height: 53px;
        }

        .btns {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
        }

        .contact-btn {
            border: 1px solid #EF88A3;
            color: #EF88A3;
            font-family: MiSans;
            font-weight: 500;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 178px;
            height: 54px;
            border-radius: 12px;
            cursor: pointer;
        }

        .login-btn {
            background-color: #EF88A3;
            border: none;
            // padding: 12px 30px;
            // border-radius: 20px;
            font-family: MiSans;
            font-weight: 500;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 178px;
            height: 54px;
            border-radius: 12px;
            cursor: pointer;
        }
    }
}
</style>