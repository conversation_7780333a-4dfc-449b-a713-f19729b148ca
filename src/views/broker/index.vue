<template>
    <div class="referral-page">
        <!-- 顶部介绍区域 -->
        <section class="intro">
            <div class="left">
                <span class="tag">{{ $t('broker.invite') }}</span>
                <span class="title">{{ $t('broker.invite_title') }}</span>
                <div class="plan-btn" @click="nav_to('login')">{{ $t('broker.plan_btn') }}</div>
            </div>

            <img class="hero-img"
                src="https://pro-oss.pinkwallet.com/image/1377671534675058688.png"
                alt="illustration" />
        </section>

        <!-- 三个步骤 {{ $t('broker.plan_btn') }}-->
        <section class="steps">
            <div class="title-container-card">
                <span class="title"> {{ $t('broker.section_title') }}</span>
            </div>
            <div class="cards">
                <div class="card" v-for="item in stepCards" :key="item.title">
                    <img :src="item.icon" class="icon" />
                    <span class="card-title">{{ item.title }}</span>
                    <span class="card-desc">{{ item.desc }}</span>
                </div>
            </div>
        </section>

        <!-- FAQ -->
        <section class="faq">
            <div class="title-container-card">
                <span class="title">FAQ</span>
            </div>

            <div v-for="(item, index) in faqListZh" :key="index" class="faq-item">
                <div class="faq-question" @click="toggle(index)">
                    <span>{{ item.question }}</span>
                    <img class="arrow-icon" :class="{ rotated: activeIndex === index }"
                        src="https://pro-oss.pinkwallet.com/image/1377683340055371776.png"
                        alt="arrow" />
                </div>

                <transition name="faq-slide">
                    <div class="faq-answer" v-show="activeIndex === index">
                        {{ item.answer }}
                    </div>
                </transition>
            </div>
        </section>

        <!-- 底部按钮 -->
        <footer class="footer">
            <div class="inner">
                <span>{{ $t('broker.footer_btn') }}</span>
                <div class="login-btn" @click="nav_to('login')">{{ $t('broker.footer_login') }}</div>
            </div>
        </footer>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const { t } = useI18n();
const activeIndex = ref(null)

const toggle = (index) => {
    activeIndex.value = activeIndex.value === index ? null : index
}
const nav_to = (e) => {
    router.push({
        path: e,
        // query: {
        //     title: '666'
        // }
    })
}


const stepCards = computed(() => [
    {
        icon: "https://pro-oss.pinkwallet.com/image/1377671064850096128.png",
        title: t("broker.step_1_title"),
        desc: t("broker.step_1_desc")
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/1377671184031244288.png",
        title: t("broker.step_2_title"),
        desc: t("broker.step_2_desc")
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/1377671326096515072.png",
        title: t("broker.step_3_title"),
        desc: t("broker.step_3_desc")
    }
])


const faqListZh = computed(() => [
    {
        question: t("broker.faq_1_q"),
        answer: t("broker.faq_1_a")
    },
    {
        question: t("broker.faq_2_q"),
        answer: t("broker.faq_2_a")
    },
    {
        question: t("broker.faq_3_q"),
        answer: t("broker.faq_3_a")
    },
    {
        question:t("broker.faq_4_q"),
        answer: t("broker.faq_4_a")
    }]);
</script>

<style lang="scss" scoped>
.referral-page {
    // padding: 40px 20px;
    min-height: 100vh;
    margin: 0 auto;

    .intro {
        margin-top: 186px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        gap: 216px;

        .left {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .tag {
                font-family: MiSans;
                font-weight: 600;
                font-size: 20px;
                line-height: 27px;
                color: #EF88A3;
            }

            .title {
                font-family: MiSans;
                font-weight: 700;
                font-size: 54px;
                line-height: 72px;
                color: #fff;
                margin: 4px 0 36px 0;
            }

            .plan-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: #EF88A3;
                color: #fff;
                border: none;
                width: 178px;
                height: 54px;
                border-radius: 12px;
                font-family: MiSans;
                font-weight: 600;
                font-size: 20px;

            }
        }



        .hero-img {
            display: flex;
            align-items: center;
            cursor: pointer;
            width: 476px;
            height: 382px;
            // margin: 0 auto;
            display: block;
        }
    }

    .steps {
        margin-top: 110px;


        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;

            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-size: 18px;
                font-weight: bold;
                color: white;
                position: relative;
                // padding: 0 16px;

                font-family: MiSans-bold;
                font-weight: 700;
                font-size: 36px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: #fff;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: 76px; // 横线宽度
                    height: 2px; // 横线高度
                    background-color: rgba(255, 255, 255, 0.2);
                }

                &::before {
                    left: -100px;
                }

                &::after {
                    right: -100px;
                }
            }
        }

        .cards {
            margin: 47px auto 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 9px;
            max-width: 1280px;

            .card {
                cursor: pointer;
                padding: 0;
                // flex: 1;
                min-height: 231px;
                min-width: 421px;
                background: #141414;
                border: 1.13px solid #363636;
                border-radius: 18px;
                // padding: 20px;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                .icon {
                    width: 59px;
                    height: 59px;
                }

                .card-title {
                    font-family: MiSans;
                    font-weight: 600;
                    font-size: 24px;
                    line-height: 150%;
                    text-align: center;
                    color: #fff;
                    margin: 3px 0 9px 0;
                }

                .card-desc {
                    width: 240px;
                    white-space: wrap;
                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 18px;
                    line-height: 150%;
                    text-align: center;
                    color: rgba(255, 255, 255, .5);
                }
            }
        }
    }

    .faq {
        margin: 117px auto 0 auto;
        max-width: 1280px;

        .title-container-card {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;
            // opacity: 0;
            // margin-bottom: 120px;
            // padding-top: -60px;
            margin-bottom: 8px;
            transition: all 0.8s;

            &.loaded-title {
                transform: translateY(60px);
                opacity: 1;
            }

            .title {
                font-size: 18px;
                font-weight: bold;
                color: white;
                position: relative;
                // padding: 0 16px;

                font-family: MiSans-bold;
                font-weight: 700;
                font-size: 36px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: #fff;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    width: 76px; // 横线宽度
                    height: 2px; // 横线高度
                    background-color: rgba(255, 255, 255, 0.2);
                }

                &::before {
                    left: -100px;
                }

                &::after {
                    right: -100px;
                }
            }
        }


        .faq-item {
            border-bottom: 1px solid rgba(255, 255, 255, .1);
            padding: 40px 0 32px 0;
        }

        .faq-question {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background 0.3s ease;

            span {
                font-family: MiSans;
                font-weight: 600;
                font-size: 24px;
                line-height: 32px;
                color: #fff;
            }
        }



        .arrow-icon {
            width: 29px;
            height: 29px;
            transition: transform 0.3s ease;
        }

        .arrow-icon.rotated {
            transform: rotate(180deg);
        }

        .faq-answer {
            overflow: hidden;
            padding-top: 24px;
            color: rgba(255, 255, 255, .8);
            font-family: MiSans;
            font-weight: 400;
            font-size: 16px;
            // line-height: 100%;
            text-align: left;

        }

        /* 动效过渡 */
        .faq-slide-enter-active,
        .faq-slide-leave-active {
            transition: all 0.3s ease;
        }

        .faq-slide-enter-from,
        .faq-slide-leave-to {
            opacity: 0;
            max-height: 0;
        }

        .faq-slide-enter-to,
        .faq-slide-leave-from {
            opacity: 1;
            max-height: 200px;
        }

        // .faq-list {
        //     margin-top: 8px;
        //     .faq-item {
        //         background-color: #111;
        //         border-radius: 10px;
        //         padding: 16px;
        //         margin-bottom: 12px;

        //         .question {
        //             font-size: 14px;
        //             color: #fff;
        //         }
        //     }
        // }
    }

    .footer {
        margin-top: 120px;
        text-align: center;
        background-image: url("https://pro-oss.pinkwallet.com/image/1377684264547082240.png");
        background-size: 100% 125%;
        height: 408px;
        color: #fff;

        .inner {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 21px;
        }

        span {
            font-family: MiSans;
            font-weight: 700;
            font-size: 40px;
            line-height: 53px;
        }

        .login-btn {
            background-color: #EF88A3;
            border: none;
            // padding: 12px 30px;
            // border-radius: 20px;
            font-family: MiSans;
            font-weight: 500;
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 178px;
            height: 54px;
            border-radius: 12px;
            cursor: pointer;
        }
    }
}
</style>