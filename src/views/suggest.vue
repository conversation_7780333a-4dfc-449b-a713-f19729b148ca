<template>
    <div class="container">
        <div class="title-container-card mt_200">
            <span class="title">{{ $t('suggest_title') }}</span>
        </div>
        <div class="iban_msg">
            {{ $t('suggest_subtitle') }}
        </div>
        <div class="suggest_img">
            <img src="https://pro-oss.pinkwallet.com/image/1377965356894216192.png" alt="">
        </div>
        <div class="title-container-card mt_200">
            <span class="title">{{ $t('suggest_submit_title') }}</span>
        </div>
        <div class="feedback_container">
            <div class="feedback_form">
                <!-- 反馈类型下拉框 -->
                <div class="form_item">
                    <div class="dropdown_wrapper" @click="toggleDropdown">
                        <div class="dropdown_selected">
                            {{ selectedType || $t('suggest_type_placeholder') }}
                            <span class="dropdown_icon" :class="{ open: dropdownOpen }"></span>
                        </div>
                        <ul v-if="dropdownOpen" class="dropdown_list">
                            <li
                                v-for="type in feedbackTypes"
                                :key="type"
                                @click.stop="selectType(type)"
                                :class="{ selected: selectedType === type }"
                            >
                                {{ type }}
                                <img v-if="selectedType === type" src="https://pro-oss.pinkwallet.com/image/1377975913617645568.png" alt="">
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- 反馈内容输入框 -->
                <div class="form_item">
                    <div class="feedback_textarea_wrapper">
                        <textarea
                            class="feedback_textarea"
                            v-model="feedback"
                            :maxlength="500"
                            :placeholder="$t('suggest_content_placeholder')"
                        ></textarea>
                        <div class="char_count">{{ feedback.length }}{{ $t('suggest_char_count') }}</div>
                    </div>
                </div>

                <!-- 附件列表 -->
                <div class="form_item attachments_row nobg">
                    <div class="checkbox_row">
                        <input type="checkbox" id="agree" v-model="agreed" />
                        <label for="agree">{{ $t('suggest_agree_text') }}</label>
                    </div>
                </div>

                <!-- 按钮区 -->
                <div class="form_item btn_row nobg">
                    <div class="btn_upload">
                        <img src="https://pro-oss.pinkwallet.com/image/1378017921455448064.png" alt="" srcset="">
                        {{ $t('suggest_upload_btn') }}
                    </div>
                    <div class="btn_submit">{{ $t('suggest_submit_btn') }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, computed, watchEffect, onMounted, watch } from 'vue'
    import { useI18n } from "vue-i18n";
    import * as echarts from 'echarts'
    const { locale, t } = useI18n();

    const feedbackTypes = [
        'suggest_type_bug',
        'suggest_type_deposit',
        'suggest_type_exchange',
        'suggest_type_stock',
        'suggest_type_contract',
        'suggest_type_support',
        'suggest_type_suggestion'
    ]
    const selectedType = ref('')
    const dropdownOpen = ref(false)
    const feedback = ref('')
    const attachments = ref(['附件1.Png', '附件2.Png'])
    const agreed = ref(false)

    function toggleDropdown() {
        dropdownOpen.value = !dropdownOpen.value
    }
    function selectType(type) {
        selectedType.value = type
        dropdownOpen.value = false
    }
</script>

<style lang="scss" scoped>
    @import "../pxto.scss";

    .title-container-card { 
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        opacity: 1;
        margin-bottom: 79px;
        transition: all 0.8s;

        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            position: relative;

            font-family: MiSans-bold;
            font-weight: 700;
            font-size: 36px;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px;
                height: 2px;
                background-color: rgba(255, 255, 255, 0.2);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }
    }

    .container {
        width: 100%;
        max-width: 1440px;
        padding: 0 20px;
        box-sizing: border-box;
        margin: 60px auto;
        font-family: 'MiSans';

        .img_title {
            img {
                width: 271px;
                height: 271px;
            }
        }
        .iban_msg {
            font-size: px(20);
            color: #EF88A3;
            max-width: 800px;
            margin: 0 auto;
        }
        .suggest_img{
            width: px(656);
            margin: 0 auto;
            margin-top: px(-100);
            img{
                width: 100%;
            }
        }
         .stock_search_bar {
            width: 1194px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: px(100);
            border-radius: px(16);
            border: 1px solid rgba(255, 255, 255, 1);
            padding: px(20) px(25);

            .stock_search_input {
                display: flex;
                align-items: center;
                justify-content: center;
                width: px(1000);
                height: px(60);
                border-radius: px(16);

                img {
                    width: px(28);
                    height: px(28);
                    margin-right: px(10);
                }

                input {
                    width: 100%;
                    height: 100%;
                    border: none;
                    background-color: transparent;
                    color: #fff;
                    font-size: px(24);
                }

                input::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                }
            }

            .stock_search_btn {
                width: px(180);
                height: px(60);
                border-radius: px(30);
                background-color: #EF88A3;
                color: #222;
                font-size: px(24);
                font-weight: 600;
                text-align: center;
                line-height: px(60);
                cursor: pointer;

            }
        }
        .help_center_list{
            display: flex;
            justify-content: center;
            align-items: center;
            margin: px(100) auto px(300);
            gap: px(70);
            .help_center_item{
                display: flex;
                justify-content: center;
                align-items: center;
                width: px(425);
                height: px(131);
                border-radius: px(16);
                background-color:#222;
                border: 1px solid rgba(255, 255, 255, 0.1);
                cursor: pointer;
                img{
                    width: px(52);
                    margin-right: px(28);
                }
                .title{
                    font-size: px(20);
                    font-weight: 600;
                    color: #fff;
                }

            }
        }
    }

    .feedback_container {
        display: flex;
        justify-content: center;
        align-items: flex-start;
    }

    .feedback_form {
        width: 540px;
        border-radius: px(16);
        padding: px(40) px(32);
        box-shadow: 0 px(4) px(32) rgba(0,0,0,0.4);
    }

    .form_item {
        margin-bottom: px(32);
        position: relative;
        background: #FFFFFF0D;
        border: 1px solid #FFFFFF33;
        border-radius: px(8);
        &.nobg{
            background-color:transparent;
            border: none;
        }
        .feedback_textarea_wrapper{
            padding: px(16) px(20);
            height: px(280);
        }
    }
    .dropdown_wrapper {
        position: relative;
        user-select: none;
    }
    .dropdown_selected {
        color: #EF88A3;
        border-radius: px(8);
        padding: px(16) px(20);
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .dropdown_icon {
        margin-left: px(8);
        border: solid #EF88A3;
        border-width: 0 px(2) px(2) 0;
        display: inline-block;
        padding: px(4);
        transform: rotate(45deg);
        transition: 0.2s;
    }
    .dropdown_icon.open {
        transform: rotate(-135deg);
    }
    .dropdown_list {
        position: absolute;
        left: 0;
        right: 0;
        top: 110%;
        background: #222;
        border-radius: px(8);
        box-shadow: 0 px(2) px(12) rgba(0,0,0,0.4);
        z-index: 10;
        padding: px(8) 0;
        margin: 0;
        list-style: none;
        padding: 0 px(20);
    }
    .dropdown_list li {
        color: #fff;
        line-height: px(40);
        cursor: pointer;
        transition: background 0.2s;
        border-radius: px(8);
        margin-top: px(10);
        text-align: left;
        padding: px(10) px(16);
        font-size: px(16);
        display: flex;
        align-items: center;
        justify-content: space-between;
        img{
            width: px(24);
            height: px(24);
        }
    }
    .dropdown_list li.selected,
    .dropdown_list li:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
    }

    .feedback_textarea {
        width: 100%;
        min-height: px(210);
        border-radius: px(8);
        border: none;
        color: #fff;
        font-size: px(18);
        resize: none;
        margin-bottom: px(8);
        background-color: transparent;
    }
    .char_count {
        color: #888;
        font-size: px(14);
        text-align: right;
    }

    .attachments_row {
        display: flex;
        align-items: center;
        gap: px(12);
        flex-wrap: wrap;
        .attachment {
            background: #222;
            color: #fff;
            border-radius: px(20);
            padding: px(6) px(16);
            display: flex;
            align-items: center;
            font-size: px(15);
            .icon_file {
                width: px(16);
                height: px(16);
                background: #888;
                border-radius: 50%;
                margin-right: px(6);
                display: inline-block;
            }
            .file_name {
                margin-right: px(8);
            }
            .icon_close {
                width: px(16);
                height: px(16);
                background: #EF88A3;
                border-radius: 50%;
                display: inline-block;
                cursor: pointer;
                position: relative;
                &::before, &::after {
                    content: '';
                    position: absolute;
                    left: px(7); top: px(3);
                    width: px(2); height: px(10);
                    background: #fff;
                }
                &::before { transform: rotate(45deg);}
                &::after { transform: rotate(-45deg);}
            }
        }
        .checkbox_row {
            display: flex;
            align-items: center;
            /* margin-left: 16px; */
            input[type="checkbox"] {
                accent-color: #EF88A3;
                width: px(18); height: px(18);
                margin-right: px(6);
            }
            label {
                color: #fff;
                font-size: px(15);
            }
        }
    }

    .btn_row {
        display: flex;
        justify-content: space-between;
        gap: px(24);
        .btn_upload, .btn_submit {
            flex: 1;
            background: #EF88A3;
            color: #000;
            border: none;
            border-radius: px(12);
            font-size: px(24);
            font-weight: 600;
            padding: px(16) 0;
            cursor: pointer;
            transition: background 0.2s;
        }
        .btn_upload {
            margin-right: px(12);
            color: #000;
            border: 2px solid #EF88A3;
            display: flex;
            align-items: center;
            justify-content: center;
            img{
                width: px(24);
                height: px(24);
                margin-right: px(12);
            }
        }
        .btn_submit {
            margin-left: px(12);
        }
    }
</style>