<template>
    <div class="container">
        <div class="remittance_header flex">
            <div class="remittance_title fz_52">
                <p>{{ t('remittance.title').split(' ')[0] }}</p>
                <p>{{ t('remittance.title').split(' ').slice(1).join(' ') }}</p>
                <div class="img mt_72">
                    <img src="https://pro-oss.pinkwallet.com/image/1376947513339764736.png"
                        :alt="t('remittance.title')" />
                </div>
            </div>
            <div class="right_cart mt_14 pl_38 pr_38 pb_21 pt_26 pb_26">
                <p class="font_num">
                    1{{ t('remittance.currency.CAD') }} = 0.1289{{ t('remittance.currency.USD') }}
                </p>
                <div class="input_div mb_40">
                    <p>{{ t('remittance.amount') }}</p>
                    <div class="black">
                        <div class="input">
                            <input type="text" v-model="send_amount">
                        </div>
                        <div class="left_name" ref="leftNameRef" @click="openCurrencySelect('left')">
                            <img class="tx" :src="currencyList[leftCurrency].flag"
                                :alt="t(`remittance.currency.${currencyList[leftCurrency].code}`)" />
                            {{ currencyList[leftCurrency].code }}
                            <img src="https://pro-oss.pinkwallet.com/image/1374799119729123328.png" alt="select" />
                        </div>
                    </div>
                </div>
                <div class="input_div mb_40">
                    <p>{{ t('remittance.receive_amount') }}</p>
                    <div class="black">
                        <div class="input">
                            <input type="text" v-model="receive_amount">
                        </div>
                        <div class="left_name" ref="rightNameRef" @click="openCurrencySelect('right')">
                            <img class="tx" :src="currencyList[rightCurrency].flag"
                                :alt="t(`remittance.currency.${currencyList[rightCurrency].code}`)" />
                            {{ currencyList[rightCurrency].code }}
                            <img src="https://pro-oss.pinkwallet.com/image/1374799119729123328.png" alt="select" />
                        </div>
                    </div>
                </div>
                <div class="text_div flex_between mt_60 border pb_20">
                    <p>{{ t('remittance.fee') }}</p>
                    <p>6.45 {{ currencyList[leftCurrency].code }}</p>
                </div>
                <div class="text_div flex_between pb_20">
                    <p>{{ t('remittance.arrival_time') }}</p>
                    <p>{{ t('remittance.arrival_time_value') }}</p>
                </div>
                <div class="button  cursor_pointer mt_40">
                    <span>
                        {{ t('remittance.transfer_btn') }}
                    </span>
                </div>
            </div>
        </div>
        <div class="title_container_card">
            <span class="title">{{ t('remittance.how_to_use') }}</span>
        </div>
        <div class="steps_progress">
            <div class="step_item" :class="{ 'active': currentIndex == 1 }">
                <span class="step_num">1</span>
            </div>
            <div class="step_line"></div>
            <div class="step_item" :class="{ 'active': currentIndex == 2 }">
                <span class="step_num">2</span>
            </div>
            <div class="step_line"></div>
            <div class="step_item" :class="{ 'active': currentIndex == 3 }">
                <span class="step_num">3</span>
            </div>
        </div>
        <div class="steps_content mb_250">
            <img src="https://pro-oss.pinkwallet.com/image/1376961097780256768.png" v-show="currentIndex == 1"
                :alt="t('remittance.step1_title')" />
            <img src="https://pro-oss.pinkwallet.com/image/1376961135038259200.png" v-show="currentIndex == 2"
                :alt="t('remittance.step2_title')" />
            <img src="https://pro-oss.pinkwallet.com/image/1376961176138244096.png" v-show="currentIndex == 3"
                :alt="t('remittance.step3_title')" />
            <div class="dow" @click="next">
                <img src="https://pro-oss.pinkwallet.com/image/1376961541634088960.png" alt="next" />
            </div>
        </div>
        <div class="range_container">
            <div class="title_container_card">
                <span class="title">{{ t('remittance.coverage') }}</span>
            </div>
            <div class="msg">
                <span>{{ t('remittance.coverage_note') }}</span>
            </div>
            <div class="country_list">
                <div class="country_item" v-for="(item, index) in countries" :key="index">
                    <div class="country_icon_wrap">
                        <img class="country_icon" :src="item.src" :alt="locale === 'en' ? item.name_en : item.name" />
                    </div>
                    <div class="country_name">{{ locale === 'en' ? item.name_en : item.name }}</div>
                </div>
            </div>
        </div>
        <!-- 币种下拉弹窗 -->
        <div v-if="showCurrencySelect" class="currency_select_popup" :style="popupStyle">
            <div v-for="(item, idx) in currencyList" :key="item.code" class="currency_select_item"
                :class="{ selected: idx === (selecting === 'left' ? leftCurrency : rightCurrency) }"
                @click="selectCurrency(idx)">
                <img :src="item.flag" class="currency_flag" :alt="t(`remittance.currency.${item.code}`)" />
                <span class="currency_code">{{ item.code }}</span>
                <span class="currency_name">{{ t(`remittance.currency.${item.code}`) }}</span>
                <span v-if="idx === (selecting === 'left' ? leftCurrency : rightCurrency)"
                    class="currency_selected_icon">
                    <img src="https://pro-oss.pinkwallet.com/image/1377975913617645568.png" alt="selected" />
                </span>
            </div>
        </div>
        <!-- 遮罩 -->
        <div v-if="showCurrencySelect" class="currency_select_mask" @click="closeCurrencySelect"></div>
    </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import countries from '../../utils/country_icon.json'

const { t, locale } = useI18n()
const send_amount = ref('10000')
const receive_amount = ref('7231.25')
const currentIndex = ref(1)

const next = () => {
    if (currentIndex.value < 3) {
        currentIndex.value++
    } else {
        currentIndex.value = 1
    }
}

const currencyList = [
    { code: 'USD', name: 'US Dollar', flag: 'https://pro-oss.pinkwallet.com/image/1374776145693204480.png' },
    { code: 'CAD', name: 'Canadian Dollar', flag: 'https://pro-oss.pinkwallet.com/image/1374799011251838976.png' },
    { code: 'GBP', name: 'British Pound', flag: 'https://pro-oss.pinkwallet.com/image/1374776180739641344.png' }
]

// 监听语言变化，更新国家名称
watch(locale, (newLocale) => {
    // 语言变化时，组件会自动重新渲染，使用新的locale值显示对应的国家名称
    // 不需要额外的处理，因为我们使用了计算属性
})

const leftCurrency = ref(1)  // 默认CAD
const rightCurrency = ref(1) // 默认CAD
const showCurrencySelect = ref(false)
const selecting = ref('left') // 当前弹窗属于左还是右
const popupStyle = ref({})
const leftNameRef = ref(null)
const rightNameRef = ref(null)

function openCurrencySelect(side) {
    selecting.value = side
    nextTick(() => {
        updatePopupPosition()
        showCurrencySelect.value = true
    })
}

function selectCurrency(idx) {
    if (selecting.value === 'left') {
        leftCurrency.value = idx
    } else {
        rightCurrency.value = idx
    }
    showCurrencySelect.value = false
}

function closeCurrencySelect() {
    showCurrencySelect.value = false
}

function updatePopupPosition() {
    let refEl = selecting.value === 'left' ? leftNameRef.value : rightNameRef.value
    if (refEl) {
        const rect = refEl.getBoundingClientRect()
        popupStyle.value = {
            position: 'fixed',
            top: `${rect.top + rect.height + 6}px`,
            left: `${rect.left}px`,
            minWidth: `${rect.width}px`,
            zIndex: 1001
        }
    }
}

function handleScrollOrResize() {
    if (showCurrencySelect.value) {
        updatePopupPosition()
    }
}

onMounted(() => {
    window.addEventListener('scroll', handleScrollOrResize, true)
    window.addEventListener('resize', handleScrollOrResize)
})
onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleScrollOrResize, true)
    window.removeEventListener('resize', handleScrollOrResize)
})
</script>

<style lang="scss" scoped>
@import '../pxto.scss';

.container {
    width: 100%;
    padding: 0 px(20);
    box-sizing: border-box;
    margin: 0 auto;
    font-family: 'MiSans';

    .remittance_header {
        width: 1194px;
        margin: px(130) auto;
        display: flex;
        justify-content: center;
        align-items: center;

        .remittance_title {
            font-weight: 700;
            color: #fff;
            text-align: left;
            margin-right: px(100);

            .img {
                background-image: url('https://pro-oss.pinkwallet.com/image/1376947563461697536.png');
                background-size: 100%;
                background-repeat: no-repeat;
                width: px(633);
                height: px(411);

                img {
                    width: px(299);
                    height: px(461);
                    margin-top: -10px;
                    margin-left: px(85);
                }
            }
        }

        .right_cart {
            width: px(578);
            min-height: px(641);
            background-color: #222222;
            border-radius: px(16);
            border: 1px solid #4B4B4B;

            .font_num {
                font-size: px(14);
                line-height: px(34);
                height: px(34);
                color: #EF88A3;
                text-align: center;
                margin-bottom: px(26);
                border: 1px solid #EF88A3;
                border-radius: px(17);
                display: inline-block;
                padding: 0 px(28);

            }

            .input_div {
                p {
                    font-size: px(18);
                    text-align: left;
                    color: rgba(255, 255, 255, 0.6);
                    margin: 0px;
                    margin-bottom: px(14);
                    font-weight: 400;
                }

                .black {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border: 1px solid #fff;
                    border-radius: px(14);
                    width: 100%;
                    padding: px(13) px(12) px(13) px(17);

                    .left_name {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        font-size: px(17);
                        font-weight: 600;
                        color: #fff;

                        .tx {
                            width: px(33);
                            height: px(33);
                            margin-right: px(14);
                            margin-left: 0px;
                        }

                        img {
                            width: 18px;
                            height: 18px;
                            margin-left: 12px;
                        }
                    }

                    .input {
                        width: 100%;
                        height: 100%;

                        input {
                            width: 100%;
                            height: 100%;
                            border: none;
                            background-color: transparent;
                            font-size: px(20);
                            font-weight: 600;
                            color: #fff;
                            text-align: left;
                        }
                    }
                }
            }

            .text_div {
                font-size: px(16);
                color: rgba(255, 255, 255, 0.6);
                margin-bottom: px(20);
                font-weight: 400;

                &.border {
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                }

                p:last-child {
                    color: rgba(255, 255, 255, 1);
                }
            }

            .button {
                height: px(60);
                // background-color: #FF95B2;
                border-radius: px(14);

                overflow: hidden;
                position: relative;

                span {
                    position: absolute;
                    line-height: px(60);
                    text-align: center;
                    font-size: px(18);
                    font-weight: 600;
                    color: #222;
                    z-index: 9;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }

            .button::before {
                content: "";
                position: absolute;
                width: 50%;
                height: 50%;
                background: #fff;
                left: 50%;
                top: 50%;
                z-index: 1;
                /* 改为正值 */
                animation: rotation 4s linear infinite;
                transform-origin: left top;

            }

            @keyframes rotation {
                to {
                    transform: rotate(360deg);

                }
            }

            .button::after {
                content: "";
                padding-top: -3px;
                position: absolute;
                inset: 1px;
                background: #FF82A3;
                border-radius: px(14);
                z-index: 1;
            }
        }
    }


}

.title_container_card {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    opacity: 1;
    margin-bottom: 79px;
    transition: all 0.8s;

    &.loaded-title {
        transform: translateY(60px);
        opacity: 1;
    }

    .title {
        font-size: px(18);
        font-weight: bold;
        position: relative;

        font-family: MiSans-bold;
        font-weight: 700;
        font-size: px(36);
        line-height: 100%;
        letter-spacing: 0px;
        text-align: center;
        text-transform: capitalize;
        color: #fff;

        &::before,
        &::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 76px;
            height: 2px;
            background-color: rgba(255, 255, 255, 0.2);
        }

        &::before {
            left: -100px;
        }

        &::after {
            right: -100px;
        }
    }
}

.steps_progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: px(40);

    .step_item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: px(35);
        height: px(35);
        border-radius: 50%;
        border: px(3) solid rgba(255, 149, 178, 0.2);
        background: transparent;
        transition: all 0.3s;
        position: relative;

        .step_num {
            font-size: px(20);
            font-weight: 700;
            color: #3A2C31;
            font-family: 'MiSans-bold';
        }
    }

    .step_item.active {
        background: #FF95B2;
        border: none;

        .step_num {
            color: #222;
        }
    }

    .step_line {
        width: px(80);
        height: px(4);
        background: #3A2C31;
        margin: 0 px(12);
        border-radius: px(2);
    }
}

.steps_content {
    margin: px(0) auto px(250);
    position: relative;
    width: px(450);
    height: px(540);

    img {
        width: px(450);
    }

    .dow {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        left: px(490);
        margin: auto;
        border: 1px dashed rgba(255, 149, 178, 1);
        border-radius: 50%;
        padding: px(10);
        width: px(44);
        height: px(44);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
            width: px(44);
        }
    }
}

.range_container {
    background-color: #141414;
    padding: px(82) px(100);

    .title_container_card {
        margin-bottom: px(26);
    }

    .msg {
        font-size: px(18);
        font-weight: 400;
        color: #848484;

    }
}

.country_list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    gap: px(80) px(0);
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    margin-top: px(110);
}

.country_item {
    width: 20%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: px(40);
    min-width: px(120);
    cursor: pointer;
}

.country_icon_wrap {
    width: px(68);
    height: px(68);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: px(12);
}

.country_icon {
    width: px(68);
    height: px(68);
    object-fit: contain;
    display: block;
}

.country_name {
    color: #fff;
    font-size: px(18);
    text-align: center;
    margin-top: px(2);
    letter-spacing: 1px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.currency_select_mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: 1000;
}

.currency_select_popup {
    background: #232323;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
    min-width: 180px;
    padding: 0;
    overflow: hidden;
    border: 1px solid #333;
    z-index: 1001;
}

.currency_select_item {
    display: flex;
    align-items: center;
    padding: 12px 18px;
    cursor: pointer;
    color: #fff;
    font-size: 14px;
    position: relative;
    background: transparent;
    transition: background 0.2s;
    border-bottom: 1px solid #2d2d2d;
    width: 221px;

    &:last-child {
        border-bottom: none;
    }

    &:hover,
    &.selected {
        background: #313131;
    }

    .currency_flag {
        width: 28px;
        height: 28px;
        margin-right: 12px;
        border-radius: 50%;
        background: #fff;
        object-fit: cover;
    }

    .currency_code {
        font-weight: bold;
        margin-right: 8px;
    }

    .currency_name {
        color: #ccc;
    }

    .currency_selected_icon {
        margin-left: auto;
        color: #FF95B2;
        font-size: 18px;
        font-weight: bold;

        img {
            width: 16px;
            height: 16px;
        }
    }
}
</style>