.root-container {
  width: 100%;
  min-height: 100vh;
  color: #fff;
  overflow-x: hidden;
}

.container {
  // max-width: 1440px;
  margin: 0 auto;
  // padding: 32px 16px;

  .download-section {
    display: flex;
    justify-content: center;
    margin: 60px 0 30px 100px;
    // width: 100%;

    .content-down {
      // width: 100%;
      width: 1440px;
      display: flex;
      align-items: center;
      // justify-content: center;
      justify-content: space-between;
      flex: 1;
      margin: 0 70px;

      .download-content {
        z-index: 10;
        // width: 50%;
        text-align: left;
        // margin-left: 114px;
        transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
        opacity: 0;

        &.animate-in {
          transform: translateX(120px); // 进入动画
          opacity: 1; // 透明度变为 100%
        }

        .title {
          width: 718px;
          white-space: wrap;
          font-family: MiSans-bold;
          font-weight: 700;
          font-size: 56px;
          line-height: 120%;
          letter-spacing: 0px;
          text-transform: capitalize;
          color: #fff;
        }

        .niu {
          margin-top: 12px;
          font-family: MiSans-dbold;
          font-weight: 600;
          font-size: 24px;
          line-height: 32px;
          letter-spacing: 0px;
          text-transform: capitalize;
          color: #ef88a3;
        }

        .inputs {
          display: flex;
          align-items: center;
          margin-bottom: 58px;

          .input {
            width: 360px;
            cursor: pointer;
            height: 64px;
            border-radius: 12px;
            // background: #F5F5F51A;
            background: rgba(245, 245, 245, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.08);
            display: flex;
            align-items: center;
            // transition: transform 0.8s ease-in-out;
            transition: all 0.8s ease-in-out; // 过渡动画

            &.active {
              border: 1px solid #ef88a3; // 只有 active 类存在时才有边框
            }

            // &:hover {
            //     border: 1px solid #EF88A3;
            // }

            .prefix {
              margin-left: 25px;
              display: flex;
              align-items: center;
              font-family: MiSans;
              font-weight: 500;
              font-size: 16px;
              line-height: 137%;
              letter-spacing: 0px;
              text-transform: capitalize;
              color: #fff;

              input {
                margin-left: 17px;
                height: 64px;
                border-style: none;
                background: none;
                font-family: MiSans;
                font-weight: 500;
                outline: none; // 去除选中状态边框
                text-transform: capitalize;

                font-size: 16px;
                line-height: 137%;
                letter-spacing: 0px;
                color: #fff;
              }

              .shu {
                margin-left: 20px;
                height: 19px;
                width: 1px;
                background: rgba(255, 255, 255, 0.1);
                opacity: 0.1;
                border-radius: 1px;
              }
            }
          }

          .login {
            cursor: pointer;
            width: 163px;
            height: 64px;
            border-radius: 12px;
            background: rgba(239, 136, 163, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            font-family: MiSans;
            font-weight: 600;
            font-size: 16px;
            line-height: 16px;
            letter-spacing: 0px;
            text-align: center;
            color: #ffffff;
            transition: all 0.8s ease-in-out; // 过渡动画

            // &.activelogin {
            //     background: rgba(185, 100, 120, 1);

            // }

            &:hover {
              background: rgba(185, 100, 120, 1);
            }
          }
        }

        .subtitle {
          color: #9ca3af;
          font-family: MiSans-regular;
          font-weight: 400;
          font-size: 18px;
          width: 531px;
          white-space: wrap;
          line-height: 137%;
          letter-spacing: 0px;
          text-transform: capitalize;
          color: rgba(255, 255, 255, 0.7);
        }

        .store-buttons {
          display: flex;
          gap: 16px;

          .store-button {
            display: flex;
            align-items: center;
            justify-content: center;
            // transition: background-color 0.8s;
            transition: all 0.8s;
            border: 1px solid rgba(255, 255, 255, 1);
            width: 213px;
            height: 76px;
            overflow: hidden;
            border-radius: 12px;
            &:hover {
              background: #fff;

              div {
                span {
                  &:nth-of-type(1) {
                    color: rgba(0, 0, 0, 0.7);
                  }

                  &:nth-of-type(2) {
                    color: #000;
                  }
                }
              }
            }

            img {
              width: 31px;
              height: 32px;
              transition: opacity 0.8s ease-in-out;
            }

            div {
              margin-left: 12px;
              display: flex;
              flex-direction: column;
              align-items: flex-start;

              span {
                &:nth-of-type(1) {
                  font-family: MiSans-normal;
                  font-weight: 400;
                  font-size: 12px;
                  line-height: 16px;
                  letter-spacing: 0px;
                  text-transform: capitalize;
                  color: rgba(255, 255, 255, 0.7);
                }

                &:nth-of-type(2) {
                  font-family: MiSans-medium;
                  font-weight: 500;
                  font-size: 18px;
                  line-height: 24px;
                  letter-spacing: 0px;
                  text-transform: capitalize;
                  color: #fff;
                }
              }
            }
          }
        }
      }

      .preview-image {
        z-index: 1;
        width: 709px !important;
        height: 690px;
        // width: 50%;
        background-image: url("https://pro-oss.pinkwallet.com/image/20250321/dab16d318b26a1bff265cd2c00aead7b_2835x2759.png");
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
        opacity: 0;

        // padding-left: 120px;
        &.animate-in {
          transform: translateX(-120px); // 进入动画
          // margin-left: 0;

          opacity: 1; // 透明度变为 100%
        }

        img {
          width: 258px;
          height: 562px;
          cursor: pointer;
          margin-right: 114px;
        }
      }
    }
  }
}

.features-grid {
  // display: grid;
  display: flex;
  justify-content: center;
  // flex: 1;
  // align-items: center;
  // justify-content: space-between;
  // grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin: 0 0 80px 0;
  padding-top: 50px;

  .features-item {
    gap: 21px;
    max-width: 1440px;
    display: flex;
    align-items: center;

    .feature-card {
      background: rgba(255, 255, 255, 0.05);
      background-blend-mode: overlay; // 让颜色 & 图片混合
      cursor: pointer;
      flex: 1;
      min-width: 425px;
      height: 131px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      // justify-content: center;

      // transition: all 0.4s ease-in-out;
      // margin-top: 50px;
      // transition: transform 0.4s ease;
      border-color: 0.4s ease;
      background: 0.4s ease;

      opacity: 0;
      transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
      transform: translateY(-60px);
      overflow: hidden;

      div {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-left: 14px;
        z-index: 999;
        span {
          &:nth-of-type(1) {
            font-family: MiSans;
            font-weight: 700;
            font-size: 18px;
            line-height: 30px;
            letter-spacing: 0px;
            text-transform: capitalize;
            color: #fff;
          }

          &:nth-of-type(2) {
            text-align: left;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 8px;
            margin-right: 36px;
            font-family: MiSans-regular;
            font-weight: 400;
            font-size: 14px;
            line-height: 19px;
            letter-spacing: 0px;
            // text-transform: capitalize;
          }
        }
      }

      img {
        margin-left: 36px;
        // margin: 36px;
        width: 34px;
        height: 34px;
      }

      &.fade-in {
        opacity: 1;
        transform: translateY(0); // 滑入到正常位置
        // transform: translateY(50px);
      }

      &:nth-of-type(1)::after {
        content: "";
        position: absolute;
        top: 60px; // 初始偏移 Y 轴 +60px
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("https://pro-oss.pinkwallet.com/image/20250324/a25d024972226ad8b77ff92ab7433d1d_850x262.png"),
          url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
        background-size: 100% 100%;
        background-position: center center;
        opacity: 0; // 初始透明
        transition: top 0.8s ease-in-out, opacity 0.8s ease-in-out;
      }

      &:nth-of-type(2)::after {
        content: "";
        position: absolute;
        top: 60px; // 初始偏移 Y 轴 +60px
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("https://pro-oss.pinkwallet.com/image/20250324/068a36a7a886d761e6b355815082dd43_850x262.png"),
          url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
        background-size: 100% 100%;
        background-position: center center;
        opacity: 0; // 初始透明
        transition: top 0.8s ease-in-out, opacity 0.8s ease-in-out;
      }

      &:nth-of-type(3)::after {
        content: "";
        position: absolute;
        top: 60px; // 初始偏移 Y 轴 +60px
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("https://pro-oss.pinkwallet.com/image/20250324/2279cb4ad6505dcc34506864a5e65ec4_850x262.png"),
          url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
        background-size: 100% 100%;
        background-position: center center;
        opacity: 0; // 初始透明
        transition: top 0.8s ease-in-out, opacity 0.8s ease-in-out;
      }

      &:hover::after {
        top: 0px; // 回到原位置
        opacity: 1; // 透明度变 1
      }

      // &:nth-of-type(2):hover {
      //     background-image: url("https://pro-oss.pinkwallet.com/image/20250324/068a36a7a886d761e6b355815082dd43_850x262.png"),
      //         url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
      //     // background-size: 60% 100%, cover; // 第一张占 60%，第二张全覆盖
      //     // background-position: right, center; // 第一张靠右，第二张居中
      //     // background-repeat: no-repeat, no-repeat; // 避免重复
      //     background-size: 100% 100%;

      // }

      // &:nth-of-type(3):hover {
      //     background-image: url("https://pro-oss.pinkwallet.com/image/20250324/2279cb4ad6505dcc34506864a5e65ec4_850x262.png"),
      //         url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
      //     // background-size: 60% 100%, cover; // 第一张占 60%，第二张全覆盖
      //     // background-position: right, center; // 第一张靠右，第二张居中
      //     // background-repeat: no-repeat, no-repeat; // 避免重复
      //     background-size: 100% 100%;

      // }

      &:hover {
        transform: translateY(-20px) !important;
        // transform: scale(1.05);
        // background: linear-gradient(135deg, rgba(255, 108, 135, 0.6), rgba(72, 28, 168, 0.6));
        // box-shadow: 0 10px 20px rgba(255, 108, 135, 0.3);
        // background-image: url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
        background-size: 100% 100%;
        // transform: scale(1.05);
        // border-color: #ff4d73;
        // background: rgba(255, 77, 115, 0.1);

        // &:nth-of-type(1) {
        //     background-image: url("https://pro-oss.pinkwallet.com/image/20250324/a25d024972226ad8b77ff92ab7433d1d_850x262.png");
        //     background-size: 100% 100%;
        //     width: 425px;
        //     height: 131px;
        // }

        // &:nth-of-type(2) {
        //     background-image: url("https://pro-oss.pinkwallet.com/image/20250324/068a36a7a886d761e6b355815082dd43_850x262.png");
        //     background-size: 100% 100%;
        // }

        // &:nth-of-type(3) {
        //     background-image: url("https://pro-oss.pinkwallet.com/image/20250324/2279cb4ad6505dcc34506864a5e65ec4_850x262.png");
        //     background-size: 100% 100%;
        // }

        .icon {
          transform: scale(1.05);
          // border-color: #ff4d73;
          // background: rgba(255, 77, 115, 0.1);
          // transform: scale(1.1);
        }

        &::before {
          bottom: 0; // 使背景从下往上出现
        }
      }

      // &:not(:hover) {
      //     transform: translateY(0);

      //     &::before {
      //         bottom: -100%; // 使背景从上往下消失
      //     }
      // }
    }
  }
}

.process-grid {
  margin: 42px 0 110px 0;
  display: flex;
  justify-content: center;

  .process-item {
    // display: grid;
    display: flex;
    align-items: center;
    // grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    max-width: 1440px;

    .process-card {
      width: 324px;
      height: 288px;
      //   background-color: #1f2937;
      // padding: 82px 43px 90px 43px;
      padding: 40px 0 80px 0;
      border-radius: 8px;
      transition: all 0.5s ease-in-out;
      cursor: pointer;
      // width: 323px;
      // height: 418px;
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.1);
      opacity: 0;
      transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out,
        background 0.5s ease-in-out;
      transform: translateY(-50px);

      display: flex;
      align-items: center;
      flex-direction: column;

      .icon {
        height: 190px;
        width: 190px;
      }

      div {
        display: flex;
        align-items: center;
        flex-direction: column;

        span {
          &:nth-of-type(1) {
            margin: 6px 30px 8px 30px;
            font-family: MiSans;
            font-weight: 600;
            font-size: 24px;
            // line-height: 150%;\
            line-height: 36px;
            letter-spacing: 0.5%;
            text-align: center;
            color: #ffffff;
          }

          &:nth-of-type(2) {
            width: 264px;
            white-space: wrap;
            font-family: MiSans-normal;
            font-weight: 500;
            font-size: 16px;
            line-height: 150%;
            letter-spacing: 0.5%;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }

      // transition: ; // 平滑过渡
      &.fade-in {
        opacity: 1;
        transform: translateY(0); // 滑入到正常位置
        // transform: translateY(50px);
      }

      &:hover {
        transform: translateY(-20px);
        // transform: scale(1.05);
        background: #ef88a3;
        backdrop-filter: blur(58.81349182128906px);
        background: 0.4s ease;

        // box-shadow: 0 10px 20px rgba(255, 108, 135, 0.3);
        backdrop-filter: blur(58.81349182128906px);

        .icon {
          // transform: scale(1.1);
        }

        &::before {
          bottom: 0; // 使背景从下往上出现
        }
      }

      // &:not(:hover) {
      //     transform: translateY(0);

      //     &::before {
      //         bottom: -100%; // 使背景从上往下消失
      //     }
      // }

      .process-header {
      }
    }
  }
}

.crypto-container {
  padding-bottom: 20px;
  text-align: center;
  background: linear-gradient(180deg, #141414 75.37%, #ef88a3 142.21%);
  // padding: 40px;
  position: relative;
  overflow: hidden;
  color: white;
  // height: 680px;
  z-index: 10;

  /* 防止遮挡点击 */
  .bgs {
    position: absolute;
    width: 100%;
    height: 200px;
    background: linear-gradient(
      180deg,
      rgba(20, 20, 20, 0) 50%,
      #ef88a3 132.43%
    );
    z-index: 9 !important;
    bottom: 0;
  }

  .title-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    margin-top: 90px;

    .title {
      font-size: 18px;
      font-weight: bold;
      color: white;
      position: relative;
      // padding: 0 16px;

      font-family: MiSans-bold;
      font-weight: 700;
      font-size: 36px;
      line-height: 48px;
      letter-spacing: 0px;
      text-align: center;
      text-transform: capitalize;
      color: #fff;

      &::before,
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        width: 74px; // 横线宽度
        height: 2px; // 横线高度
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 10px;
      }

      &::before {
        left: -100px;
      }

      &::after {
        right: -100px;
      }
    }
  }

  .bg {
    bottom: -194px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 604px;
    height: 604px;
    z-index: 1;
    // background: linear-gradient(180deg, rgba(0,0,0) 50%, #000 132.43%);
    // background: linear-gradient(180deg, #141414 175.37%, #EF88A3 0%);
    // background: radial-gradient(circle, rgba(0, 0, 0, 0.9) 30%, rgba(0, 0, 0, 0.1) 70%, rgba(0, 0, 0, 0) 100%);
    // border-radius: 50%;
    /* 确保是圆形 */
    // background: rgba(20, 20, 20, 1);
    // background: radial-gradient(50% 60% at 50% 40%, #141414 0%, #141414 60%, rgba(20, 20, 20, 0) 100%);

    background-image: url("https://pro-oss.pinkwallet.com/image/20250327/76208944cc7cd2bd87a33e7619ae1df7_1506x964.png");
    background-size: 100% 300px;
    background-repeat: no-repeat, no-repeat; // 避免重复

    // background: radial-gradient(
    //   50% 50% at 50% 50%,
    //   #141414 0%,
    //   #141414 73.57%,
    //   rgba(0, 0, 0, 0) 100%
    // );
    // background: radial-gradient(50% 80% at 50% 20%,
    //         #141414 0%,
    //         /* 顶部深色 */
    //         #141414 40%,
    //         #141414 70%,
    //         rgba(0, 0, 0, 0) 100%
    //         /* 底部完全透明 */
    //     );
    // backdrop-filter: blur(64px) !important;
    // -webkit-backdrop-filter: blur(64px);

    // border-radius: 50%;

    .animated-image {
      z-index: 1 !important;
      width: 530px !important;
      height: 530px !important;
      background-size: 100% 100%;
      animation: playFrames 5s steps(150, end) infinite;
    }
  }

  .subtitles {
    font-family: MiSans-regular;
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    text-transform: capitalize;
    color: rgba(255, 255, 255, 0.4);
    padding-bottom: 45px;
    margin-top: 14px;
  }

  .coinall {
    max-width: 99vw;
    margin-top: 120px;
    // height: fit-content;
    opacity: 0;
    transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;

    &.coin-in {
      transform: translateY(-120px); // 进入动画
      opacity: 1; // 透明度变为 100%
    }

    .line1 {
      display: flex;
      align-items: center;

      /* 滚动行 */
      .scroll-container {
        width: 100%;
        margin: 0 32px 20px 0;

        /* 货币卡片 */
        .crypto-item {
          display: flex;
          align-items: center;
          // padding: 10px 20px;
          background: rgba(255, 255, 255, 0.1);
          // transform: translateY(120px);
          transition: opacity 0.5s, transform 1s;
          gap: 17px;
          width: 245px;
          height: 75px;
          border-radius: 100px;
          font-family: MiSans-regular;
          font-weight: 400;
          font-size: 20px;
          line-height: 100%;
          letter-spacing: 0px;
          text-align: center;
          text-transform: capitalize;
          color: #fff;
          cursor: pointer;
          opacity: 0.3;

          img {
            width: 60px;
            height: 60px;
            margin-left: 8px;
          }

          &:hover {
            opacity: 1 !important;
            animation-play-state: paused;
            transform: translateY(0);
          }
        }
      }
    }

    .line2 {
      display: flex;
      align-items: center;

      /* 滚动行 */
      .scroll-container {
        width: 100%;
        margin: 0 32px 20px 0;

        /* 货币卡片 */
        .crypto-item {
          display: flex;
          align-items: center;
          // padding: 10px 20px;
          background: rgba(255, 255, 255, 0.1);
          // transform: translateY(120px);
          transition: opacity 0.5s, transform 1s;
          gap: 17px;
          width: 245px;
          height: 75px;
          border-radius: 100px;
          font-family: MiSans-regular;
          font-weight: 400;
          font-size: 20px;
          line-height: 100%;
          letter-spacing: 0px;
          text-align: center;
          text-transform: capitalize;
          color: #fff;
          cursor: pointer;
          opacity: 0.6;

          img {
            width: 60px;
            height: 60px;
            margin-left: 8px;
          }

          &:hover {
            opacity: 1 !important;
            animation-play-state: paused;
            transform: translateY(0);
          }
        }
      }
    }

    .line3 {
      display: flex;
      align-items: center;

      /* 滚动行 */
      .scroll-container {
        width: 100%;
        margin: 0 32px 20px 0;

        /* 货币卡片 */
        .crypto-item {
          display: flex;
          align-items: center;
          // padding: 10px 20px;
          background: rgba(255, 255, 255, 0.1);
          // transform: translateY(120px);
          transition: opacity 0.5s, transform 1s;
          gap: 17px;
          width: 245px;
          height: 75px;
          border-radius: 100px;
          font-family: MiSans-regular;
          font-weight: 400;
          font-size: 20px;
          line-height: 100%;
          letter-spacing: 0px;
          text-align: center;
          text-transform: capitalize;
          color: #fff;
          cursor: pointer;
          opacity: 0.9;

          img {
            width: 60px;
            height: 60px;
            margin-left: 8px;
          }

          &:hover {
            opacity: 1 !important;
            animation-play-state: paused;
            transform: translateY(0);
          }
        }
      }
    }
  }

  /* 滚动动画 */
  @keyframes scroll-left {
    from {
      transform: translateX(0%);
    }

    to {
      transform: translateX(-50%);
    }
  }

  @keyframes scroll-right {
    from {
      transform: translateX(-50%);
    }

    to {
      transform: translateX(0%);
    }
  }
}

.title-container-card {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  opacity: 0;
  margin-bottom: 120px;
  // padding-top: -60px;

  transition: all 0.8s;

  &.loaded-title {
    transform: translateY(60px);
    opacity: 1;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
    color: white;
    position: relative;
    // padding: 0 16px;

    font-family: MiSans-bold;
    font-weight: 700;
    font-size: 36px;
    line-height: 100%;
    letter-spacing: 0px;
    text-align: center;
    text-transform: capitalize;
    color: #fff;

    &::before,
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 76px; // 横线宽度
      height: 2px; // 横线高度
      background-color: rgba(255, 255, 255, 0.2);
    }

    &::before {
      left: -100px;
    }

    &::after {
      right: -100px;
    }
  }
}

.container-card {
  // padding: 20px;
  // padding: 0 60px 0 70px;
  margin: 47px 0 140px 0;
  display: flex;
  justify-content: center;

  .card-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    max-width: 1440px;

    .card.loaded {
      transform: translateY(0);
      opacity: 1;
    }

    .card {
      height: 200px;
      position: relative;
      background: rgba(30, 30, 30, 1);
      color: #fff;
      padding: 32px 30px 44px 30px;
      cursor: pointer;
      border-radius: 22px;
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow: hidden;
      transform: translateY(60px);
      opacity: 0;
      //   transition: transform 0.8s ease-out, opacity 0.8s ease-out;
      transition: all 0.8s ease-out;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border: 1.13px solid rgba(75, 75, 75, 1);
      overflow: hidden;

      .title {
        font-family: MiSans;
        font-weight: 600;
        font-size: 26px;
        line-height: 39px;
        letter-spacing: 0.5%;
        text-align: center;
        margin: 8px 0 10px 0;
      }

      .desc {
        width: 366px;
        white-space: wrap;
        font-family: MiSans-normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 27px;
        letter-spacing: 0.5%;
        text-align: center;
        color: rgba(255, 255, 255, 0.5);
      }

      img {
        width: 49px;
        height: 53px;
      }

      .hover-border {
        // position: absolute;
        // bottom: 0;
        // left: 0;
        // height: 0;
        // // background: pink;
        // width: 0;
        // transition: width 0.8s ease-in-out, height 0.8s ease-in-out;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: #e2819b;
        transform: scaleX(0); // 初始状态缩小
        transform-origin: center; // 以中间为基点展开
        transition: transform 0.8s ease-in-out;
      }

      &::after {
        content: "";
        position: absolute;
        top: 80px; // 初始偏移 80px
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("https://pro-oss.pinkwallet.com/image/20250325/9fb69368f39e19d2fcb1454cf0668610_680x376.png");
        background-size: 100% 100%;
        background-position: center center;
        opacity: 0; // 初始透明
        // transition: opacity 0.8s ease-in-out;
        transition: all 0.8s ease-in-out;
      }
      // top 0.8s ease-in-out,
      &:hover::after {
        top: 0px; // 上移到正常位置
        opacity: 1; // 渐显
      }

      &:hover {
        transform: scale(1.05);
        // box-shadow: 0px -1px 80.1px 0px #000;
        box-shadow: 0px -1px 80.1px 0px #000;
        opacity: 1; // 确保 hover 后是完全显示
        z-index: 9999;
        // widows: 452px;
        // height: 250px;
        // box-shadow: 0px 8px 20p  x rgba(255, 192, 203, 0.5);
        // background: linear-gradient(180deg, rgba(30, 30, 30, 0) 37.36%, #EF88A3 167.71%);
        // background-image: url("https://pro-oss.pinkwallet.com/image/20250325/9fb69368f39e19d2fcb1454cf0668610_680x376.png");
        // background-size: 100% 100%;
        .hover-border {
          transform: scaleX(1); // hover 时从中间向两边展开
        }
      }
    }
  }
}

.crypto-box {
  overflow: hidden;
  background: #ff95b2;
  // padding: 20px;
  position: relative;

  .shadows {
    z-index: 999;
    width: 183px;
    height: 452px;
    background: linear-gradient(90deg, #ff95b2 0%, rgba(255, 149, 178, 0) 100%);
    position: absolute;
    top: 200px;
    left: 0;
  }

  .shadows2 {
    z-index: 999;
    width: 183px;
    height: 452px;
    background: linear-gradient(90deg, #ff95b2 0%, rgba(255, 149, 178, 0) 100%);
    position: absolute;
    top: 200px;
    right: 0;
    rotate: 180deg;
  }

  .title-container2 {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    margin-top: 90px;

    .title2 {
      color: #000;
      position: relative;
      // padding: 0 16px;
      font-family: MiSans-bold;
      font-weight: bold;
      font-size: 36px;
      line-height: 48px;
      letter-spacing: 0px;
      text-align: center;
      text-transform: capitalize;

      &::before,
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        width: 76px; // 横线宽度
        height: 2px; // 横线高度
        background-color: rgba(94, 9, 32, 0.2);
        border-radius: 10px;
      }

      &::before {
        left: -100px;
      }

      &::after {
        right: -100px;
      }
    }
  }

  .subtitles {
    margin-top: 14px;
    font-family: MiSans-regular;
    font-weight: 400;
    font-size: 16px;
    line-height: 21px;
    letter-spacing: 0px;
    text-align: center;
    text-transform: capitalize;

    color: #5e0920;
    padding-bottom: 61px;
  }

  .marquee-container {
    margin-bottom: 124px;
    max-width: 98.5vw;

    .line1 {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      // gap: 8px;

      // &:last-child {
      //     margin-bottom: 141px;
      // }
      .scroll-wrapper2 {
        margin-left: 8px;
        min-width: 379px;
        height: 123px;
        cursor: pointer;
        border-radius: 16px;
        background: rgba(255, 255, 255, 0.55);
        opacity: 0.3;

        border: 1px solid transparent;
        /* 初始边框透明，避免布局偏移 */
        outline: 1px solid rgba(255, 255, 255, 0.55);
        transition: outline-width 0.8s ease-in-out, transform 0.8s ease-in-out,
          opacity 0.8s ease-in-out, border-width 0.8s ease-in-out;

        // padding: 30px 105px 30px 32px;
        display: flex;
        align-items: center;

        &:hover {
          opacity: 1;
          outline-width: 4px;
        }

        .name {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          font-family: MiSans-dbold;
          font-weight: 600;
          font-size: 24px;
          line-height: 100%;
          letter-spacing: 0px;
          text-align: center;
          text-transform: capitalize;
          color: rgba(0, 0, 0, 1);

          .bom {
            margin-top: 4px;

            span {
              &:nth-of-type(1) {
                font-family: MiSans-regular;
                font-weight: 400;
                font-size: 20px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: rgba(0, 0, 0, 0.6);
              }

              &:nth-of-type(2) {
                margin-left: 14px;
                font-family: MiSans-regular;
                font-weight: 400;
                font-size: 20px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;

                // // 单数红色 双数绿色
                // &:nth-of-type(odd) {
                //   color: rgba(223, 55, 95, 1);
                // }

                // &:nth-of-type(even) {
                //   color: rgba(44, 168, 96, 1);
                // }
              }
            }
          }
        }

        img {
          width: 34px;
          margin: 0 16px 0 32px;
        }
      }
    }

    .line2 {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      // gap: 8px;

      // &:last-child {
      //     margin-bottom: 141px;
      // }
      .scroll-wrapper2 {
        margin-left: 8px;
        min-width: 379px;
        height: 123px;
        cursor: pointer;
        border-radius: 16px;
        background: rgba(255, 255, 255, 0.55);
        border: 1px solid transparent;
        /* 初始边框透明，避免布局偏移 */
        outline: 1px solid rgba(255, 255, 255, 0.55);
        transition: outline-width 0.8s ease-in-out, transform 0.8s ease-in-out,
          opacity 0.8s ease-in-out, border-width 0.8s ease-in-out;
        // padding: 30px 105px 30px 32px;
        display: flex;
        opacity: 0.6;
        align-items: center;

        .name {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          font-family: MiSans-dbold;
          font-weight: 600;
          font-size: 24px;
          line-height: 100%;
          letter-spacing: 0px;
          text-align: center;
          text-transform: capitalize;
          color: rgba(0, 0, 0, 1);

          .bom {
            margin-top: 4px;

            span {
              &:nth-of-type(1) {
                font-family: MiSans-regular;
                font-weight: 400;
                font-size: 20px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: rgba(0, 0, 0, 0.6);
              }

              &:nth-of-type(2) {
                margin-left: 14px;
                font-family: MiSans-regular;
                font-weight: 400;
                font-size: 20px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;

                // // 单数红色 双数绿色
                // &:nth-of-type(odd) {
                //   color: rgba(223, 55, 95, 1);
                // }

                // &:nth-of-type(even) {
                //   color: rgba(44, 168, 96, 1);
                // }
              }
            }
          }
        }

        img {
          width: 34px;
          margin: 0 16px 0 32px;
        }

        &:hover {
          outline-width: 4px;

          opacity: 1;
        }
      }
    }

    .line3 {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      margin-bottom: 16px;
      display: flex;
      align-items: center;

      // gap: 8px;
      // margin-bottom: 10px;
      // &:last-child {
      //     margin-bottom: 141px;
      // }
      .scroll-wrapper2 {
        // padding-bottom: 7px;
        margin-left: 8px;
        width: 379px;
        height: 123px;
        cursor: pointer;
        // box-shadow: 10px 100px 100.3px 10px rgba(220, 62, 105, 0.7);
        // box-shadow: 0px 7px 25.3px 0px rgba(220, 62, 105, 0.7);

        border-radius: 16px;
        background: rgba(255, 255, 255, 0.55);
        // border: 1px solid rgba(255, 255, 255, 0.55);
        border: 1px solid transparent;
        /* 初始边框透明，避免布局偏移 */
        outline: 1px solid rgba(255, 255, 255, 0.55);
        opacity: 0.9;
        transition: outline-width 0.8s ease-in-out, transform 0.8s ease-in-out,
          opacity 0.8s ease-in-out, border-width 0.8s ease-in-out;
        // padding: 30px 105px 30px 32px;
        display: flex;
        align-items: center;
        overflow: hidden;

        .name {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          font-family: MiSans-dbold;
          font-weight: 600;
          font-size: 24px;
          line-height: 100%;
          letter-spacing: 0px;
          text-align: center;
          text-transform: capitalize;
          color: rgba(0, 0, 0, 1);

          .bom {
            margin-top: 4px;

            span {
              &:nth-of-type(1) {
                font-family: MiSans-regular;
                font-weight: 400;
                font-size: 20px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;
                color: rgba(0, 0, 0, 0.6);
              }

              &:nth-of-type(2) {
                margin-left: 14px;
                font-family: MiSans-regular;
                font-weight: 400;
                font-size: 20px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                text-transform: capitalize;

                // // 单数红色 双数绿色
                // &:nth-of-type(odd) {
                //   color: rgba(223, 55, 95, 1);
                // }

                // &:nth-of-type(even) {
                //   color: rgba(44, 168, 96, 1);
                // }
              }
            }
          }
        }

        img {
          width: 34px;
          margin: 0 16px 0 32px;
        }

        &:hover {
          // border: 4px solid rgba(255, 255, 255, 0.55);
          outline-width: 4px;
          opacity: 1;
        }
      }
    }
  }
}

.applink {
  max-width: 100vw;
  background: #0a0a0a;
  padding: 140px 208px 151px 177px;
  display: flex;
  align-items: center;
  justify-content: center;
  // justify-content: space-between;
  gap: 153px;

  .leftapp {
    height: 575px;
    width: 373px;
    position: relative;
    // margin-left: -450px;
    transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
    // opacity: 0;

    &:hover {
      img {
        &:nth-of-type(1) {
          transform: translateX(-40px); // 进入动画
        }

        &:nth-of-type(2) {
          transform: translateX(40px); // 进入动画
        }
      }
    }

    &.animate-in-link {
      transform: translateX(450px); // 进入动画
      opacity: 1; // 透明度变为 100%
    }

    // .animate-in-up {
    //     transform: translateY(160px); // 进入动画
    //     opacity: 1; // 透明度变为 100%
    // }

    // .animate-in-down {
    //     transform: translateY(-160px); // 进入动画
    //     opacity: 1; // 透明度变为 100%
    // }

    img {
      position: absolute;
      transition: transform 0.5s ease-in-out;

      &:nth-of-type(1) {
        // margin-top: -160px;
        left: 0;
        top: 0;
        width: 217px;
        height: 491px;
      }

      &:nth-of-type(2) {
        right: 0;
        // margin-top: 160px;
        bottom: 0;
        width: 217px;
        height: 510px;
      }
    }
  }

  .rightapp {
    display: flex;
    // justify-content: flex-start;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
    // margin-right: -450px;
    transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
    // opacity: 0;

    &.animate-in-down {
      transform: translateX(-450px); // 进入动画

      opacity: 1; // 透明度变为 100%
    }

    .store-buttons {
      display: flex;
      gap: 16px;
      margin-top: 45px;

      .store-button {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.8s;
        border: 1px solid rgba(255, 255, 255, 1);
        width: 213px;
        height: 76px;
        overflow: hidden;
        border-radius: 12px;

        &:hover {
          background: #fff;

          div {
            span {
              &:nth-of-type(1) {
                color: rgba(0, 0, 0, 0.7);
              }

              &:nth-of-type(2) {
                color: #000;
              }
            }
          }
        }

        img {
          width: 31px;
          height: 32px;
          transition: opacity 0.3s ease-in-out;
        }

        div {
          margin-left: 12px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          span {
            &:nth-of-type(1) {
              font-family: MiSans;
              font-weight: 400;
              font-size: 12px;
              line-height: 16px;
              letter-spacing: 0px;
              text-transform: capitalize;
              color: rgba(255, 255, 255, 0.7);
            }

            &:nth-of-type(2) {
              font-family: MiSans-medium;
              font-weight: 500;
              font-size: 18px;
              line-height: 24px;
              letter-spacing: 0px;
              text-transform: capitalize;
              color: #fff;
            }
          }
        }
      }
    }

    .subtitle-link {
      font-family: MiSans;
      font-weight: 700;
      font-size: 52px;
      line-height: 69px;
      letter-spacing: 0px;
      text-transform: capitalize;
      color: #ff95b2;
      width: 600px;
      white-space: wrap;
    }

    .title-link {
      margin-bottom: 5px;
      font-family: MiSans;
      font-weight: 500;
      font-size: 20px;
      line-height: 32px;
      letter-spacing: 0px;
      text-transform: capitalize;
      color: rgba(255, 255, 255, 0.6);
    }
  }
}

.scroll-wrapper {
  margin: 0 62px 0 61px;
  overflow: hidden;
  white-space: nowrap;
  // width: 100%;
  padding: 20px 0;
  position: relative;

  .shadow {
    width: 191px;
    rotate: 180deg;
    height: 237px;
    background: linear-gradient(90deg, rgba(10, 10, 10, 0) 0%, #0a0a0a 71.91%);
    position: absolute;
    left: -1px;
    top: 0;
    z-index: 1;
  }

  .shadow2 {
    width: 191px;
    height: 237px;
    background: linear-gradient(90deg, rgba(10, 10, 10, 0) 0%, #0a0a0a 71.91%);
    position: absolute;
    right: 0;
    top: 0;
  }
}

.scroll-content {
  display: flex;
  gap: 20px;
  transform: translateX(0);
  transition: transform 0.1s linear;
}

.feature-cardscoll {
  flex: 0 0 auto;
  padding: 28px 60px;
  text-align: center;
  transition: transform 0.4s ease, border-color 0.4s ease, background 0.4s ease;

  width: 379px;
  // height: 215px;
  border-radius: 16px;
  background: #1e1e1e;
  border: 1px solid #4b4b4b;
  display: flex;
  flex-direction: column;
  align-items: center;

  span {
    &:nth-of-type(1) {
      font-family: MiSans;
      font-weight: 500;
      font-size: 24px;
      line-height: 150%;
      letter-spacing: 0.5%;
      text-align: center;
      color: #fff;
      line-height: 36px;
      margin: 16px 0 8px 0;
    }

    &:nth-of-type(1) {
      font-family: MiSans;
      font-weight: 400;
      font-size: 16px;
      line-height: 150%;
      letter-spacing: 0.5%;
      text-align: center;
      color: rgba(255, 255, 255, 0.5);
    }
  }

  &:hover {
    transform: scale(1.1);
    background: linear-gradient(180deg, #1e1e1e 60%, #ef88a3 234.88%);
    border-bottom: 3px solid #e2819b;
  }
}

.iconscroll {
  width: 43px;
  height: 47px;
  // margin-bottom: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 64px;
}

.stat-card {
  background-color: #1f2937;
  // padding: 24px;
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 30px;
  font-weight: bold;
  color: #ec4899;
  margin-bottom: 8px;
}

.stat-label {
  color: #9ca3af;
}

@media (max-width: 768px) {
  .download-section {
    flex-direction: column;
  }

  .download-content,
  .preview-image {
    width: 100%;
    margin-bottom: 32px;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .process-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
