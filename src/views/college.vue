<template>
    <div class="container">
        <div class="img_title">
            <img src="https://pro-oss.pinkwallet.com/image/1377667744802627584.png" :alt="t('college.title')">
        </div>
        <div class="title-container-card">
            <span class="title">{{ t('college.title') }}</span>
        </div>
        <div class="stock_search_bar">
            <div class="stock_search_input">
                <img src="https://pro-oss.pinkwallet.com/image/1377300822835683328.png" alt="search" srcset="">
                <input type="text" :placeholder="t('college.search_placeholder')">
            </div>
            <div class="stock_search_btn">
                {{ t('college.search_btn') }}
            </div>
        </div>
        <div class="title-container-card mt_288">
            <span class="title">{{ t('college.crypto_trading_title') }}</span>
        </div>
        <div class="msg_view">
            <div class="mt_68">{{ t('college.crypto_trading_desc1') }}</div>
            <div class="mt_30">{{ t('college.crypto_trading_desc2') }}</div>
            <div class="mt_44">
                <img src="https://pro-oss.pinkwallet.com/image/1377670251801042944.png" alt="crypto trading" srcset="">
            </div>
        </div>
        <!-- PinkWallet 介绍区块 开始 -->
        <div class="wallet_intro">
            <div class="wallet_title">{{ t('college.wallet_title') }}</div>
            <div class="wallet_subtitle">
                <span>{{ t('college.wallet_subtitle1') }}</span>
                <span class="divider">|</span>
                <span>{{ t('college.wallet_subtitle2') }}</span>
                <span class="divider">|</span>
                <span>{{ t('college.wallet_subtitle3') }}</span>
            </div>
            <div class="wallet_cards">
                <div class="wallet_card">
                    <div class="wallet_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377684146401927168.png" :alt="t('college.wallet_card1_title')" />
                    </div>
                    <div class="wallet_card_title">{{ t('college.wallet_card1_title') }}</div>
                </div>
                <div class="wallet_card">
                    <div class="wallet_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377684316610977792.png" :alt="t('college.wallet_card2_title')" />
                    </div>
                    <div class="wallet_card_title">{{ t('college.wallet_card2_title') }}</div>
                </div>
                <div class="wallet_card">
                    <div class="wallet_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377684225007378432.png" :alt="t('college.wallet_card3_title')" />
                    </div>
                    <div class="wallet_card_title">{{ t('college.wallet_card3_title') }}</div>
                </div>
            </div>
        </div>
        <!-- PinkWallet 介绍区块 结束 -->

        <!-- 新增内容开始 -->
        <div class="why_invest">
            <div class="title-container-card mt_288">
                <span class="title">{{ t('college.invest_title') }}</span>
            </div>
            <div class="section_desc">
                {{ t('college.invest_desc') }}
            </div>
            <div class="invest_cards">
                <div class="invest_card">
                    <div class="invest_card_num">01</div>
                    <div class="invest_card_text">{{ t('college.invest_card1') }}</div>
                </div>
                <div class="invest_card">
                    <div class="invest_card_num">02</div>
                    <div class="invest_card_text">{{ t('college.invest_card2') }}</div>
                </div>
                <div class="invest_card">
                    <div class="invest_card_num">03</div>
                    <div class="invest_card_text">{{ t('college.invest_card3') }}</div>
                </div>
                <div class="invest_card">
                    <div class="invest_card_num">04</div>
                    <div class="invest_card_text">{{ t('college.invest_card4') }}</div>
                </div>
                <div class="invest_card">
                    <div class="invest_card_num">05</div>
                    <div class="invest_card_text">{{ t('college.invest_card5') }}</div>
                </div>
                <div class="invest_card">
                    <div class="invest_card_num">06</div>
                    <div class="invest_card_text">{{ t('college.invest_card6') }}</div>
                </div>
            </div>
        </div>

        <div class="blockchain_section">
            <div class="title-container-card mt_288">
                <span class="title">{{ t('college.blockchain_title') }}</span>
            </div>
            <div class="blockchain_desc">
                {{ t('college.blockchain_desc') }}
            </div>
            <div class="blockchain_cards">
                <div class="blockchain_card">
                    <div class="blockchain_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377683630657724416.png" :alt="t('college.blockchain_card1')">
                    </div>
                    <div class="blockchain_card_text">{{ t('college.blockchain_card1') }}</div>
                </div>
                <div class="blockchain_card">
                    <div class="blockchain_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377683534222286848.png" :alt="t('college.blockchain_card2')">
                    </div>
                    <div class="blockchain_card_text">{{ t('college.blockchain_card2') }}</div>
                </div>
            </div>
        </div>

        <div class="security_section">
            <div class="title-container-card mt_288 mb_288">
                <span class="title">{{ t('college.security_title') }}</span>
            </div>
            <div class="security_cards">
                <div class="security_card">
                    <div class="security_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377684951779598336.png" :alt="t('college.security_card1_title')">
                    </div>
                    <div class="security_card_title">{{ t('college.security_card1_title') }}</div>
                    <div class="security_card_arrow">
                        <img src="https://pro-oss.pinkwallet.com/image/1377681646353473536.png" alt="arrow" srcset="">
                    </div>
                </div>
                <div class="security_card">
                    <div class="security_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377692438939394048.png" :alt="t('college.security_card2_title')">
                    </div>
                    <div class="security_card_title">{{ t('college.security_card2_title') }}</div>
                    <div class="security_card_arrow">
                        <img src="https://pro-oss.pinkwallet.com/image/1377681646353473536.png" alt="arrow" srcset="">
                    </div>
                </div>
                <div class="security_card">
                    <div class="security_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377692506270556160.png" :alt="t('college.security_card3_title')">
                    </div>
                    <div class="security_card_title">{{ t('college.security_card3_title') }}</div>
                    <div class="security_card_arrow">
                        <img src="https://pro-oss.pinkwallet.com/image/1377681646353473536.png" alt="arrow" srcset="">
                    </div>
                </div>
                <div class="security_card">
                    <div class="security_icon">
                        <img src="https://pro-oss.pinkwallet.com/image/1377692703423815680.png" :alt="t('college.security_card4_title')">
                    </div>
                    <div class="security_card_title">{{ t('college.security_card4_title') }}</div>
                    <div class="security_card_arrow">
                        <img src="https://pro-oss.pinkwallet.com/image/1377681646353473536.png" alt="arrow" srcset="">
                    </div>
                </div>
            </div>
        </div>
        <!-- 新增内容结束 -->
    </div>
</template>

<script setup>
    import { ref, computed, watchEffect, onMounted, watch } from 'vue'
    import { useI18n } from "vue-i18n";
    import * as echarts from 'echarts'
    const { locale, t } = useI18n();

    watchEffect(() => {
        console.log("语言切换为：", locale.value);
        localStorage.setItem('lang', locale.value)
    });
</script>

<style lang="scss" scoped>
    @import "../pxto.scss";

    .title-container-card {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        opacity: 1;
        margin-bottom: 79px;
        transition: all 0.8s;

        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }

        .title {
            font-weight: bold;
            position: relative;

            font-family: MiSans-bold;
            font-weight: 700;
            font-size: px(36);
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px;
                height: 2px;
                background-color: rgba(255, 255, 255, 0.2);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }
    }

    .container {
        width: 100%;
        max-width: 1440px;
        padding: 0 20px;
        box-sizing: border-box;
        margin: 60px auto;
        font-family: 'MiSans-regular', Arial, sans-serif;

        .img_title {
            img {
                width: 271px;
                height: 271px;
            }
        }

        .stock_search_bar {
            width: 1194px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: px(100);
            border-radius: px(16);
            border: 1px solid rgba(255, 255, 255, 1);
            padding: px(20) px(25);

            .stock_search_input {
                display: flex;
                align-items: center;
                justify-content: center;
                width: px(1000);
                height: px(60);
                border-radius: px(16);

                img {
                    width: px(28);
                    height: px(28);
                    margin-right: px(10);
                }

                input {
                    width: 100%;
                    height: 100%;
                    border: none;
                    background-color: transparent;
                    color: #fff;
                    font-size: px(24);
                }

                input::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                }
            }

            .stock_search_btn {
                width: px(180);
                height: px(60);
                border-radius: px(30);
                background-color: #EF88A3;
                color: #222;
                font-size: px(24);
                font-weight: 600;
                text-align: center;
                line-height: px(60);
                cursor: pointer;

            }
        }

        .msg_view {
            width: 748px;
            margin: 0 auto;

            div {
                font-size: px(18);
                color: #A7A7A7;
                line-height: 1.5;
            }

            img {
                width: px(396);
                height: px(396);
            }
        }
    }

    .wallet_intro {
        width: 100%;
        max-width: 1200px;
        margin: 60px auto 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #fff;

        .wallet_title {
            font-family: MiSans-bold;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: 1px;
            color: #fff;
            margin-bottom: 18px;
            position: relative;
            padding: 0 24px;

            &::before,
            &::after {
                content: '';
                display: inline-block;
                width: 60px;
                height: 2px;
                background: rgba(255, 255, 255, 0.2);
                vertical-align: middle;
                margin: 0 16px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }

        .wallet_subtitle {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #EF88A3;
            margin-bottom: 38px;

            span {
                margin: 0 8px;
            }

            .divider {}
        }

        .wallet_cards {
            display: flex;
            justify-content: center;
            gap: 10px;
            width: 100%;
            margin-top: 10px;
        }

        .wallet_card {
            background: #222222;
            border-radius: px(18);
            border: 1px solid rgba(255, 255, 255, 0.12);
            width: 429px;
            min-height: px(241);
            padding: px(40) px(18) px(24) px(18);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            transition: box-shadow 0.3s, border-color 0.3s;
            cursor: pointer;
        }

        .wallet_icon {
            width: px(48);
            height: px(48);
            margin-bottom: px(40);

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }

        .wallet_card_title {
            font-size: px(20);
            color: #fff;
            text-align: center;
            line-height: 1.6;
            font-weight: 600;
            word-break: break-all;
            font-family: 'MiSans';
        }
    }

    .why_invest {
        width: 100%;
        margin: 80px auto 0 auto;
        color: #fff;

        .section_title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 16px;
            position: relative;

            &::before,
            &::after {
                content: '';
                display: inline-block;
                width: 60px;
                height: 2px;
                background: rgba(255, 255, 255, 0.2);
                vertical-align: middle;
                margin: 0 16px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }

        .section_desc {
            text-align: center;
            color: #EF88A3;
            font-size: 16px;
            margin-bottom: 36px;
            line-height: 1.7;
        }

        .invest_cards {
            display: flex;
            flex-wrap: wrap;
            gap: px(20);
            justify-content: center;

            .invest_card {
                width: 400px;
                height: px(241);
                background: #222222;
                border-radius: 16px;
                border: 1px solid #4B4B4B;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                font-family: 'MiSans';

                &:hover {
                    box-shadow: 0 4px 32px 0 rgba(239, 136, 163, 0.18);
                    border-color: #EF88A3;
                }

                .invest_card_num {
                    font-size: 28px;
                    color: #EF88A3;
                    font-weight: 700;
                    margin-bottom: 8px;
                }

                .invest_card_text {
                    color: #fff;
                    font-size: 18px;
                    text-align: center;
                    line-height: 1.5;
                }
            }
        }
    }

    .blockchain_section {
        width: 100%;
        max-width: 1200px;
        margin: 80px auto 0 auto;
        color: #fff;

        .section_title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 16px;
            position: relative;
            font-family: 'MiSans';

            &::before,
            &::after {
                content: '';
                display: inline-block;
                width: 60px;
                height: 2px;
                background: rgba(255, 255, 255, 0.2);
                vertical-align: middle;
                margin: 0 16px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }

        .blockchain_desc {
            text-align: center;
            color: #A7A7A7;
            font-size: 16px;
            margin-bottom: 36px;
            line-height: 1.7;
        }

        .blockchain_cards {
            display: flex;
            justify-content: center;
            gap: px(23);

            .blockchain_card {
                width: 471px;
                min-height: px(213);
                background: #222222;
                border-radius: 16px;
                border: 1px solid #4B4B4B;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 32px 18px 24px 18px;
                transition: box-shadow 0.3s, border-color 0.3s;
                cursor: pointer;

                &:hover {
                    box-shadow: 0 4px 32px 0 rgba(239, 136, 163, 0.18);
                    border-color: #EF88A3;
                }

                .blockchain_icon {
                    width: 48px;
                    height: 48px;
                    margin-bottom: 18px;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                }

                .blockchain_card_text {
                    font-size: 18px;
                    color: #fff;
                    text-align: center;
                    line-height: 1.6;
                    font-weight: 600;
                    font-family: 'MiSans';
                }
            }
        }
    }

    .security_section {
        width: 100%;
        max-width: 1440px;
        margin: 80px auto 0 auto;
        color: #fff;

        .section_title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 36px;
            position: relative;

            &::before,
            &::after {
                content: '';
                display: inline-block;
                width: 60px;
                height: 2px;
                background: rgba(255, 255, 255, 0.2);
                vertical-align: middle;
                margin: 0 16px;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }

        .security_cards {
            display: flex;
            justify-content: center;
            gap: px(10);

            .security_card {
                background: #222222;
                border-radius: 16px;
                border: 1px solid rgba(255, 255, 255, 0.12);
                width: 323px;
                min-height: 388px;
                padding: 55px 44px 44px 44px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;
                transition: box-shadow 0.3s, border-color 0.3s;
                cursor: pointer;
                font-family: 'MiSans';

                &:hover {
                    border-color: #EF88A3;

                    .security_icon {
                        img {
                            filter: drop-shadow(0 0 8px #EF88A3);
                        }
                    }
                }

                .security_icon {
                    width: 86px;
                    height: 86px;
                    margin-bottom: 78px;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;

                    }
                }

                .security_card_title {
                    font-size: 18px;
                    color: #fff;
                    text-align: center;
                    line-height: 1.6;
                    font-weight: 600;
                    margin-bottom: 18px;
                    min-height: 48px;
                }

                .security_card_arrow {
                    text-align: center;

                    img {
                        width: px(36);

                    }
                }
            }
        }
    }
</style>