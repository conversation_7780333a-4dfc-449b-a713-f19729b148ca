const express = require("express");
const { createProxyMiddleware } = require("http-proxy-middleware");

const app = express();

// 反向代理 Zendesk
app.use(
  "/",
  createProxyMiddleware({
    target: "https://pinkwallet.zendesk.com",
    changeOrigin: true,
    pathRewrite: {
      "^/": "/hc/zh-sg/", // 将本地根路径映射到目标路径
    },
    onProxyRes(proxyRes) {
      // 移除阻止 iframe 加载的头部
      delete proxyRes.headers["x-frame-options"];
      delete proxyRes.headers["content-security-policy"];
    },
  })
);

app.listen(3000, () => {
  console.log("代理服务器运行在 http://localhost:3000");
});
