<template>
    <div class="container">
        <div class="img_title">
            <img src="https://pro-oss.pinkwallet.com/image/1374776145693204480.png"
                :alt="t('help.title')">
        </div>
        <div class="title-container-card">
            <span class="title">{{ t('help.title') }}</span>
        </div>
        <div class="stock_search_bar">
            <div class="stock_search_input">
                <img src="https://pro-oss.pinkwallet.com/image/1377300822835683328.png"
                    alt="search" srcset="">
                <input type="text" v-model="inputvalue" @keydown.enter="search"
                    :placeholder="t('help.search_placeholder')">
            </div>
            <div class="stock_search_btn" @click="search">
                {{ t('help.search_btn') }}
            </div>
        </div>
        <div class="title-container-card mt_300">
            <span class="title">{{ t('help.contact_title') }}</span>
        </div>
        <div class="help_center_list mb_300">
            <div class="help_center_item">
                <img src="https://pro-oss.pinkwallet.com/image/1377963145908805632.png"
                    :alt="t('help.online_service')" srcset="">
                <span class="title">{{ t('help.online_service') }}</span>
            </div>
            <div class="help_center_item">
                <img src="https://pro-oss.pinkwallet.com/image/1377963192432025600.png"
                    :alt="t('help.online_service')" srcset="">
                <span class="title">{{ t('help.online_service') }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watchEffect, onMounted, watch } from 'vue'
import { useI18n } from "vue-i18n";
import * as echarts from 'echarts'
const { locale, t } = useI18n();
const inputvalue = ref('')

const search = () => {
    window.open('https://pinkwallet.zendesk.com/hc/zh-sg/search?utf8=✓&query=' + inputvalue.value);
};

</script>

<style lang="scss" scoped>
@import "../pxto.scss";

.title-container-card {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    opacity: 1;
    margin-bottom: 79px;
    transition: all 0.8s;

    &.loaded-title {
        transform: translateY(60px);
        opacity: 1;
    }

    .title {
        font-size: 18px;
        font-weight: bold;
        position: relative;

        font-family: MiSans-bold;
        font-weight: 700;
        font-size: 36px;
        line-height: 100%;
        letter-spacing: 0px;
        text-align: center;
        text-transform: capitalize;
        color: #fff;

        &::before,
        &::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 76px;
            height: 2px;
            background-color: rgba(255, 255, 255, 0.2);
        }

        &::before {
            left: -100px;
        }

        &::after {
            right: -100px;
        }
    }
}

.container {
    width: 100%;
    max-width: 1440px;
    padding: 0 20px;
    box-sizing: border-box;
    margin: 60px auto;
    font-family: 'MiSans';

    .img_title {
        img {
            width: 271px;
            height: 271px;
        }
    }

    .stock_search_bar {
        width: 1194px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: px(100);
        border-radius: px(16);
        border: 1px solid rgba(255, 255, 255, 1);
        padding: px(20) px(25);

        .stock_search_input {
            display: flex;
            align-items: center;
            justify-content: center;
            width: px(1000);
            height: px(60);
            border-radius: px(16);

            img {
                width: px(28);
                height: px(28);
                margin-right: px(10);
            }

            input {
                width: 100%;
                height: 100%;
                border: none;
                background-color: transparent;
                color: #fff;
                font-size: px(24);
                border-style: none;
                background: none;
                font-family: MiSans;
                font-weight: 500;
                outline: none; // 去除选中状态边框
                text-transform: capitalize;


            }

            input::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }
        }

        .stock_search_btn {
            width: px(180);
            height: px(60);
            border-radius: px(30);
            background-color: #EF88A3;
            color: #222;
            font-size: px(24);
            font-weight: 600;
            text-align: center;
            line-height: px(60);
            cursor: pointer;

        }
    }

    .help_center_list {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: px(100) auto px(300);
        gap: px(70);

        .help_center_item {
            display: flex;
            justify-content: center;
            align-items: center;
            width: px(425);
            height: px(131);
            border-radius: px(16);
            background-color: #222;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;

            img {
                width: px(52);
                margin-right: px(28);
            }

            .title {
                font-size: px(20);
                font-weight: 600;
                color: #fff;
            }

        }
    }
}
</style>