<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
    <div class="root-container">
        <!-- Hero Section -->
        <div class="container">

            <!-- Download Section -->
            <div class="download-section">
                <div class="content-down">
                    <div class="download-content" :class="{ 'animate-in': isVisible }">
                        <div class="title">{{ $t("index.title") }}</div>
                        <div class="niu">{{ $t("index.tip") }}</div>
                        <p class="subtitle">{{ $t("index.tips") }}</p>

                        <div class="inputs">
                            <div class="input" :class="{ active: isActive }" @click="isActive = true"
                                @mouseleave="isActive = false">
                                <div class="prefix">
                                    <span>+1</span>
                                    <div class="shu"></div>
                                    <input type="text">
                                </div>
                            </div>

                            <div class="login" @click="nav_to('login'), isActivelogin = true"
                                :class="{ activelogin: isActivelogin }" @mouseleave="isActivelogin = false">{{
                                    $t("title.register") }} / {{ $t("title.login") }}
                            </div>

                        </div>
                        <div class="store-buttons">
                            <button class="store-button !rounded-button" @mouseenter="hovered = 'appstore'"
                                @mouseleave="hovered = ''">
                                <img :src="hovered == 'appstore'
                                    ? 'https://pro-oss.pinkwallet.com/image/20250327/4e3624591d6c397f1559893919411032_124x128.png'
                                    : 'https://pro-oss.pinkwallet.com/image/20250321/bef50247b4acf6ba44f795f38ccfc81a_124x128.png'
                                    " />
                                <div>
                                    <span>Download on the</span>
                                    <span>App Store</span>
                                </div>
                            </button>
                            <button class="store-button !rounded-button" @mouseenter="hovered = 'googleplay'"
                                @mouseleave="hovered = ''">
                                <img :src="hovered === 'googleplay'
                                    ? 'https://pro-oss.pinkwallet.com/image/20250324/b11ff143f668e2b79d0899aa7b7fbdf4_124x128.png'
                                    : 'https://pro-oss.pinkwallet.com/image/20250321/deff5942b1f51393727b5c6c278b83c7_124x128.png'
                                    " />
                                <div>
                                    <span>get on</span>
                                    <span>Google play</span>
                                </div>
                            </button>
                        </div>
                    </div>
                    <!-- <div style="width:240px;"></div> -->
                    <div class="preview-image" :class="{ 'animate-in': isVisible }">
                        <img :src="heroImage" alt="App Preview">
                    </div>
                </div>

            </div>

            <!-- Features Section -->
            <div class="features-grid">
                <div class="features-item">
                    <div v-for="(feature, index) in features" :key="index" class="feature-card"
                        :class="{ 'fade-in': visibleItems[index] }">
                        <img :src="feature.icon" />
                        <div>
                            <span>{{ feature.title }}</span>
                            <span>{{ feature.description }}</span>
                        </div>

                    </div>
                </div>

            </div>

            <!-- Business Process -->
            <div class="title-container-card" :class="{ 'loaded-title': isVisibleprocess }">
                <span class="title">{{ $t("process.subtitle") }}</span>
            </div>
            <div class="process-grid">
                <div class="process-item">
                    <div v-for="(process, index) in businessProcess" :key="index" class="process-card"
                        :class="{ 'fade-in': functionItems[index] && isVisibleprocess }">
                        <!-- <div class="process-header"> -->
                        <img :src="process.icon" class="icon" />
                        <!-- :style="{ height: process.h + 'px', width: process.w + 'px' }" -->
                        <div>
                            <span>{{ process.title }}</span>
                            <span>{{ process.description }}</span>
                        </div>
                        <!-- </div> -->
                    </div>
                </div>
            </div>


            <!-- container-card -->
            <div class="title-container-card" ref="titleContainer" :class="{ 'loaded-title': isVisiblefunction }">
                <span class="title">{{ $t("function.subtitle") }}</span>
            </div>
            <div class="container-card">
                <div class="card-container">
                    <div v-for="(item, index) in itemsFear" :key="index" class="card"
                        :class="{ 'loaded': isVisiblefunction }" ref="cardContainers">
                        <img :src="item.icon" />
                        <span class="title">{{ item.title }}</span>
                        <span class="desc">{{ item.description }}</span>
                        <div class="hover-border"></div>
                    </div>
                </div>


            </div>


            <!-- 支持 2000+ 币种 -->
            <div class="crypto-container">
                <!-- <div class="bgs"></div> -->
                <div class="title-container">
                    <span class="title">{{ $t("global.subtitle") }}</span>
                </div>

                <div class="subtitles">{{ $t("global.tip") }}</div>

                <div class="coinall" :class="{ 'coin-in': isVisible }">
                    <Vue3Marquee pauseOnHover="true" duration="40">
                        <div class="line1">
                            <div v-for="(coins, rowIndex) in coins[0]" :key="rowIndex" class="scroll-container">
                                <div class="crypto-item">
                                    <img :src="coins.img" />
                                    <span>{{ coins.name }}</span>
                                </div>
                            </div>
                        </div>
                    </Vue3Marquee>
                    <Vue3Marquee pauseOnHover="true" duration="40" direction="reverse">
                        <div class="line2">
                            <div v-for="(coins, rowIndex) in coins[1]" :key="rowIndex" class="scroll-container">
                                <div class="crypto-item">
                                    <img :src="coins.img" />
                                    <span>{{ coins.name }}</span>
                                </div>
                            </div>
                        </div>
                    </Vue3Marquee>
                    <Vue3Marquee pauseOnHover="true" duration="40">
                        <div class="line3">
                            <div v-for="(coins, rowIndex) in coins[2]" :key="rowIndex" class="scroll-container">
                                <div class="crypto-item">
                                    <img :src="coins.img" />
                                    <span>{{ coins.name }}</span>
                                </div>
                            </div>
                        </div>
                    </Vue3Marquee>
                </div>
                <!-- <div class="scroll-row" :class="{ 'reverse': rowIndex === 1 }">
                            <div v-for="(coin, index) in [...coins, ...coins]" :key="index" class="crypto-item">
                                <img :src="coin.img" :alt="coin.name" />
                                <span>{{ coin.name }}</span>
                            </div>
                        </div> -->
                <div class="bg">
                    <div class="animated-image">
                    </div>
                </div>
            </div>

            <!-- crypto-box -->
            <div class="crypto-box">
                <div class="shadows"></div>
                <div class="shadows2"></div>
                <!-- style="margin: 60px 0 14px 0 ;" -->
                <div class="title-container2">
                    <span class="title2">{{ $t("crypto.title") }}</span>
                </div>
                <div class="subtitles">{{ $t("crypto.tip") }}</div>


                <div class="marquee-container">
                    <Vue3Marquee pauseOnHover="true" duration="40">
                        <div class="line1">
                            <div v-for="(item, rowIndex) in cryptoData[0]" :key="rowIndex" class="scroll-wrapper2">
                                <img :src="item.img" :alt="item.name" />
                                <div class="name">
                                    <span>{{ item.name }}</span>
                                    <div class="bom">
                                        <span>${{ item.price }}</span>
                                        <span :style="{ color: item.change < 0 ? '#DF375F' : '#2CA860' }">
                                            {{ item.change > 0 ? '+' : '' }}{{ item.change }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Vue3Marquee>

                    <Vue3Marquee pauseOnHover="true" direction="reverse" duration="40">
                        <div class="line2">
                            <div v-for="(item, rowIndex) in cryptoData[1]" :key="rowIndex" class="scroll-wrapper2">
                                <img :src="item.img" :alt="item.name" />
                                <div class="name">
                                    <span>{{ item.name }}</span>
                                    <div class="bom">
                                        <span>${{ item.price }}</span>
                                        <span :style="{ color: item.change < 0 ? '#DF375F' : '#2CA860' }">
                                            {{ item.change > 0 ? '+' : '' }}{{ item.change }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Vue3Marquee>

                    <Vue3Marquee pauseOnHover="true" duration="40">
                        <div class="line3">
                            <div v-for="(item, rowIndex) in cryptoData[2]" :key="rowIndex" class="scroll-wrapper2">
                                <img :src="item.img" :alt="item.name" />
                                <div class="name">
                                    <span>{{ item.name }}</span>
                                    <div class="bom">
                                        <span>${{ item.price }}</span>
                                        <span :style="{ color: item.change < 0 ? '#DF375F' : '#2CA860' }">
                                            {{ item.change > 0 ? '+' : '' }}{{ item.change }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Vue3Marquee>
                </div>

            </div>


            <!-- applink -->
            <div class="applink" ref="animatedElement">
                <!-- :class="{ 'animate-in-link': isVisible }"   :class="{ 'animate-in-link': isVisible }"-->
                <div class="leftapp" :style="computedStyle">
                    <img :class="{ 'animate-in-up': isVisible }"
                        src="https://pro-oss.pinkwallet.com/image/20250325/bbc00d5cad9c29a38bb40a25267e45e4_868x1964.png" />
                    <img :class="{ 'animate-in-down': isVisible }"
                        src="https://pro-oss.pinkwallet.com/image/20250325/045c451f62cf9692591f3e962ee6ae27_868x2040.png" />

                </div>
                <!-- :class="{ 'animate-in-down': isVisible }" -->
                <div class="rightapp" :style="RecomputedStyle">
                    <span class="title-link">{{ $t("applink.subtitle") }}</span>
                    <span class="subtitle-link">{{ $t("applink.title") }} </span>
                    <!-- <span class="subtitle-link">随时随地进行交易</span> -->
                    <div class="store-buttons">
                        <button class="store-button !rounded-button" @mouseenter="hovered = 'appstore'"
                            @mouseleave="hovered = ''">
                            <img :src="hovered === 'appstore'
                                ? 'https://pro-oss.pinkwallet.com/image/20250327/4e3624591d6c397f1559893919411032_124x128.png'
                                : 'https://pro-oss.pinkwallet.com/image/20250321/bef50247b4acf6ba44f795f38ccfc81a_124x128.png'
                                " />
                            <div>
                                <span>Download on the</span>
                                <span>App Store</span>
                            </div>
                        </button>
                        <button class="store-button !rounded-button" @mouseenter="hovered = 'googleplay'"
                            @mouseleave="hovered = ''">
                            <img :src="hovered === 'googleplay'
                                ? 'https://pro-oss.pinkwallet.com/image/20250324/b11ff143f668e2b79d0899aa7b7fbdf4_124x128.png'
                                : 'https://pro-oss.pinkwallet.com/image/20250321/deff5942b1f51393727b5c6c278b83c7_124x128.png'
                                " />
                            <div>
                                <span>get on</span>
                                <span>Google play</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { Vue3Marquee } from 'vue3-marquee'
import { ref, onMounted, onUnmounted, computed, reactive, watchEffect } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from 'vue-router'
const { locale, t } = useI18n();
import { getCoinList } from "../api/pinkexchange"
const router = useRouter()
const route = useRoute()


const nav_to = (e) => {
    router.push({
        path: e,
        // query: {
        //     title: '666'
        // }
    });
};

const isVisible = ref(false);
const isVisibleprocess = ref(false)
const isVisiblefunction = ref(false)
const isActive = ref(false)
const isActivelogin = ref(false)

const animatedElement = ref(null);
const visibilityRatio = ref(0); // 记录元素可见比例
const hasExitedBottom = ref(false); // 记录是否完全滑出底部

// 计算 X 轴偏移量
const computedStyle = computed(() => {
    // console.log(hasExitedBottom.value);

    if (hasExitedBottom.value) return {};
    const translateX = (1 - visibilityRatio.value) * 450; // 可见度越低，X 偏移越大
    console.log(translateX);

    return {
        transform: `translateX(${-translateX}px)`,
        transition: 'transform 0.8s ease-out'
    };
});

const RecomputedStyle = computed(() => {
    // console.log(visibilityRatio.value, 'hahaha');
    if (hasExitedBottom.value) return {};
    const translateX = (1 - visibilityRatio.value) * 450; // 可见度越低，X 偏移越大
    // console.log(translateX);

    return {
        transform: `translateX(${translateX}px)`,
        transition: 'transform 0.8s ease-out'
    };


});
// onMounted(() => {

// });
const imageUrl = 'https://pinkwallet.com/img/'; // 图片地址前缀

// let keyframes = '@keyframes playFrames {';
// for (let i = 1; i <= 150; i++) {
//     let percent = ((i - 1) / 149) * 100; // 计算百分比
//     let paddedIndex = String(i).padStart(3, '0'); // 补零
//     keyframes += `${percent.toFixed(2)}% { background-image: url('https://pinkwallet.com/img/${paddedIndex}.png'); } `;
// }
// keyframes += '}';

// // 在页面插入 keyframes
// const style = document.createElement('style');
// style.innerHTML = keyframes;
// document.head.appendChild(style);

const frameRate = 1000 / 30; // 30帧/秒，每帧 33.33ms
const totalFrames = 150; // 5 秒内 150 帧
const images = ref([]); // 存放所有图片路径
const currentFrame = ref(0);
// 1. 预加载所有帧图像
// const totalFrames = 150;
const imageBaseUrl = 'https://pinkwallet.com/img/';
const loadedImages = new Array(totalFrames).fill(false);

function preloadImages(callback) {
    let loadedCount = 0;

    for (let i = 1; i <= totalFrames; i++) {
        const paddedIndex = String(i).padStart(3, '0');
        const img = new Image();
        img.src = `${imageBaseUrl}${paddedIndex}.png`;

        img.onload = () => {
            loadedImages[i - 1] = true;
            loadedCount++;
            if (loadedCount === totalFrames) {
                callback(); // 全部加载完成后执行回调
            }
        };

        img.onerror = () => {
            console.warn(`第 ${paddedIndex} 张图片加载失败`);
            loadedCount++;
            if (loadedCount === totalFrames) {
                callback();
            }
        };
    }
}

// 2. 生成 keyframes 动画样式
function createKeyframes() {
    let keyframes = '@keyframes playFrames {';
    for (let i = 1; i <= totalFrames; i++) {
        let percent = ((i - 1) / (totalFrames - 1)) * 100;
        let paddedIndex = String(i).padStart(3, '0');
        keyframes += `${percent.toFixed(2)}% { background-image: url('${imageBaseUrl}${paddedIndex}.png'); } `;
    }
    keyframes += '}';

    const style = document.createElement('style');
    style.innerHTML = keyframes;
    document.head.appendChild(style);
}

// 3. 动画播放初始化
function startAnimation() {
    const target = document.getElementById('anim-box'); // 替换成你实际的元素 ID
    target.style.animation = 'playFrames 2.5s steps(150) infinite'; // 可以根据需要调整时长
}

// 4. 入口：先预加载，再生成动画
preloadImages(() => {
    console.log('所有帧已加载完毕，开始动画');
    createKeyframes();
    startAnimation();
});



// 加载 60 张图片
for (let i = 1; i <= 151; i++) {
    // images.value.push(`../assets/iloveimg-compressed/${i}.png`, import.meta.url); // 你可以修改路径
    const paddedIndex = String(i).padStart(3, "0"); // 补零，确保 001 ~ 060
    images.value.push(new URL(`https://pinkwallet.com/img/${paddedIndex}.png`));
    // images.value.push(new URL(`../assets/img/${paddedIndex}.png`, import.meta.url).href);
    // images.value.push(new URL(`https://pinkwallet.com/img/${paddedIndex}.png`, import.meta.url).href);
}

console.log(images.value);

// 计算当前应该显示哪张图片（150帧 -> 60 张图循环）
const currentImage = ref(images.value[0]);
const updateFrame = () => {
    currentFrame.value = (currentFrame.value + 1) % totalFrames;
    const imageIndex = Math.floor((currentFrame.value / totalFrames) * 150); // 映射到 60 张图片
    currentImage.value = images.value[imageIndex];
};
// const cryptoData = ref([
//     [
//         { name: "BTC", img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", price: "85,274.00", change: -2.51 },
//         { name: "ETH", img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", price: "1,911.05", change: -5.63 },
//         { name: "ZEC", img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", price: "36.02", change: -6.00 },
//         { name: "BSV", img: "https://pro-oss.pinkwallet.com/image/20250409/51eb14d51c7db041b9d8603b76903f39_200x200.png", price: "33.52", change: -5.20 },
//         { name: "BTC", img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", price: "85,274.00", change: -2.51 },
//         { name: "ETH", img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", price: "1,911.05", change: -5.63 },
//         { name: "ZEC", img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", price: "36.02", change: -6.00 },
//         { name: "BSV", img: "https://pro-oss.pinkwallet.com/image/20250409/51eb14d51c7db041b9d8603b76903f39_200x200.png", price: "33.52", change: -5.20 },
//     ],
//     [
//         { name: "ETH", img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", price: "1,911.05", change: -5.63 },
//         { name: "ZEC", img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", price: "36.02", change: -6.00 },
//         { name: "BTC", img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", price: "85,274.00", change: -2.51 },
//         { name: "BSV", img: "https://pro-oss.pinkwallet.com/image/20250409/51eb14d51c7db041b9d8603b76903f39_200x200.png", price: "33.52", change: -5.20 },
//         { name: "ETH", img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", price: "1,911.05", change: -5.63 },
//         { name: "ZEC", img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", price: "36.02", change: -6.00 },
//         { name: "BTC", img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", price: "85,274.00", change: -2.51 },
//         { name: "BSV", img: "https://pro-oss.pinkwallet.com/image/20250409/51eb14d51c7db041b9d8603b76903f39_200x200.png", price: "33.52", change: -5.20 },
//     ],
//     [
//         { name: "BSV", img: "https://pro-oss.pinkwallet.com/image/20250409/51eb14d51c7db041b9d8603b76903f39_200x200.png", price: "33.52", change: -5.20 },
//         { name: "BTC", img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", price: "85,274.00", change: -2.51 },
//         { name: "ZEC", img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", price: "36.02", change: -6.00 },
//         { name: "ETH", img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", price: "1,911.05", change: -5.63 },
//         { name: "BSV", img: "https://pro-oss.pinkwallet.com/image/20250409/51eb14d51c7db041b9d8603b76903f39_200x200.png", price: "33.52", change: -5.20 },
//         { name: "BTC", img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", price: "85,274.00", change: -2.51 },
//         { name: "ZEC", img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", price: "36.02", change: -6.00 },
//         { name: "ETH", img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", price: "1,911.05", change: -5.63 },
//     ]
// ]);

const iconList = [
    { img: "https://pro-oss.pinkwallet.com/image/20250324/924bb0d5c65dcd97e6a8e4ed538285f6_200x200.png", name: "XRP" },
    { img: "https://test-oss.pinkwallet.com/image/2025-06-30/1389262368554311680.png", name: "SOL" },
    { img: "https://test-oss.pinkwallet.com/image/2025-06-30/1389262702131503104.png", name: 'TRX' },
    { img: "https://test-oss.pinkwallet.com/image/2025-06-30/1389263004758925312.png", name: 'DOGE' },
    { img: "https://test-oss.pinkwallet.com/image/2025-06-30/1389263258560454656.png", name: 'ADA' },
    { img: "https://test-oss.pinkwallet.com/image/1365016564398493696.png", name: 'TON' },
    { img: "https://cdn.investing.com/crypto-logos/20x20/v2/chainlink.png", name: 'LINK' },
    { img: "https://test-oss.pinkwallet.com/image/1365015530120876032.png", name: 'AVAX' },
    { name: "BTC", img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png" },
    { name: "ETH", img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png" }
]

const cryptoData = ref([])

const coins = ref([
    // [
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", name: "BTC" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", name: "ETH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/755e43a416d0e5c8da12f05fdbc66f8a_200x200.png", name: "LTC" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", name: "BTC" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", name: "ETH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/755e43a416d0e5c8da12f05fdbc66f8a_200x200.png", name: "LTC" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", name: "BTC" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/aa8742781d4e085e1e4859628aed1fc7_200x200.png", name: "ETH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/755e43a416d0e5c8da12f05fdbc66f8a_200x200.png", name: "LTC" }
    // ],
    // [
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", name: "ZEC" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/074268a360b7591140ebd122e59e07e7_200x200.png", name: "DASH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/77561c8e96b56dffd63fa9095d7e1c2f_200x200.png", name: "XMR" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", name: "ZEC" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/074268a360b7591140ebd122e59e07e7_200x200.png", name: "DASH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/77561c8e96b56dffd63fa9095d7e1c2f_200x200.png", name: "XMR" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/caf96eacb819b4e3643a4c896511eb71_200x200.png", name: "ZEC" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/074268a360b7591140ebd122e59e07e7_200x200.png", name: "DASH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/77561c8e96b56dffd63fa9095d7e1c2f_200x200.png", name: "XMR" }
    // ],
    // [
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", name: "BCH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/5f3098c925856fba0dfd7772ca5f5563_236x200.png", name: "XLM" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250324/924bb0d5c65dcd97e6a8e4ed538285f6_200x200.png", name: "XRP" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", name: "BCH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/5f3098c925856fba0dfd7772ca5f5563_236x200.png", name: "XLM" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250324/924bb0d5c65dcd97e6a8e4ed538285f6_200x200.png", name: "XRP" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/e9547411f975530b274a7fa414ca0b17_201x200.png", name: "BCH" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250409/5f3098c925856fba0dfd7772ca5f5563_236x200.png", name: "XLM" },
    //     { img: "https://pro-oss.pinkwallet.com/image/20250324/924bb0d5c65dcd97e6a8e4ed538285f6_200x200.png", name: "XRP" }
    // ]
]);


const getCoinLists = async () => {
    const res = await getCoinList({
        pageNum: 1,
        pageSize: 20
    })
    // icon,name,
    if (res.code === 200) {
        const coinData = res.result.data.map(coin => {
            const matchedIcon = iconList.find(icon => icon.name === coin.base)

            const open = Number(coin.openPrice)
            const close = Number(coin.closePrice)

            // ✅ 计算涨跌幅，确保 open 和 close 都为有效数字
            let change = 0
            if (isFinite(open) && isFinite(close)) {
                change = ((close - open) / open * 100).toFixed(2)
            }

            return {
                name: coin.base,
                img: matchedIcon ? matchedIcon.img : '',
                price: isFinite(close) ? close : '--',
                change: Number(change) || '0.00'
            }
        })

        // 🔁 拷贝三次
        cryptoData.value = [coinData, coinData, coinData].map(group => [...group])
        coins.value = [coinData, coinData, coinData].map(group => [...group])
        console.log('cryptoData:', cryptoData.value)
    }
}
getCoinLists()


const hovered = ref("");
const chartRef = ref < HTMLElement | null > (null);
const heroImage = "https://pro-oss.pinkwallet.com/image/20250321/6c3a8dfc64c4bc3c884a108891ccfa41_1056x2272.png"

const features = computed(() => [
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/27cf738fa67e6c7d456243488fb9b00c_34x35.png",
        title: t("feature.title"),
        description: t("feature.content")
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/9789f670fa7cd1eb6534efc3a5227003_137x137.png",
        title: t("feature.title2"),
        description: t("feature.content2")
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/5ac3555b28ec181b53baabe141a00483_137x137.png",
        title: t("feature.title3"),
        description: t("feature.content3")
    },

]);

const visibleItems = ref(features.value.map(() => false));
setTimeout(() => {
    features.value.forEach((_, index) => {
        setTimeout(() => {
            visibleItems.value[index] = true;
        }, index * 200); // 每个元素延迟 0.2s
    });
}, 200);


const businessProcess = computed(() => [
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250326/7cdd36ea485aca7b1c597fa84be6342d_380x380.png",
        title: t("process.title"),
        description: t("process.content"),
        w: "190",
        l: "102"
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250326/66cd75436bc6133d2d186fb6b848d84f_760x760.png",
        title: t("process.title2"),
        description: t("process.content2"),
        w: "141",
        l: "117"
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250326/7a9632d1ef3a317c89b83072a406c7a1_760x760.png",
        title: t("process.title3"),
        description: t("process.content3"),
        w: "142",
        l: "107"
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250326/8f149e2bc638923ad55a6a0c460d5c79_760x760.png",
        title: t("process.title4"),
        description: t("process.content4"),
        w: "150",
        l: "150"
    }
]);





const itemsFear = computed(() => [
    { title: t("function.title"), description: t("function.content"), icon: "https://pro-oss.pinkwallet.com/image/20250325/1cefb8f985b547256a09c791b43cb7a2_196x212.png" },
    { title: t("function.title2"), description: t("function.content2"), icon: "https://pro-oss.pinkwallet.com/image/20250325/7a8812edb54a9e42f95b30baf09c9b0c_196x212.png" },
    { title: t("function.title3"), description: t("function.content3"), icon: "https://pro-oss.pinkwallet.com/image/20250325/38ebcf9e0bdd79508c5cf53a018be7b1_196x212.png" },
    { title: t("function.title4"), description: t("function.content4"), icon: "https://pro-oss.pinkwallet.com/image/20250325/1637bd23971d1daa8d3f0311f32d27df_196x212.png" },
    { title: t("function.title5"), description: t("function.content5"), icon: "https://pro-oss.pinkwallet.com/image/20250325/976d9378234fca5485826093d5b0d75f_196x212.png" },
    { title: t("function.title6"), description: t("function.content6"), icon: "https://pro-oss.pinkwallet.com/image/20250325/6f45a78f5a8c67e6d1ed0cb811c4f064_196x212.png" },
]);

const duplicatedItems = computed(() => [...itemsFear.value, ...itemsFear.value]); // 复制两遍，实现无缝滚动

const scrollContainer = ref(null);
const scrollContent = ref(null);
let scrollX = 0;
const scrollSpeed = 1.5;
let isScrolling = true;
let animationFrame;

const startAutoScroll = () => {
    const step = () => {
        if (scrollContainer.value && isScrolling) {
            scrollX -= scrollSpeed;
            if (Math.abs(scrollX) >= scrollContent.value.scrollWidth / 2) {
                scrollX = 0; // 回到起点，实现无缝循环
            }
            scrollContent.value.style.transform = `translateX(${scrollX}px)`;
        }
        animationFrame = requestAnimationFrame(step);
    };
    animationFrame = requestAnimationFrame(step);
};

const pauseScroll = () => {
    isScrolling = false;
};

const resumeScroll = () => {
    isScrolling = true;
};
let animationTimer;
const functionItems = ref(businessProcess.value.map(() => false));
const cardContainers = ref(null)
const titleContainer = ref(null);
onMounted(() => {

    const titleElement = document.querySelector('.title-container-card');
    const processElements = document.querySelectorAll('.process-grid');
    const functionElements = document.querySelectorAll('.container-card');
    console.log(functionElements);

    const observer2 = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                console.log(entry.target, processElements, 3333);
                // else if (entry.target === functionElements) {
                //     isVisiblefunction.value = true; // 标题动画
                // }
                if (entry.target === titleElement) {
                    isVisibleprocess.value = true; // 标题动画
                    console.log(functionItems);

                    setTimeout(() => {
                        businessProcess.value.forEach((_, index) => {
                            setTimeout(() => {
                                functionItems.value[index] = true;
                            }, index * 200); // 每个元素延迟 0.2s
                        });
                    }, 200);
                } else {
                    const index = [...processElements].indexOf(entry.target);
                    if (index !== -1) {
                        functionItems.value[index] = true; // 卡片动画
                    }
                }



            }
        });
    }, {
        threshold: 0.3 // 30% 进入可视范围触发
    })

    if (titleElement) observer2.observe(titleElement);
    processElements.forEach(el => observer2.observe(el));

    functionElements.forEach(el => observer2.observe(el));
    onUnmounted(() => observer2.disconnect()); // 组件销毁时移除监听


    const observer3 = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                isVisiblefunction.value = true;  // Change visibility status when element is in the viewport
            }
        });
    }, { threshold: 0.5 }); // Trigger when 50% of element is visible

    if (titleContainer.value) {
        observer3.observe(titleContainer.value); // Observe title container
    }

    cardContainers.value.forEach((card) => {
        observer3.observe(card); // Observe each card container
    });

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            // visibilityRatio.value = entry.intersectionRatio; // 记录元素可见部分比例（0~1）
            visibilityRatio.value = entry.intersectionRatio; // 记录可见部分比例

            const { bottom, top } = entry.boundingClientRect;
            const { height } = entry.rootBounds || {};

            console.log(bottom, top, height);
            // alert(bottom)

            // 1️⃣ **如果元素完全滑出底部，则锁定 translateX**
            if (bottom < 800) {
                hasExitedBottom.value = true;
            }

            // 2️⃣ **如果元素再次进入顶部，解锁 translateX**
            if (top > 0 && top < height) {
                hasExitedBottom.value = false;
            }
        });
    }, {
        threshold: Array.from({ length: 11 }, (_, i) => i * 0.1) // 生成 0.1, 0.2, ..., 1.0 的数组
    });

    if (animatedElement.value) {
        observer.observe(animatedElement.value);
    }

    animationTimer = setInterval(updateFrame, frameRate); // 每 33.33ms 刷新一次帧

    setTimeout(() => {
        isVisible.value = true;
    }, 800);
    startAutoScroll();
});



watchEffect(() => {
    // features.value.forEach((item) => {
    //     item.title = t(item.titleKey); // 监听语言变化，自动更新 title
    // });
    console.log("语言切换为：", locale.value);
    localStorage.setItem('lang', locale.value)
});
onUnmounted(() => {
    clearInterval(animationTimer);
    cancelAnimationFrame(animationFrame);
    if (animatedElement.value) {
        observer.unobserve(animatedElement.value);
    }
});
</script>


<style scoped lang="scss">
@import "./index.scss"
</style>