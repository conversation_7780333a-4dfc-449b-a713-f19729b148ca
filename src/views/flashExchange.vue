<template>
    <div class="container">
        <div class="title-container-card mt_180">
            <span class="title">{{ t('flash_exchange.title') }}</span>
        </div>
        <div class="sub_smg mt_20">
            {{ t('flash_exchange.subtitle') }}
        </div>
        <div class="swap_card">
            <div class="swap_card_div mb_30">
                <div class="left_name">
                    <div class="swap_card_label">{{ t('flash_exchange.you_have') }}</div> 
                    <div class="swap_card_input">
                        <input type="number" v-model="swapFromAmount" />
                    </div>
                </div>
                <div class="right_select" ref="fromSelectRef" @click="openCurrencySelect('from')">
                    <img :src="currencyList[fromCurrency].flag" :alt="currencyList[fromCurrency].code" />
                    <span>{{ currencyList[fromCurrency].code }}</span>
                    <img class="icon" src="https://pro-oss.pinkwallet.com/image/1377323693809164288.png" alt="" />
                </div>
            </div>
            <div class="swap_zh">
                <img src="https://pro-oss.pinkwallet.com/image/1377326965815074816.png" alt="" srcset="">
            </div>
            <div class="swap_card_div out">
                <div class="left_name">
                    <div class="swap_card_label">{{ t('flash_exchange.you_want') }}</div>
                    <div class="swap_card_input">
                        <input type="number" v-model="swapToAmount" />
                    </div>
                </div>
                <div class="right_select" ref="toSelectRef" @click="openCurrencySelect('to')">
                    <img :src="currencyList[toCurrency].flag" :alt="currencyList[toCurrency].code" />
                    <span>{{ currencyList[toCurrency].code }}</span>
                    <img class="icon" src="https://pro-oss.pinkwallet.com/image/1377323693809164288.png" alt="" />
                </div>
            </div>
        </div>
        <div class="swap_btn">
            {{ t('flash_exchange.swap_now') }}
        </div>
        <div class="title-container-card mt_300"> 
            <span class="title">{{ t('flash_exchange.advantages') }}</span>
        </div> 
        <div class="advantage_grid">
            <div class="advantage_card">
                <img class="advantage_icon" src="https://pro-oss.pinkwallet.com/image/1377329476890353664.png"
                    :alt="t('flash_exchange.advantage1_title')" />
                <div class="advantage_title">{{ t('flash_exchange.advantage1_title') }}</div>
                <div class="advantage_desc">{{ t('flash_exchange.advantage1_desc') }}</div>
            </div>
            <div class="advantage_card">
                <img class="advantage_icon" src="https://pro-oss.pinkwallet.com/image/1377329476890353664.png"
                    :alt="t('flash_exchange.advantage2_title')" />
                <div class="advantage_title">{{ t('flash_exchange.advantage2_title') }}</div>
                <div class="advantage_desc">{{ t('flash_exchange.advantage2_desc') }}</div>
            </div>
            <div class="advantage_card">
                <img class="advantage_icon" src="https://pro-oss.pinkwallet.com/image/1377329476890353664.png"
                    :alt="t('flash_exchange.advantage3_title')" />
                <div class="advantage_title">{{ t('flash_exchange.advantage3_title') }}</div>
                <div class="advantage_desc">{{ t('flash_exchange.advantage3_desc') }}</div>
            </div>
            <div class="advantage_card">
                <img class="advantage_icon" src="https://pro-oss.pinkwallet.com/image/1377329476890353664.png"
                    :alt="t('flash_exchange.advantage4_title')" />
                <div class="advantage_title">{{ t('flash_exchange.advantage4_title') }}</div>
                <div class="advantage_desc">{{ t('flash_exchange.advantage4_desc') }}</div>
            </div>
        </div>
        <div class="title-container-card mt_300 mb_44">
            <span class="title">{{ t('flash_exchange.process') }}</span>
        </div>
        <div class="view_grid" v-if="currentIndex === 0" :class="[`slide_${slideDirection}`]">
            <div class="view_container">
                <div class="left_view">
                    <p class="title">{{ t('flash_exchange.process1_title') }}</p>
                    <p class="desc">{{ t('flash_exchange.process1_desc') }}</p>
                </div>
                <div class="right_view">
                    <img src="https://pro-oss.pinkwallet.com/image/1377356820178886656.png" alt="闪兑流程" />
                </div>
            </div>
            <div class="view_bottom right mt_48" @click="nextPage(1)">
                <div class="right_icon">
                    <img src="https://pro-oss.pinkwallet.com/image/1377356567308492800.png" alt="" srcset="">
                </div>
            </div>
        </div>
        <div class="view_grid" v-if="currentIndex === 1" :class="[`slide_${slideDirection}`]">
            <div class="view_container">
                <div class="left_view">
                    <p class="title">{{ t('flash_exchange.process2_title') }}</p>
                    <p class="desc">{{ t('flash_exchange.process2_desc') }}</p>
                </div>
                <div class="right_view">
                    <img src="https://pro-oss.pinkwallet.com/image/1377360479633629184.png" alt="闪兑流程" />
                </div>
            </div>
            <div class="view_bottom mt_48">
                <div class="left_icon huise" @click="nextPage(0)">
                    <img src="https://pro-oss.pinkwallet.com/image/1377597318021865472.png" alt="" srcset="">
                </div>
                <div class="right_icon" @click="nextPage(2)">
                    <img src="https://pro-oss.pinkwallet.com/image/1377356567308492800.png" alt="" srcset="">
                </div>
            </div>
        </div>
        <div class="view_grid" v-if="currentIndex === 2" :class="[`slide_${slideDirection}`]">
            <div class="view_container">
                <div class="left_view">
                    <p class="title">{{ t('flash_exchange.process3_title') }}</p>
                    <p class="desc">{{ t('flash_exchange.process3_desc') }}</p>
                </div>
                <div class="right_view">
                    <img src="https://pro-oss.pinkwallet.com/image/1377360531487809536.png" alt="闪兑流程" />
                </div>
            </div>
            <div class="view_bottom left mt_48">
                <div class="left_icon" @click="nextPage(1)">
                    <img src="https://pro-oss.pinkwallet.com/image/1377356519657005056.png" alt="" srcset="">
                </div>
            </div>
        </div>
        <div class="step_indicator">
            <span
                v-for="i in 3"
                :key="i"
                :class="['dot', { active: currentIndex === i - 1 }]"
                @click="nextPage(i-1)"
                style="cursor:pointer"
            ></span>
        </div>
        <!-- 币种下拉弹窗 -->
        <div
          v-if="showCurrencySelect"
          class="currency_select_popup"
          :style="popupStyle"
        >
          <div
            v-for="(item, idx) in currencyList"
            :key="item.code"
            class="currency_select_item"
            :class="{ selected: idx === (selecting === 'from' ? fromCurrency : toCurrency) }"
            @click="selectCurrency(idx)"
          >
            <img :src="item.flag" class="currency_flag" />
            <span class="currency_code">{{ item.code }}</span>
            <span class="currency_name">{{ item.name }}</span>
            <span v-if="idx === (selecting === 'from' ? fromCurrency : toCurrency)" class="currency_selected_icon"><img src="https://pro-oss.pinkwallet.com/image/1377975913617645568.png" alt="" srcset=""></span>
          </div>
        </div>
        <!-- 遮罩 -->
        <div v-if="showCurrencySelect" class="currency_select_mask" @click="closeCurrencySelect"></div>
    </div>
</template>

<script setup>
    import { ref, computed, watchEffect, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
    import { useI18n } from "vue-i18n";
    import * as echarts from 'echarts'
    const { locale, t } = useI18n();
    const activeIndex = ref(0);
    const currentIndex = ref(0);
    const slideDirection = ref('right');
    const compareList = [
        { name: 'WISE', logo: 'https://pro-oss.pinkwallet.com/image/1374815832294121472.png', rate: '0.1288', fee: '6.44USD' },
        { name: 'BMO', logo: 'https://pro-oss.pinkwallet.com/image/1374815832294121472.png', rate: '0.1256', fee: '37.89USD' },
        { name: 'CITIBANK', logo: 'https://pro-oss.pinkwallet.com/image/1374815832294121472.png', rate: '0.1256', fee: '50.00USD' },
        { name: 'CIBC', logo: 'https://pro-oss.pinkwallet.com/image/1374815832294121472.png', rate: '0.1252', fee: '65.00USD' },
        { name: 'OCBC', logo: 'https://pro-oss.pinkwallet.com/image/1374815832294121472.png', rate: '0.1251', fee: '65.00USD' },
        { name: 'RBC', logo: 'https://pro-oss.pinkwallet.com/image/1374815832294121472.png', rate: '0.1254', fee: '115.00USD' },
        { name: 'OFX', logo: 'https://pro-oss.pinkwallet.com/ima ge/1374815832294121472.png', rate: '0.1252', fee: '100.00USD' },
        { name: 'TD', logo: 'https://pro-oss.pinkwallet.com/image/1374815832294121472.png', rate: '0.1253', fee: '230.00USD' },
        { name: 'PayPal', logo: 'https://pro-oss.pinkwallet.com/image/1374815832294121472.png', rate: '0.1205', fee: '38.99USD' }
    ]
    const advantages = computed(() => [
        {
            img: 'https://pro-oss.pinkwallet.com/image/1376617913472606208.png',
            title: t("realTimeER.advantage1_title"),
            desc: t("realTimeER.advantage1_desc")
        },
        {
            img: 'https://pro-oss.pinkwallet.com/image/1376617992510070784.png',
            title: t("realTimeER.advantage2_title"),
            desc: t("realTimeER.advantage2_desc")
        },
        {
            img: 'https://pro-oss.pinkwallet.com/image/1376618091109769216.png',
            title: t("realTimeER.advantage3_title"),
            desc: t("realTimeER.advantage3_desc")
        },
        {
            img: 'https://pro-oss.pinkwallet.com/image/1376618144067051520.png',
            title: t("realTimeER.advantage4_title"),
            desc: t("realTimeER.advantage4_desc")
        }
    ])

    const handleClick = (index) => {
        activeIndex.value = index
    }
    const nextPage = (index) => {
        if (index > currentIndex.value) {
            slideDirection.value = 'right';
        } else {
            slideDirection.value = 'left';
        }
        currentIndex.value = index;
    }
    watchEffect(() => {
        // features.value.forEach((item) => {
        //     item.title = t(item.titleKey); // 监听语言变化，自动更新 title
        // });
        console.log("语言切换为：", locale.value);
        localStorage.setItem('lang', locale.value)

    });

    const stockChartRef = ref(null)
    let chartInstance = null

    const stockChartData = ref({
        xData: [
            '05:45', '06:10', '06:35', '07:00', '07:25', '07:50', '08:15', '08:40', '14:25', '14:50', '15:15', '15:40', '16:05', '16:30', '16:55', '17:20', '17:45', '18:10', '18:35', '19:00', '19:25', '19:50', '20:15', '20:40'
        ],
        yData: [102, 98, 105, 95, 110, 90, 115, 100, 120, 85, 125, 80, 130, 75, 135, 140, 120, 145, 110, 150, 100, 155, 95, 160]
    })

    const renderChart = () => {
        if (!stockChartRef.value) return
        if (!chartInstance) {
            chartInstance = echarts.init(stockChartRef.value)
        }
        const option = {
            backgroundColor: 'transparent',
            tooltip: {
                trigger: 'axis',
                backgroundColor: '#222',
                borderColor: '#222',
                borderWidth: 1,
                borderRadius: 16,
                textStyle: { color: 'rgba(255,255,255,0.5)', fontSize: 14 },
                formatter: function (params) {
                    // params[0] 是当前点
                    const time = params[0].axisValue;
                    const value = params[0].data;
                    return `日期：${time}<br/>收盘价：${value}`;
                },
                extraCssText: 'box-shadow:none;padding:8px 14px;line-height:1.6;',
                axisPointer: {
                    lineStyle: {
                        color: 'rgba(255, 130, 163, 1)',
                        width: 1,
                        type: 'dashed'
                    }
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: stockChartData.value.xData,
                axisLine: { show: false },
                axisLabel: { color: 'rgba(255,255,255,0.5)' }
            },
            yAxis: {
                type: 'value',
                position: 'right',
                axisLine: { lineStyle: { color: '#fff' } },
                splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } },
                axisLabel: { color: 'rgba(255,255,255,0.5)' }
            },
            series: [
                {
                    data: stockChartData.value.yData,
                    type: 'line',
                    smooth: false,
                    showSymbol: false,
                    lineStyle: { color: '#EF88A3', width: 2 },
                    itemStyle: { color: '#EF88A3' },
                    emphasis: {
                        focus: 'series',
                        itemStyle: {
                            color: 'rgba(255, 130, 163, 1)',
                            borderColor: 'rgba(79, 48, 56, 1)',
                            borderWidth: 10
                        },
                        symbolSize: 14
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(239,136,163,0.3)' },
                            { offset: 1, color: 'rgba(239,136,163,0)' }
                        ])
                    }
                }
            ]
        }
        chartInstance.setOption(option)
    }

    onMounted(() => {
        renderChart()
        window.addEventListener('resize', () => {
            chartInstance && chartInstance.resize()
        })
    })

    const timeTabs = [
        { label: '24h', value: '24h' },
        { label: '7天', value: '7d' },
        { label: '1月', value: '1m' },
        { label: '3月', value: '3m' },
        { label: '1年', value: '1y' }
    ]
    const currentTab = ref('24h')
    const selectTab = (val) => {
        currentTab.value = val
        // 这里可以触发图表数据切换
    }

    // tools 下拉菜单
    const showTools = ref(false)
    const toolsList = [
        { label: '导出图片', value: 'export_img' },
        { label: '下载数据', value: 'download_data' },
        { label: '自定义指标', value: 'custom_indicator' }
    ]
    const toggleTools = () => {
        showTools.value = !showTools.value
    }
    const selectTool = (val) => {
        showTools.value = false
        // 这里可以根据 val 做不同操作
    }

    const currencyList = [
        { code: 'USDC', name: 'USDC', flag: 'https://pro-oss.pinkwallet.com/image/1377323227146706944.png' },
        { code: 'USDT', name: 'USDT', flag: 'https://pro-oss.pinkwallet.com/image/1377323227146706944.png' },
        { code: 'BTC', name: '比特币', flag: 'https://pro-oss.pinkwallet.com/image/1377323227146706944.png' }
        // ...如有更多币种可补充
    ]

    const fromCurrency = ref(0)  // 默认USDC
    const toCurrency = ref(0)    // 默认USDC
    const showCurrencySelect = ref(false)
    const selecting = ref('from') // 当前弹窗属于from还是to
    const popupStyle = ref({})
    const fromSelectRef = ref(null)
    const toSelectRef = ref(null)

    function openCurrencySelect(side) {
        selecting.value = side
        nextTick(() => {
            updatePopupPosition()
            showCurrencySelect.value = true
        })
    }

    function selectCurrency(idx) {
        if (selecting.value === 'from') {
            fromCurrency.value = idx
        } else {
            toCurrency.value = idx
        }
        showCurrencySelect.value = false
    }

    function closeCurrencySelect() {
        showCurrencySelect.value = false
    }

    function updatePopupPosition() {
        let refEl = selecting.value === 'from' ? fromSelectRef.value : toSelectRef.value
        if (refEl) {
            const rect = refEl.getBoundingClientRect()
            popupStyle.value = {
                position: 'fixed',
                top: `${rect.top + rect.height + 6}px`,
                left: `${rect.left}px`,
                minWidth: `${rect.width}px`,
                zIndex: 1001
            }
        }
    }

    function handleScrollOrResize() {
        if (showCurrencySelect.value) {
            updatePopupPosition()
        }
    }

    onMounted(() => {
        window.addEventListener('scroll', handleScrollOrResize, true)
        window.addEventListener('resize', handleScrollOrResize)
    })
    onBeforeUnmount(() => {
        window.removeEventListener('scroll', handleScrollOrResize, true)
        window.removeEventListener('resize', handleScrollOrResize)
    })
</script>

<style lang="scss" scoped>
    @import "../pxto.scss";

    .title-container-card {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        opacity: 1;
        transition: all 0.8s;

        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            position: relative;

            font-family: MiSans-bold;
            font-weight: 700;
            font-size: 36px;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px;
                height: 2px;
                background-color: rgba(255, 255, 255, 0.2);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }
    }

    .container {
        width: 100%;
        max-width: 1440px;
        padding: 0 20px;
        box-sizing: border-box;
        margin: 60px auto;
        font-family: 'MiSans';

        .sub_smg {
            color: rgba(181, 181, 181, 1);
            font-size: px(16);
            font-weight: 400;
            line-height: 1.5;
            text-align: center;
            margin-bottom: 79px;
        }

        .swap_card {
            width: 800px;
            margin: 0 auto;
            position: relative;

            .swap_card_div {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                width: 100%;
                height: px(154);
                background-color: #212121;
                border: 1px solid rgba(255, 255, 255, 0.08);
                border-radius: px(12);
                padding: px(47) px(60) px(47) px(40);

                &.out {
                    background-color: #000;
                    border: 1px solid #FFFFFF;
                }

                .left_name {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: rgba(142, 142, 142, 0.5);
                    font-size: px(20);
                    height: px(46);

                    .swap_card_label {
                        padding-right: px(20);
                        border-right: 1px solid rgba(255, 255, 255, 0.08);
                    }

                    .swap_card_input {
                        margin-left: px(57);

                        input {
                            height: px(46);
                            font-size: px(36);
                            font-weight: 600;
                            color: rgba(255, 255, 255, 1);
                            background-color: transparent;
                            border: none;
                        }
                    }
                }

                .right_select {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 1px solid #fff;
                    padding: px(10) px(20);
                    border-radius: px(50);
                    cursor: pointer;
                    font-size: px(24);
                    color: #fff;

                    img {
                        width: px(34);
                        height: px(34);
                        margin-right: px(10);
                    }

                    .icon {
                        width: px(28);
                        height: px(28);
                        margin-left: px(10);
                    }
                }
            }

            .swap_zh {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: auto;
                z-index: 9;
                cursor: pointer;
                width: px(60);
                height: px(60);

                img {
                    width: px(60);
                    height: px(60);
                }
            }
        }

        .swap_btn {
            width: 749px;
            height: px(60);
            background-color: #EF88A3;
            color: #222;
            font-size: px(20);
            font-weight: 600;
            text-align: center;
            line-height: px(60);
            border-radius: px(15);
            cursor: pointer;
            margin: px(44) auto;
        }

        .img_title {
            img {
                width: 271px;
                height: 271px;
            }
        }

        .cart_div {
            width: 1194px;
            background-color: #222222;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 0 auto;
            border-radius: 10px;
            padding: 28px;
            margin-bottom: px(207);

            .input_black {
                display: flex;
                justify-content: space-between;
                justify-items: flex-end;

                .input_div {
                    p {
                        font-size: px(24);
                        text-align: left;
                        color: rgba(255, 255, 255, 0.6);
                        margin: 0px;
                        margin-bottom: 15px;
                        font-weight: 400;
                    }

                    .black {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        border: 1px solid #fff;
                        border-radius: 15px;
                        width: 503px;
                        padding: 13px 12px 13px 17px;

                        .left_name {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            font-size: 17px;
                            font-weight: 600;

                            .tx {
                                width: 33px;
                                height: 33px;
                                margin-right: 14px;
                                margin-left: 0px;
                            }

                            img {
                                width: 18px;
                                height: 18px;
                                margin-left: 12px;
                            }
                        }

                        .input {
                            width: 100%;
                            height: 100%;

                            input {
                                width: 100%;
                                height: 100%;
                                border: none;
                                background-color: transparent;
                                font-size: 17px;
                                font-weight: 600;
                                color: #fff;
                                text-align: right;
                            }
                        }
                    }

                }

                .icon {
                    display: flex;
                    align-items: flex-end;
                    padding-bottom: 14px;

                    img {
                        width: px(54);
                        height: px(54);
                    }
                }
            }

            .button_text {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: px(31);

                >div {
                    font-size: px(24);
                    font-weight: 600;
                    color: #fff;
                }

                button {
                    width: px(180);
                    height: px(59);
                    border-radius: px(29);
                    color: #000000;
                    font-size: px(20);
                    background-color: #FF95B2;
                    outline: none;
                }
            }
        }

        .hv_list {
            width: 1194px;
            margin: 0 auto;

            .header {
                display: flex;
                justify-content: space-between;
                margin-bottom: px(40);
                padding: 0 px(25);

                li {
                    font-size: px(24);
                    color: #EF88A3;

                    &.icon {
                        width: 384px;
                        text-align: left;

                        p {
                            margin: 0;
                            margin-left: px(107);
                        }
                    }

                    &.parities {
                        text-align: center;
                        width: 450px;
                    }

                    &.service_charge {
                        text-align: center;
                        width: 359px;
                    }
                }
            }
        }

        .table {
            width: 1194px;

            li {
                display: flex;
                justify-content: space-between;
                align-items: center;
                background-color: #212121;
                height: px(100);
                border-radius: px(16);
                border: 1px solid #4B4B4B;
                margin-bottom: px(15);
                padding: px(25);
                font-size: px(20);

                &.active {
                    background-color: rgba(239, 136, 163, 0.1);
                    border: 1px solid rgba(239, 136, 163, 1);
                }

                .icon {
                    width: 384px;
                    text-align: left;
                    height: px(70);
                    line-height: px(70);
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;

                    span {
                        font-size: px(24);
                        color: #fff;
                    }

                    img {
                        width: px(44);
                        margin-left: px(107);
                        margin-right: px(10);
                    }
                }

                .parities {
                    text-align: center;
                    width: 450px;
                    border-right: 1px solid rgba(255, 255, 255, 0.2);
                    border-left: 1px solid rgba(255, 255, 255, 0.2);
                    flex: 1;
                    height: px(70);
                    line-height: px(70);
                    font-size: px(24);
                    color: #fff;

                    &.active {
                        border-right: 1px solid rgba(239, 136, 163, 0.2);
                        border-left: 1px solid rgba(239, 136, 163, 0.2)
                    }
                }

                .service_charge {
                    text-align: center;
                    width: 359px;
                    height: px(70);
                    line-height: px(70);
                    font-size: px(24);

                    &.red {
                        color: #EF88A3;
                    }

                    &.green {
                        color: #04A431;
                    }
                }
            }
        }

        .stock_search_bar {
            width: 1194px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: px(100);
            border-radius: px(16);
            border: 1px solid rgba(255, 255, 255, 1);
            padding: px(20) px(25);

            .stock_search_input {
                display: flex;
                align-items: center;
                justify-content: center;
                width: px(1000);
                height: px(60);
                border-radius: px(16);

                img {
                    width: px(28);
                    height: px(28);
                    margin-right: px(10);
                }

                input {
                    width: 100%;
                    height: 100%;
                    border: none;
                    background-color: transparent;
                    color: #fff;
                    font-size: px(24);
                }

                input::placeholder {
                    color: rgba(255, 255, 255, 0.6);
                }
            }

            .stock_search_btn {
                width: px(180);
                height: px(60);
                border-radius: px(30);
                background-color: #EF88A3;
                color: #222;
                font-size: px(24);
                font-weight: 600;
                text-align: center;
                line-height: px(60);
                cursor: pointer;

            }
        }
    }

    .stock_chart_container {
        width: 1194px;
        margin: 40px auto 0 auto;
        border-radius: 16px;
        padding: 24px 0;
    }

    .stock_chart {
        width: 100%;
        height: 340px;
        min-height: 240px;
        background: transparent;
    }

    .stock_info_container {}

    .stock_info_header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        width: 1194px;
        margin: 0 auto px(24) auto;

        .stock_info_left {
            .stock_name {
                color: #fff;
                font-size: px(24);
                font-weight: 500;
                margin-bottom: px(12);
                text-align: left;
            }

            .stock_price_row {
                display: flex;
                align-items: baseline;
                margin-bottom: px(12);

                .stock_price {
                    color: #fff;
                    font-size: px(48);
                    font-weight: bold;
                    margin-right: px(24);
                }

                .stock_price_change {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .stock_price_change_num,
                    .stock_price_change_percent {
                        font-size: px(24);
                        font-weight: 600;

                        &.green {
                            color: #04A431;
                        }

                        &.red {
                            color: #EF88A3;
                        }
                    }
                }
            }

            .stock_time_tabs {
                display: flex;
                gap: px(12);

                ._tab {
                    padding: px(2) px(16);
                    border-radius: px(12);
                    background: none;
                    color: #fff;
                    font-size: px(24);
                    cursor: pointer;
                    border: none;
                    transition: background 0.2s, color 0.2s;

                    &.active {
                        background: #EF88A3;
                        color: #222;
                    }
                }
            }
        }

        .stock_info_tools {
            position: relative;

            .tools_dropdown {
                display: flex;
                align-items: center;
                gap: px(4);
                background: #232026;
                color: #fff;
                border-radius: px(8);
                padding: px(8) px(16);
                cursor: pointer;
                font-size: px(24);
                user-select: none;
                border: 1px solid rgba(255, 255, 255, 0.1);

                svg {
                    margin-left: px(4);
                    transition: transform 0.2s;
                }
            }

            .tools_menu {
                position: absolute;
                top: px(40);
                right: 0;
                background: #232026;
                border-radius: px(8);
                box-shadow: 0 px(2) px(8) rgba(0, 0, 0, 0.15);
                min-width: px(120);
                z-index: 10;

                .tools_menu_item {
                    padding: px(10) px(20);
                    color: #fff;
                    cursor: pointer;
                    font-size: px(15);

                    &:hover {
                        background: #EF88A3;
                        color: #222;
                    }
                }
            }
        }
    }

    .advantage_grid {
        width: 100%;
        max-width: 1040px;
        margin: px(60) auto 0 auto;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        grid-gap: px(25);
        justify-items: center;
    }

    .advantage_card {
        background: #222;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: px(16);
        padding: px(40) 0 px(32) 0;
        width: 100%;
        min-height: px(180);
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
        position: relative;
        transition: box-shadow 0.2s;
    }

    .advantage_icon {
        width: px(40);
        height: px(40);
        margin-bottom: px(18);
        filter: brightness(1.2) saturate(1.2);
    }

    .advantage_title {
        color: #fff;
        font-size: px(26);
        font-weight: 700;
        margin-bottom: px(12);
        letter-spacing: 1px;
    }

    .advantage_desc {
        color: rgba(255, 255, 255, 0.5);
        font-size: px(18);
        font-weight: 400;
        text-align: center;
        line-height: 1.6;
        max-width: px(320);
    }

    .view_grid {
        width: 884px;
        max-width: 884px;
        background-color: #222;
        border-radius: px(16);
        padding: px(50) px(48);
        margin: 0 auto;
        border: 1px solid #4B4B4B;
        position: relative;
        overflow: hidden;
        transition: all 0.5s ease-in-out;
        opacity: 0;

        &.slide_right {
            animation: slideRight 0.5s forwards;
        }

        &.slide_left {
            animation: slideLeft 0.5s forwards;
        }

        .view_container {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;

            .left_view {
                width: 300px;
                background-color: #222;
                border-radius: 16px;
                padding: px(14) 0;
                text-align: left;

                .title {
                    font-size: px(26);
                    font-weight: 600;
                    color: #FF95B2;
                    margin-bottom: px(35);
                }

                .desc {
                    font-size: px(20);
                    font-weight: 400;
                    color: rgba(255, 255, 255, 0.5);
                    margin-bottom: px(34);
                }
            }

            .right_view {
                width: 324px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }

        }

        .view_bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            &.right{
                justify-content: flex-end;
            }
            &.left{
                justify-content: flex-start;
            }
            .left_icon {
                border: 1px dashed #fff;
                width: px(70);
                height: px(70);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                &.huise{
                    background-color:#1F1F1F;
                    border:none;
                }
                img {
                    width: px(25);
                }
            }

            .right_icon {
                border: 1px dashed #fff;
                width: px(70);
                height: px(70);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    width: px(25);
                }
            }
        }
    }

    @keyframes slideRight {
        0% {
            transform: translateX(100%);
            opacity: 0;
        }
        100% {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideLeft {
        0% {
            transform: translateX(-100%);
            opacity: 0;
        }
        100% {
            transform: translateX(0);
            opacity: 1;
        }
    }

    .step_indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: px(99) 0 0 0;
        gap: px(11);
        .dot {
            width: px(8);
            height: px(8);
            border-radius: 6px;
            background: #333;
            opacity: 0.5;
            transition: all 0.3s;
            &.active {
                background: #ef88a3;
                opacity: 1;
                width: px(21);
            }
        }
    }

    .currency_select_mask {
        position: fixed;
        left: 0; top: 0; right: 0; bottom: 0;
        background: transparent;
        z-index: 1000;
    }
    .currency_select_popup {
        background: #232323;
        border-radius: 12px;
        box-shadow: 0 4px 24px rgba(0,0,0,0.18);
        min-width: 180px;
        padding: 0;
        overflow: hidden;
        border: 1px solid #333;
        z-index: 1001;
    }
    .currency_select_item {
        display: flex;
        align-items: center;
        padding: 12px 18px;
        cursor: pointer;
        color: #fff;
        font-size: 17px;
        position: relative;
        background: transparent;
        transition: background 0.2s;
        border-bottom: 1px solid #2d2d2d;
        width: 221px;
        &:last-child {
            border-bottom: none;
        }
        &:hover, &.selected {
            background: #313131;
        }
        .currency_flag {
            width: 28px; height: 28px; margin-right: 12px; border-radius: 50%;
            background: #fff;
            object-fit: cover;
        }
        .currency_code { font-weight: bold; margin-right: 8px; }
        .currency_name { color: #ccc; }
        .currency_selected_icon {
            margin-left: auto;
            color: #FF95B2;
            font-size: 18px;
            font-weight: bold; img {
        width: 16px;
        height: 16px;
      }
        }
    }
</style>