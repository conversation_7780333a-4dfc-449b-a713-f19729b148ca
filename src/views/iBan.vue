<template>
    <div class="container">
        <div class="title-container-card mt_200">
            <span class="title">{{ t('iban.title') }}</span>
        </div>
        <div class="iban_msg">
            {{ t('iban.intro') }}
        </div>
        <div class="search_section">
            <p>{{ t('iban.search_title') }}</p>
            <div class="search_input">
                <input type="text" :placeholder="t('iban.search_placeholder')">
            </div>
        </div>
        <div class="iban_btn">
            {{ t('iban.check_btn') }}
        </div>
        <div class="title-container-card mt_300">
            <span class="title">{{ t('iban.what_title') }}</span>
        </div>
        <div class="msg_view">
            <img src="https://pro-oss.pinkwallet.com/image/1377949234056749056.png" :alt="t('iban.what_title')" srcset="">
            <div class="mt_30">{{ t('iban.what_desc1') }}</div>
            <div class="mt_30">{{ t('iban.what_desc2') }}</div>
            <div class="mt_30">{{ t('iban.what_desc3') }}</div>
        </div>
        <div class="title-container-card mt_300">
            <span class="title">{{ t('iban.structure_title') }}</span>
        </div>
        <div class="msg_view">
            <div class="mt_30">{{ t('iban.structure_desc') }}</div>
        </div>
        <div class="iban_example_card">
            <div class="iban_example_header">
                <span>{{ t('iban.example_title') }}</span>
                <img class="flag_icon" src="https://pro-oss.pinkwallet.com/image/1377950614167314432.png" alt="German flag">
            </div>
            <div class="iban_example_table">
                <div class="iban_example_row iban_example_row--top">
                    <div class="iban_example_cell">DE</div>
                    <div class="iban_example_cell">
                        <img src="https://pro-oss.pinkwallet.com/image/1377950863631933440.png" alt="">
                    </div>
                    <div class="iban_example_cell">22</div>
                    <div class="iban_example_cell">
                        <img src="https://pro-oss.pinkwallet.com/image/1377950863631933440.png" alt="">
                    </div>
                    <div class="iban_example_cell">
                        <img src="https://pro-oss.pinkwallet.com/image/1377950863631933440.png" alt="">
                    </div>
                    <div class="iban_example_cell iban_example_cell--iban max_width">**********************</div>
                </div>
                <div class="iban_example_row iban_example_row--bottom">
                    <div class="iban_example_cell iban_example_label">{{ t('iban.example_country') }}</div>
                    <div class="iban_example_cell iban_example_label">{{ t('iban.example_sepa') }}</div>
                    <div class="iban_example_cell iban_example_label">{{ t('iban.example_length') }}</div>
                    <div class="iban_example_cell iban_example_label">{{ t('iban.example_check') }}</div>
                    <div class="iban_example_cell iban_example_label">{{ t('iban.example_branch') }}</div>
                    <div class="iban_example_cell iban_example_label max_width">{{ t('iban.example_iban') }}</div>
                </div>
            </div>
        </div>
        <div class="title-container-card mt_300">
            <span class="title">{{ t('iban.diff_title') }}</span>
        </div>
        <div class="msg_view">
            <img src="https://pro-oss.pinkwallet.com/image/1377953902698782720.png" alt="SWIFT vs IBAN vs Routing" srcset="">
            <div class="mt_30">{{ t('iban.diff_desc') }}</div>
        </div>
        <div class="title-container-card mt_300">
            <span class="title">{{ t('iban.error_title') }}</span>
        </div>
        <div class="iban_error_section">
            <div class="iban_error_cards">
                <div class="iban_error_card">
                    <img class="iban_error_icon" src="https://pro-oss.pinkwallet.com/image/1377953902698782720.png"
                        :alt="t('iban.error_input_title')">
                    <div class="iban_error_title">{{ t('iban.error_input_title') }}</div>
                    <div class="iban_error_desc">{{ t('iban.error_input_desc') }}</div>
                </div>
                <div class="iban_error_card">
                    <img class="iban_error_icon" src="https://pro-oss.pinkwallet.com/image/1377953902698782720.png"
                        :alt="t('iban.error_format_title')">
                    <div class="iban_error_title">{{ t('iban.error_format_title') }}</div>
                    <div class="iban_error_desc">{{ t('iban.error_format_desc') }}</div>
                </div>
                <div class="iban_error_card">
                    <img class="iban_error_icon" src="https://pro-oss.pinkwallet.com/image/1377953902698782720.png"
                        :alt="t('iban.error_recipient_title')">
                    <div class="iban_error_title">{{ t('iban.error_recipient_title') }}</div>
                    <div class="iban_error_desc">{{ t('iban.error_recipient_desc') }}</div>
                </div>
                <div class="iban_error_card">
                    <img class="iban_error_icon" src="https://pro-oss.pinkwallet.com/image/1377953902698782720.png"
                        :alt="t('iban.error_country_title')">
                    <div class="iban_error_title">{{ t('iban.error_country_title') }}</div>
                    <div class="iban_error_desc">{{ t('iban.error_country_desc') }}</div>
                </div>
            </div>
            <div class="iban_error_btn_wrap mt_80">
                {{ t('iban.check_btn') }}
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, computed, watchEffect, onMounted, watch } from 'vue'
    import { useI18n } from "vue-i18n";
    const { locale, t } = useI18n();
    watchEffect(() => {
        console.log("语言切换为：", locale.value);
        localStorage.setItem('lang', locale.value)

    });


</script>

<style lang="scss" scoped>
    @import "../pxto.scss";

    .title-container-card {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        opacity: 1;
        margin-bottom: 79px;
        transition: all 0.8s;

        &.loaded-title {
            transform: translateY(60px);
            opacity: 1;
        }

        .title {
            font-weight: bold;
            position: relative;

            font-family: MiSans-bold;
            font-weight: 700;
            font-size: px(36);
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: #fff;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px;
                height: 2px;
                background-color: rgba(255, 255, 255, 0.2);
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }
    }

    .container {
        width: 100%;
        max-width: 1440px;
        padding: 0 20px;
        box-sizing: border-box;
        margin: 60px auto;
        font-family: 'MiSans-regular', Arial, sans-serif;

        .iban_msg {
            font-size: px(20);
            color: #EF88A3;
            max-width: 800px;
            margin: 0 auto;
        }

        .search_section {
            width: px(800);
            height: px(270);
            background-color: #222;
            border-radius: px(16);
            margin: 0 auto;
            margin-top: px(52);
            border: 1px solid #FFFFFF33;
            padding: px(34);

            p {
                font-size: px(18);
                color: rgba(255, 255, 255, 0.6);
                margin-bottom: 20px;
                text-align: left;
            }

            .search_input {
                width: px(708);
                height: px(74);
                background-color: #333;
                border-radius: px(15);
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid #FFFFFF33;
                padding: px(20) px(45);

                input {
                    width: 100%;
                    height: 100%;
                    background-color: transparent;
                    outline: none;
                    border: none;
                    font-size: px(20);
                    color: #fff;
                    font-family: 'MiSans';
                }
            }
        }

        .iban_btn {
            width: px(498);
            height: px(60);
            background-color: #EF88A3;
            border-radius: px(15);
            font-size: px(20);
            color: #000;
            margin: 0 auto;
            margin-top: px(37);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-family: 'MiSans';
        }

        .msg_view {
            width: 748px;
            margin: 0 auto;

            div {
                font-size: px(18);
                color: #A7A7A7;
                line-height: 1.5;

                span {
                    color: #FF95B2;
                }
            }

            img {
                width: px(172);
                height: px(172);
                margin-top: px(40);
            }
        }

        .iban_example_card {
            background: #181818;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            padding: 0;
            margin: 40px auto 0 auto;
            max-width: px(1193);
            border: 1px solid #222;
            overflow: hidden;
        }

        .iban_example_header {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: px(24);
            font-weight: 600;
            color: #fff;
            background: #181818;
            border-bottom: 1px solid #222;
            padding: px(37) 0 px(37) 0;
            letter-spacing: 1px;

            .flag_icon {
                width: 24px;
                height: 24px;
                margin-left: 8px;
                vertical-align: middle;
            }
        }

        .iban_example_table {
            width: 100%;
            background: #181818;
            padding: 0 0 10px 0;
        }

        .iban_example_row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            min-width: px(100);

            &.iban_example_row--top {
                padding: 24px 32px 0 32px;
            }

            &.iban_example_row--bottom {
                padding: 10px 32px 18px 32px;
            }

            &.max_width {
                width: px(300);
            }
        }

        .iban_example_cell {
            /* flex: 1 1 0; */
            text-align: center;
            color: #fff;
            font-size: px(20);
            font-family: 'MiSans-regular', Arial, sans-serif;
            min-width: px(100);

            img {
                width: px(36);
                height: px(36);
            }

            &.iban_example_cell--iban {
                font-weight: 600;
                font-size: px(20);
                color: #fff;
                text-align: right;
                letter-spacing: 1px;
                font-family: 'MiSans-bold', Arial, sans-serif;
            }

            &.iban_example_label {
                color: #A7A7A7;
                font-size: px(20);
                font-weight: 400;
                margin-top: px(40);
                text-align: center;
                min-width: px(100);
                font-family: 'MiSans-regular', Arial, sans-serif;
            }

            &.max_width {
                width: px(300);
            }
        }

        .iban_error_section {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: px(40);
            margin-bottom: px(60);
        }

        .iban_error_cards {
            width: 100%;
            max-width: 1194px;   
            display: flex;
            justify-content: center;
            gap: px(8);
            margin-bottom: px(40);
            flex-wrap: wrap;
        }

        .iban_error_card {
            background: #181818;
            border-radius: 12px;
            border: 1px solid #222;
            width: px(323);
            min-height: px(388);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: px(32) px(18) px(24) px(18);
            transition: box-shadow 0.2s;
            cursor: pointer;
        }

        .iban_error_icon {
            width: px(88);
            height: px(88);
            margin-bottom: px(45);
            filter: brightness(1.2) saturate(1.2) drop-shadow(0 0 0 #FF95B2);
        }

        .iban_error_title {
            color: #fff;
            font-size: px(24);
            font-weight: 600;
            margin-bottom: px(10);
            text-align: center;
            font-family: 'MiSans';
        }

        .iban_error_desc {
            color: rgba(255, 255, 255, 0.5);
            font-size: px(16);
            text-align: center;
            line-height: 1.6;
            font-family: 'MiSans-regular', Arial, sans-serif;
        }

        .iban_error_btn_wrap {
            width: px(287); 
            height: px(60);
            display: flex;
            justify-content: center;
            align-items: center;
            background: #FF95B2;
            color: #181818;
            border: none;
            border-radius: px(30);
            font-size: px(20);
            font-family: 'MiSans-bold', Arial, sans-serif;
            padding: px(10) px(20);
            cursor: pointer;
        }
    }
</style>