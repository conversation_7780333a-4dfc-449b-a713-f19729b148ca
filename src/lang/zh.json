{"title.welcome": "欢迎使用PinkWallet", "title.LiveSupport": "在线客服", "title.history": "交易记录", "title.confirm": "确认", "title.cancel": "取消", "title.SetPasswordTitle": "安全设置：双重密码", "title.SetPasswordContent": "您尚未设置登录密码与二级密码，请立即完成安全设置", "title.SetPasswordconfirm": "立即设置", "title.SetPasswordCancel": "暂不设置", "title.SetPasswordLoginPwd": "设置登录密码：", "title.SetPasswordPayPwd": "设置二级密码：", "title.submit": "确认", "title.startText": "获取验证码", "title.changeText": "x秒后重新获取", "title.endText": "重新获取", "title.chart": "价格走势", "title.copy": "复制成功", "page.detail": "当前货币详情", "page.forget": "忘记密码", "page.login": "登录", "page.register": "注册", "page.center": "账户中心", "page.profile": "实名认证", "page.deposit": "充值", "page.withdraw": "转出", "page.Record": "交易记录", "page.CurrencyCharge": "法币充值", "page.swap": "闪兑", "page.TransferOut": "转出", "page.Notification": "系统通知", "page.AddCount": "添加收款人", "page.security": "安全设置", "page.settingPwd": "设置密码", "page.AccountDetail": "账户详情", "page.QuickBuy": "快捷买入", "page.Receive": "充值", "page.TransactionHistory": "交易历史", "page.sell": "卖出", "Please.email": "请输入邮箱", "Please.verification": "请输入验证码", "Please.pwd": "请输入密码", "Please.erroremail": "邮箱格式错误", "Please.PasswordRequirement1": "至少 8 个字符", "Please.PasswordRequirement2": "必须包含至少 1 个小写字母", "Please.PasswordRequirement3": "必须包含至少 1 个大写字母", "Please.full": "请输入完整信息", "Please.agreements": "请先勾选协议", "Types.Deposit": "存款", "Types.Withdrawal": "取款", "Types.Transfer": "转账", "Types.Swap": "兑换", "Types.Reward": "奖励", "Types.Buy": "买币", "Types.Receive": "收款", "Types.Send": "转账", "Types.Sell": "卖出", "Types.convert": "兑换", "Onboarding.FullyCompliant": "全球合规认证", "Onboarding.FullyCompliant.Licensed": "持牌机构：欧盟（VASP牌照）、美国（MSB牌照）、加拿大（MSB牌照）、新西兰（FSP牌照）、香港（MSO牌照）", "Onboarding.FullyCompliant.Insurance": "50亿美元担保", "Onboarding.FullyCompliant.Security": "Fireblocks机构级托管", "Onboarding.OffshoreBank": "远程开通离岸数字银行账户", "Onboarding.OffshoreBank.InstantSetup": "3分钟极速开户", "Onboarding.OffshoreBank.MinimalDocs": "极简KYC材料", "Onboarding.OffshoreBank.FreeService": "零费用开通-免开户费/管理费", "Onboarding.BestRates": "最优汇率保障", "Onboarding.BestRates.Description": "实时比对30+家顶级做市商报价，确保您获得最优兑换汇率", "Onboarding.BuyCrypto": "极速购买数字货币", "Onboarding.BuyCrypto.Description": "支持银联/万事达/Visa信用卡快捷购买", "Onboarding.GetStarted": "立即体验", "Onboarding.Skip": "跳过引导", "Auth.SignIn": "账户登录", "Auth.SignIn.Description": "请输入您的账户信息", "Auth.SignIn.PhoneNumber": "邮箱", "Auth.SignIn.VerificationCodeLogin": "验证码登录", "Auth.SignIn.Password": "登录密码", "Auth.SignIn.ForgotPassword": "忘记密码？", "Auth.SignIn.Button": "立即登录", "Auth.SignIn.SignInWith": "其他登录方式", "Auth.SignIn.Google": "Google账号", "Auth.SignIn.FaceID": "面容识别", "Auth.SignIn.NoAccount": "没有账户？", "Auth.SignIn.SignUp": "注册新账户", "Auth.FaceID": "点击进行面容ID登录", "Auth.FaceID.Access": "进入资产总览", "Auth.FaceID.NotRecognized": "未能识别面孔", "Auth.FaceID.TryAgain": "再此尝试面容ID", "Auth.FaceID.SignInWithPhoneNumber": "切换手机号登录", "Auth.ForgotPassword": "密码重置", "Auth.ForgotPassword.Description": "请输入您注册时使用的邮箱", "Auth.ForgotPassword.PhoneNumber": "已绑定邮箱", "Auth.ForgotPassword.VerificationCode": "短信验证码", "Auth.ForgotPassword.GetOTP": "获取验证码", "Auth.ForgotPassword.EnterOTP": "请输入6位验证码", "Auth.ForgotPassword.OTPTimer": "剩余59秒", "Auth.ForgotPassword.NoOTP": "收不到验证码？联系人工客服", "Auth.ForgotPassword.NewPassword": "新登录密码", "Auth.ForgotPassword.PasswordRequirement1": "至少8个字符", "Auth.ForgotPassword.PasswordRequirement2": "包含小写字母", "Auth.ForgotPassword.PasswordRequirement3": "包含大写字母", "Auth.ForgotPassword.Submit": "确认重置", "Auth.SignUp": "新用户注册", "Auth.SignUp.Description": "请输入以下信息完成开户", "Auth.SignUp.PhoneNumber": "邮箱", "Auth.SignUp.VerificationCode": "验证码", "Auth.SignUp.GetOTP": "获取验证码", "Auth.SignUp.Agreement": "我已阅读并同意", "Auth.SignUp.Terms": "《用户协议》", "Auth.SignUp.Privacy": "《隐私协议》", "Auth.SignUp.and": "及", "Auth.SignUp.Button": "立即开户", "Auth.SignUp.SignUpWith": "快速注册", "Auth.SignUp.AlreadyHaveAccount": "已有账户？", "Auth.SignUp.SignIn": "立即登录", "Auth.InvitationCode": "邀请码", "register.title": "注册", "register.PhoneNumber": "手机号码", "register.VerificationCode": "验证码", "register.agree": "我已同意", "register.registerAgree": "《注册协议》", "register.IBan": "《IBAN开户协议》", "register.and": "和", "register.cancel": "取消", "register.Google": "用 Google 登录", "register.PwdLogin": "密码登录", "register.GetingCode": "正在获取验证码", "register.PhonePlz": "请输入手机号再获取", "register.Later": "倒计时结束后再发送", "register.PhonePlzVerfy": "请输入手机号", "register.CodePlz": "请输入验证码", "register.AgreementPlz": "请先勾选协议", "register.success": "注册成功", "register.Send": "验证码已发送", "Login.title": "登录", "Login.PhoneNumber": "手机号码", "Login.VerificationCodeLogin": "验证码登录", "Login.PasswordLogin": "密码登录", "Login.Google": "用 Google 登录", "Login.ForgotPwd": "忘记密码", "Login.Getcode": "获取验证码", "Login.Login": "登录", "Login.cancel": "取消", "ForgetPwd.title": "忘记密码", "ForgetPwd.PhoneNumber": "手机号码", "ForgetPwd.VerificationCode": "验证码", "ForgetPwd.GetCode": "获取验证码", "ForgetPwd.NewPwd": "新密码", "ForgetPwd.tips": "密码长度 8 个字符以上", "ForgetPwd.tip1": "必须包含至少 1 个小写字母和 1 个大写字母", "ForgetPwd.ResetPwd": "重置密码", "ForgetPwd.cancel": "取消", "index.Balance.YourBalance": "资产总览", "index.Balance.ReceiveCurrencies": "买入", "index.Balance.ReceiveCrypto": "充值", "index.TransactionHistory.Title": "交易历史", "index.TransactionHistory.All": "全部", "index.TransactionHistory.Empty": "暂无交易记录", "index.TransactionHistory.EmptyDescription": "立即充值开启数字资产管理", "index.day": "天", "index.Currencies.Title": "法币", "index.Crypto.Title": "数字货币", "index.Crypto.HideZero": "隐藏小额资产", "index.more": "更多", "index.Deposit": "存款", "index.Transfer": "确认", "index.History": "历史", "Transaction.Success.Title": "交易成功", "Transaction.Success.Description": "数字资产已到账，可前往资产总览查看", "Transaction.Details.Title": "交易记录", "Transaction.Details.ID": "交易哈希", "Transaction.Details.Amount": "到账数量", "Transaction.Details.TotalCost": "实际扣款金额", "Transaction.Details.DateTime": "区块确认时间", "Transaction.Button.GoHome": "返回资产总览", "notice.title": "通知", "notice.LiveSupport": "在线支持", "notice.KYCPassed": "KYC 已通过", "notice.WithdrawalCompleted": "取款完成", "notice.SwapCompleted": "交换完成", "Send.Send": "转出", "Send.CurrencyForPayment": "选择账户", "Send.Available": "可用余额", "Send.RecipientAccount": "接收账户", "Send.AddAccount": "新增接收账户", "Send.PleaseSelectTheRecipientAccount": "请选择常用接收账户", "Send.pwcount": "PinkWallet账户(0手续费)", "Send.EstimatedAmountToBeReceived": "预计到账金额", "Send.addpee": "添加收款人", "Send.PleaseSelectAccountType": "选择账户类型", "Send.SetPassword": "安全验证", "Send.YouHaventSetALoginPasswordAndAPaymentPasswordYet": "您尚未设置登录密码与资金密码", "Send.SetLoginPassword": "设置登录密码：", "Send.SetPaymentPassword": "设置资金密码：", "Send.Submit": "确认设置", "Send.Cancel": "取消操作", "Send.ConfirmTransfer": "确认发送", "Send.YouAreCurrentlyMakingAPaymentOf": "您正在转出：", "Send.PleaseEnterYourPaymentPassword": "请输入6位资金密码", "Send.Show": "显示", "Send.SetNow": "立即设置", "Send.Later": "暂不设置", "Send.EditAccount": "编辑收款人", "Send.Currency": "币种类型", "Send.AllCurrencies": "全币种账户", "Send.AccountHolderName": "账户持有人姓名", "Send.InMyOwnName": "本人同名账户", "Send.BankName": "银行全称", "Send.SwiftCode": "SWIFT账号", "Send.IbanNumber": "IBAN国际账号", "Send.AccountNumber": "银行账号", "Send.BankAddress": "银行注册地址", "Send.ReferenceOptional": "附言（可选，限20字符）", "Send.TokenPayAccount": "通证支付账户", "Send.PleaseEnterYourTokenPayUsersMobileNumberEmailAddressOrScanTheOtherPartysPaymentQRCode": "请输入收款方PinkWallet ID/手机号/邮箱 或 扫描收款二维码", "Send.LongPressToPaste": "长按粘贴剪贴板内容", "Send.TokenAddress": "合约地址", "Send.SetAsGeneralAddressApplicableToAllCurrencies": "设为默认地址", "Send.Network": "主网类型", "Send.Memo": "区块链备注", "Send.TagOptional": "标签（可选）", "Send.SearchAsset": "搜索资产", "Send.Cash": "现金账户", "Send.Crypto": "数字货币", "Send.nobalance": "小额资产", "Send.Recent": "最近", "Send.Frequent": "常用", "Send.PinkWalletAccount": "PinkWallet 账户", "Send.AccountDetail": "账户详情", "Record.type": "类型", "Record.Date": "日期", "Record.Status.Init": "初始化", "Record.Status.InProgress": "进行中", "Record.Status.Completed": "完成", "Record.Status.Failed": "失败", "Sell.SellCrypto": "出售数字货币", "Sell.YouSell": "数量", "Sell.Max": "最大可售", "Sell.Available": "可用余额", "Sell.YouGet": "到账", "Sell.ReceiveFundsIn": "收款至", "Sell.AddNew": "新增账户", "Sell.Sell": "立即出售", "Deposit.title": "充值", "Deposit.DepositCurrency": "选择币种类型", "Deposit.Search": "搜索", "Deposit.Network": "选择网络", "Deposit.tip": "充币地址创建中，请耐心等待", "Deposit.Address": "区块链地址", "Deposit.tipsRed": "！重要：请务必选择正确主网类型", "Deposit.tipsLong": "该地址仅支持", "Deposit.tipsShort": "链上充值，转入NFT/铭文等资产将永久丢失", "Deposit.tipsShort2": "（不可撤销且无法追回）", "Deposit.Minimum": "最低充值金额", "Deposit.Estimated": "预计到账时间：", "Deposit.history": "充值记录", "Deposit.Receive": "数字货币收款", "Deposit.nodata": "请先选择币种类型", "Deposit.TransferTips1": "请向以下账户汇款：", "Deposit.TransferTips2": "（仅限同名账户转账）", "Deposit.AccountHolderName": "账户名称：", "Deposit.BankName": "开户银行：", "Deposit.SortCode": "银行分行代码：", "Deposit.BICSWIFT": "SWIFT代码：", "Deposit.IBAN": "IBAN国际账号：", "Deposit.AccountNumber": "银行账号：", "Deposit.BankAddress": "银行地址：", "Deposit.Reference": "汇款附言", "Deposit.Referencetips": "填写此字段可加速入账", "Deposit.CopyAll": "一键复制", "Swap.title": "闪兑", "Swap.tipleft": "当前市场价兑换：", "Swap.From": "转出", "Swap.To": "转入", "Swap.Available": "可用余额", "Swap.ExchangeRate": "兑换汇率", "Swap.Compared": "与昨日相比", "Swap.Max": "全部", "Swap.tips": "请在", "Swap.secong": "秒内完成兑换", "Swap.Fee": "手续费", "Swap.arrival": "预计到账时间：即时到账", "Swap.PopupSwapTitle": "确认兑换", "Swap.PopupQuoteExpiredTitle": "报价已过期", "Swap.for": "兑换", "Swap.PopupSwapContent": "您正在兑换", "Swap.PopupQuoteExpiredContent": "当前汇率已失效，请刷新获取最新报价", "Swap.GetNewRate": "刷新汇率", "Swap.Successfully": "兑换成功", "Swap.completed": "您的加密货币兑换已成功完成。", "Swap.selectpair": "请选择币对", "Swap.NotTradable": "当前币对不可交易", "Swap.nobalance": "余额不足，请先充值", "Swap.error": "兑换失败，请重试。", "Transfer.Transfer": "转账", "Transfer.Payment": "支付币种", "Transfer.Receipt": "收款币种", "Transfer.Max": "最大", "Transfer.RecipientAccount": "收款账户", "Transfer.Fee": "手续费", "Transfer.arrivaltime": "预计到账时间", "Transfer.Detail": "详情", "Transfer.DetailTitle": "当前账户为多币种账户。", "Transfer.EditAccount": "编辑账户", "Transfer.DeleteAccount": "删除账户", "Transfer.Currency": "币种", "Transfer.BankAccount": "银行账户", "Transfer.TokenAddress": "代币地址", "Transfer.Network": "网络", "Transfer.Address": "地址", "Transfer.Tag": "标签", "Transfer.PWPayAccount": "PinkWallet 支付账户", "Transfer.QRCode": "二维码：", "Transfer.selectaccounttype": "请选择账户类型：", "Transfer.Scantips": "设置为通用地址，适用于所有币种。", "Transfer.AllCurrencies": "所有币种", "Transfer.InMyOwnName": "以我自己名义", "Transfer.LongPress": "长按粘贴", "withdraw.SendCrypto": "提币", "withdraw.WithdrawalCurrency": "提币币种", "withdraw.Fee": "GAS费用", "withdraw.amount": "实际到账：", "withdraw.amounts": "金额", "withdraw.Withdrawalamount": "提币数量：", "withdraw.Reminders": "风险提示：", "withdraw.ALL": "全部", "withdraw.tips": "请勿提币至ICO/空投地址（可能导致资产丢失），避免与受制裁实体交易", "withdraw.confirm": "确认提币（链上操作不可逆）", "withdraw.title": "您正在提币：", "withdraw.edit": "修改到账金额", "withdraw.tip": "是否调整到账金额至", "withdraw.tip1": "？调整后提币详情：", "withdraw.sendaddress": "历史提币地址", "withdraw.add": "新增地址", "withdraw.Manage": "地址簿管理", "withdraw.ViewDetails": "查看区块链浏览器", "face.AccessDashboard": "进入主页", "face.again": "再次尝试Face Id", "face.not": "面部未识别", "Notification.Notification": "系统通知", "Notification.OnlineCustomerService": "在线客服", "Notification.text": "是否需要人工协助", "My.title": "账户中心", "My.Refer": "邀请有奖", "My.Notification": "消息通知", "My.Support": "客服中心", "My.Security": "安全中心", "My.Aboutus": "关于我们", "My.Language": "语言设置", "My.LanguageSelect": "切换语言", "My.VibrationSettings": "震动反馈", "My.FaceID": "生物识别登录：", "My.NoidentityTips": "！您尚未完成身份认证", "My.VerifyNow": "立即认证", "My.NoPwdTips": "！未设置二级密码", "My.SetNow": "立即设置", "My.FAQ": "常见问题", "Security.title": "安全设置", "Security.pwd": "密码", "Security.LoginPassword": "登录密码管理：", "Security.SetNow": "立即设置", "Security.PaymentPassword": "二级密码管理：", "Security.devices": "登录设备管理：", "Security.Remove": "移除", "Security.Removesuccess": "解绑成功", "Security.Edit": "修改", "Security.EditLoginPassword": "修改登录密码", "Security.EditPaymentPassword": "修改二级密码", "Security.PhoneNumber": "绑定手机：", "Security.VerificationCode": "验证码：", "Security.SetLoginPwd": "设置登录密码：", "Security.SetPaymentPwd": "设置二级密码：", "Security.Set": "设置", "Security.Login": "登录", "Security.Payment": "支付", "profile.title": "实名认证", "profile.VerifyNow": "立即认证", "profile.QRcode": "我的收款码：", "profile.KYCstatus": "个人认证状态：", "profile.KYBstatus": "企业认证状态：", "profile.MyName": "姓名：", "profile.IDtype": "证件类型：", "profile.IDnumber": "证件号码：", "profile.Passed": "已通过", "profile.Verify": "立即验证", "profile.Bind": "绑定", "profile.Edit": "修改", "profile.Email": "安全邮箱", "profile.Phone": "安全手机", "profile.Logout": "退出登录", "profile.Editemail": "编辑邮箱", "profile.oldemail": "旧邮箱", "profile.newemail": "新邮箱", "profile.avatarTip": "头像每30天只能修改一次", "profile.nickname": "昵称", "profile.nicknamePlaceholder": "请输入昵称", "profile.nicknameTip": "昵称每30天只能修改1次", "userInfo.title": "基本信息", "userInfo.registerInfo": "注册信息", "userInfo.auth": "身份认证", "userInfo.authPending": "待审批", "userInfo.authUnverified": "未认证", "userInfo.authVerified": "已认证", "userInfo.security": "账户安全", "userInfo.copySuccess": "UID已复制", "setting.title": "设置", "setting.general": "通用类", "setting.theme": "主题", "setting.color": "颜色配置", "setting.route": "路线配置", "setting.routeValue": "北美", "setting.themeBg": "主题背景", "setting.coinLike": "币种偏好", "setting.langLike": "语言偏好", "setting.followSystem": "跟随系统", "setting.update": "更新", "setting.checkUpdate": "检查更新", "setting.latestVersion": "您当前版本为最新版本", "setting.logout": "退出登录", "setting.logoutSuccess": "退出成功", "realName.title": "身份认证", "realName.residence": "居住国家/地区", "realName.visaCountry": "签证签发国家/地区", "realName.idType": "证件类型", "realName.idCard": "身份证", "realName.passport": "护照", "realName.other": "其他", "realName.recommend": "推荐", "realName.continue": "继续", "realName.center": "认证中心", "realName.standardAuth": "标准身份认证", "realName.advancedAuth": "高级身份认证", "realName.upgradeTip": "升级认证等级以将您的法币限额提升至2M USD每日", "realName.openAdvanced": "开通高级身份认证", "realName.currentLimit": "当前账户限额", "realName.fiatDepositLimit": "法币充值限额", "realName.fiatWithdrawLimit": "法币提现限额", "realName.cryptoDepositLimit": "加密货币充值限额", "realName.cryptoWithdrawLimit": "加密货币提现限额", "realName.c2cLimit": "C2C交易限额", "realName.unlimited": "无限额", "realName.perDay": "每日", "realName.authLevel": "认证等级", "realName.fiatLimit": "法币限额 XX USD 每日", "realName.audit": "审核中", "realName.auditDesc": "您已完成初级认证，我们将通过邮件将审核结果发送给您，审核预计在1小时内完成。", "realName.goHome": "前往首页", "realName.uploadTitle": "上传证件", "realName.upload": "上传", "realName.uploadAllTip": "请上传所有证件照片", "realName.confirmTitle": "确认个人信息", "realName.nationality": "国籍", "realName.nationalityPlaceholder": "请选择国籍", "realName.name": "姓名", "realName.namePlaceholder": "请输入姓名", "realName.idNumber": "身份证号码", "realName.idNumberPlaceholder": "请输入身份证号码", "realName.birth": "出生日期（年/月/日）", "realName.birthPlaceholder": "请选择出生日期", "realName.confirmTip": "选择继续，即表示您认为上述个人信息准确无误", "realName.idFront": "身份证人像面", "realName.idBack": "身份证国徽面", "realName.idHand": "手持身份证", "realName.passportFront": "护照人像页", "realName.passportBack": "护照签证页", "realName.passportHand": "手持护照", "realName.docFront": "证件正面", "realName.docBack": "证件反面", "realName.uploadSuccess": "上传成功", "realName.uploadFail": "上传失败，请重试", "realName.tip1": "1、请保持证件信息清晰可见、不遮盖。", "realName.tip2": "2、请保持证件信息号码一致。", "email.title": "邮箱验证", "email.confirmChange": "您确定要更改邮箱地址吗？", "email.rule1": "为保障您的资产安全，更改邮箱后，提现和C2C交易将禁用", "email.hour": "小时", "email.rule2": "旧邮箱地址在更新后的", "email.day": "天内无法用于重新注册", "email.cancel": "取消", "email.continue": "继续", "emailUpdata.title": "更改邮箱", "emailUpdata.topDesc": "为保障您的资产安全，更改或解绑邮箱后，该账户将禁用支付服务、提币及C2C卖币24小时", "emailUpdata.newEmail": "新邮箱", "emailUpdata.newEmailPlaceholder": "请输入新邮箱地址", "emailUpdata.codeTitle": "填写验证码", "emailUpdata.codePlaceholder": "请输入验证码", "emailUpdata.getCode": "获取验证码", "emailUpdata.reGetCode": "重新获取", "emailUpdata.submit": "提交", "emailUpdata.successTitle": "邮箱更改成功", "emailUpdata.successDesc": "您的邮箱地址已修改，下次登录请使用新的邮箱地址", "emailUpdata.ok": "好的", "emailUpdata.inputNewEmail": "请输入新邮箱地址", "emailUpdata.gettingCode": "正在获取验证码", "emailUpdata.codeSent": "验证码已发送", "emailUpdata.waitReget": "倒计时结束后再发送", "emailUpdata.inputAll": "请填写完整信息", "emailUpdata.submitSuccess": "提交成功", "emailVerify.title": "邮箱验证", "emailVerify.topDesc": "请输入{email}收到的6位验证码", "emailVerify.codeTitle": "邮箱验证码", "emailVerify.codePlaceholder": "请输入验证码", "emailVerify.getCode": "获取验证码", "emailVerify.reGetCode": "重新获取", "emailVerify.submit": "提交", "emailVerify.gettingCode": "正在获取验证码", "emailVerify.codeSent": "验证码已发送", "emailVerify.waitReget": "倒计时结束后再发送", "emailVerify.input6Code": "请输入6位验证码", "emailVerify.submitSuccess": "提交成功", "password.title": "密码", "password.topDesc": "为确保账户安全，需要选择其中一种验证方式进行身份验证", "password.emailVerify": "邮箱验证", "password.googleVerify": "谷歌身份验证", "password.selectType": "请选择一种验证方式", "password.confirm": "确认", "passwordUpdata.title": "更改密码", "passwordUpdata.topDesc": "为保障您的资产安全，在您更改密码后，该账户可能会禁用支付服务、提币及C2C卖币24小时", "passwordUpdata.newPwd": "新密码", "passwordUpdata.newPwdPlaceholder": "请输入新密码", "passwordUpdata.confirmPwd": "确认密码", "passwordUpdata.confirmPwdPlaceholder": "请再次输入新密码", "passwordUpdata.ruleLength": "8-20个字符", "passwordUpdata.ruleNumber": "至少1个数字", "passwordUpdata.ruleUppercase": "至少1个大写字母", "passwordUpdata.submit": "提交", "passwordUpdata.successTitle": "密码更改成功", "passwordUpdata.successDesc": "您的登录密码已修改，请重新登录账户。密码修改成功后，24小时内禁止提币", "passwordUpdata.ok": "好的", "passwordUpdata.inputAll": "请填写所有字段", "passwordUpdata.notSame": "两次输入的新密码不一致", "passwordUpdata.notRule": "新密码不符合所有规则", "passwordUpdata.submitSuccess": "提交成功", "passwordVerify.title": "邮箱验证", "passwordVerify.topDesc": "请输入{email}收到的6位验证码", "passwordVerify.codeTitle": "邮箱验证码", "passwordVerify.codePlaceholder": "请输入验证码", "passwordVerify.getCode": "获取验证码", "passwordVerify.reGetCode": "重新获取", "passwordVerify.submit": "提交", "passwordVerify.gettingCode": "正在获取验证码", "passwordVerify.codeSent": "验证码已发送", "passwordVerify.waitReget": "倒计时结束后再发送", "passwordVerify.input6Code": "请输入6位验证码", "passwordVerify.submitSuccess": "提交成功", "passwordVerify.submitFail": "提交失败：", "pinCode.title": "需要添加其他验证方式", "pinCode.topDesc": "为确保账户安全，需要选择其中一种验证方式进行身份验证", "pinCode.emailVerify": "邮箱验证", "pinCode.googleVerify": "谷歌身份验证", "pinCode.selectType": "请选择一种验证方式", "pinCode.confirm": "确认", "pinCodeIndex.title": "需要添加其他验证方式", "pinCodeIndex.topDesc": "为确保账户安全，需要选择其中一种验证方式进行身份验证", "pinCodeIndex.emailVerify": "邮箱验证", "pinCodeIndex.googleVerify": "谷歌身份验证", "pinCodeIndex.selectType": "请选择一种验证方式", "pinCodeIndex.confirm": "确认", "pinCodeUpdata.title": "设置支付PIN码", "pinCodeUpdata.topDesc": "支付PIN码将用来授权支付交易", "pinCodeUpdata.createTitle": "创建支付PIN码", "pinCodeUpdata.ruleDesc": "密码不能过于简单，同一个数字不能出现两次以上", "pinCodeUpdata.accept": "继续即代表接受", "pinCodeUpdata.terms": "使用条款！", "pinCodeUpdata.confirm": "确认", "pinCodeUpdata.successTitle": "密码更改成功", "pinCodeUpdata.successDesc": "您的登录密码已修改，请重新登录账户。密码修改成功后，24小时内禁止提币", "pinCodeUpdata.ok": "好的", "pinCodeUpdata.input6Pin": "请输入6位PIN码", "pinCodeUpdata.noRepeat": "同一个数字不能出现两次以上", "pinCodeUpdata.success": "成功", "securityIndex.title": "账户安全", "securityIndex.2faTitle": "双重验证（2FA）", "securityIndex.2faDesc": "为保障账户安全，请至少启用两种双重身份验证方式。", "securityIndex.email": "邮箱", "securityIndex.password": "密码", "securityIndex.pinCode": "支付PIN码", "securityIndex.emailBinded": "已绑定", "securityIndex.emailUnbind": "未绑定", "securityIndex.setted": "已设置", "securityIndex.unset": "未设置", "pinCodeVerify.title": "邮箱验证", "pinCodeVerify.topDesc": "请输入{email}收到的6位验证码", "pinCodeVerify.codeTitle": "邮箱验证码", "pinCodeVerify.codePlaceholder": "请输入验证码", "pinCodeVerify.getCode": "获取验证码", "pinCodeVerify.reGetCode": "重新获取", "pinCodeVerify.submit": "提交", "pinCodeVerify.gettingCode": "正在获取验证码", "pinCodeVerify.codeSent": "验证码已发送", "pinCodeVerify.waitReget": "倒计时结束后再发送", "pinCodeVerify.input6Code": "请输入6位验证码", "pinCodeVerify.submitSuccess": "提交成功", "serviceTab.common": "常用功能", "serviceTab.crypto": "加密业务", "serviceTab.fiat": "法币业务", "serviceTab.activity": "活动与奖励", "serviceTab.help": "帮助", "serviceTab.other": "其他", "service.contract": "合约", "service.finance": "理财", "service.node": "节点", "service.charge": "充值", "service.order": "带单", "service.notice": "公告", "service.stock": "股市交易", "service.hotEvent": "热门活动", "service.invite": "邀请", "service.c2c": "C2C", "service.swap": "<PERSON><PERSON><PERSON>", "service.flashSwap": "闪兑", "service.spot": "现货交易", "service.cryptoCharge": "加密货币充值", "service.cryptoWithdraw": "加密货币提现", "service.quickBuy": "快捷买币", "service.transferOut": "转出", "service.quickSell": "快捷卖币", "service.assetTransfer": "资产划转", "service.hkStock": "港股交易", "service.usStock": "美股交易", "service.swissBank": "瑞士银行开户", "service.fiatCharge": "法币充值", "service.fiatWithdraw": "法币提现", "service.fiatTransfer": "法币转账", "service.fiatSwap": "法币互兑", "service.agent": "代理合作", "service.kol": "KOL合作", "service.myReward": "我的奖励", "service.airdrop": "空投", "service.support": "在线客服", "service.faq": "帮助中心", "service.burnQuery": "销毁查询", "service.api": "API", "userIndex.normalUser": "普通用户", "userIndex.verified": "已认证"}