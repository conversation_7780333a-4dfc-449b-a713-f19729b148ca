{"title.welcome": "Welcome to CW", "title.LiveSupport": "Customer Support", "title.history": "History", "title.confirm": "Confirm", "title.cancel": "Cancel", "title.SetPasswordTitle": "Set Password", "title.SetPasswordContent": "You haven't set a login password and a payment password yet.", "title.SetPasswordconfirm": "Set Now", "title.SetPasswordCancel": "Set Later", "title.SetPasswordLoginPwd": "Set Login Password:", "title.SetPasswordPayPwd": "Set Payment Password:", "title.submit": "Submit", "title.startText": "Get Code", "title.changeText": "X s Get code later", "title.endText": "Get Again", "title.chart": "Price Chart", "title.copy": "Copy success", "page.detail": "<PERSON><PERSON><PERSON><PERSON> Details", "page.forget": "Forgot Password", "page.login": "<PERSON><PERSON>", "page.register": "Register", "page.center": "Account Center", "page.profile": "Identity Verification", "page.deposit": "<PERSON><PERSON><PERSON><PERSON>", "page.withdraw": "Withdraw", "page.Record": "Transaction Record", "page.CurrencyCharge": "Fiat Deposit", "page.swap": "<PERSON><PERSON><PERSON>", "page.TransferOut": "Transfer Out", "page.Notification": "System Notification", "page.AddCount": "Add Payee", "page.security": "Security Settings", "page.settingPwd": "Set Password", "page.AccountDetail": "Account Details", "page.QuickBuy": "Quick Buy", "page.Receive": "<PERSON><PERSON><PERSON><PERSON>", "page.TransactionHistory": "Transaction History", "page.sell": "<PERSON>ll", "Please.email": "Please enter your email address", "Please.verification": "Please enter verification code", "Please.pwd": "Please enter your password", "Please.erroremail": "Invalid email format", "Please.PasswordRequirement1": "Must be at least 8 characters long", "Please.PasswordRequirement2": "Must contain at least one lowercase letter", "Please.PasswordRequirement3": "Must contain at least one uppercase letter", "Please.full": "Please complete all required information", "Please.agreements": "Please check the agreements first", "Types.Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Types.Withdrawal": "<PERSON><PERSON><PERSON>", "Types.Transfer": "Transfer", "Types.Swap": "<PERSON><PERSON><PERSON>", "Types.Reward": "<PERSON><PERSON>", "Types.Buy": "Buy", "Types.Receive": "Receive", "Types.Send": "Send", "Types.Sell": "<PERSON>ll", "Types.convert": "Convert", "Onboarding.FullyCompliant": "<PERSON>y Compliant", "Onboarding.FullyCompliant.Licensed": "Licensed in Europe (VASP), USA (MSB), Canada (MSB), New Zealand (FSP), and Hong Kong (MSO).", "Onboarding.FullyCompliant.Insurance": "Backed by a $5 Billion insurance guarantee.", "Onboarding.FullyCompliant.Security": "Secure asset custody with Fireblocks.", "Onboarding.OffshoreBank": "Open an Offshore Bank Account Remotely", "Onboarding.OffshoreBank.InstantSetup": "Instant setup – Open an account in just 3 minutes (Bank name to be added).", "Onboarding.OffshoreBank.MinimalDocs": "Minimal documentation required.", "Onboarding.OffshoreBank.FreeService": "Completely free – No setup or management fees.", "Onboarding.BestRates": "Best Rates Guaranteed", "Onboarding.BestRates.Description": "We compare prices across 30+ liquidity providers to ensure you get the best exchange rates.", "Onboarding.BuyCrypto": "Buy Crypto Instantly", "Onboarding.BuyCrypto.Description": "Purchase crypto with credit/debit cards.", "Onboarding.GetStarted": "Get Started", "Onboarding.Skip": "<PERSON><PERSON>", "Auth.SignIn": "Sign In", "Auth.SignIn.Description": "Sign in to your account.", "Auth.SignIn.PhoneNumber": "Email", "Auth.SignIn.VerificationCodeLogin": "Verification Code Login", "Auth.SignIn.Password": "Password", "Auth.SignIn.ForgotPassword": "Forgot Password?", "Auth.SignIn.Button": "Sign In", "Auth.SignIn.SignInWith": "Sign in with", "Auth.SignIn.Google": "Google", "Auth.SignIn.FaceID": "Face ID", "Auth.SignIn.NoAccount": "Don't have an account?", "Auth.SignIn.SignUp": "Sign Up", "Auth.FaceID": "Face ID", "Auth.FaceID.Access": "Access Dashboard", "Auth.FaceID.NotRecognized": "Face Not Recognized", "Auth.FaceID.TryAgain": "Try Face ID Again", "Auth.FaceID.SignInWithPhoneNumber": "Sign in with Phone Number", "Auth.ForgotPassword": "Forgot Password", "Auth.ForgotPassword.Description": "Please enter registered email.", "Auth.ForgotPassword.PhoneNumber": "Email", "Auth.ForgotPassword.VerificationCode": "Verification Code", "Auth.ForgotPassword.GetOTP": "Get OTP Code", "Auth.ForgotPassword.EnterOTP": "Enter the OTP Code", "Auth.ForgotPassword.OTPTimer": "00:59", "Auth.ForgotPassword.NoOTP": "Not receiving the verification code? Contact Manual Service.", "Auth.ForgotPassword.NewPassword": "Password", "Auth.ForgotPassword.PasswordRequirement1": "8 characters minimum.", "Auth.ForgotPassword.PasswordRequirement2": "Must include at least 1 lowercase letter.", "Auth.ForgotPassword.PasswordRequirement3": "1 uppercase letter.", "Auth.ForgotPassword.Submit": "Submit", "Auth.SignUp": "Sign Up", "Auth.SignUp.Description": "Enter below details to create a new account.", "Auth.SignUp.PhoneNumber": "Email", "Auth.SignUp.VerificationCode": "Verification Code", "Auth.SignUp.GetOTP": "Get OTP Code", "Auth.SignUp.Agreement": "I agree to the", "Auth.SignUp.Terms": "'User Agreement'", "Auth.SignUp.Privacy": "'Privacy Terms'", "Auth.SignUp.and": "and", "Auth.SignUp.Button": "Sign Up", "Auth.SignUp.SignUpWith": "Sign up with", "Auth.SignUp.AlreadyHaveAccount": "Already have an account?", "Auth.SignUp.SignIn": "Sign In", "Auth.InvitationCode": "Invitation Code", "index.Balance.YourBalance": "Your Balance", "index.Balance.ReceiveCurrencies": "Receive Currencies", "index.Balance.ReceiveCrypto": "Receive Crypto", "index.TransactionHistory.Title": "Transaction History", "index.TransactionHistory.All": "All", "index.TransactionHistory.Empty": "Transaction will appear here", "index.TransactionHistory.EmptyDescription": "Add some currencies to your wallet", "index.day": "day", "index.Currencies.Title": "Currencies", "index.Crypto.Title": "Crypto", "index.Crypto.HideZero": "Hide $0 Currency", "index.more": "more", "index.Deposit": "<PERSON><PERSON><PERSON><PERSON>", "index.Transfer": "Transfer", "index.History": "History", "BuyCrypto_Title": "Quick Buy Crypto", "BuyCrypto_YouSpend": "You Spend", "BuyCrypto_YouGet": "You Get", "BuyCrypto_Placeholder": "0", "BuyCrypto_PlaceholderRange": "{symbol}{min} ~ {symbol}{max}", "BuyCrypto_PayUsing": "Pay Using", "BuyCrypto_BuyButton": "Buy Crypto", "BuyCrypto_ChoosePaymentMethod": "Choose Payment Method", "BuyCrypto_ProcessingFee": "Processing Fee", "BuyCrypto_NetworkFee": "Network Fee", "BuyCrypto_Connecting": "Connecting you to Banxa...", "BuyCrypto_ContinuePayment": "Continue Payment", "BuyCrypto_PaymentMethodDebitCard": "Debit Card", "BuyCrypto_PaymentMethodCreditCard": "Credit Card", "BuyCrypto_PaymentMethodApplePay": "Apple Pay", "BuyCrypto_PaymentMethodGooglePay": "Google Pay", "BuyCrypto_Select": "Select", "BuyCrypto_Selected": "Selected", "BuyCrypto_Close": "Close", "BuyCrypto_Available": "Available", "BuyCrypto_Max": "Max", "BuyCrypto_Fiat": "Fiat", "BuyCrypto_Crypto": "Crypto", "BuyCrypto_Processing": "Processing...", "BuyCrypto_Fee": "Fee", "BuyCrypto_Network": "Network", "BuyCrypto_ExchangeRate": "Exchange Rate", "BuyCrypto_InvalidAmount": "Invalid amount", "BuyCrypto_InvalidPair": "Invalid pair", "BuyCrypto_Error": "Error, please try again.", "BuyCrypto_DebitCreditCard": "Debit/Credit Card", "BuyCrypto_InteracBankTransfer": "Interac Bank Transfer", "Transaction.Success.Title": "Transaction Successfully", "Transaction.Success.Description": "Your cryptocurrency purchase has been completed successfully. The funds have been added to your wallet.", "Transaction.Details.Title": "Transaction Details", "Transaction.Details.ID": "Transaction ID", "Transaction.Details.Amount": "Amount", "Transaction.Details.TotalCost": "Total Cost", "Transaction.Details.DateTime": "Date & Time", "Transaction.Button.GoHome": "Go to Home", "Send.Send": "Send", "Send.CurrencyForPayment": "Currency for Payment", "Send.Available": "Available", "Send.RecipientAccount": "Recipient Account", "Send.AddAccount": "Add Account", "Send.PleaseSelectTheRecipientAccount": "Please select the recipient account", "Send.EstimatedAmountToBeReceived": "Estimated amount to be received", "Send.addpee": "Add Payee", "Send.PleaseSelectAccountType": "Please Select Account Type", "Send.pwcount": "PinkWallet Account(0 transfer Fee)", "Send.SetPassword": "Set Password", "Send.YouHaventSetALoginPasswordAndAPaymentPasswordYet": "You haven't set a login password and a payment password yet", "Send.SetLoginPassword": "Set Login Password", "Send.SetPaymentPassword": "Set Payment Password", "Send.Submit": "Submit", "Send.Cancel": "Cancel", "Send.ConfirmTransfer": "Confirm Transfer", "Send.YouAreCurrentlyMakingAPaymentOf": "You are currently making a payment of", "Send.PleaseEnterYourPaymentPassword": "Please enter your payment password", "Send.Show": "Show", "Send.SetNow": "Set Now", "Send.Later": "Later", "Send.EditAccount": "Edit Account", "Send.Currency": "<PERSON><PERSON><PERSON><PERSON>", "Send.AllCurrencies": "All Currencies", "Send.AccountHolderName": "Account Holder Name", "Send.InMyOwnName": "In my own name", "Send.BankName": "Bank Name", "Send.SwiftCode": "Swift Code", "Send.IbanNumber": "<PERSON>ban Number", "Send.AccountNumber": "Account Number", "Send.BankAddress": "Bank Address", "Send.ReferenceOptional": "Reference (Optional)", "Send.TokenPayAccount": "Token Pay Account", "Send.PleaseEnterYourTokenPayUsersMobileNumberEmailAddressOrScanTheOtherPartysPaymentQRCode": "Please enter your Pink Wallet user's mobile number / email address or scan the other party's payment QR code", "Send.LongPressToPaste": "Long press to paste", "Send.TokenAddress": "Token Address", "Send.SetAsGeneralAddressApplicableToAllCurrencies": "Set as general address, applicable to all currencies", "Send.Network": "Network", "Send.Memo": "Memo", "Send.TagOptional": "Tag (Optional)", "Send.SearchAsset": "Search Asset", "Send.Cash": "Cash", "Send.Crypto": "Crypto", "Send.nobalance": "Asset without balance", "Send.Recent": "Recent", "Send.Frequent": "Frequent", "Send.PinkWalletAccount": "Pink<PERSON><PERSON><PERSON> Account", "Send.AccountDetail": "Account Detail", "Record.type": "Type", "Record.Date": "Date", "Record.Status.Init": "Initialize", "Record.Status.InProgress": "Progressing", "Record.Status.Completed": "Completed", "Record.Status.Failed": "Failed", "Sell.SellCrypto": "<PERSON><PERSON>", "Sell.YouSell": "You Sell", "Sell.Max": "Max", "Sell.Available": "Available", "Sell.YouGet": "You Get", "Sell.ReceiveFundsIn": "Receive Funds in", "Sell.AddNew": "Add New Account", "Sell.Sell": "<PERSON>ll", "Notification.Notification": "Notification", "Notification.OnlineCustomerService": "Online Customer Service", "Notification.text": "Is there anything you need help with?", "register.title": "Sign up", "register.PhoneNumber": "Phone Number", "register.VerificationCode": "Verification Code", "register.agree": "I agree to the ", "register.registerAgree": " 'Registration Agreement'", "register.IBan": "'IBAN Account Opening Agreement'", "register.and": "and", "register.cancel": "cancel", "register.Google": "Sign in with Google", "register.PwdLogin": "Password Login", "register.GetingCode": "Getting verification code", "register.PhonePlz": "Please enter phone number to get code", "register.Later": "Send after countdown ends", "register.PhonePlzVerfy": "Please enter phone number", "register.CodePlz": "Please enter verification code", "register.AgreementPlz": "Please select agreement first", "register.success": "Registration successful", "register.Send": "Verification code has been sent", "Login.title": "<PERSON><PERSON>", "Login.PhoneNumber": "Phone Number", "Login.VerificationCodeLogin": "Verification Code Login", "Login.PasswordLogin": "Password Login", "Login.Google": "Sign in with Google", "Login.ForgotPwd": "Forgot Password", "Login.Getcode": "Get Code", "Login.Login": "<PERSON><PERSON>", "Login.cancel": "Cancel", "ForgetPwd.title": "Forgot Password", "ForgetPwd.PhoneNumber": "Phone Number", "ForgetPwd.VerificationCode": "Verification Code", "ForgetPwd.GetCode": "Get Code", "ForgetPwd.NewPwd": "New Password", "ForgetPwd.tips": "8 characters long", "ForgetPwd.tip1": "Must contain at least 1 lowercase letter and 1 uppercase letter", "ForgetPwd.ResetPwd": "Reset Password", "ForgetPwd.cancel": "Cancel", "notice.title": "Notification", "notice.LiveSupport": "Live Support", "notice.KYCPassed": "KYC Passed", "notice.WithdrawalCompleted": "Withdrawal Completed", "notice.SwapCompleted": "Swap Completed", "Deposit.title": "<PERSON><PERSON><PERSON><PERSON>", "Deposit.Search": "Search", "Deposit.DepositCurrency": "De<PERSON><PERSON><PERSON>", "Deposit.Network": "Network", "Deposit.tip": " Deposit address is being generated,please wait patiently.", "Deposit.Address": "Address", "Deposit.tipsRed": "Required field. Leaving this blank may result in financial loss", "Deposit.tipsLong": "This address is exclusively for ", "Deposit.tipsShort": "deposits. Do not use it for sending inscriptions, NFTs, or any other assets. Any non-", "Deposit.tipsShort2": "assets sent to this address will be permanently lost and cannot be recovered or refunded.", "Deposit.Minimum": "Minimum Deposit", "Deposit.Estimated": "Estimated Arrival Time:", "Deposit.history": "history", "Deposit.Receive": "Receive", "Deposit.nodata": "Please select the currency type", "Deposit.TransferTips1": "Please transfer", "Deposit.TransferTips2": " to the following recipient account:", "Deposit.AccountHolderName": "Account Holder Name：", "Deposit.BankName": "Bank Name：", "Deposit.SortCode": "Sort Code：", "Deposit.BICSWIFT": "BIC/SWIFT：", "Deposit.IBAN": "IBAN Number：", "Deposit.AccountNumber": "Account Number：", "Deposit.BankAddress": "Bank Address：", "Deposit.Reference": "Reference", "Deposit.Referencetips": "Complete this field for faster processing", "Deposit.CopyAll": "Copy All", "Swap.title": "<PERSON><PERSON><PERSON>", "Swap.tipleft": "Exchange at the current market price:", "Swap.From": "From", "Swap.To": "To", "Swap.Available": "Available", "Swap.ExchangeRate": "Exchange Rate", "Swap.Compared": "Compared to yesterday", "Swap.Max": "Max", "Swap.tips": "Please complete your swap within", "Swap.secong": "seconds.", "Swap.Fee": "Fee", "Swap.arrival": "Estimated arrival time: within seconds;", "Swap.PopupSwapTitle": "Confirm Swap", "Swap.PopupQuoteExpiredTitle": "Quote Expired", "Swap.for": "for", "Swap.PopupSwapContent": "You are currently exchanging", "Swap.PopupQuoteExpiredContent": "Please refresh to get the latest swap rate.", "Swap.GetNewRate": "Get New Rate", "Swap.Successfully": "Swap Successfully Completed", "Swap.completed": "Your cryptocurrency swap has been completed successfully.", "Swap.selectpair": "Please select a coin pair", "Swap.NotTradable": "Coin Pair Currently Not Tradable", "Swap.nobalance": "Insufficient balance, please recharge first", "Swap.error": "<PERSON><PERSON><PERSON> failed. Please try again.", "Transfer.Transfer": "Transfer", "Transfer.Payment": "Currency for Payment", "Transfer.Receipt": "Currency for Receipt", "Transfer.Max": "Max", "Transfer.RecipientAccount": "Recipient Account", "Transfer.Fee": "Fee", "Transfer.arrivaltime": "Estimated arrival time", "Transfer.Detail": "Detail", "Transfer.DetailTitle": "The current account is a multi-currency account.", "Transfer.EditAccount": "Edit Account", "Transfer.DeleteAccount": "Delete Account", "Transfer.Currency": "<PERSON><PERSON><PERSON><PERSON>", "Transfer.TokenAddress": "Token Address：", "Transfer.BankAccount": " Bank Account：", "Transfer.Network": "Network", "Transfer.Address": "Address", "Transfer.Tag": "Tag", "Transfer.PWPayAccount": "PinkWallet Pay Account", "Transfer.QRCode": "QR Code：", "Transfer.selectaccounttype": "Please select account type:", "Transfer.Scantips": "Set as a general address, applicable to all currencies.", "Transfer.AllCurrencies": "All currencies", "Transfer.InMyOwnName": "In my own name", "Transfer.LongPress": "Long press to paste", "withdraw.SendCrypto": "Send Crypto", "withdraw.WithdrawalCurrency": "<PERSON><PERSON><PERSON>", "withdraw.Fee": "<PERSON><PERSON><PERSON>", "withdraw.amount": "Received Amount:", "withdraw.amounts": "Amount", "withdraw.ALL": "ALL", "withdraw.Withdrawalamount": "<PERSON><PERSON><PERSON> Amount:", "withdraw.Reminders": "Reminders", "withdraw.tips": "Avoid direct withdraw to crowdfunding/ICO address (token delivery may fail).New transact with sanctioned entities.", "withdraw.confirm": "Confirm <PERSON>", "withdraw.title": "You are initiating a withdrawal of", "withdraw.edit": "Edit Received Amount", "withdraw.tip": "Would you like to adjust the received amount to", "withdraw.tip1": "‌? Your latest withdrawal details will be:", "withdraw.sendaddress": "Send Crypto History Address", "withdraw.add": "Add", "withdraw.Manage": "Manage", "withdraw.ViewDetails": "View Details", "withdraw.success": "Send Successfully", "withdraw.successtip": "Your cryptocurrency purchase has been completed successfully. The funds have been added to your wallet.", "face.AccessDashboard": "Access Dashboard", "face.again": "Try Face ID Again", "face.not": "Face Not Recognized", "My.title": "MY Account", "My.Refer": "<PERSON><PERSON>", "My.Notification": "Notification", "My.Support": "Customer Support", "My.Security": "Security", "My.Aboutus": "About us", "My.Language": "Language", "My.LanguageSelect": "Select Language", "My.VibrationSettings": "Vibration", "My.FaceID": "Face ID login：", "My.NoidentityTips": "You haven't completed identity verification yet!！", "My.VerifyNow": "Verify Now", "My.NoPwdTips": "You haven't set a login password and a payment password yet!", "My.SetNow": "Set Now", "My.FAQ": "FAQ", "Security.title": "Security", "Security.pwd": "Password：", "Security.LoginPassword": "Login Password：", "Security.SetNow": "Set Now", "Security.PaymentPassword": "Payment Password：", "Security.devices": "My login devices:", "Security.Remove": "Remove", "Security.Removesuccess": "Unbind Successful", "Security.Edit": "Edit", "Security.EditLoginPassword": "Edit Login Password", "Security.EditPaymentPassword": "Edit Payment Password", "Security.PhoneNumber": "Phone Number：", "Security.VerificationCode": "Verification Code:", "Security.SetLoginPwd": "Set Login Password：", "Security.SetPaymentPwd": "Set Payment Password：", "Security.Set": "Set", "Security.Login": "<PERSON><PERSON>", "Security.Payment": "Payment", "profile.title": "My profile", "profile.VerifyNow": "Verify Now", "profile.QRcode": "My QR code:", "profile.KYCstatus": "KYC status：", "profile.KYBstatus": "KYB status：", "profile.MyName": "My name：", "profile.IDtype": "ID type：", "profile.IDnumber": "ID number：", "profile.Passed": "Passed", "profile.Verify": "Verify Now", "profile.Bind": "Bind", "profile.Edit": "Edit", "profile.Email": "Email", "profile.Phone": "Phone", "profile.Logout": "Logout", "profile.Editemail": "<PERSON> Email", "profile.oldemail": "old Email", "profile.newemail": "Email", "profile.avatarTip": "Avatar can only be changed once every 30 days", "profile.nickname": "Nickname", "profile.nicknamePlaceholder": "Please enter a nickname", "profile.nicknameTip": "Nickname can only be changed once every 30 days", "userInfo.title": "Basic Info", "userInfo.registerInfo": "Register Info", "userInfo.auth": "Identity Verification", "userInfo.authPending": "Pending", "userInfo.authUnverified": "Unverified", "userInfo.authVerified": "Verified", "userInfo.security": "Account Security", "userInfo.copySuccess": "UID copied", "setting.title": "Settings", "setting.general": "General", "setting.theme": "Theme", "setting.color": "Color Config", "setting.route": "Route Config", "setting.routeValue": "North America", "setting.themeBg": "Theme Background", "setting.coinLike": "Coin Preference", "setting.langLike": "Language Preference", "setting.followSystem": "Follow System", "setting.update": "Update", "setting.checkUpdate": "Check Update", "setting.latestVersion": "You are using the latest version", "setting.logout": "Logout", "setting.logoutSuccess": "Logout successful", "setting.language": "Language", "setting.search": "Search", "setting.selectLanguage": "Select Language", "realName.title": "Identity Verification", "realName.residence": "Country/Region of Residence", "realName.visaCountry": "Country/Region of Visa Issuance", "realName.idType": "Document Type", "realName.idCard": "ID Card", "realName.passport": "Passport", "realName.other": "Other", "realName.recommend": "Recommended", "realName.continue": "Continue", "realName.center": "Verification Center", "realName.standardAuth": "Standard Verification", "realName.advancedAuth": "Advanced Verification", "realName.upgradeTip": "Upgrade your verification level to increase your fiat limit to 2M USD per day", "realName.openAdvanced": "Enable Advanced Verification", "realName.currentLimit": "Current Account <PERSON>it", "realName.fiatDepositLimit": "Fiat Deposit Limit", "realName.fiatWithdrawLimit": "Fiat Withdraw Limit", "realName.cryptoDepositLimit": "Crypto Deposit Limit", "realName.cryptoWithdrawLimit": "Crypto Withdraw Limit", "realName.c2cLimit": "C2C Trading Limit", "realName.unlimited": "Unlimited", "realName.perDay": "per day", "realName.authLevel": "Verification Level", "realName.fiatLimit": "Fiat limit XX USD per day", "realName.audit": "Under Review", "realName.auditDesc": "You have completed the primary verification. We will send the result to your email. The review is expected to be completed within 1 hour.", "realName.goHome": "Go to Home", "realName.uploadTitle": "Upload Document", "realName.upload": "Upload", "realName.uploadAllTip": "Please upload all required document photos", "realName.confirmTitle": "Confirm Personal Info", "realName.nationality": "Nationality", "realName.nationalityPlaceholder": "Please select nationality", "realName.name": "Name", "realName.namePlaceholder": "Please enter your name", "realName.idNumber": "ID Number", "realName.idNumberPlaceholder": "Please enter your ID number", "realName.birth": "Date of Birth (YYYY/MM/DD)", "realName.birthPlaceholder": "Please select date of birth", "realName.confirmTip": "By continuing, you confirm the above information is accurate", "realName.idFront": "ID Card Front", "realName.idBack": "ID Card Back", "realName.idHand": "Hand-held ID Card", "realName.passportFront": "Passport Info Page", "realName.passportBack": "Passport Visa Page", "realName.passportHand": "Hand-held Passport", "realName.docFront": "Document Front", "realName.docBack": "Document Back", "realName.uploadSuccess": "Upload Successful", "realName.uploadFail": "Upload failed, please try again", "realName.tip1": "1. Please keep the document information clear and unobstructed.", "realName.tip2": "2. Please keep the document number consistent.", "email.title": "Email Verification", "email.confirmChange": "Are you sure you want to change your email address?", "email.rule1": "To protect your assets, withdrawals and C2C trading will be disabled for ", "email.hour": " hours after changing your email.", "email.rule2": "The old email address cannot be used to re-register within ", "email.day": " days after update.", "email.cancel": "Cancel", "email.continue": "Continue", "emailUpdata.title": "Change Email", "emailUpdata.topDesc": "To protect your assets, after changing or unbinding your email, payment services, withdrawal, and C2C selling will be disabled for 24 hours.", "emailUpdata.newEmail": "New Email", "emailUpdata.newEmailPlaceholder": "Please enter new email address", "emailUpdata.codeTitle": "Enter Verification Code", "emailUpdata.codePlaceholder": "Please enter the verification code", "emailUpdata.getCode": "Get Code", "emailUpdata.reGetCode": "Resend", "emailUpdata.submit": "Submit", "emailUpdata.successTitle": "Email Changed Successfully", "emailUpdata.successDesc": "Your email address has been updated. Please use the new email address for next login.", "emailUpdata.ok": "OK", "emailUpdata.inputNewEmail": "Please enter new email address", "emailUpdata.gettingCode": "Getting verification code", "emailUpdata.codeSent": "Verification code sent", "emailUpdata.waitReget": "Resend after countdown ends", "emailUpdata.inputAll": "Please complete all information", "emailUpdata.submitSuccess": "Submitted successfully", "emailVerify.title": "Email Verification", "emailVerify.topDesc": "Please enter the 6-digit code sent to {email}", "emailVerify.codeTitle": "Email Verification Code", "emailVerify.codePlaceholder": "Please enter the verification code", "emailVerify.getCode": "Get Code", "emailVerify.reGetCode": "Resend", "emailVerify.submit": "Submit", "emailVerify.gettingCode": "Getting verification code", "emailVerify.codeSent": "Verification code sent", "emailVerify.waitReget": "Resend after countdown ends", "emailVerify.input6Code": "Please enter the 6-digit code", "emailVerify.submitSuccess": "Submitted successfully", "password.title": "Password", "password.topDesc": "To ensure account security, please select one verification method for identity verification.", "password.emailVerify": "Email Verification", "password.googleVerify": "Google Authenticator", "password.selectType": "Please select a verification method", "password.confirm": "Confirm", "passwordUpdata.title": "Change Password", "passwordUpdata.topDesc": "To protect your assets, after changing your password, payment services, withdrawal, and C2C selling may be disabled for 24 hours.", "passwordUpdata.newPwd": "New Password", "passwordUpdata.newPwdPlaceholder": "Please enter new password", "passwordUpdata.confirmPwd": "Confirm Password", "passwordUpdata.confirmPwdPlaceholder": "Please re-enter new password", "passwordUpdata.ruleLength": "8-20 characters", "passwordUpdata.ruleNumber": "At least 1 number", "passwordUpdata.ruleUppercase": "At least 1 uppercase letter", "passwordUpdata.submit": "Submit", "passwordUpdata.successTitle": "Password Changed Successfully", "passwordUpdata.successDesc": "Your login password has been updated. Please log in again. Withdrawals are disabled for 24 hours after password change.", "passwordUpdata.ok": "OK", "passwordUpdata.inputAll": "Please fill in all fields", "passwordUpdata.notSame": "The two passwords do not match", "passwordUpdata.notRule": "The new password does not meet all rules", "passwordUpdata.submitSuccess": "Submitted successfully", "passwordVerify.title": "Email Verification", "passwordVerify.topDesc": "Please enter the 6-digit code sent to {email}", "passwordVerify.codeTitle": "Email Verification Code", "passwordVerify.codePlaceholder": "Please enter the verification code", "passwordVerify.getCode": "Get Code", "passwordVerify.reGetCode": "Resend", "passwordVerify.submit": "Submit", "passwordVerify.gettingCode": "Getting verification code", "passwordVerify.codeSent": "Verification code sent", "passwordVerify.waitReget": "Resend after countdown ends", "passwordVerify.input6Code": "Please enter the 6-digit code", "passwordVerify.submitSuccess": "Submitted successfully", "passwordVerify.submitFail": "Submit failed: ", "pinCode.title": "Add Other Verification Method", "pinCode.topDesc": "To ensure account security, please select one verification method for identity verification.", "pinCode.emailVerify": "Email Verification", "pinCode.googleVerify": "Google Authenticator", "pinCode.selectType": "Please select a verification method", "pinCode.confirm": "Confirm", "pinCodeIndex.title": "Add Other Verification Method", "pinCodeIndex.topDesc": "To ensure account security, please select one verification method for identity verification.", "pinCodeIndex.emailVerify": "Email Verification", "pinCodeIndex.googleVerify": "Google Authenticator", "pinCodeIndex.selectType": "Please select a verification method", "pinCodeIndex.confirm": "Confirm", "pinCodeUpdata.title": "Set Payment PIN Code", "pinCodeUpdata.topDesc": "The payment PIN code will be used to authorize payment transactions.", "pinCodeUpdata.createTitle": "Create Payment PIN Code", "pinCodeUpdata.ruleDesc": "The password should not be too simple, and the same number cannot appear more than twice.", "pinCodeUpdata.accept": "By continuing, you accept the", "pinCodeUpdata.terms": "Terms of Use ！", "pinCodeUpdata.confirm": "Confirm", "pinCodeUpdata.successTitle": "Password Changed Successfully", "pinCodeUpdata.successDesc": "Your login password has been updated. Please log in again. Withdrawals are disabled for 24 hours after password change.", "pinCodeUpdata.ok": "OK", "pinCodeUpdata.input6Pin": "Please enter a 6-digit PIN code", "pinCodeUpdata.noRepeat": "The same number cannot appear more than twice", "pinCodeUpdata.success": "Success", "securityIndex.title": "Account Security", "securityIndex.2faTitle": "Two-Factor Authentication (2FA)", "securityIndex.2faDesc": "To protect your account, please enable at least two types of two-factor authentication.", "securityIndex.email": "Email", "securityIndex.password": "Password", "securityIndex.pinCode": "Payment PIN Code", "securityIndex.emailBinded": "Bound", "securityIndex.emailUnbind": "Unbound", "securityIndex.setted": "Set", "securityIndex.unset": "Unset", "pinCodeVerify.title": "Email Verification", "pinCodeVerify.topDesc": "Please enter the 6-digit code sent to {email}", "pinCodeVerify.codeTitle": "Email Verification Code", "pinCodeVerify.codePlaceholder": "Please enter the verification code", "pinCodeVerify.getCode": "Get Code", "pinCodeVerify.reGetCode": "Resend", "pinCodeVerify.submit": "Submit", "pinCodeVerify.gettingCode": "Getting verification code", "pinCodeVerify.codeSent": "Verification code sent", "pinCodeVerify.waitReget": "Resend after countdown ends", "pinCodeVerify.input6Code": "Please enter the 6-digit code", "pinCodeVerify.submitSuccess": "Submitted successfully", "serviceTab.common": "Common", "serviceTab.crypto": "Crypto", "serviceTab.fiat": "Fiat", "serviceTab.activity": "Activity & Rewards", "serviceTab.help": "Help", "serviceTab.other": "Other", "service.contract": "Contract", "service.finance": "Finance", "service.node": "Node", "service.charge": "<PERSON><PERSON><PERSON><PERSON>", "service.order": "Copy Trading", "service.notice": "Notice", "service.stock": "Stock Trading", "service.hotEvent": "Hot Event", "service.invite": "Invite", "service.c2c": "C2C", "service.swap": "<PERSON><PERSON><PERSON>", "service.flashSwap": "<PERSON>", "service.spot": "Spot Trading", "service.cryptoCharge": "Crypto Deposit", "service.cryptoWithdraw": "Crypto Withdraw", "service.quickBuy": "Quick Buy", "service.transferOut": "Transfer Out", "service.quickSell": "<PERSON>", "service.assetTransfer": "Asset Transfer", "service.hkStock": "HK Stock", "service.usStock": "US Stock", "service.swissBank": "Swiss Bank Account", "service.fiatCharge": "Fiat Deposit", "service.fiatWithdraw": "Fiat Withdraw", "service.fiatTransfer": "Fiat Transfer", "service.fiatSwap": "Fiat Swap", "service.agent": "Agent", "service.kol": "KOL", "service.myReward": "My Reward", "service.airdrop": "Airdrop", "service.support": "Support", "service.faq": "Help Center", "service.burnQuery": "Burn Query", "service.api": "API", "userIndex.normalUser": "Normal User", "userIndex.verified": "Verified", "personal.estimatedAssets": "Estimated Total Assets", "personal.yesterday": "Yesterday", "personal.myWallet": "My Wallet", "personal.addFunds": "Add Funds", "personal.transfer": "Transfer", "personal.swap": "<PERSON><PERSON><PERSON>", "personal.tradeRecord": "Transaction History", "personal.deposit": "<PERSON><PERSON><PERSON><PERSON>", "personal.withdraw": "Withdraw", "personal.flashSwap": "<PERSON>", "personal.assetInfo": "Asset Info", "personal.search": "Search", "personal.hideSmallAssets": "Hide assets less than $1", "personal.viewMore": "+ View More", "personal.noData": "No Data", "personal.selectDepositMethod": "Select Deposit Method", "personal.onChainDeposit": "On-chain Deposit", "personal.quickBuy": "Quick Buy", "personal.c2c": "C2C Trading", "personal.selectWithdrawMethod": "Select Withdraw Method", "personal.onChainWithdraw": "On-chain Withdraw", "personal.history": "History", "personal.fundsAccountCrypto": "Funds Account (Crypto)", "personal.usdtContractAccount": "USDT Contract Account (Crypto)", "personal.fundsAccountFiat": "Funds Account (Fiat)", "personal.usStockAccountFiat": "US Stock Account (Fiat)", "personal.hkStockAccountFiat": "HK Stock Account (Fiat)", "personal.toUsdtContract": "(Transfer to USDT Contract Account)", "personal.toFundsAccount": "(Transfer to Funds Account)", "personal.onlyFiatTransfer": "(Only transfer within fiat account)", "personal.toHkFundsAccount": "(Transfer to HK Funds Account)", "personal.toUsFundsAccount": "(Transfer to US Funds Account)", "personal.onChainDepositDesc": "Deposit crypto from other platforms/wallets to PinkWallet account", "personal.quickBuyDesc": "Buy crypto with fiat, deposit to PinkWallet after purchase", "personal.c2cDesc": "Peer-to-peer trading, best price, supports multiple local payment methods", "personal.onChainWithdrawDesc": "Withdraw crypto from PinkWallet to other platforms/wallets", "bDetails.distribution": "Asset Distribution", "bDetails.contracts": "Contract Trading", "bDetails.history": "History Record", "bDetails.FUNDS": "Funds Account (Fiat)", "bDetails.STOCK_US": "US Stock Account (Fiat)", "bDetails.STOCK_HK": "HK Stock Account (Fiat)", "bDetails.CONTRACT": "Contract Account", "wallet.pieTip": "Asset pie chart does not include liabilities", "wallet.assetType": "Asset Type", "wallet.search": "Search", "wallet.selectWallet": "Select Wallet", "wallet.onChainDeposit": "On-chain Deposit", "wallet.onChainDepositDesc": "Deposit crypto from other platforms/wallets to PinkWallet account", "wallet.quickBuy": "Quick Buy", "wallet.quickBuyDesc": "Buy crypto with fiat, deposit to PinkWallet after purchase", "wallet.c2c": "C2C Trading", "wallet.c2cDesc": "Peer-to-peer trading, best price, supports multiple local payment methods", "wallet.fundsAccountCrypto": "Funds Account (Crypto)", "wallet.fundsAccountCryptoDesc": "≈40,999.******** USD (Transfer to USDT Contract Account)", "wallet.usdtContractAccount": "USDT Contract Account (Crypto)", "wallet.usdtContractAccountDesc": "≈40,999.******** USD (Transfer to Funds Account)", "wallet.fundsAccountFiat": "Funds Account (Fiat)", "wallet.fundsAccountFiatDesc": "≈40,999.******** USD (Only transfer within fiat account)", "wallet.usStockAccountFiat": "US Stock Account (Fiat)", "wallet.usStockAccountFiatDesc": "40,999.******** USD (Transfer to HK Funds Account)", "wallet.hkStockAccountFiat": "HK Stock Account (Fiat)", "wallet.hkStockAccountFiatDesc": "40,999.******** HKD (Transfer to US Funds Account)", "wallet.contractAccountCrypto": "Contract Account (Crypto)", "wallet.transfer": "Transfer", "wallet.floatProfit": "Floating P/L", "wallet.marketValue": "Market Value", "wallet.realizedProfit": "Realized P/L", "wallet.trade": "Trade", "wallet.flashSwap": "<PERSON>", "wallet.available": "Available", "wallet.frozen": "Frozen", "wallet.positionAvailable": "Position/Available", "wallet.costLimit": "Cost/Limit Price", "wallet.currentPosition": "Current Position", "wallet.historyEntrust": "History Entrust", "wallet.usStockAccount": "US Stock Account", "wallet.hkStockAccount": "HK Stock Account", "wallet.walletBalance": "Wallet Balance", "wallet.unrealizedProfit": "Unrealized P/L", "wallet.historyPosition": "History Position", "personal.selected": "You selected: ", "menu.c2c": "C2C", "menu.deposit": "<PERSON><PERSON><PERSON><PERSON>", "menu.contract": "Contract", "menu.invite": "Invite", "menu.more": "More", "market.cryptoMarket": "Crypto Market", "market.stockMarket": "Stock Market", "market.subTabs.favorite": "Favorites", "market.subTabs.hot": "Hot", "market.subTabs.gainers": "Gaine<PERSON>", "market.subTabs.losers": "Losers", "market.subTabs.volume": "24H Volume", "market.stock.us": "US Stocks", "market.stock.hk": "HK Stocks", "market.stock.hsi": "HSI", "market.stock.dji": "<PERSON>", "market.stock.nasdaq": "NASDAQ", "market.table.name": "Name", "market.table.stockName": "Stock Name", "market.table.latestPrice": "Latest Price", "market.table.dayChange": "24h Change", "market.table.change": "Change", "market.table.volume": "Volume", "market.button.loadMore": "Load More", "market.tips.noMore": "No More Data", "market.tips.loading": "Loading..."}