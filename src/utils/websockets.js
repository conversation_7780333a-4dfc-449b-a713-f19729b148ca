import Pako from "pako";
import store from "@/store";
export function startSocket() {
  uni.connectSocket({
    // market_ticker  24小时涨跌幅  channel

    // #ifdef H5
    url: process.env.VUE_APP_WS_API_URL,
    // #endif

    // #ifdef APP
    url: getApp().globalData.socketApi,
    // #endif

    success: (res) => {
      console.log("连上了");
    },
  });

  uni.onSocketOpen(function (res) {
    console.log(res, "打开的消息");

    let Data_24h = {
      event: "sub",
      params: {
        channel: "market_ticker",
        cb_id: `e_${`btc`}`,
      },
    };
    let message_24 = JSON.stringify(Data_24h);
    uni.sendSocketMessage({
      data: message_24,
      complete(res) {
        console.log("24小时涨跌幅", res);
      },
    });

    let subDatadepth2 = {
      event: "sub",
      params: {
        channel: `market_e_${`btc`}_depth_step0`,
        cb_id: `e_${`btc`}`,
      },
    };
    let messagedep = JSON.stringify(subDatadepth2);
    uni.sendSocketMessage({
      data: messagedep,
      complete(res) {
        console.log("深度图发送的消息", res);
      },
    });

    let subDatadepth = {
      event: "sub",
      params: {
        channel: `market_price`,
        cb_id: "",
      },
    };
    let msg = JSON.stringify(subDatadepth);
    uni.sendSocketMessage({
      data: msg,
      complete(res) {
        // console.log("jiage", res);
      },
    });

    let uid = uni.getStorageSync("uid");
    let uidSub = {
      event: "sub",
      params: {
        channel: `user_setting_${uid}`,
        cb_id: "",
      },
    };
    let msgUid = JSON.stringify(uidSub);

    uni.sendSocketMessage({
      data: msgUid,
      complete(res) {
        console.log("message", res);
      },
    });

    // let fundingRateData = {
    //   event: "sub",
    //   params: { channel: `market_${symbol}_info`, cb_id: `e_${symbol}` },
    // };
    // let messagefundingRate = JSON.stringify(fundingRateData);
    // uni.sendSocketMessage({
    //   data: messagefundingRate,
    //   complete(res) {
    //     console.log("资金费率发送的消息", res);
    //   },
    // });

    // let subData = {
    //   event: "sub",
    //   params: {
    //     channel: `market_e_${symbol}_trade_ticker`,
    //     cb_id: `e_${symbol}`,
    //   },
    // };
    // let message = JSON.stringify(subData);
    // uni.sendSocketMessage({
    //   data: message,
    //   complete(res2) {
    //     console.log("发送的消息exchange", res2);
    //   },
    //   fail(err) {
    //     // this.FetchLatestPrice()
    //     console.log("exchange连接失败 connectSocket=", err);
    //   },
    // });
  });

  uni.onSocketMessage((event) => {
    var uint8array = new Uint8Array(event.data);
    const output = Pako.inflate(uint8array, {
      to: "string",
    });
    let res = JSON.parse(output);
    // console.log(res, "channelsubscribe");

    if (res.channel == "market_price") {
      store.commit("changebom", res.data);
    } else if (!res.channel) {
      store.commit("changeRate", res);
    }
  });

  uni.onSocketError(function (res) {
    uni.connectSocket({
      // url: 'wss://ws.okx.com:8443/ws/v5/public',
      // #ifdef H5
      url: process.env.VUE_APP_WS_API_URL,
      // #endif
      // #ifdef APP
      url: getApp().globalData.socketApi,
      // #endif
      success: (res) => {},
    });
  });
}

let socketTime = []; //这个时间用来记录第一次触发的时间
let timeIndex = -1; //记录时间索引 第一次收到消息是0，第二次收到小时就+1
function calculationTime() {
  timeIndex += 1;

  socketTime[timeIndex] = new Date().getTime();
  if (socketTime.length >= 2) {
    if (socketTime[timeIndex] / 1000 - socketTime[timeIndex - 1] / 1000 > 5) {
      uni.closeSocket();
      uni.onSocketClose(function (res) {
        console.log("WebSocket 已关闭！");
        uni.connectSocket({
          // url: 'wss://ws.okx.com:8443/ws/v5/public',
          // #ifdef H5
          url: process.env.VUE_APP_WS_API_URL,
          // #endif
          // #ifdef APP
          url: getApp().globalData.socketApi,
          // #endif
          success: (res) => {
            console.log("连接", res);
          },
        });
      });
    }
  }

  if (timeIndex == 200) {
    socketTime.length = 0;
    timeIndex = -1;
  }
}

export function onSocketOpen() {
  uni.closeSocket();
}
