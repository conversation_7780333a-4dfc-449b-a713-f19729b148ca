export const downloadBlob = (blobFile, fileName) => {
  const blob = new Blob([blobFile], {
    type: "application/vnd.ms-excel;charset=utf-8",
  });
  const a = document.createElement("a");
  fileName && (a.download = fileName);
  a.style.display = "none";
  a.href = URL.createObjectURL(blob);
  document.body.appendChild(a);
  a.click();
  URL.revokeObjectURL(a.href); // 释放URL 对象
  document.body.removeChild(a);
};

export const userType = () => {
  let isAdmin;
  let username = localStorage.getItem("username");
  let adminArray = ["凌凡", "文君", "shiqi", "dingqiang", "admin"];
  isAdmin = adminArray.includes(username);
  return isAdmin;
};

export const formatTimestamp = (seconds) => {
  if (!seconds) {
    return "--";
  }
  const date = new Date(seconds * 1000); // 转成毫秒时间戳
  const Y = date.getFullYear();
  const M = String(date.getMonth() + 1).padStart(2, "0");
  const D = String(date.getDate()).padStart(2, "0");
  const h = String(date.getHours()).padStart(2, "0");
  const m = String(date.getMinutes()).padStart(2, "0");
  const s = String(date.getSeconds()).padStart(2, "0");
  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
};

export const removeMilliseconds = (isoString) => {
  if (!isoString) return "--";
  const date = new Date(isoString);
  if (isNaN(date.getTime())) return "--";

  const pad = (n) => n.toString().padStart(2, "0");

  const yyyy = date.getFullYear();
  const MM = pad(date.getMonth() + 1);
  const dd = pad(date.getDate());
  const HH = pad(date.getHours());
  const mm = pad(date.getMinutes());
  const ss = pad(date.getSeconds());

  return `${yyyy}-${MM}-${dd} ${HH}:${mm}:${ss}`;
};
