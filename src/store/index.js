import Vue from "vue";
import Vuex from "vuex";
Vue.use(Vuex);
const store = new Vuex.Store({
  state: {
    userInfo: { name: "" },
    hasLogin: false,
    token: "",
    address: "",
    shouldVibrate: true, // 用于控制是否震动的全局变量
    indexBottom: [],
    indexRate: {},
    isMsg: false,
    stockcode: "0700", // 默认的股票名称
    // stockname: "TSLA",
    hqbdList: {},
    Kline: [],
    usDepth: {},
  },
  mutations: {
    changeUsDepth(state, val) {
      state.usDepth = val;
    },
    changeKline(state, val) {
      state.Kline = val;
    },
    changehqbdList(state, val) {
      state.hqbdList = val;
    },
    changeStockname(state, val) {
      state.stockname = val;
    },
    changeMsgBoolean(state, val) {
      state.isMsg = val;
    },
    changeRate(state, val) {
      state.indexRate = val;
    },
    changebom(state, val) {
      state.indexBottom = val;
    },
    toggleVibration(state, value) {
      state.shouldVibrate = value; // 修改震动状态
    },
    // handleVibrate() {
    // 	if (this.$store.state.shouldVibrate) {
    // 	  uni.vibrateLong();  // 如果全局变量为 true，则执行震动
    // 	}
    //   }
    login(state, provider) {
      state.hasLogin = true;
      state.userInfo.userName = provider.userName;
      state.userInfo.userPass = provider.userPass;
      uni.setStorage({
        key: "uerInfo",
        data: provider,
      });
    },
    tokenStorage(state, provider) {
      state.token = provider;
      uni.setStorage({
        key: "token",
        data: provider,
      });
    },
    GetAddress(state, provider) {
      state.address = provider.address;
    },
    // setUser(state, user) {
    // 	state.user = user
    // 	console.log('存储用户成功', state.user)
    // },
  },
});
export default store;
