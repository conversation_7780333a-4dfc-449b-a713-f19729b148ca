/**数据列表对应的路由表模块
 * 注意：path必须跟pages.json中的地址对应，最前面别忘了加'/'哦
 * //对于h5端你必须在首页加上aliasPath并设置为/
 */
const dataList = [
  {
    path: "/pages/project/index/index",
    aliasPath: "/",
    name: "index",
  },
  {
    path: "/pages/project/login/login",
    aliasPath: "/",
    name: "login",
  },
  {
    path: "/pages/project/login/register",
    aliasPath: "/",
    name: "register",
  },
  {
    path: "/pagesA/project/index/PointMaster",
    aliasPath: "/",
    name: "PointMaster",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/swap/search",
    aliasPath: "/",
    name: "SwapSearch",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/index",
    aliasPath: "/",
    name: "personal",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/profile",
    aliasPath: "/",
    name: "profile",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/notice/webView",
    aliasPath: "/",
    name: "webView",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/otc/charge",
    aliasPath: "/",
    name: "charge",
    meta: {
      title: "",
    },
  },
  // {
  //   path: "/pagesA/project/otc/withdraw",
  //   aliasPath: "/",
  //   name: "withdraw",
  //   meta: {
  //     title: "",
  //   },
  // },
  {
    path: "/pagesA/project/otc/Record",
    aliasPath: "/",
    name: "Record",
    meta: {
      title: "",
    },
  },
  // CurrencyCharge
  {
    path: "/pagesA/project/otc/CurrencyCharge",
    aliasPath: "/",
    name: "CurrencyCharge",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/swap/swap",
    aliasPath: "/",
    name: "swap",
    meta: {
      title: "",
    },
  },
  // TransferOut
  {
    path: "/pagesA/project/otc/TransferOut",
    aliasPath: "/",
    name: "TransferOut",
    meta: {
      title: "",
    },
  },
  // Notification
  {
    path: "/pagesA/project/Notification/index",
    aliasPath: "/",
    name: "Notification",
    meta: {
      title: "",
    },
  },
  // AddCount
  {
    path: "/pagesA/project/otc/AddCount",
    aliasPath: "/",
    name: "AddCount",
    meta: {
      title: "",
    },
  },
  // security
  {
    path: "/pagesA/project/personal/security",
    aliasPath: "/",
    name: "Security",
    meta: {
      title: "",
    },
  },
  // settingPwd
  {
    path: "/pagesA/project/personal/settingPwd",
    aliasPath: "/",
    name: "SettingPwd",
    meta: {
      title: "",
    },
  },
  // forget
  {
    path: "/pages/project/login/forget",
    aliasPath: "/",
    name: "forget",
    meta: {
      title: "",
    },
  },
  // /otc/AccountDetail
  {
    path: "/pagesA/project/otc/AccountDetail",
    aliasPath: "/",
    name: "AccountDetail",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/generalAgreement",
    aliasPath: "/",
    name: "generalAgreement",
    meta: {
      title: "协议",
    },
  },
  // QuickBuy
  {
    path: "/pagesA/project/otc/QuickBuy",
    aliasPath: "/",
    name: "QuickBuy",
    meta: {
      title: "",
    },
  },
  // Receive
  {
    path: "/pagesA/project/otc/Receive",
    aliasPath: "/",
    name: "Receive",
    meta: {
      title: "",
    },
  },
  // CurrentCryptocurrencyDetails
  {
    path: "/pages/project/index/CurrentCryptocurrencyDetails",
    aliasPath: "/",
    name: "CurrentCryptocurrencyDetails",
    meta: {
      title: "",
    },
  },
  // TransactionHistory
  {
    path: "/pagesA/project/otc/TransactionHistory",
    aliasPath: "/",
    name: "TransactionHistory",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/login/login_back",
    aliasPath: "/",
    name: "login_back",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/otc/withdrawAddAccount",
    aliasPath: "/",
    name: "withdrawAddAccount",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/otc/sell",
    aliasPath: "/",
    name: "sell",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/otc/buy",
    aliasPath: "/",
    name: "buy",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/face",
    aliasPath: "/",
    name: "face",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/security/index",
    aliasPath: "/",
    name: "security_index",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/security/2Step",
    aliasPath: "/",
    name: "2Step",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/security/2fa",
    aliasPath: "/",
    name: "2FA",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/index",
    aliasPath: "/",
    name: "C2C",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/main",
    aliasPath: "/",
    name: "Main",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/order",
    aliasPath: "/",
    name: "C2Corder",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchant",
    aliasPath: "/",
    name: "merchant",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/buy",
    aliasPath: "/",
    name: "C2Cbuy",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/sell",
    aliasPath: "/",
    name: "C2Csell",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/progress/sell",
    aliasPath: "/",
    name: "progressSell",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/progress/buy",
    aliasPath: "/",
    name: "progressBuy",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/progress/cancel",
    aliasPath: "/",
    name: "progressCancel",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/progress/add",
    aliasPath: "/",
    name: "progressAdd",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/progress/order",
    aliasPath: "/",
    name: "progressOrder",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/pay/index",
    aliasPath: "/",
    name: "payway",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/pay/add",
    aliasPath: "/",
    name: "addpay",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/ban/appeal",
    aliasPath: "/",
    name: "C2Cappeal",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/ban/index",
    aliasPath: "/",
    name: "ban",
    meta: {
      title: "",
    },
  },

  {
    path: "/pagesA/project/C2C/merchant/apply",
    aliasPath: "/",
    name: "bindMerchant",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchant/form",
    aliasPath: "/",
    name: "merchantForm",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchant/license",
    aliasPath: "/",
    name: "license",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchant/nation",
    aliasPath: "/",
    name: "nation",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchant/publish_buy",
    aliasPath: "/",
    name: "publish_buy",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchant/publish_sell",
    aliasPath: "/",
    name: "publish_sell",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchant/realname",
    aliasPath: "/",
    name: "realname",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchant/unbind",
    aliasPath: "/",
    name: "unbind",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/coin",
    aliasPath: "/",
    name: "C2Ccoin",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/C2C/merchantDetails",
    aliasPath: "/",
    name: "merchantDetails",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/trade/index",
    aliasPath: "/",
    name: "trade",
    meta: {
      title: "",
    },
  },
  // trade/All-order
  {
    path: "/pagesA/project/trade/All-order",
    aliasPath: "/",
    name: "All-order",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/us/index",
    aliasPath: "/",
    name: "us",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/us/details",
    aliasPath: "/",
    name: "usDetails",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/more",
    aliasPath: "/",
    name: "more",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/ServiceSearch",
    aliasPath: "/",
    name: "ServiceSearch",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/personal/comming",
    aliasPath: "/",
    name: "comming",
    meta: {
      title: "",
    },
  },
  {
    path: "/pages/project/index/Home",
    aliasPath: "/",
    name: "Home",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/trade/detail",
    aliasPath: "/",
    name: "detail",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/user/setting",
    aliasPath: "/",
    name: "setting",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/user/setLanguage",
    aliasPath: "/",
    name: "setLanguage",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/user/setB",
    aliasPath: "/",
    name: "setB",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/user/setNamePic",
    aliasPath: "/",
    name: "setNamePic",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/user/userInfo",
    aliasPath: "/",
    name: "userInfo",
  },
  {
    path: "/pagesA/project/user/realName",
    aliasPath: "/",
    name: "realName",
  },
  {
    path: "/pagesA/project/user/realNameInfo",
    aliasPath: "/",
    name: "realNameInfo",
  },
  {
    path: "/pagesA/project/user/realNameImg",
    aliasPath: "/",
    name: "realNameImg",
  },
  {
    path: "/pagesA/project/user/realNameHigh",
    aliasPath: "/",
    name: "realNameHigh",
  },
  {
    path: "/pagesA/project/user/security/index",
    aliasPath: "/",
    name: "security",
  },
  {
    path: "/pagesA/project/user/security/email",
    aliasPath: "/",
    name: "email",
  },
  {
    path: "/pagesA/project/user/security/emailVerify",
    aliasPath: "/",
    name: "emailVerify",
  },
  {
    path: "/pagesA/project/user/security/emailUpdata",
    aliasPath: "/",
    name: "emailUpdata",
  },
  {
    path: "/pagesA/project/user/security/password",
    aliasPath: "/",
    name: "password",
  },
  {
    path: "/pagesA/project/user/security/passwordVerify",
    aliasPath: "/",
    name: "passwordVerify",
  },
  {
    path: "/pagesA/project/user/security/passwordUpdata",
    aliasPath: "/",
    name: "passwordUpdata",
  },
  {
    path: "/pagesA/project/user/security/pinCode",
    aliasPath: "/",
    name: "pinCode",
  },
  {
    path: "/pagesA/project/user/security/pinCodeFirst",
    aliasPath: "/",
    name: "pinCodeFirst",
  },
  {
    path: "/pagesA/project/user/security/pinCodeVerify",
    aliasPath: "/",
    name: "pinCodeVerify",
  },
  {
    path: "/pagesA/project/user/security/pinCodeUpdata",
    aliasPath: "/",
    name: "pinCodeUpdata",
  },
  {
    path: "/pagesA/project/user/index",
    aliasPath: "/",
    name: "user",
  },
  {
    path: "/pages/project/login/setPwd",
    aliasPath: "/",
    name: "setPwd",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/assets/transfer",
    name: "Mutualtransfer",
    aliasPath: "/",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/assets/history",
    name: "transfer_history",
    aliasPath: "/",
    meta: {
      title: "",
    },
  },
  {
    path: "/pagesA/project/assets/addCapital",
    aliasPath: "/",
    name: "addCapital",
  },
  {
    path: "/pagesA/project/assets/withdraw",
    aliasPath: "/",
    name: "withdraw",
  },
  {
    path: "/pagesA/project/assets/transfer",
    aliasPath: "/",
    name: "transfer",
  }
];
export default dataList;
