import layoutHeaderAside from "@/layout/header-aside";

// 由于懒加载页面太多的话会造成webpack热更新太慢，所以开发环境不使用懒加载，只有生产环境使用懒加载
const _import = require("@/libs/util.import." + process.env.NODE_ENV);

/**
 * 在主框架内显示
 */
const frameIn = [
  {
    path: "/",
    redirect: {
      name: "index",
    },
    component: layoutHeaderAside,
    children: [
      // 首页
      {
        path: "index",
        name: "index",
        meta: {
          auth: true,
          cache: true,
        },
        component: _import("system/index"),
      },
      {
        path: "CoinSymbolConfig",
        name: "CoinSymbolConfig",
        meta: {
          title: "币对配置",
          auth: true,
          cache: true,
        },
        component: _import("Coin"),
      },
      // CoinDetails
      {
        path: "CoinDetails",
        name: "CoinDetails",
        meta: {
          title: "配置明细",
          cache: true,
          auth: true,
        },
        component: _import("Coin/CoinDetails"),
      },
      // TradeRecord
      {
        path: "TradeRecord",
        name: "TradeRecord",
        meta: {
          title: "交易记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/TradeRecord"),
      },
      // valutAccount
      {
        path: "valutAccount",
        name: "valutAccount",
        meta: {
          title: "查询估值账户",
          cache: true,
          auth: true,
        },
        component: _import("Coin/fireBlocks/valutAccount"),
      },
      // wallet
      {
        path: "wallet",
        name: "wallet",
        meta: {
          title: "查询钱包",
          cache: true,
          auth: true,
        },
        component: _import("Coin/fireBlocks/wallet"),
      },
      // AllAssets
      {
        path: "AllAssets",
        name: "AllAssets",
        meta: {
          title: "查询所有支持资产",
          cache: true,
          auth: true,
        },
        component: _import("Coin/fireBlocks/AllAssets"),
      },
      // Address
      {
        path: "Address",
        name: "Address",
        meta: {
          title: "查询地址",
          cache: true,
          auth: true,
        },
        component: _import("Coin/fireBlocks/Address"),
      },
      // Trade
      {
        path: "Trade",
        name: "Trade",
        meta: {
          title: "查询交易",
          cache: true,
          auth: true,
        },
        component: _import("Coin/fireBlocks/Trade"),
      },
      // b2c2
      {
        path: "b2c2order",
        name: "b2c2order",
        meta: {
          title: "b2c2订单",
          cache: true,
          auth: true,
        },
        component: _import("Coin/b2c2/order"),
      },
      {
        path: "b2c2fee",
        name: "b2c2fee",
        meta: {
          title: "b2c2手续费",
          cache: true,
          auth: true,
        },
        component: _import("Coin/b2c2/fee"),
      },
      // 基础配置
      {
        path: "configuration",
        name: "configuration",
        meta: {
          title: "基础配置",
          cache: true,
          auth: true,
        },
        component: _import("Coin/configuration"),
      },
      // banner配置
      {
        path: "bannerConfig",
        name: "bannerConfig",
        meta: {
          title: "banner配置",
          cache: true,
          auth: true,
        },
        component: _import("Coin/bannerConfig"),
      },
      // 添加/编辑banner配置
      {
        path: "addBanner",
        name: "addBanner",
        meta: {
          title: "banner配置",
          cache: true,
          auth: true,
        },
        component: _import("Coin/bannerConfig/addBanner"),
      },
      // userList
      {
        path: "userList",
        name: "userList",
        meta: {
          title: "用户列表",
          auth: true,
          cache: true,
        },
        component: _import("Coin/user"),
      },
      // userBalance
      {
        path: "userBalances",
        name: "userBalances",
        meta: {
          title: "用户余额",
          auth: true,
          cache: true,
        },
        component: _import("Coin/user/balance"),
      },
      // transferHistory
      {
        path: "transferHistory",
        name: "transferHistory",
        meta: {
          title: "查询后台转账记录",
          auth: true,
          cache: true,
        },
        component: _import("Coin/fireBlocks/transferHistory"),
      },
      // ServiceDeposit
      {
        path: "ServiceDeposit",
        name: "ServiceDeposit",
        meta: {
          title: "充值列表（加密）",
          auth: true,
          cache: true,
        },
        component: _import("Coin/service/deposit"),
      },
      // flashexchange
      {
        path: "flashexchange",
        name: "flashexchange",
        meta: {
          title: "闪兑列表",
          auth: true,
          cache: true,
        },
        component: _import("Coin/service/flashexchange"),
      },
      // 对冲敞口
      {
        path: "Hedgingexposure",
        name: "Hedgingexposure",
        meta: {
          title: "对冲敞口",
          auth: true,
          cache: true,
        },
        component: _import("Coin/finance/Hedgingexposure"),
      },
      // 提现转出列表-法币
      {
        path: "WithdrawalListFiaty",
        name: "WithdrawalListFiaty",
        meta: {
          title: "提现转出列表-法币",
          auth: true,
          cache: true,
        },
        component: _import("Coin/finance/WithdrawalListFiaty"),
      },
      // 提现转出列表-加密
      {
        path: "WithdrawalListCrypto",
        name: "WithdrawalListCrypto",
        meta: {
          title: "提现转出列表-加密",
          auth: true,
          cache: true,
        },
        component: _import("Coin/finance/WithdrawalListCrypto"),
      },
      // 充值列表（法币）
      {
        path: "DepositListFiaty",
        name: "DepositListFiaty",
        meta: {
          title: "充值列表-法币",
          auth: true,
          cache: true,
        },
        component: _import("Coin/finance/DepositListFiaty"),
      },
      // 充值列表（加密）
      {
        path: "DepositListCrypto",
        name: "DepositListCrypto",
        meta: {
          title: "充值列表-加密",
          auth: true,
          cache: true,
        },
        component: _import("Coin/finance/DepositListCrypto"),
      },
      // 各平台资产统计
      {
        path: "AssetAllPlatform",
        name: "AssetAllPlatform",
        meta: {
          title: "各平台资产统计",
          auth: true,
          cache: true,
        },
        component: _import("Coin/finance/AssetAllPlatform"),
      },
      // 资金提现和调拨预警
      {
        path: "WarningWithdrawalAllocation",
        name: "WarningWithdrawalAllocation",
        meta: {
          title: "资金提现和调拨预警",
          auth: true,
          cache: true,
        },
        component: _import("Coin/finance/WarningWithdrawalAllocation"),
      },
      // 未归集统计 Nocollet
      {
        path: "Nocollet",
        name: "Nocollet",
        meta: {
          title: "未归集统计",
          auth: true,
          cache: true,
        },
        component: _import("Coin/finance/Nocollet"),
      },
      // 闪兑配置
      {
        path: "ConvertConfiguration",
        name: "ConvertConfiguration",
        meta: {
          title: "闪兑配置",
          auth: true,
          cache: true,
        },
        component: _import("Coin/operation/ConvertConfiguration"),
      },
      // 提现配置加密
      {
        path: "WithdrawalConfiguration",
        name: "WithdrawalConfiguration",
        meta: {
          title: "提现配置",
          auth: true,
          cache: true,
        },
        component: _import("Coin/operation/WithdrawalConfiguration"),
      },
      // 提现配置法币
      {
        path: "WithdrawalConfigurationFiat",
        name: "WithdrawalConfigurationFiat",
        meta: {
          title: "提现配置",
          auth: true,
          cache: true,
        },
        component: _import("Coin/operation/WithdrawalConfigurationFiat"),
      },
      // 充值配置
      {
        path: "RechargeConfiguration",
        name: "RechargeConfiguration",
        meta: {
          title: "充值配置",
          auth: true,
          cache: true,
        },
        component: _import("Coin/operation/RechargeConfiguration"),
      },
      // announcement
      {
        path: "announcement",
        name: "announcement",
        meta: {
          title: "公告",
          cache: true,
          auth: true,
        },
        component: _import("Coin/operation/announcement"),
      },
      // addnotice
      {
        path: "addnotice",
        name: "addnotice",
        meta: {
          title: "增加公告",
          cache: true,
          auth: true,
        },
        component: _import("Coin/operation/addnotice"),
      },
      // Useropera
      {
        path: "Useropera",
        name: "Useropera",
        meta: {
          title: "用户列表",
          cache: true,
          auth: true,
        },
        component: _import("Coin/operation/user"),
      },
      {
        path: "Userpermissions",
        name: "Userpermissions",
        meta: {
          title: "用户权限",
          cache: true,
          auth: true,
        },
        component: _import("Coin/userpermissions"),
      },
      {
        path: "permissions",
        name: "permissions",
        meta: {
          title: "权限列表",
          cache: true,
          auth: true,
        },
        component: _import("Coin/permissions"),
      },
      {
        path: "msgPush",
        name: "msgPush",
        meta: {
          title: "消息模版",
          cache: true,
          auth: true,
        },
        component: _import("Coin/msgPush"),
      },
      {
        path: "quickbuy",
        name: "quickbuy",
        meta: {
          title: "快捷买币记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/operation/quickbuy"),
      },
      {
        path: "C2Capply",
        name: "C2Capply",
        meta: {
          title: "申请管理",
          cache: true,
          auth: true,
        },
        component: _import("Coin/C2C/apply"),
      },
      {
        path: "C2Cshop",
        name: "C2Cshop",
        meta: {
          title: "商家审核",
          cache: true,
          auth: true,
        },
        component: _import("Coin/C2C/shop"),
      },
      {
        path: "C2Corder",
        name: "C2Corder",
        meta: {
          title: "委托单",
          cache: true,
          auth: true,
        },
        component: _import("Coin/C2C/order"),
      },
      {
        path: "C2COrderInfo",
        name: "C2COrderInfo",
        meta: {
          title: "用户单",
          cache: true,
          auth: true,
        },
        component: _import("Coin/C2C/orderInfo"),
      },
      // {
      //   path: "contractConfig",
      //   name: "contractConfig",
      //   meta: {
      //     title: "合约配置",
      //     cache: true,
      //     auth: true,
      //   },
      //   component: _import("Coin/Trade/config"),
      // },
      // {
      //   path: "coinPairConfig",
      //   name: "coinPairConfig",
      //   meta: {
      //     title: "币对配置",
      //     cache: true,
      //     auth: true,
      //   },
      //   component: _import("Coin/Trade/coin_config"),
      // },
      // EarningsRecord EarningsSummaryQuery
      {
        path: "EarningsSummaryQuery",
        name: "EarningsSummaryQuery",
        meta: {
          title: "收益汇总查询",
          cache: true,
          auth: true,
        },
        component: _import("Coin/partener/EarningsSummaryQuery"),
      },
      {
        path: "EarningsRecord",
        name: "EarningsRecord",
        meta: {
          title: "收益记录查询",
          cache: true,
          auth: true,
        },
        component: _import("Coin/partener/EarningsRecord"),
      },
      // PartnerApplicationManagement
      {
        path: "PartnerApplicationManagement",
        name: "PartnerApplicationManagement",
        meta: {
          title: "合伙人申请记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/partener/PartnerApplicationManagement"),
      },
      // InvitationLink
      {
        path: "InvitationLink",
        name: "InvitationLink",
        meta: {
          title: "邀请链接管理",
          cache: true,
          auth: true,
        },
        component: _import("Coin/partener/InvitationLink"),
      },
      // PartnerConfiguration
      {
        path: "PartnerConfiguration",
        name: "PartnerConfiguration",
        meta: {
          title: "合伙人配置",
          cache: true,
          auth: true,
        },
        component: _import("Coin/partener/PartnerConfiguration"),
      },
      // EntrustSummary
      {
        path: "EntrustSummary",
        name: "EntrustSummary",
        meta: {
          title: "委托汇总",
          cache: true,
          auth: true,
        },
        component: _import("Coin/partener/EntrustSummary"),
      },
      // DepositWithdrawalDetails
      {
        path: "DepositWithdrawalDetails",
        name: "DepositWithdrawalDetails",
        meta: {
          title: "出入金明细",
          cache: true,
          auth: true,
        },
        component: _import("Coin/partener/DepositWithdrawalDetails"),
      },
      {
        path: "DepositWithdrawal",
        name: "DepositWithdrawal",
        meta: {
          title: "出入金汇总",
          cache: true,
          auth: true,
        },
        component: _import("Coin/partener/DepositWithdrawal"),
      },
      {
        path: "contractConfig",
        name: "contractConfig",
        meta: {
          title: "合约配置",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/ShopOrderParameters"),
      },
      {
        path: "coinPairConfig",
        name: "coinPairConfig",
        meta: {
          title: "币对配置",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/symbolList"),
      },
      // stopProfitAndLossRecord
      {
        path: "stopProfitAndLossRecord",
        name: "stopProfitAndLossRecord",
        meta: {
          title: "止盈止损记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/stoploss"),
      },
      // TradeDetails
      {
        path: "TradeDetails",
        name: "TradeDetails",
        meta: {
          title: "成交记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/TradeDetails"),
      },
      // path: "/position",
      // title: "持仓记录",
      {
        path: "position",
        name: "position",
        meta: {
          title: "持仓记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/hold"),
      },
      {
        path: "open",
        name: "open",
        meta: {
          title: "开仓记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/open"),
      },
      {
        path: "boom",
        name: "boom",
        meta: {
          title: "爆仓记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/boom"),
      },
      {
        path: "close",
        name: "close",
        meta: {
          title: "平仓记录",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/close"),
      },
      // path: "/entrust",
      // title: "主力委托",
      {
        path: "entrust",
        name: "entrust",
        meta: {
          title: "主力委托",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/mainEntru"),
      },
      {
        path: "card",
        name: "card",
        meta: {
          title: "风控看板",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/card"),
      },
      {
        path: "kyc",
        name: "kyc",
        meta: {
          title: "初级认证查询",
          cache: true,
          auth: true,
        },
        component: _import("audit/kyc"),
      },
      {
        path: "kyb",
        name: "kyb",
        meta: {
          title: "高级认证查询",
          cache: true,
          auth: true,
        },
        component: _import("audit/kyb"),
      },
      // Shop orders
      {
        path: "Shoporders",
        name: "Shoporders",
        meta: {
          title: "铺单配置",
          cache: true,
          auth: true,
        },
        component: _import("Coin/contract/pudan"),
      },
    ],
  },
];

/**
 * 在主框架之外显示
 */
const frameOut = [
  // 登录
  {
    path: "/login",
    name: "login",
    component: _import("system/login"),
  },
];

/**
 * 错误页面
 */
const errorPage = [
  {
    path: "*",
    name: "404",
    component: _import("system/error/404"),
  },
];

// 导出需要显示菜单的
export const frameInRoutes = frameIn;

// 重新组织后导出
export default [...frameIn, ...frameOut, ...errorPage];
