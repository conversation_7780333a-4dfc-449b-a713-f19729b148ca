{
  "easycom": {
    "^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    // #ifdef APP
    // {
    //   "path": "pages/project/index/coverIndex",
    //   "style": {
    //     "navigationBarTitleText": "",
    //     "navigationStyle": "custom",
    //     "disableScroll": true,
    //     // 禁止页面滚动（如有必要）
    //     "app-plus": {
    //       "bounce": "none"
    //       // 禁止iOS回弹效果（如有必要）
    //     },
    //     "meta": {
    //       "disableSwipeBack": true
    //       // 禁止手势返回（如有必要）
    //     }
    //   }
    // },
    {
      "path": "pages/project/index/Home",
      "style": {
        "navigationBarTitleText": "Pinkwallet",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/index/index",
      "style": {
        "navigationBarTitleText": "Pink Wallet",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
        // "onReachBottomDistance": 400,
      }
    },
    // #endif
    // #ifdef H5
    {
      "path": "pages/project/index/Home",
      "style": {
        "navigationBarTitleText": "Pinkwallet",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/index/index",
      "style": {
        "navigationBarTitleText": "Pink Wallet",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
        // "onReachBottomDistance": 400,
      }
    },
    {
      "path": "pages/project/index/coverIndex",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom",
        "disableScroll": true,
        // 禁止页面滚动（如有必要）
        "app-plus": {
          "bounce": "none"
          // 禁止iOS回弹效果（如有必要）
        },
        "meta": {
          "disableSwipeBack": true
          // 禁止手势返回（如有必要）
        }
      }
    },
    // #endif
    {
      "path": "pages/project/login/forget",
      "style": {
        // "navigationBarTitleText": "忘记密码",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/index/download",
      "style": {
        "navigationBarTitleText": "Pink Wallet",
        "navigationStyle": "custom"
      }
    },
    // "style": "default"
    {
      "path": "uni_modules/uview-ui/components/u-avatar-cropper/u-avatar-cropper",
      "style": {
        "navigationBarTitleText": "裁剪头像",
        "navigationBarBackgroundColor": "#fff"
      }
    },
    {
      "path": "pages/project/notice/index",
      "style": {
        "navigationBarTitleText": "Pink Wallet",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom",
        // "onReachBottomDistance": 400,
        "pullToRefresh": {
          "support": true,
          "color": "#D8B662"
        }
      }
    },
    // "style": "default"
    {
      "path": "pages/project/putaway/index",
      "style": {
        "navigationBarTitleText": "Pink Wallet",
        // "enablePullDownRefresh": true,
        "navigationStyle": "custom",
        // "onReachBottomDistance": 400,
        "pullToRefresh": {
          "support": true,
          "color": "#D8B662"
        }
      }
    },
    // "style": "default"
    {
      "path": "pages/project/personal/index",
      "style": {
        "navigationBarTitleText": "Pink Wallet",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom",
        // "onReachBottomDistance": 400,
        "pullToRefresh": {
          "support": true,
          "color": "#D8B662"
        }
      }
    },
    // "style": "default"
    {
      "path": "pages/project/login/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
        // "pullToRefresh" : {
        //     "support" : true,
        //     "color" : "#1E1E1E"
        // }
      }
    },
    // "style": "default"
    {
      "path": "pages/project/login/loginMain",
      "style": {
        "navigationBarTitleText": "登录",
        // "enablePullDownRefresh" : true,
        "navigationStyle": "custom"
        // "onReachBottomDistance": 400,
        // "pullToRefresh" : {
        //     "support" : true,
        //     "color" : "#1E1E1E"
        // }
      }
    },
    {
      "path": "pages/project/login/location",
      "style": {
        // "enablePullDownRefresh" : true,
        "navigationStyle": "custom"
        // "onReachBottomDistance": 400,
        // "pullToRefresh" : {
        //     "support" : true,
        //     "color" : "#1E1E1E"
        // }
      }
    },
    {
      "path": "pages/project/login/loginPwd",
      "style": {
        "navigationBarTitleText": "账号登录",
        "navigationStyle": "custom"
        // "pullToRefresh" : {
        //     "support" : true,
        //     "color" : "#1E1E1E"
        // }
      }
    },
    // "style": "default"
    {
      "path": "pages/project/login/register",
      "style": {
        "navigationBarTitleText": "注册",
        // "enablePullDownRefresh" : true,
        "navigationStyle": "custom"
        // "onReachBottomDistance": 400,
        // "pullToRefresh" : {
        //     "support" : true,
        //     "color" : "#1E1E1E"
        // }
      }
    },
    {
      "path": "pages/project/notice/webView",
      "style": {
        "navigationBarTitleText": "Pink Wallet",
        "enablePullDownRefresh": true,
        "navigationBarBackgroundColor": "#fff",
        "navigationBarTextStyle": "black",
        "pullToRefresh": {
          "support": true,
          "color": "#D8B662"
        }
      }
    },
    // CurrentCryptocurrencyDetails
    {
      "path": "pages/project/index/CurrentCryptocurrencyDetails",
      "style": {
        "navigationBarTitleText": "当前货币详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/login/login_back",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/project/login/setPwd",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pagesA",
      "pages": [
        {
          "path": "project/swap/search",
          "style": {
            "navigationBarTitleText": "搜索",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/index",
          "style": {
            "navigationBarTitleText": "账户中心",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/profile",
          "style": {
            "navigationBarTitleText": "实名认证",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/charge",
          "style": {
            "navigationBarTitleText": "充值",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/withdraw",
          "style": {
            "navigationBarTitleText": "转出",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/Record",
          "style": {
            "navigationBarTitleText": "记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/CurrencyCharge",
          "style": {
            "navigationBarTitleText": "法币充值",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/swap/swap",
          "style": {
            "navigationBarTitleText": "兑换",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/TransferOut",
          "style": {
            "navigationBarTitleText": "转出",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/Notification/index",
          "style": {
            "navigationBarTitleText": "系统通知",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/AddCount",
          "style": {
            "navigationBarTitleText": "添加收款人",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/security",
          "style": {
            "navigationBarTitleText": "安全设置",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/settingPwd",
          "style": {
            "navigationBarTitleText": "设置密码",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/AccountDetail",
          "style": {
            "navigationBarTitleText": "账户详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/QuickBuy",
          "style": {
            "navigationBarTitleText": "快捷买入",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/Receive",
          "style": {
            "navigationBarTitleText": "接收",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/TransactionHistory",
          "style": {
            "navigationBarTitleText": "交易历史",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/withdrawAddAccount",
          "style": {
            "navigationBarTitleText": "添加账户",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/sell",
          "style": {
            "navigationBarTitleText": "卖出",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/otc/buy",
          "style": {
            "navigationBarTitleText": "buy",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/face",
          "style": {
            "navigationBarTitleText": "face",
            "navigationStyle": "custom",
            "disableSwipeBack": true
          }
        },
        {
          "path": "project/personal/Redirect",
          "style": {
            "navigationBarTitleText": "Redirect",
            "navigationStyle": "custom",
            "disableSwipeBack": true
          }
        },
        {
          "path": "project/personal/generalAgreement",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/2Step",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/security/2fa",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/order",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchant",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/buy",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/sell",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/progress/buy",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/progress/sell",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/progress/cancel",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/progress/add",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/progress/order",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/pay/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/pay/add",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/ban/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/ban/appeal",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },

        {
          "path": "project/C2C/merchant/apply",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchant/license",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchant/nation",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchant/publish_buy",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchant/publish_sell",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchant/realname",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchant/unbind",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchant/form",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/test",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/coin",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/trade/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/merchantDetails",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/C2C/main",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/trade/All-order",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/us/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/us/details",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/more",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/ServiceSearch",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/personal/comming",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/trade/detail",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/setNamePic",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/setting",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/setLanguage",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/setB",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/userInfo",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/realName",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/realNameImg",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/realNameInfo",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/realNameHigh",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/email",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/emailVerify",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/emailUpdata",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/password",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/passwordVerify",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/passwordUpdata",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/pinCode",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/pinCodeFirst",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/pinCodeVerify",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/user/security/pinCodeUpdata",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/transfer",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
         "path": "project/assets/history",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/addCapital",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/withdraw",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/address",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/checkwithdraw",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/succeedWithdraw",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/bDetails",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/symbol",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/email",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/assets/emailCode",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/wallet/index",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/wallet/tractAccount",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/wallet/capitalAccount",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/wallet/capitalAccountLaw",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/wallet/usAccount",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/wallet/hkAccount",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "project/wallet/search",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "navigationBarTitleText": "Pink Wallet",
    "backgroundColor": "#FF82A3",
    "onReachBottomDistance": 700,
    "navigationBarBackgroundColor": "#fff",
    // 单独设置绿色背景色
    "navigationBarTextStyle": "white",
    // 文字颜色
    "adaptiveFont": true,
    "screenWidth": 750,
    "designWidth": 750,
    // 或其他您设计稿对应的宽度
    "pageOrientation": "auto",
    "pullToRefresh": {
      "backgroundColor": "#fff"
      // 这里替换为你想要的背景颜色
      // 其他可能需要的样式配置...
    }
  },
  // "navigationBarBackgroundColor": "#FFFFFF",
  // "backgroundColor": "#FFFFFF"
  // "tabBar": {
  //   "color": "rgba(216, 182, 98, 0.3)",
  //   "height": "100",
  //   "selectedColor": "#D8B662",
  //   "backgroundColor": "#020200",
  //   "list": [
  //     {
  //       "pagePath": "pagesA/project/C2C/index",
  //       "iconPath": "static/newtabBar/index.png",
  //       "selectedIconPath": "static/newtabBar/index_c.png",
  //       "text": "首页"
  //     },
  //     {
  //       "pagePath": "pagesA/project/C2C/order",
  //       "iconPath": "static/newtabBar/notice.png",
  //       "selectedIconPath": "static/newtabBar/notice_c.png",
  //       "text": "订单"
  //     },
  //     {
  //       "pagePath": "pages/project/C2C/merchant",
  //       "iconPath": "static/newtabBar/personal.png",
  //       "selectedIconPath": "static/newtabBar/personal_c.png",
  //       "text": "我的"
  //     }
  //   ]
  // },
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "",
        "path": "",
        "query": ""
      }
    ]
  }
}
