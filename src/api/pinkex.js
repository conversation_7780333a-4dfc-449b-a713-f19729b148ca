import request from "@/utils/request.js";

const serviceUrl = "/pinkexchangeadmin";

/**
 * 查询币对配置
 */
export const queryCoinConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractSymbolConfig/searchPaged`, data);

/**
 * 更改状态
 */
export const updateStatus = (data) =>
  request.post(
    `${serviceUrl}/adminApi/contractSymbolConfig/changeStatus`,
    data
  );

/**
 * 更改展示状态
 */
export const updateShowStatus = (data) =>
  request.post(
    `${serviceUrl}/adminApi/contractSymbolConfig/changeShowStatus`,
    data
  );

/**
 * 添加或更新币对配置
 */
export const addOrUpdateCoin = (data) =>
  request.post(`${serviceUrl}/adminApi/contractSymbolConfig/addOrUpdate`, data);

// /**
//  * 合约配置查询
//  */
// export const queryContractConfig = (data) =>
//   request.post(`${serviceUrl}/adminApi/contractConfig/search`, data);

// /**
//  * 添加或更新配置
//  */
// export const addOrUpdateContractConfig = (data) =>
//   request.post(`${serviceUrl}/adminApi/contractConfig/addOrUpdateConfig`, data);

/**
 * 修改代理商配置
 */
export const updateAgentConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/rebate/AgentConfig/updateConfig`, data);

/**
 * 查询代理商配置
 */
export const queryAgentConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/rebate/AgentConfig/searchConfig`, data);

/**
 * 添加代理商配置
 */
export const addAgentConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/rebate/AgentConfig/addConfig`, data);

/**
 * 查询代理商申请记录
 */
export const queryAgentApply = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/Application/getAgentApplicationPage`,
    data
  );

/**
 * 代理商申请审批
 */
export const agentApplyApproval = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/Application/agentApplicationApproval`,
    data
  );

/**
 * 查询平台返佣记录
 */
export const queryPlatformRebate = (data) =>
  request.post(`${serviceUrl}/adminApi/rebate/Record/searchRebateRecord`, data);

/**
 * 返佣汇总（分页详细数据）
 */
export const queryRebateSummary = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/Record/getRebateRecordAggregate`,
    data
  );

/**
 * 返佣汇总（全体统计数据）
 */
export const queryRebateSummaryAll = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/Record/getRebateRecordAggregateAll`,
    data
  );

/**
 * 查询邀请链接数据
 */
export const queryInviteLink = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/AgentInvite/searchInviteData`,
    data
  );

/**
 * 查询委托统计信息
 */
export const queryEntrustStatistics = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/EntrustInfo/getEntrustInfo`,
    data
  );

/**
 * 查询出入金统计信息
 */
export const queryInOutStatistics = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/FundInOut/searchFundInOutInfo`,
    data
  );

/**
 * 查询出入金明细
 */
export const queryInOutDetail = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/FundInOut/searchFundInOutDetail`,
    data
  );

/**
 * 合约配置查询
 */
export const queryContractConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/search`, data);

/**
 * 添加或更新配置
 */
export const addOrUpdateContractConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/addOrUpdateConfig`, data);

/**
 * 币对查询配置
 */
export const queryCoinPairConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractSymbolConfig/searchPaged`, data);

/**
 * 添加或更新币对配置
 */
export const addOrUpdateCoinPairConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractSymbolConfig/addOrUpdate`, data);

/**
 * 更改币对状态
 */
export const changeCoinPairStatus = (data) =>
  request.post(
    `${serviceUrl}/adminApi/contractSymbolConfig/changeStatus`,
    data
  );

/**
 * 更改展示状态
 */
export const changeCoinPairShowStatus = (data) =>
  request.post(
    `${serviceUrl}/adminApi/contractSymbolConfig/changeShowStatus`,
    data
  );

/**
 * 止盈止损记录
 */
export const getStopProfitAndLossRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/contractReport/triggerRecord`, data);

/**
 * 成交记录
 */
export const getDealRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/contractReport/tradeRecord`, data);

/**
 * 风控看板
 */
export const getRiskControlBoard = (data) =>
  request.post(`${serviceUrl}/adminApi/contractReport/riskReport`, data);

/**
 * 持仓记录
 */
export const getPositionRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/contractReport/positionRecord`, data);

/**
 * 开仓记录
 */
export const getOpenRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/contractReport/openRecord`, data);

/**
 * 爆仓记录
 */
export const getBustRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/contractReport/explosionRecord`, data);

/**
 * 平仓记录
 */
export const getCloseRecord = (data) =>
  request.post(`${serviceUrl}/adminApi/contractReport/closeRecord`, data);

/**
 * 主力委托
 */
export const getMainEntrust = (data) =>
  request.post(`${serviceUrl}/adminApi/contract/robotDelegating`, data);

/**
 * 撤销主力委托
 */
export const cancelMainEntrust = (data) =>
  request.post(`${serviceUrl}/adminApi/contract/cancelRobotDelegating`, data);

/**
 * 查询简单配置
 */
export const getSimpleConfig = (data) =>
  request.post(
    `${serviceUrl}/adminApi/contractSymbolConfig/searchSimpleList`,
    data
  );

/**
 * 生成代理商账号密码
 */
export const generateAgentAccount = (data) =>
  request.post(
    `${serviceUrl}/adminApi/rebate/AgentLogin/generateAgentAccount`,
    data
  );

/**
 * 查询全部铺单配置
 */
// export const getAllPushConfig = (data) =>
//   request.post(`${serviceUrl}/adminApi/contractConfig/selectAll`, data);

export const getAllPushConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/searchOrderConfig`, data);

/**
 * 更改铺单配置状态
 */
export const updatePushConfigStatus = (data) =>
  request.post(
    `${serviceUrl}/adminApi/contractConfig/updateOrderConfigStatus`,
    data
  );

/**
 * 添加或更新铺单配置
 */
export const addOrUpdatePushConfig = (data) =>
  request.post(
    `${serviceUrl}/adminApi/contractConfig/addOrUpdateOrderConfig`,
    data
  );

/**
 * 修改订单配置
 */
export const updateOrderConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/update`, data);

/**
 * 添加订单配置
 */
export const addOrderConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/save`, data);

/**
 * 删除订单配置
 */
export const deleteOrderConfig = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/delete`, data);

/**
 * 根据合约名称查询铺单配置
 */
export const getOrderConfigByName = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/byName`, data);

/**
 * 根据 symbol、contractName、contractType 搜索合约配置订单
 */
export const getOrderConfigBySymbol = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/selectByThird`, data);

/**
 * 根据 ID 查询
 */
export const getOrderConfigById = (data) =>
  request.post(`${serviceUrl}/adminApi/contractConfig/selectById`, data);
