<template>
	<view class="container">
		<!-- <button class="button" type="primary" @click="toggle('center')"><text class="button-text">居中</text></button> -->
		<uni-popup ref="popup" background-color="#fff" type="center"  :mask-click="false">
		<!-- <uni-popup ref="popup" background-color="#fff" type="center" :mask-click="false"/> -->
			<view class="popup-content">
				<view class="content">
					<h4>版本更新</h4>
					<view class="text_view">
						<view v-for="(item,index) in verdata.description">{{item}}</view>
					</view>
				</view>
				<view class="button" type="default" @click="gouper">
					<text class="button-text">{{uptext}}</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	/**
	 *  版本获取，强更
	 * 使用方法：
	 * 		<fpop ref="fpop"></fpop>
	 * 
	 * import fpop from '@/components/force-updates/force-updates.vue'
	 * 
	 * components: {
			fpop
		},
	 * 	 * 
	 * openuper(){
				console.log('父组件open')	
				this.$refs.fpop.toggle('center')
			},
	 */
	// 	
	export default {
		data() {
			return {
				verdata: {
					currentVersion: '',
					downloadUrl: '',
					updateStatus: 0,
					versionDesc: '',
				},
				htmlte: '',
				uptext: '立即更新',
				iosUrl:""
			}
		},
		created() {
			// #ifdef APP
				this.cuuur()
			// #endif
			
			// this.cuuur() 
			// setTimeout(()=>{
			// 	this.$refs.popup.open()
			// },300)
			// this.toggle()
		},
		// watch: {
		// 	verdata: {
		// 		deep: true,
		// 		handler(nVal, oVal) {
		// 			console.log("a11111--- change", nVal)
		// 		}
		// 	}
		// },

		mounted() {
			
		},
		methods: {
			toggle() {
				this.$refs.popup.open()
			},
			gouper() {
				if(uni.getSystemInfoSync().platform == 'ios'){
					 plus.runtime.launchApplication({
					      action: this.verdata.downloadUrl
					 });
					return false
				}
				let _this = this
				console.log("去下载", _this.verdata.downloadUrl)
				let url = _this.verdata.downloadUrl
				var dtask = plus.downloader.createDownload(url, {},
					function(d, status) {
						console.log(d)
						if (status == 200) {
							plus.runtime.install(plus.io.convertLocalFileSystemURL(d.filename), {}, {}, function(
								error) {
								uni.showToast({
									title: '安装失败',
									mask: false,
									duration: 1500
								});
							})
						} else {
							uni.showToast({
								title: '更新失败',
								mask: false,
								duration: 1500
							});
						}
					});
				try {
					dtask.start(); // 开启下载的任务
					var prg = 0;
					var showLoading = plus.nativeUI.showWaiting("正在下载"); //创建一个showWaiting对象 
					dtask.addEventListener('statechanged', function(
						task,
						status
					) {
						// 给下载任务设置一个监听 并根据状态  做操作
						switch (task.state) {
							case 1:
								_this.uptext = "正在下载"
								// showLoading.setTitle("正在下载");
								break;
							case 2:
								showLoading.setTitle("已连接到服务器");
								break;
							case 3:
								prg = parseInt(
									(parseFloat(task.downloadedSize) /
										parseFloat(task.totalSize)) *
									100
								);
								_this.uptext = prg + "%"
								// showLoading.setTitle("  正在下载" + prg + "%  ");
								break;
							case 4:
								plus.nativeUI.closeWaiting();
								//下载完成
								break;
						}
					});
				} catch (err) {
					plus.nativeUI.closeWaiting();
					uni.showToast({
						title: '更新失败-03',
						mask: false,
						duration: 1500
					});
				}
				// this.$refs.popup.close()
			},
			cuuur() {
				var _this = this
				uni.getSystemInfo({
					success: function(res) {
						let v = res.appVersion
						let type = res.osName === 'ios' ? 'IOS' : 'ANDROID'
						console.log("当前版本", res.appVersion, res.osName)
						if (res) {
							if (type == 'IOS' || type == 'ANDROID') {
								_this.getVersion(v, type)
							}
						} else {
							uni.showToast({
								title: '获取当前版本失败',
								icon: 'none',
								duration: 3000
							});
						}
					}
				})
			},
			async getVersion(v, type) {
				console.log(v)
				let res = await this.$api.getAppReleaseInfo({
					type: type,
					version: v
				});
				console.log(res)
				console.log("版本", res.result)
				console.log(res.result.description.split("\n"))
				res.result.description=res.result.description.split("\n")
				this.verdata = res.result
				if (res.code == 200) {
					if (res.result.forceUpdate) {
						this.$refs.popup.open()
					}
				} else {
					uni.showToast({
						title: res.status.msg,
						icon: 'none',
						duration: 3000
					});
				}
			},
			isMaskClick(){
				return false
			}

		}
	}
</script>

<style lang="scss">
	::v-deep .uni-popup{
		z-index:1000;
	}
	.popup-content {
		width: 520rpx;
		min-height: 600rpx;
		background: #fff;
		border-radius: 16rpx;
		position: relative;
		font-family: PingFang SC;
		.content {
			padding: 26rpx 50rpx 80rpx;
			line-height: 50rpx;
			height: 550rpx;
			overflow-y: auto;
			overflow: hidden;
			.text_view{
				height:300rpx;
				overflow:auto;
			}
			>h4 {
				font-weight: bold;
			}
			>view{
				font-size: 24rpx;
				letter-spacing: 0px;
				line-height: 35rpx;
				color:#6E6E6E;
				margin-bottom:20rpx;
			}
		}

		.button {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 88rpx;
			width: 100%;
			font-size: 32rpx;
			font-weight: 500;
			letter-spacing: 12.8rpx;
			line-height: 42rpx;
			color:#FF82A3;
			border-top:1px solid #DBDBDB;
		}
	}
</style>