<template>
	<view class="area-echart-root">
		<!-- 图表区 -->
		<view class="chart-wrap">
			<l-echart ref="chart"></l-echart>
		</view>
		<!-- 下拉选择区 -->
		<view class="option">
			<view class="dropdown_wrap" style="position: relative;">
				<view class="dropdown_btn max" @click="toggleAssetTypeDropdown" ref="assetBtn">
					<text>{{ assetType.label }}</text>
					<u-icon name="arrow-down" size="22" color="#222"
						:style="{ transform: showAssetTypeDropdown ? 'rotate(180deg)' : 'rotate(0deg)' }" />
				</view>
				<view class="dropdown-option" v-show="showAssetTypeDropdown"
					:style="{ minWidth: assetBtnWidth + 'px' }">
					<view v-for="item in assetTypeList" :key="item.value" class="dropdown-item"
						:class="{ active: assetType.value == item.value }" @click.stop="selectAssetType(item)">
						{{ item.label }}
					</view>
				</view>
			</view>
			<view class="dropdown_wrap" style="margin-left: 32rpx; position: relative;">
				<view class="dropdown_btn" @click="toggleTimeTypeDropdown" ref="timeBtn">
					<text>{{ timeType.label }}</text>
					<u-icon name="arrow-down" size="22" color="#222"
						:style="{ transform: showTimeTypeDropdown ? 'rotate(180deg)' : 'rotate(0deg)' }" />
				</view>
				<view class="dropdown-option" v-show="showTimeTypeDropdown" :style="{ minWidth: timeBtnWidth + 'px' }">
					<view v-for="item in timeTypeList" :key="item.value" class="dropdown-item"
						:class="{ active: timeType.value == item.value }" @click.stop="selectTimeType(item)">
						{{ item.label }}
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import * as echarts from 'echarts';
	export default {
		props: {
			type: {
				type: String,
				default: '1'
			},
			query: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				assetTypeList: [{
					label: '资产总额',
					value: '1'
				},],
				assetType: {
					label: '资产总额',
					value: '1'
				},
				showAssetTypeDropdown: false,
				timeTypeList: [{
					label: '1D',
					value: 'day'
				},
				{
					label: '1W',
					value: 'week'
				},
				{
					label: '1M',
					value: 'month'
				},
				{
					label: '6M',
					value: 'half_year'
				}
				],
				timeType: {
					label: '1D',
					value: 'day'
				},
				showTimeTypeDropdown: false,
				assetBtnWidth: 0,
				timeBtnWidth: 0,
				chartOption: null,
				loading: false,
				assetInfo: null, // 资产信息数据
				option: {
					grid: {
						top: 10,      // 顶部留白，数值越小越贴近
						bottom: 40,   // 底部留白，防止 label 被遮挡
						left: 40,
						right: 20,
						containLabel: true
					},
					xAxis: {
						type: 'category',
						data: [],
						splitLine: { show: false } // 隐藏竖向网格线
					},
					yAxis: {
						type: 'value',
						splitLine: { show: false } // 隐藏横向网格线
					},
					series: [{
						data: [820, 932, 901, 934, 1290, 1330, 1320],
						type: 'line',
						areaStyle: {}
					}],
					dataZoom: [
						{
							type: 'slider',
							show: true,
							start: 0,
							end: 100,
							xAxisIndex: 0,
							height: 24,
							bottom: 0,
							handleSize: '80%',
							handleStyle: {
								color: '#EF88A3'
							},
							textStyle: {
								color: '#999'
							}
						}
					]
				}
			}
		},
		methods: {
			toggleAssetTypeDropdown() {
				this.showAssetTypeDropdown = !this.showAssetTypeDropdown
				this.showTimeTypeDropdown = false
			},
			toggleTimeTypeDropdown() {
				this.showTimeTypeDropdown = !this.showTimeTypeDropdown
				this.showAssetTypeDropdown = false
			},
			selectAssetType(item) {
				this.assetType = item
				this.showAssetTypeDropdown = false
				this.fetchChartData()
			},
			selectTimeType(item) {
				this.timeType = item
				this.showTimeTypeDropdown = false
				this.fetchChartData()
			},
			getBtnWidth() {
				this.$nextTick(() => {
					const assetBtn = this.$refs.assetBtn
					const timeBtn = this.$refs.timeBtn
					if (assetBtn && assetBtn.$el) {
						this.assetBtnWidth = assetBtn.$el.offsetWidth
					}
					if (timeBtn && timeBtn.$el) {
						this.timeBtnWidth = timeBtn.$el.offsetWidth
					}
				})
			},
			async fetchChartData() {
				this.loading = true
				// 这里根据 type 决定接口和参数
				let params = {
					...this.query,
					timeType: this.timeType.value,
					convertCoin:this.query.nowsymbol?this.query.nowsymbol:this.query.convertCoin
				}
				// 假设 this.$api 里有这些方法
				let res = await this.$api.assetRecordList(params)
				console.log(res.result)
				this.option = this.formatChartOption(res.result)
				this.init()
			},
			formatChartOption(dataArr) {
				const x = dataArr.map(item => this.formatXAxisLabel(item.recordAt))
				const y = dataArr.map(item => item.amount)
				console.log(x)
				return {
					grid: {
						left: '3%',
						right: '4%',
						bottom: '3%',
						top: 20, // 顶部距离设为0
						containLabel: true
					},
					xAxis: {
						type: 'category',
						data: x,
						splitLine: { show: false } // 隐藏竖向网格线
					},
					yAxis: {
						type: 'value',
						splitLine: { show: false } // 隐藏横向网格线
					},
					series: [{
						data: y,
						type: 'line',
						smooth: false,
						showSymbol: false,
						lineStyle: { color: '#EF88A3', width: 2 },
						itemStyle: { color: '#EF88A3' },
						areaStyle: {
							color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
								{ offset: 0, color: 'rgba(239,136,163,0.3)' },
								{ offset: 1, color: 'rgba(239,136,163,0)' }
							])
						}
					}],
					dataZoom: [
						{
							type: 'slider',
							show: true,
							start: 0,
							end: 100,
							xAxisIndex: 0,
							height: 24,
							bottom: 0,
							handleSize: '80%',
							handleStyle: {
								color: '#EF88A3'
							},
							textStyle: {
								color: '#999'
							}
						}
					]
				}
			},
			init() {
				this.$refs.chart.init(echarts, chart => {
					chart.setOption(this.option);
				});
			},
			formatXAxisLabel(recordAt) {
				const date = new Date(recordAt * 1000)
				const m = (date.getMonth() + 1).toString().padStart(2, '0')
				const d = date.getDate().toString().padStart(2, '0')
				const h = date.getHours().toString().padStart(2, '0')
				const min = date.getMinutes().toString().padStart(2, '0')
				if (this.timeType.label === '1D') {
					return `${h}:${min}`
				} else if (this.timeType.label === '1W') {
					return `${m}-${d} ${h}:${min}`
				} else if (this.timeType.label === '1M' || this.timeType.label === '6M') {
					return `${m}-${d}`
				}
				return ''
			}
		},
		watch: {
			type: {
				handler() {
					this.fetchChartData()
				},
				immediate: true
			},
			'query.convertCoin': {
				handler() {
					console.log(111);
					this.fetchChartData()
				},
				deep: true
			}
		},
		mounted() {
			this.getBtnWidth()
		}
	}
</script>

<style lang="scss" scoped>
	.area-echart-root {
		.option {
			display: flex;
			align-items: center;
			margin: 0;
			position: relative;
			margin-top:20rpx;
			.dropdown_wrap {
				position: relative;
			}

			.dropdown_btn {
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: #f7f7f7;
				border-radius: 18rpx;
				padding: 0 32rpx;
				height: 64rpx;
				font-size: 22rpx;
				color: #121212;
				font-weight: 500;
				cursor: pointer;
				width: 152rpx;

				&.max {
					width: 214rpx;
				}
			}
		}

		.dropdown-option {
			position: absolute;
			left: 0;
			top: 60rpx;
			background: #fff;
			box-shadow: 0 4rpx 24rpx 0 #00000010;
			border-radius: 16rpx;
			padding: 8rpx 0;
			z-index: 99;
			min-width: 100%;
			width: max-content;

			.dropdown-item {
				padding: 18rpx 32rpx;
				cursor: pointer;
				transition: background 0.2s;
				font-size: 22rpx;
				color: #121212;

				&:hover {
					background: #f2f2f2;
				}

				&.active {
					color: #008E28;
					font-weight: bold;
				}
			}
		}

		.chart-wrap {
			height: 320rpx;
		}

		.asset-info {
			margin-top: 32rpx;
			font-size: 28rpx;
			color: #222;
			background: #f7f7f7;
			border-radius: 18rpx;
			padding: 32rpx;
		}
	}
</style>