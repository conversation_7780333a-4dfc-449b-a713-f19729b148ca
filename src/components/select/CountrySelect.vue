<template>
  <u-popup v-model="show" mode="bottom" border-radius="14" height="1500rpx">
    <view class="country_select_popup">
      <!-- 头部 -->
      <view class="country_select_header">
        <text class="country_select_title">国家/地区</text>
        <u-icon name="close" class="country_select_close" @click="close" size="40"></u-icon>
      </view>
      <view class="search_box_">
        <view class="search_inner_">
          <image class="search_icon_" src="https://pro-oss.pinkwallet.com/image/1385297482358546432.png" />
          <input @input="onSearch" v-model="keyword" class="search_input_" placeholder="搜索"
            placeholder-style="color:#bdbdbd;font-size:30rpx;" type="text" />
        </view>
      </view>
      <!-- 列表 -->
      <view class="country_select_list">
        <view v-for="item in displayList" :key="item.value" class="country_select_item" @click="selectCountry(item)">
          <image class="country_flag" :src="item.icon" mode="aspectFill" />
          <text class="country_name">{{ item.showLabel }}</text>
          <view v-if="item.value === value" class="country_dot"></view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
  export default {
    name: 'CountrySelect',
    props: {
      show: Boolean,
      value: [String, Number],
      type: String,
    },
    data() {
      return {
        keyword: '',
        language: 'zh', // 默认简体
        list: [{
          "label": "澳大利亚",
          "en_label": "Australia",
          "zhhant_label": "澳大利亞",
          "value":1,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/australia.png"
        },
        {
          "label": "美国",
          "en_label": "United States",
          "zhhant_label": "美國",
          "value":2,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/usa.png"
        },
        {
          "label": "中国",
          "en_label": "China",
          "zhhant_label": "中國",
          "value":3,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/china.png"
        },
        {
          "label": "加拿大",
          "en_label": "Canada",
          "zhhant_label": "加拿大",
          "value":4,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/canada.png"
        },
        {
          "label": "尼日利亚",
          "en_label": "Nigeria",
          "zhhant_label": "尼日利亞",
          "value":5,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/nigeria.png"
        },
        {
          "label": "爱尔兰",
          "en_label": "Ireland",
          "zhhant_label": "愛爾蘭",
          "value":6,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/ireland.png"
        },
        {
          "label": "意大利",
          "en_label": "Italy",
          "zhhant_label": "義大利",
          "value":7,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/italy.png"
        },
        {
          "label": "日本",
          "en_label": "Japan",
          "zhhant_label": "日本",
          "value":8,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/japan.png"
        },
        {
          "label": "英国",
          "en_label": "United Kingdom",
          "zhhant_label": "英國",
          "value":9,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/uk.png"
        },
        {
          "label": "韩国",
          "en_label": "South Korea",
          "zhhant_label": "韓國",
          "value":10,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/korea.png"
        },
        {
          "label": "德国",
          "en_label": "Germany",
          "zhhant_label": "德國",
          "value":11,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/germany.png"
        },
        {
          "label": "希腊",
          "en_label": "Greece",
          "zhhant_label": "希臘",
          "value":12,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/greece.png"
        },
        {
          "label": "塞浦路斯",
          "en_label": "Cyprus",
          "zhhant_label": "塞浦路斯",
          "value":13,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/cyprus.png"
        },
        {
          "label": "印度",
          "en_label": "India",
          "zhhant_label": "印度",
          "value":14,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/india.png"
        },
        {
          "label": "泰国",
          "en_label": "Thailand",
          "zhhant_label": "泰國",
          "value":15,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/thailand.png"
        },
        {
          "label": "捷克",
          "en_label": "Czech Republic",
          "zhhant_label": "捷克",
          "value":16,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/czech.png"
        },
        {
          "label": "丹麦",
          "en_label": "Denmark",
          "zhhant_label": "丹麥",
          "value":17,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/denmark.png"
        },
        {
          "label": "爱沙尼亚",
          "en_label": "Estonia",
          "zhhant_label": "愛沙尼亞",
          "value":18,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/estonia.png"
        },
        {
          "label": "芬兰",
          "en_label": "Finland",
          "zhhant_label": "芬蘭",
          "value":19,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/finland.png"
        },
        {
          "label": "法国",
          "en_label": "France",
          "zhhant_label": "法國",
          "value":20,
          "icon": "https://pro-oss.pinkwallet.com/image/pc/icon/france.png"
        }
        ],
        filteredList: [],
      }
    },
    created() {
      // 读取本地缓存的语言
      let lang = ''
      try {
        // uniapp 推荐用 uni.getStorageSync
        lang = uni.getStorageSync('__language__')
      } catch (e) {
        // 兼容 web
        lang = localStorage.getItem('__language__') || 'zh'
      }
      if (lang) this.language = lang
      this.onSearch()
    },
    watch: {
      list: {
        immediate: true,
        handler(val) {
          this.filteredList = val || []
        }
      },
      keyword() {
        this.onSearch()
      }
    },
    computed: {
      displayList() {
        // 根据语言切换展示字段
        return this.filteredList.map(item => {
          let showLabel = item.label
          if (this.language === 'en') showLabel = item.en_label
          else if (this.language === 'zhhant') showLabel = item.zhhant_label
          return {
            ...item,
            showLabel
          }
        })
      }
    },
    methods: {
      selectCountry(item) {
        // 根据当前语言返回对应的 label
        let label = item.label
        if (this.language === 'en') label = item.en_label
        else if (this.language === 'zhhant') label = item.zhhant_label
        this.$emit('select', { ...item, label }, this.type)
      },
      onSearch() {
        const kw = this.keyword.trim().toLowerCase()
        if (!kw) {
          this.filteredList = this.list
        } else {
          // 搜索时也根据当前语言字段搜索
          let searchField = 'label'
          if (this.language === 'en') searchField = 'en_label'
          else if (this.language === 'zhhant') searchField = 'zhhant_label'
          this.filteredList = this.list.filter(item =>
            (item[searchField] || '').toLowerCase().includes(kw)
          )
        }
      },
      close() {
        this.$emit('update:show', false)
      }
    },
  }
</script>

<style lang="scss" scoped>
  .search_box_ {
    padding: 24rpx 24rpx 0 24rpx;
    margin-top: 40rpx;
  }

  .search_inner_ {
    display: flex;
    align-items: center;
    background: #f7f7f7;
    border-radius: 32rpx;
    height: 74rpx;
    padding: 0 24rpx;
  }

  .search_icon_ {
    width: 52rpx;
    height: 52rpx;
    margin-right: 16rpx;
  }

  .search_input_ {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 30rpx;
    color: #111;
    height: 74rpx;
    line-height: 74rpx;
  }

  .country_select_mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.05);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  .country_select_popup {
    background: #fff;
    width: 100vw;
    border-radius: 0 0 32rpx 32rpx;
    margin-top: 0;
  }

  .country_select_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 0 32rpx;
  }

  .country_select_title {
    font-size: 32rpx;
    font-weight: 600;
  }

  .country_select_close {
    color: #999;
  }

  .country_select_list {
    margin-top: 32rpx;
  }

  .country_select_item {
    display: flex;
    align-items: center;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    position: relative;
    margin-bottom: 40rpx;
  }

  .country_flag {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    margin-right: 24rpx;
    background: #f5f5f5;
  }

  .country_name {
    font-size: 28rpx;
    color: #222;
  }

  .country_dot {
    width: 18rpx;
    height: 18rpx;
    border-radius: 50%;
    background: #F97C9A;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    margin-left: -36rpx;
  }
</style>