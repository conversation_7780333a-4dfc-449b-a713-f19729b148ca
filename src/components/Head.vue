<template>
    <div class="head" :class="{ 'animate-in': isVisible }">
        <div class="left">
            <img @click="nav_to('home')"
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250321/4fa4903b6172115e5f0d1a2bb33da0e6_712x94.png" />
            <div class="menu">
                <div class="item-text" style="line-height: 1.2;margin-top:-2px;position:relative;"
                    @mouseenter="openPopup('remit')" @mouseleave="onMenuMouseLeave" @click="openPopup('remit')"
                    ref="remitBtn">
                    {{ $t("nav.remittance") }}
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378033616633290752.png"
                        alt="" srcset="">
                </div>
                <div class="item-text" @click="nav_to('stock')" @mouseenter="openPopup(); isHovered2 = true"
                    @mouseleave="isHovered2 = false">
                    <!-- style="margin-top: 13px;" -->
                    <div class="text-wrapper">
                        <!-- 第一行文字 -->
                        <span v-for="(letter, index) in text2" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered2, 'reset-up': !isHovered2 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                    <div class="text-wrapper">
                        <!-- 第一行文字 -->
                        <span v-for="(letter, index) in text2" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered2, 'reset-up': !isHovered2 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                </div>
                <div class="item-text" style="line-height: 1.2;margin-top:-2px;position:relative;"
                    @mouseenter="openPopup('crypto')" @mouseleave="onMenuMouseLeave" @click="openPopup('crypto')"
                    ref="cryptoBtn">
                    {{ $t("nav.crypto") }}
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378033616633290752.png"
                        alt="" srcset="">
                </div>

                <div class="item-text" @click="nav_to('pinkCard')" @mouseenter="isHovered3 = true; openPopup()"
                    @mouseleave="isHovered3 = false">
                    <!-- style="margin-top: 13px;" -->
                    <div class="text-wrapper">
                        <!-- 第一行文字 -->
                        <span v-for="(letter, index) in text3" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered3, 'reset-up': !isHovered3 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                    <div class="text-wrapper">
                        <!-- 第一行文字 -->
                        <span v-for="(letter, index) in text3" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered3, 'reset-up': !isHovered3 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                </div>
                <div class="item-text" style="line-height: 1.2;margin-top:-2px;position:relative;"
                    @mouseenter="openPopup('square')" @mouseleave="onMenuMouseLeave" @click="openPopup('square')"
                    ref="squareBtn">
                    {{ $t("nav.square") }}
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378033616633290752.png"
                        alt="" srcset="">
                </div>
                <div class="item-text" style="line-height: 1.2;margin-top:-2px;position:relative;"
                    @mouseenter="openPopup('more')" @mouseleave="onMenuMouseLeave" @click="openPopup('more')"
                    ref="moreBtn">
                    {{ $t("nav.more") }}
                    <img src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/1378033616633290752.png"
                        alt="" srcset="">
                </div>
                <!-- <div class="item-text" @click="nav_to('home')" @mouseenter="isHovered = true"
                    @mouseleave="isHovered = false">
                    <div class="text-wrapper">
                        <span v-for="(letter, index) in text" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered, 'reset-up': !isHovered }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                    <div class="text-wrapper">
                        <span v-for="(letter, index) in text" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered, 'reset-up': !isHovered }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                </div> -->

                <!-- <div @mouseenter="isHovered2 = true" @mouseleave="isHovered2 = false">
                    <div class="text-wrapper" style="margin-top: 13px;">
                        <span v-for="(letter, index) in text2" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered2, 'reset-up': !isHovered2 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                    <div class="text-wrapper">
                        <span v-for="(letter, index) in text2" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered2, 'reset-up': !isHovered2 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                </div>
                <div @mouseenter="isHovered3 = true" @mouseleave="isHovered3 = false">
                    <div class="text-wrapper" style="margin-top: 13px;">
                        <span v-for="(letter, index) in text3" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered3, 'reset-up': !isHovered3 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                    <div class="text-wrapper">
                        <span v-for="(letter, index) in text3" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered3, 'reset-up': !isHovered3 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}  
                        </span>
                    </div>
                </div> -->
                <!-- <div class="item-text" @click="nav_to('faq')" @mouseenter="isHovered4 = true"
                    @mouseleave="isHovered4 = false">
                    <div class="text-wrapper">
                        <span v-for="(letter, index) in text4" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered4, 'reset-up': !isHovered4 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                    <div class="text-wrapper">
                        <span v-for="(letter, index) in text4" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered4, 'reset-up': !isHovered4 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                </div>
                <div class="item-text" @click="nav_to('broker')" @mouseenter="isHovered5 = true"
                    @mouseleave="isHovered5 = false">-->
                <!-- style="margin-top: 13px;"  -->
                <div class="item-text" @click="nav_to('broker')" @mouseenter="isHovered5 = true; openPopup()"
                    @mouseleave="isHovered5 = false">
                    <div class="text-wrapper">
                        <!-- 第一行文字 -->
                        <span v-for="(letter, index) in text5" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered5, 'reset-up': !isHovered5 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                    <div class="text-wrapper">
                        <!-- 第一行文字 -->
                        <span v-for="(letter, index) in text5" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered5, 'reset-up': !isHovered5 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                </div>
                <div class="item-text" @click="nav_to('faq')" @mouseenter="isHovered4 = true; openPopup()"
                    @mouseleave="isHovered4 = false">
                    <div class="text-wrapper">
                        <span v-for="(letter, index) in text4" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered4, 'reset-up': !isHovered4 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                    <div class="text-wrapper">
                        <span v-for="(letter, index) in text4" :key="'t1-' + index" class="letter"
                            :class="{ 'up': isHovered4, 'reset-up': !isHovered4 }"
                            :style="{ transitionDelay: `${index * 50}ms` }">
                            {{ letter }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="right">
            <div class="signup" @click="nav_to('Register')">{{ $t("title.register") }}</div>
            <div class="login" @click="nav_to('Login')">{{ $t("title.login") }}</div>
            <img @click.stop="toggleLanguageMenu"
                src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250327/43db3658f4dab44f4be2a44d86a10c0d_96x96.png"
                alt="">
        </div>
    </div>
    <!-- 语言弹窗  :class="{ selected: selectedLanguage === language.value }"-->
    <transition name="fade">
        <div v-if="isMenuVisible" class="language-menu" @click.stop ref="languageMenu">

            <div class="items">
                {{ $t("title.lang") }}
            </div>
            <div v-for="(language, index) in languages" :key="index" class="item" @click="selectLanguage(language)">
                {{ language.label }}
                <img v-if="language.value == lang"
                    src="https://pinkwallet-pro.oss-ap-southeast-1.aliyuncs.com/image/20250327/046a950e72e553c6f91489c57eb7d08b_64x64.png" />
            </div>
        </div>
    </transition>
    <transition name="fade">
        <div class="select_div" v-if="showPopup" @mouseenter="onPopupMouseEnter" @mouseleave="onPopupMouseLeave"
            ref="popupDiv" :style="{ left: popupLeft + 'px' }">
            <!-- 汇款弹窗内容 -->
            <template v-if="activePopup === 'remit'">
                <div class="item" @click="nav_to('RealTimeER'), closePopup">{{
                    $t("nav.remittance_submenu_exchange_rate") }}</div>
                <div class="item" @click="nav_to('Remittance'), closePopup">{{
                    $t("nav.remittance_submenu_quick_transfer") }}</div>
            </template>
            <!-- 数字货币弹窗内容 -->
            <template v-else-if="activePopup === 'crypto'">
                <div class="item" @click="nav_to('flashExchange'), closePopup">{{
                    $t("nav.crypto_submenu_flash_exchange") }}</div>
                <div class="item" @click="nav_to(''), closePopup">{{ $t("nav.crypto_submenu_price_query") }}</div>
            </template>
            <!-- 广场弹窗内容 -->
            <template v-else-if="activePopup === 'square'">
                <!-- <div class="item">快讯</div> -->
                <div class="item" @click="nav_to('college'), closePopup">{{ $t("nav.square_submenu_college") }}</div>
            </template>
            <!-- 更多弹窗内容 -->
            <template v-else-if="activePopup === 'more'">
                <div class="item" @click="nav_to('help'), closePopup">{{ $t("nav.more_submenu_help_center") }}</div>
                <div class="item" @click="nav_to('iBan'), closePopup">{{ $t("nav.more_submenu_iban_search") }}</div>
            </template>
        </div>
    </transition>
</template>

<script setup>
import { ref, onMounted, computed, onBeforeUnmount, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const { locale } = useI18n();
const { t } = useI18n();

const isVisible = ref(false);
const isHovered = ref(false);
const isHovered2 = ref(false);
const isHovered3 = ref(false);
const isHovered4 = ref(false);
const isHovered5 = ref(false);
const text5 = computed(() =>
    (t("nav.earn").split(""))
)
const text4 = computed(() =>
    (t("nav.faq").split(""))
)
const text3 = computed(() => ('PinkWallet Card'.split("")))
const text2 = computed(() => (t("nav.stock").split("")))
const text = computed(() => (t("nav.home").split(""))); // 需要动画的文字
const lang = ref(localStorage.getItem('lang'))
const showPopup = ref(false)
const activePopup = ref('') // 'remit' | 'crypto' | 'square' | 'more' | ''
const isMenuVisible = ref(false); // 控制语言菜单的显示与隐藏
const selectedLanguage = ref("English"); // 当前选中的语言
const languages = ref([{
    label: "中文",
    value: 'zh'
}, {
    label: "English",
    value: 'en'
}]); // 支持的语言

const languageMenu = ref(null);
const remitBtn = ref(null);
const cryptoBtn = ref(null);
const popupDiv = ref(null);
const isInMenuOrPopup = ref(false);
const popupLeft = ref(0);
const squareBtn = ref(null)
const moreBtn = ref(null)

const nav_to = (e) => {
    if (e == 'faq') {
        window.open("https://pinkwallet.zendesk.com/hc/zh-sg")
        return
    }
    if (e == 'broker') {
        let token = localStorage.getItem('token')
        if (token) {
            router.push({
                path: 'broker-data',
            })
        } else {
            router.push({
                path: e,
            })
        }
        return
    }
    router.push({
        path: e,
        // query: {
        //     title: '666'
        // }
    })
}


// 切换语言菜单显示/隐藏
const toggleLanguageMenu = () => {
    lang.value = localStorage.getItem('lang')

    // 如果菜单已经可见，点击时关闭菜单；如果不可见，点击时打开菜单
    isMenuVisible.value = !isMenuVisible.value;
};

// 选择语言
const selectLanguage = (language) => {
    locale.value = language.value;
    localStorage.setItem("lang", language.value); // 记住用户选择的语言

    selectedLanguage.value = language.value;
    isMenuVisible.value = false; // 选择语言后关闭菜单
};

// 点击外部时关闭菜单
const closeMenuOnClickOutside = (event) => {
    console.log(123);

    if (languageMenu.value && !languageMenu.value.contains(event.target)) {
        isMenuVisible.value = false;
    }
};

function openPopup(type) {
    if (!type) {
        showPopup.value = false;
        return
    }
    if (showPopup.value != type) {
        showPopup.value = false
    }
    // showPopup.value = false
    activePopup.value = type
    showPopup.value = true
    isInMenuOrPopup.value = true

    // 计算弹窗left
    nextTick(() => {
        let btn = null;
        if (type === 'remit') btn = remitBtn.value;
        if (type === 'crypto') btn = cryptoBtn.value;
        if (type === 'square') btn = squareBtn.value;
        if (type === 'more') btn = moreBtn.value;
        if (btn) {
            const menu = btn.parentNode;
            const btnRect = btn.getBoundingClientRect();
            popupLeft.value = btnRect.left;
        }
    });
}
function onMenuMouseLeave() {
    setTimeout(() => {
        if (!isInMenuOrPopup.value) {
            showPopup.value = false
            activePopup.value = ''
        }
    }, 100)
}

function onPopupMouseEnter() {
    isInMenuOrPopup.value = true
}
function onPopupMouseLeave() {
    isInMenuOrPopup.value = false
    setTimeout(() => {
        if (!isInMenuOrPopup.value) {
            showPopup.value = false
            activePopup.value = ''
        }
    }, 100)
}

// 点击页面其他地方关闭弹窗
function handleClickOutside(event) {
    if (
        remitBtn.value && !remitBtn.value.contains(event.target) &&
        cryptoBtn.value && !cryptoBtn.value.contains(event.target) &&
        squareBtn.value && !squareBtn.value.contains(event.target) &&
        moreBtn.value && !moreBtn.value.contains(event.target) &&
        popupDiv.value && !popupDiv.value.contains(event.target)
    ) {
        showPopup.value = false
        activePopup.value = ''
    }
}

function closePopup() {
    showPopup.value = false
    activePopup.value = ''
}

onMounted(() => {
    document.addEventListener("click", closeMenuOnClickOutside);
    document.addEventListener("click", handleClickOutside);

    setTimeout(() => {
        isVisible.value = true;
    }, 800);
});

onBeforeUnmount(() => {
    document.removeEventListener("click", closeMenuOnClickOutside);
    document.removeEventListener("click", handleClickOutside);
});

</script>

<style lang="scss" scoped>
.language-menu {
    position: absolute;
    top: 79px;
    right: 48px;
    /* width: 200px; */
    border-radius: 8px;
    z-index: 10;
    animation: slide-in 0.8s ease-out;
    background: #1C1D1F;
    transition: all .8s;

    .items {
        width: 221px;
        height: 39px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all .8s;
        padding-left: 14px;
        font-family: MiSans;
        font-weight: 400;
        font-size: 14px;
        line-height: 137%;
        letter-spacing: 0px;
        text-transform: capitalize;
        color: rgba(255, 255, 255, .6);
    }

    .item {
        width: 221px;
        height: 39px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        transition: all .8s;
        padding: 0 17px 0 14px;
        font-family: MiSans;
        font-weight: 600;
        font-size: 14px;
        line-height: 137%;
        letter-spacing: 0px;
        text-transform: capitalize;
        color: #fff;

        img {
            width: 16px;
            height: 16px;
        }

        &:hover {
            &:last-child {
                border-bottom-right-radius: 8px;
                border-bottom-left-radius: 8px;
            }

            background-color: rgba(255, 255, 255, .1);
        }
    }
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

@keyframes slide-in {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.text-container {
    /* display: inline-block; */
    position: relative;
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
}

.text-wrapper {
    /* overflow: hidden; */
    transition: max-height 0.8s ease;
    white-space: nowrap;

    &:nth-child(2) {
        margin-top: 10px;
        /* 第二个 text-wrapper */
        max-height: 0;
        /* 默认隐藏 */
    }

    &:nth-child(1) {
        max-height: 10px;
        /* 控制一开始只显示一行 */
    }
}

.second-line {
    transform: translateY(100%);
    /* 初始状态：隐藏在下方 */
}

.letter {
    display: inline-block;
    line-height: 16px;
    overflow: hidden;
    /* transition: transform 0.8s ease-out, opacity 0.8s ease-in-out; */
    transition: all 0.8s ease-in-out;
}

/* Hover 时第一行上跳并消失 */
.up {
    transform: translateY(-20px);
    /* opacity: 0; */
    color: #fff;
}

/* Hover 结束，第一行回到原始位置 */
.reset-up {
    transform: translateY(0);
    opacity: 1;
}

/* Hover 时第二行从下方替换上来 */
.down {
    transform: translateY(0);
    opacity: 1;
}

/* Hover 结束，第二行回到底部隐藏 */
.reset-down {
    transform: translateY(20px);
    /* opacity: 0; */
}

/* 初始状态，第二行往下隐藏 */
.second-line .letter {
    transform: translateY(20px);
    /* opacity: 0; */
}

// &:hover {
//     // &:nth-child(2) {
//     // 第二个 text-wrapper
//     // max-height: 12px; // hover 时展开第二行
//     // }
// }

.head {
    /* width: 100%; */
    /* max-width: 100vw; */
    /* min-width: 1920px; */
    /* width: 1440px; */
    height: 66px;
    background: #161616;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 48px 0 45px;
    /* transition: transform 0.8s ease-in-out; */
    opacity: 0;
    margin-top: -60px;
    transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;

    &.animate-in {
        transform: translateY(60px);
        /* 进入动画 */
        opacity: 1;
        /* 透明度变为 100% */
    }

    .right {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;

        .signup {
            width: 85px;
            height: 40px;
            border-radius: 25px;
            /* background: rgba(239, 136, 163, 0.2); */
            border: 1px solid rgba(239, 136, 163, 1);
            font-family: HarmonyOS Sans;
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0px;
            color: #EF88A3;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: .8s all;

            &:hover {
                background: rgba(239, 136, 163, 0.2);
            }
        }

        .login {
            width: 75px;
            height: 40px;
            border-radius: 25px;
            background: #EF88A3;
            font-family: HarmonyOS Sans;
            font-weight: 500;
            font-size: 14px;
            color: #161616;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: .8s all;

            &:hover {
                background: rgba(185, 100, 120, 1);
                color: rgba(255, 255, 255, 1);
            }
        }

        img {
            margin-left: 6px;
            width: 24px;
            height: 24px;
        }
    }

    .left {
        display: flex;
        align-items: center;
        margin-left: 40px;
        // height: 28px;
        // height: 17px;
        height: fit-content;
        overflow: hidden;
        margin-top: 6px;

        img {
            width: 178px;
            cursor: pointer;
            height: 23px;
        }

        // .item-text {
        //     font-family: MiSans-medium;
        //     font-weight: 400;
        //     font-size: 16px;
        //     // line-height: 16px;
        //     // letter-spacing: 0px;
        //     color: rgba(255, 255, 255, .5);
        //     cursor: pointer;
        //     // height: 20px;
        //     line-height: 0.8;
        //     // display: flex;
        //     // flex-direction: column;
        //     // align-items: flex-start;
        //     // margin-top: 3px;



        .menu {
            position: relative;
            gap: 40px;
            display: flex;
            align-items: center;
            margin-left: 40px;
            /* height: 28px; */
            /* height: 17px; */
            height: fit-content;
            overflow: hidden;
            margin-top: 6px;

            .item-text {
                font-family: MiSans-medium;
                font-weight: 400;
                font-size: 16px;
                /* line-height: 16px; */
                /* letter-spacing: 0px; */
                color: rgba(255, 255, 255, .5);
                cursor: pointer;
                /* height: 20px; */
                line-height: 0.8;
                /* display: flex; */
                /* flex-direction: column; */
                /* align-items: flex-start; */
                /* margin-top: 3px; */
                /* overflow: hidden; */
                /* display: flex; */
                /* align-items: center; */
                /* justify-content: center; */
                /* flex-direction: column; */

                &:hover {
                    color: #fff;
                    transition: 0.8s ease-in-out;
                }

                img {
                    width: 11px;
                    height: 11px;
                }
            }
        }

        // }
    }


}

.select_div {
    position: absolute;
    top: 70px;
    left: 0;
    border-radius: 8px;
    z-index: 99;
    animation: slide-in 0.8s ease-out;
    background: #1C1D1F;
    transition: all .5s;

    .item {
        width: 221px;
        height: 39px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        padding: 8px 17px 8px 14px;
        font-family: MiSans;
        font-weight: 600;
        font-size: 14px;
        line-height: 137%;
        letter-spacing: 0px;
        text-transform: capitalize;
        color: #fff;
        border-bottom: 1px solid rgba(51, 52, 53, 1);

        img {
            width: 16px;
            height: 16px;
        }

        &:hover {
            &:last-child {
                border-bottom-right-radius: 8px;
                border-bottom-left-radius: 8px;
            }

            background-color: rgba(255, 255, 255, .1);
        }
    }

    .item:last-child {
        border-bottom: none;
    }
}
</style>