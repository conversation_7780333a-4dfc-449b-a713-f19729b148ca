<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
    <div class="root-container">
        <!-- Hero Section -->
        <div class="container">

            <!-- Download Section -->
            <div class="download-section">
                <div class="download-content" :class="{ 'animate-in': isVisible }">
                    <div class="title">终极全球金融资产钱包</div>
                    <div class="niu">无缝链接，智能便捷，突破界限</div>
                    <p class="subtitle">2000+币种闪电兑换，3分钟极简开户享最优汇率，跨境资产自由流动如虎添翼</p>

                    <div class="inputs">
                        <div class="input">
                            <div class="prefix">
                                <span>+1</span>
                                <div class="shu"></div>
                                <input type="text">
                            </div>
                        </div>
                        <div class="login">Sign Up / Login</div>
                    </div>
                    <div class="store-buttons">
                        <button class="store-button !rounded-button" @mouseenter="hovered = 'appstore'"
                            @mouseleave="hovered = ''">
                            <img :src="hovered === 'appstore'
                                ? 'https://pro-oss.pinkwallet.com/image/20250324/7a909be8fc9acf2501128098e0b213fc_95x114.png'
                                : 'https://pro-oss.pinkwallet.com/image/20250321/bef50247b4acf6ba44f795f38ccfc81a_124x128.png'
                                " />
                            <div>
                                <span>Download on the</span>
                                <span>App Store</span>
                            </div>
                        </button>
                        <button class="store-button !rounded-button" @mouseenter="hovered = 'googleplay'"
                            @mouseleave="hovered = ''">
                            <img :src="hovered === 'googleplay'
                                ? 'https://pro-oss.pinkwallet.com/image/20250324/b11ff143f668e2b79d0899aa7b7fbdf4_124x128.png'
                                : 'https://pro-oss.pinkwallet.com/image/20250321/deff5942b1f51393727b5c6c278b83c7_124x128.png'
                                " />
                            <div>
                                <span>get on</span>
                                <span>Google play</span>
                            </div>
                        </button>
                    </div>
                </div>
                <div class="preview-image" :class="{ 'animate-in': isVisible }">
                    <img :src="heroImage" alt="App Preview">
                </div>
            </div>

            <!-- Features Section -->
            <div class="features-grid">
                <div v-for="(feature, index) in features" :key="index" class="feature-card"
                    :class="{ 'fade-in': visibleItems[index] }">
                    <img :src="feature.icon" class="icon" />
                    <div>
                        <span>{{ feature.title }}</span>
                        <span>{{ feature.description }}</span>
                    </div>

                </div>
            </div>

            <div class="title-container">
                <h2 class="title">操作流程</h2>
            </div>


            <!-- Business Process -->
            <div class="process-grid">
                <div v-for="(process, index) in businessProcess" :key="index" class="process-card"
                    :class="{ 'fade-in': functionItems[index] }">
                    <div class="process-header">
                        <img :src="process.icon" class="icon"
                            :style="{ height: process.h + 'px', width: process.w + 'px' }" />
                        <div>
                            <span>{{ process.title }}</span>
                            <span>{{ process.description }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="animated-text" :style="computedStyle">
                Scroll Effect
            </div>
            <!-- <div class="business-scope">
                <div class="card" :style="{ width: Width1 }" @mouseenter="handleHover(0)" @mouseleave="resetHover">
                    <div class="card-content">
                        <span>法币兑法币</span>
                        <p>50+法币自由兑换</p>
                    </div>
                    <img class="icon" v-if="activeIndex == 0 || activeIndex == null"
                        src="https://pro-oss.pinkwallet.com/image/20250321/dc5151bb73003a8f8b8fbe85afe5b3b9_589x470.png"
                        alt="icon" />
                </div>

                <div class="card" style="margin-left: 8px;" :style="{ width: Width2 }" @mouseenter="handleHover(1)"
                    @mouseleave="resetHover">
                    <div class="card-content">
                        <span>法币兑加密货币</span>
                        <p>50+法币与2000+加密货币自由兑换</p>
                    </div>
                    <img class="icon" v-if="activeIndex == 1"
                        src="https://pro-oss.pinkwallet.com/image/20250321/b43b58c270c9a432a5db6a99772ead8e_578x481.png"
                        alt="icon" />
                </div>
            </div> -->

            <!-- <div style="height: 8px;"></div>
            <div class="business-scope">

                <div class="card" :style="{ width: Width3 }" @mouseenter="handleHover2(0)" @mouseleave="resetHover2">
                    <div class="card-content">
                        <span>加密货币兑加密货币</span>
                        <p>2000+加密货币自由兑换</p>
                    </div>
                    <img class="icon" v-if="activeIndex2 == 1"
                        src="https://pro-oss.pinkwallet.com/image/20250321/f2e1474f2cf71f68dd92d4612acfb208_584x500.png"
                        alt="icon" />
                </div>

                <div class="card" style="margin-left: 8px;" :style="{ width: Width4 }" @mouseenter="handleHover2(1)"
                    @mouseleave="resetHover2">
                    <div class="card-content">
                        <span>加密货币直接消费</span>
                        <p>免费申请Visa国际卡，支持Visa全渠道消费场景</p>
                    </div>
                    <img class="icon" v-if="activeIndex2 == 0 || activeIndex2 == null"
                        src="https://pro-oss.pinkwallet.com/image/20250321/bbdfc5160e69380fb11397f6a699bc26_587x469.png"
                        alt="icon" />
                </div>
            </div> -->


            <!-- <div class="title-container" style="margin: 139px 0 47px 0;">
                <h2 class="title">产品功能</h2>
            </div>

            <div class="scroll-wrapper" ref="scrollContainer">
                <div class="shadow"></div>
                <div class="scroll-content" ref="scrollContent">
                    <div v-for="(item, index) in duplicatedItems" :key="index" class="feature-cardscoll"
                        @mouseenter="pauseScroll" @mouseleave="resumeScroll">
                        <img :src="item.icon" class="iconscroll" />
                        <span>{{ item.title }}</span>
                        <span>{{ item.description }}</span>
                    </div>
                </div>
                <div class="shadow2"></div>

            </div> -->
            <div class="title-container">
                <h2 class="title">产品功能</h2>
            </div>
            <div class="container-card">
                <div v-for="(item, index) in itemsFear" :key="index" class="card" :class="{ 'loaded': isVisible }">
                    <img :src="item.icon"></img>
                    <h3 class="title">{{ item.title }}</h3>
                    <p class="desc">{{ item.description }}</p>
                    <div class="hover-bg"></div>
                    <div class="hover-border"></div>
                </div>
            </div>
            <Vue3Marquee pauseOnHover="true">
            </Vue3Marquee>
            <!-- 支持 2000+ 币种 -->
            <!-- <div class="crypto-container">
                <div class="title-container" style="margin: 60px 0 14px 0 ;">
                    <h2 class="title">支持 2000+ 币种</h2>
                </div>

                <div class="subtitles">我们支持 50+ 法币币种和 2000+ 加密货币币种，实现全球金融资产托管</div>
                <div class="coinall" :class="{ 'coin-in': isVisible }">
                    <div v-for="(coins, rowIndex) in coins" :key="rowIndex" class="scroll-container">
                        <div class="scroll-row" :class="{ 'reverse': rowIndex === 1 }">
                            <div v-for="(coin, index) in [...coins, ...coins]" :key="index" class="crypto-item">
                                <img :src="coin.img" :alt="coin.name" />
                                <span>{{ coin.name }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="animated-image"></div>
            </div> -->

            <div class="crypto-box">

                <div class="shadows"></div>
                <div class="shadows2"></div>
                <!-- style="margin: 60px 0 14px 0 ;" -->
                <div class="title-container2">
                    <span class="title2">打造您的加密投资组合</span>
                </div>
                <div class="subtitles">打造您的全球资产配置组合。</div>

                <div v-for="(coins, rowIndex) in cryptoData" :key="rowIndex" class="scroll-wrapper2">
                    <!-- <div class="scroll-track" :class="{ 'reverse': rowIndex === 1 }"> -->
                        <div v-for="(coin, index) in [...coins, ...coins]" :key="index" class="crypto-card">
                            <img :src="coin.img" :alt="coin.name" />
                            <div class="name">
                                <span>{{ coin.name }}</span>
                                <div class="bom">
                                    <span>$46,421.34</span>
                                    <span>+3.34%</span>
                                </div>
                            </div>
                        </div>
                    <!-- </div> -->
                </div>
            </div>

            <div class="applink" ref="animatedElement">
                <!-- :class="{ 'animate-in-link': isVisible }"   :class="{ 'animate-in-link': isVisible }"-->
                <div class="leftapp" :style="computedStyle">
                    <img :class="{ 'animate-in-up': isVisible }"
                        src="https://pro-oss.pinkwallet.com/image/20250325/bbc00d5cad9c29a38bb40a25267e45e4_868x1964.png" />
                    <img :class="{ 'animate-in-down': isVisible }"
                        src="https://pro-oss.pinkwallet.com/image/20250325/045c451f62cf9692591f3e962ee6ae27_868x2040.png" />

                </div>
                <!-- :class="{ 'animate-in-down': isVisible }" -->
                <div class="rightapp" :style="RecomputedStyle">
                    <span class="title-link">加密货币平台</span>
                    <span class="subtitle-link">立即下载PinkWallet </span>
                    <span class="subtitle-link">随时随地进行交易</span>
                    <div class="store-buttons">
                        <button class="store-button !rounded-button" @mouseenter="hovered = 'appstore'"
                            @mouseleave="hovered = ''">
                            <img :src="hovered === 'appstore'
                                ? 'https://pro-oss.pinkwallet.com/image/20250324/7a909be8fc9acf2501128098e0b213fc_95x114.png'
                                : 'https://pro-oss.pinkwallet.com/image/20250321/bef50247b4acf6ba44f795f38ccfc81a_124x128.png'
                                " />
                            <div>
                                <span>Download on the</span>
                                <span>App Store</span>
                            </div>
                        </button>
                        <button class="store-button !rounded-button" @mouseenter="hovered = 'googleplay'"
                            @mouseleave="hovered = ''">
                            <img :src="hovered === 'googleplay'
                                ? 'https://pro-oss.pinkwallet.com/image/20250324/b11ff143f668e2b79d0899aa7b7fbdf4_124x128.png'
                                : 'https://pro-oss.pinkwallet.com/image/20250321/deff5942b1f51393727b5c6c278b83c7_124x128.png'
                                " />
                            <div>
                                <span>get on</span>
                                <span>Google play</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>

import { Vue3Marquee } from 'vue3-marquee'
import { ref, onMounted, onUnmounted, computed, reactive } from "vue";

const isVisible = ref(false);


const animatedElement = ref(null);
const visibilityRatio = ref(0); // 记录元素可见比例

// 计算 X 轴偏移量
const computedStyle = computed(() => {
    const translateX = (1 - visibilityRatio.value) * 450; // 可见度越低，X 偏移越大
    return {
        transform: `translateX(${-translateX}px)`,
        transition: 'transform 0.8s ease-out'
    };
});

const RecomputedStyle = computed(() => {
    const translateX = (1 - visibilityRatio.value) * 450; // 可见度越低，X 偏移越大
    return {
        transform: `translateX(${translateX}px)`,
        transition: 'transform 0.8s ease-out'
    };
});
// onMounted(() => {

// });
const imageUrl = 'https://pinkwallet.com/img/'; // 图片地址前缀

let keyframes = '@keyframes playFrames {';
for (let i = 1; i <= 150; i++) {
    let percent = ((i - 1) / 149) * 100; // 计算百分比
    let paddedIndex = String(i).padStart(3, '0'); // 补零
    keyframes += `${percent.toFixed(2)}% { background-image: url('https://pinkwallet.com/img/${paddedIndex}.png'); } `;
}
keyframes += '}';

// 在页面插入 keyframes
const style = document.createElement('style');
style.innerHTML = keyframes;
document.head.appendChild(style);


const frameRate = 1000 / 30; // 30帧/秒，每帧 33.33ms
const totalFrames = 150; // 5 秒内 150 帧
const images = ref([]); // 存放所有图片路径
const currentFrame = ref(0);

// 加载 60 张图片
for (let i = 1; i <= 151; i++) {
    // images.value.push(`../assets/iloveimg-compressed/${i}.png`, import.meta.url); // 你可以修改路径
    const paddedIndex = String(i).padStart(3, "0"); // 补零，确保 001 ~ 060
    images.value.push(new URL(`https://pinkwallet.com/img/${paddedIndex}.png`));
    // images.value.push(new URL(`../assets/img/${paddedIndex}.png`, import.meta.url).href);
    // images.value.push(new URL(`https://pinkwallet.com/img/${paddedIndex}.png`, import.meta.url).href);
}

console.log(images.value);

// 计算当前应该显示哪张图片（150帧 -> 60 张图循环）
const currentImage = ref(images.value[0]);
const updateFrame = () => {
    currentFrame.value = (currentFrame.value + 1) % totalFrames;
    const imageIndex = Math.floor((currentFrame.value / totalFrames) * 150); // 映射到 60 张图片
    currentImage.value = images.value[imageIndex];
};

const cryptoData = ref([
    [
        { name: "BTC", img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png" },
        { name: "ETH", img: "https://cryptologos.cc/logos/ethereum-eth-logo.png" },
        { name: "ZEC", img: "https://cryptologos.cc/logos/zcash-zec-logo.png" },
        { name: "BSV", img: "https://cryptologos.cc/logos/bitcoin-sv-bsv-logo.png" },
        { name: "BTC", img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png" },
        { name: "ETH", img: "https://cryptologos.cc/logos/ethereum-eth-logo.png" },
        { name: "ZEC", img: "https://cryptologos.cc/logos/zcash-zec-logo.png" },
        { name: "BSV", img: "https://cryptologos.cc/logos/bitcoin-sv-bsv-logo.png" },
    ],
    [
        { name: "ETH", img: "https://cryptologos.cc/logos/ethereum-eth-logo.png" },
        { name: "ZEC", img: "https://cryptologos.cc/logos/zcash-zec-logo.png" },
        { name: "BTC", img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png" },
        { name: "BSV", img: "https://cryptologos.cc/logos/bitcoin-sv-bsv-logo.png" },
        { name: "ETH", img: "https://cryptologos.cc/logos/ethereum-eth-logo.png" },
        { name: "ZEC", img: "https://cryptologos.cc/logos/zcash-zec-logo.png" },
        { name: "BTC", img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png" },
        { name: "BSV", img: "https://cryptologos.cc/logos/bitcoin-sv-bsv-logo.png" },
    ],
    [
        { name: "BSV", img: "https://cryptologos.cc/logos/bitcoin-sv-bsv-logo.png" },
        { name: "BTC", img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png" },
        { name: "ZEC", img: "https://cryptologos.cc/logos/zcash-zec-logo.png" },
        { name: "ETH", img: "https://cryptologos.cc/logos/ethereum-eth-logo.png" },
        { name: "BSV", img: "https://cryptologos.cc/logos/bitcoin-sv-bsv-logo.png" },
        { name: "BTC", img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png" },
        { name: "ZEC", img: "https://cryptologos.cc/logos/zcash-zec-logo.png" },
        { name: "ETH", img: "https://cryptologos.cc/logos/ethereum-eth-logo.png" },
    ]
]);

const hovered = ref("");
const chartRef = ref<HTMLElement | null>(null);
const heroImage = "https://pro-oss.pinkwallet.com/image/20250321/6c3a8dfc64c4bc3c884a108891ccfa41_1056x2272.png"
const features = ref([
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/27cf738fa67e6c7d456243488fb9b00c_34x35.png",
        title: '100+国家合规持牌',
        description: '美国，加拿大，英国等'
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/9789f670fa7cd1eb6534efc3a5227003_137x137.png",
        title: '4大类金融资产',
        description: '加密货币,股票，债券，衍生品'
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/5ac3555b28ec181b53baabe141a00483_137x137.png",
        title: '30+',
        description: '30+ 顶级供应商接入，确保最优价格'
    },

]);

const visibleItems = ref(features.value.map(() => false));
setTimeout(() => {
    features.value.forEach((_, index) => {
        setTimeout(() => {
            visibleItems.value[index] = true;
        }, index * 200); // 每个元素延迟 0.2s
    });
}, 200);


const businessProcess = ref([
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/9c205a919d83a84f191fc603bbe3e423_760x412.png",
        title: '下载App',
        description: '各大应用市场均支持PinkWallet下载',
        w: "190",
        l: "102"
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/20da494e81a61dfc06fb189a635a3965_564x469.png",
        title: '用户注册',
        description: '手机号快捷注册体验，调过KYC认证',
        w: "141",
        l: "117"
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/6419c58d4606fb828702bcffe1e6af4b_568x429.png",
        title: '新手入金',
        description: '支持法币与加密货币多渠道入金，支持信用卡快捷购币',
        w: "142",
        l: "107"
    },
    {
        icon: "https://pro-oss.pinkwallet.com/image/20250321/bb00fce51746916f54f9592a040da133_601x600.png",
        title: '快捷闪兑',
        description: '支持法币与法币，法币与加密货币，加密货币与加密货币全币对自由闪兑',
        w: "150",
        l: "150"
    }
]);

const functionItems = ref(businessProcess.value.map(() => false));
setTimeout(() => {
    businessProcess.value.forEach((_, index) => {
        setTimeout(() => {
            functionItems.value[index] = true;
        }, index * 200); // 每个元素延迟 0.2s
    });
}, 200);

const coins = ref([
    [
        { img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png", name: "BTC" },
        { img: "https://cryptologos.cc/logos/ethereum-eth-logo.png", name: "ETH" },
        { img: "https://cryptologos.cc/logos/litecoin-ltc-logo.png", name: "LTC" },
        { img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png", name: "BTC" },
        { img: "https://cryptologos.cc/logos/ethereum-eth-logo.png", name: "ETH" },
        { img: "https://cryptologos.cc/logos/litecoin-ltc-logo.png", name: "LTC" },
        { img: "https://cryptologos.cc/logos/bitcoin-btc-logo.png", name: "BTC" },
        { img: "https://cryptologos.cc/logos/ethereum-eth-logo.png", name: "ETH" },
        { img: "https://cryptologos.cc/logos/litecoin-ltc-logo.png", name: "LTC" }
    ],
    [
        { img: "https://cryptologos.cc/logos/zcash-zec-logo.png", name: "ZEC" },
        { img: "https://cryptologos.cc/logos/dash-dash-logo.png", name: "DASH" },
        { img: "https://cryptologos.cc/logos/monero-xmr-logo.png", name: "XMR" },
        { img: "https://cryptologos.cc/logos/zcash-zec-logo.png", name: "ZEC" },
        { img: "https://cryptologos.cc/logos/dash-dash-logo.png", name: "DASH" },
        { img: "https://cryptologos.cc/logos/monero-xmr-logo.png", name: "XMR" },
        { img: "https://cryptologos.cc/logos/zcash-zec-logo.png", name: "ZEC" },
        { img: "https://cryptologos.cc/logos/dash-dash-logo.png", name: "DASH" },
        { img: "https://cryptologos.cc/logos/monero-xmr-logo.png", name: "XMR" }
    ],
    [
        { img: "https://cryptologos.cc/logos/bitcoin-cash-bch-logo.png", name: "BCH" },
        { img: "https://cryptologos.cc/logos/stellar-xlm-logo.png", name: "XLM" },
        { img: "https://pro-oss.pinkwallet.com/image/20250324/924bb0d5c65dcd97e6a8e4ed538285f6_200x200.png", name: "XRP" },
        { img: "https://cryptologos.cc/logos/bitcoin-cash-bch-logo.png", name: "BCH" },
        { img: "https://cryptologos.cc/logos/stellar-xlm-logo.png", name: "XLM" },
        { img: "https://pro-oss.pinkwallet.com/image/20250324/924bb0d5c65dcd97e6a8e4ed538285f6_200x200.png", name: "XRP" },
        { img: "https://cryptologos.cc/logos/bitcoin-cash-bch-logo.png", name: "BCH" },
        { img: "https://cryptologos.cc/logos/stellar-xlm-logo.png", name: "XLM" },
        { img: "https://pro-oss.pinkwallet.com/image/20250324/924bb0d5c65dcd97e6a8e4ed538285f6_200x200.png", name: "XRP" }
    ]
]);



const Width1 = ref('881px')
const Width2 = ref('426px')

const Width3 = ref('426px')
const Width4 = ref('881px')
const items = ref([
    {
        title: "法币兑法币",
        desc: "50+法币自由兑换",
        img: "https://cdn-icons-png.flaticon.com/128/9913/9913942.png",
    },
    {
        title: "法币兑加密货币",
        desc: "50+法币与2000+加密货币自由兑换",
        img: "https://cdn-icons-png.flaticon.com/128/11714/11714314.png",
    }
]);
const items2 = ref([
    {
        title: "加密货币兑加密货币",
        desc: "2000+加密货币自由兑换",
        img: "https://cdn-icons-png.flaticon.com/128/7339/7339040.png",
    },
    {
        title: "加密货币直接消费",
        desc: "免费申请Visa国际卡，支持Visa全渠道消费场景",
        img: "https://cdn-icons-png.flaticon.com/128/6464/6464126.png",
    },
]);
const statistics = [
    {
        value: '100万+',
        label: '注册用户'
    },
    {
        value: '￥50亿',
        label: '交易总额'
    },
    {
        value: '99.9%',
        label: '系统可用性'
    },
    {
        value: '24/7',
        label: '全天候服务'
    }
];
const activeIndex = ref(null);
const activeIndex2 = ref(null);


const handleHover = (index) => {
    activeIndex.value = index;

    if (index === 1) {
        Width1.value = '426px'
        Width2.value = '881px'
    }
    if (index === 0) {
        Width1.value = '881px'
        Width2.value = '426px'
    }
};
const handleHover2 = (index) => {
    activeIndex2.value = index;

    if (index === 0) {
        Width3.value = '426px'
        Width4.value = '881px'
    }
    if (index === 1) {
        Width3.value = '881px'
        Width4.value = '426px'
    }
};
const resetHover = () => {
    activeIndex.value = null;
    Width1.value = '881px'
    Width2.value = '426px'
};
const resetHover2 = () => {
    activeIndex2.value = null; Width3.value = '426px'
    Width4.value = '881px'
};

const itemsFear = ref([
    { title: "便捷买币", description: "支持信用卡支付，快捷买币", icon: "https://pro-oss.pinkwallet.com/image/20250325/1cefb8f985b547256a09c791b43cb7a2_196x212.png" },
    { title: "极速闪兑", description: "50+法币与2000+加密货币，极速在线，一键闪兑", icon: "https://pro-oss.pinkwallet.com/image/20250325/7a8812edb54a9e42f95b30baf09c9b0c_196x212.png" },
    { title: "国际汇款", description: "支持50+国家地区跨境汇款，收费低，到账速度快", icon: "https://pro-oss.pinkwallet.com/image/20250325/38ebcf9e0bdd79508c5cf53a018be7b1_196x212.png" },
    { title: "加密货币直接消费", description: "免费申请Pink-Visa 卡，全渠道消费不设限", icon: "https://pro-oss.pinkwallet.com/image/20250325/1637bd23971d1daa8d3f0311f32d27df_196x212.png" },
    { title: "个人与公司账户类型均支持", description: "可灵活认证个人账户或公司账户，方便灵活业务开展", icon: "https://pro-oss.pinkwallet.com/image/20250325/976d9378234fca5485826093d5b0d75f_196x212.png" },
    { title: "足不出户，开设海外银行账户", description: "个人专属银行账户在线免费开通，拥有海外个人同名iban账户", icon: "https://pro-oss.pinkwallet.com/image/20250325/6f45a78f5a8c67e6d1ed0cb811c4f064_196x212.png" },

]);

const duplicatedItems = computed(() => [...itemsFear.value, ...itemsFear.value]); // 复制两遍，实现无缝滚动

const scrollContainer = ref(null);
const scrollContent = ref(null);
let scrollX = 0;
const scrollSpeed = 1.5;
let isScrolling = true;
let animationFrame;

const startAutoScroll = () => {
    const step = () => {
        if (scrollContainer.value && isScrolling) {
            scrollX -= scrollSpeed;
            if (Math.abs(scrollX) >= scrollContent.value.scrollWidth / 2) {
                scrollX = 0; // 回到起点，实现无缝循环
            }
            scrollContent.value.style.transform = `translateX(${scrollX}px)`;
        }
        animationFrame = requestAnimationFrame(step);
    };
    animationFrame = requestAnimationFrame(step);
};

const pauseScroll = () => {
    isScrolling = false;
};

const resumeScroll = () => {
    isScrolling = true;
};
let animationTimer;
onMounted(() => {

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            visibilityRatio.value = entry.intersectionRatio; // 记录元素可见部分比例（0~1）
        });
    }, {
        threshold: Array.from({ length: 11 }, (_, i) => i * 0.1) // 生成 0.1, 0.2, ..., 1.0 的数组
    });

    if (animatedElement.value) {
        observer.observe(animatedElement.value);
    }

    animationTimer = setInterval(updateFrame, frameRate); // 每 33.33ms 刷新一次帧

    setTimeout(() => {
        isVisible.value = true;
    }, 800);
    startAutoScroll();
});

onUnmounted(() => {
    clearInterval(animationTimer);
    cancelAnimationFrame(animationFrame);
    if (animatedElement.value) {
        observer.unobserve(animatedElement.value);
    }
});
</script>
<style scoped lang="scss">
.root-container {
    width: 100%;
    min-height: 100vh;
    color: #fff;
    overflow-x: hidden;
}

.container {
    // max-width: 1440px;
    margin: 0 auto;
    // padding: 32px 16px;


    .download-section {
        margin-top: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30px;

        .download-content {
            width: 50%;
            text-align: left;
            margin-left: 114px;
            transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
            opacity: 0;

            &.animate-in {
                transform: translateX(120px); // 进入动画
                opacity: 1; // 透明度变为 100%
            }

            .title {
                // width: 418px;
                white-space: wrap;
                font-family: MiSans;
                font-weight: 700;
                font-size: 56px;
                line-height: 120%;
                letter-spacing: 0px;
                text-transform: capitalize;
                color: #fff;
            }

            .niu {
                font-family: MiSans;
                font-weight: 600;
                font-size: 24px;
                line-height: 32px;
                letter-spacing: 0px;
                text-transform: capitalize;
                color: #EF88A3;
            }

            .inputs {
                display: flex;
                align-items: center;
                margin-bottom: 58px;

                .input {
                    width: 360px;
                    cursor: pointer;
                    height: 64px;
                    border-radius: 12px;
                    // background: #F5F5F51A;
                    background: rgba(245, 245, 245, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.08);
                    display: flex;
                    align-items: center;
                    // transition: transform 0.8s ease-in-out;
                    transition: all 0.8s ease-in-out; // 过渡动画

                    &:hover {
                        border: 1px solid #EF88A3;
                    }

                    .prefix {
                        margin-left: 25px;
                        display: flex;
                        align-items: center;
                        font-family: MiSans;
                        font-weight: 500;
                        font-size: 16px;
                        line-height: 137%;
                        letter-spacing: 0px;
                        text-transform: capitalize;
                        color: #fff;

                        input {
                            margin-left: 17px;
                            height: 64px;
                            border-style: none;
                            background: none;
                            font-family: MiSans;
                            font-weight: 500;
                            outline: none; // 去除选中状态边框
                            font-size: 16px;
                            line-height: 137%;
                            letter-spacing: 0px;
                            text-transform: capitalize;
                            color: #fff;
                        }

                        .shu {
                            margin-left: 20px;
                            height: 19px;
                            width: 1px;
                            background: rgba(255, 255, 255, .1);
                            opacity: 0.1;
                            border-radius: 1px;

                        }
                    }
                }

                .login {
                    cursor: pointer;
                    width: 163px;
                    height: 64px;
                    border-radius: 12px;
                    background: rgba(239, 136, 163, 1);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 8px;
                    font-family: MiSans;
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 16px;
                    letter-spacing: 0px;
                    text-align: center;
                    color: #ffffff;
                    transition: all 0.8s ease-in-out; // 过渡动画

                    &:hover {
                        background: rgba(185, 100, 120, 1);

                    }
                }
            }

            .subtitle {
                color: #9ca3af;
                font-family: MiSans;
                font-weight: 400;
                font-size: 18px;
                width: 531px;
                white-space: wrap;
                line-height: 137%;
                letter-spacing: 0px;
                text-transform: capitalize;
                color: rgba(255, 255, 255, .7);
            }

            .store-buttons {
                display: flex;
                gap: 16px;

                .store-button {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: background-color 0.8s;
                    border: 1px solid rgba(255, 255, 255, 1);
                    width: 213px;
                    height: 76px;
                    overflow: hidden;
                    border-radius: 12px;


                    &:hover {
                        background: #fff;

                        div {
                            span {
                                &:nth-of-type(1) {
                                    color: rgba(0, 0, 0, .7);
                                }

                                &:nth-of-type(2) {
                                    color: #000;
                                }

                            }
                        }
                    }

                    img {
                        width: 31px;
                        height: 32px;
                        transition: opacity 0.3s ease-in-out;
                    }

                    div {
                        margin-left: 12px;
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;

                        span {
                            &:nth-of-type(1) {
                                font-family: MiSans;
                                font-weight: 400;
                                font-size: 12px;
                                line-height: 16px;
                                letter-spacing: 0px;
                                text-transform: capitalize;
                                color: rgba(255, 255, 255, .7);
                            }

                            &:nth-of-type(2) {
                                font-family: MiSans;
                                font-weight: 500;
                                font-size: 18px;
                                line-height: 24px;
                                letter-spacing: 0px;
                                text-transform: capitalize;
                                color: #fff;
                            }

                        }
                    }
                }
            }


        }

        .preview-image {
            width: 50%;
            background-image: url("https://pro-oss.pinkwallet.com/image/20250321/dab16d318b26a1bff265cd2c00aead7b_2835x2759.png");
            background-size: 100% 100%;
            height: 700px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
            opacity: 0;

            &.animate-in {
                transform: translateX(-120px); // 进入动画
                opacity: 1; // 透明度变为 100%
            }

            img {
                width: 258px;
                height: 562px;
                cursor: pointer;
                margin-right: 114px;
            }
        }
    }

}









.features-grid {
    // display: grid;
    display: flex;
    // flex: 1;
    // align-items: center;
    // justify-content: space-between;
    // grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin: 0 61px 64px 61px;
    padding-top: 50px;

    .feature-card {
        background: rgba(255, 255, 255, 0.05);
        background-blend-mode: overlay; // 让颜色 & 图片混合
        cursor: pointer;
        transition: transform 0.3s;
        flex: 1;
        min-width: 425px;
        height: 131px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        // justify-content: center;

        // transition: all 0.4s ease-in-out;
        // margin-top: 50px;
        // transition: transform 0.4s ease;
        border-color: 0.4s ease;
        background: 0.4s ease;

        opacity: 0;
        transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
        transform: translateY(-50px);

        &.fade-in {
            opacity: 1;
            transform: translateY(0); // 滑入到正常位置
            // transform: translateY(50px);
        }

        &:nth-of-type(1):hover {
            background-image: url("https://pro-oss.pinkwallet.com/image/20250324/a25d024972226ad8b77ff92ab7433d1d_850x262.png"),
                url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png"); // 覆盖独特背景
            // background-size: 60% 100%;
            // background-position: right;
            background-size: 60% 100%, cover; // 第一张占 60%，第二张全覆盖
            background-position: right, center; // 第一张靠右，第二张居中
            background-repeat: no-repeat, no-repeat; // 避免重复
        }




        &:nth-of-type(2):hover {
            background-image: url("https://pro-oss.pinkwallet.com/image/20250324/068a36a7a886d761e6b355815082dd43_850x262.png"),
                url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
            background-size: 60% 100%, cover; // 第一张占 60%，第二张全覆盖
            background-position: right, center; // 第一张靠右，第二张居中
            background-repeat: no-repeat, no-repeat; // 避免重复
        }

        &:nth-of-type(3):hover {
            background-image: url("https://pro-oss.pinkwallet.com/image/20250324/2279cb4ad6505dcc34506864a5e65ec4_850x262.png"),
                url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
            background-size: 60% 100%, cover; // 第一张占 60%，第二张全覆盖
            background-position: right, center; // 第一张靠右，第二张居中
            background-repeat: no-repeat, no-repeat; // 避免重复
        }

        &:hover {
            transform: translateY(-20px) !important;
            // transform: scale(1.05);
            // background: linear-gradient(135deg, rgba(255, 108, 135, 0.6), rgba(72, 28, 168, 0.6));
            // box-shadow: 0 10px 20px rgba(255, 108, 135, 0.3);
            background-image: url("https://pro-oss.pinkwallet.com/image/20250324/c1cf7e5d3481c444bdbcda2503f336d8_850x262.png");
            background-size: 100% 100%;
            transform: scale(1.05);
            border-color: #ff4d73;
            // background: rgba(255, 77, 115, 0.1);

            // &:nth-of-type(1) {
            //     background-image: url("https://pro-oss.pinkwallet.com/image/20250324/a25d024972226ad8b77ff92ab7433d1d_850x262.png");
            //     background-size: 100% 100%;
            //     width: 425px;
            //     height: 131px;
            // }

            // &:nth-of-type(2) {
            //     background-image: url("https://pro-oss.pinkwallet.com/image/20250324/068a36a7a886d761e6b355815082dd43_850x262.png");
            //     background-size: 100% 100%;
            // }

            // &:nth-of-type(3) {
            //     background-image: url("https://pro-oss.pinkwallet.com/image/20250324/2279cb4ad6505dcc34506864a5e65ec4_850x262.png");
            //     background-size: 100% 100%;
            // }

            .icon {
                transform: scale(1.05);
                // border-color: #ff4d73;
                // background: rgba(255, 77, 115, 0.1);
                transform: scale(1.1);
            }

            &::before {
                bottom: 0; // 使背景从下往上出现
            }
        }

        // &:not(:hover) {
        //     transform: translateY(0);

        //     &::before {
        //         bottom: -100%; // 使背景从上往下消失
        //     }
        // }
        div {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-left: 14px;

            span {
                &:nth-of-type(1) {
                    font-family: Source Sans Pro;
                    font-weight: 700;
                    font-size: 24px;
                    line-height: 30px;
                    letter-spacing: 0px;
                    text-transform: capitalize;
                    color: #fff;
                }

                &:nth-of-type(2) {
                    margin-top: 8px;
                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 15px;
                    line-height: 19px;
                    letter-spacing: 0px;
                    text-transform: capitalize;

                }
            }
        }

        img {
            margin: 36px;
            width: 34px;
            height: 34px;
        }





    }

}



.title-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;

    .title {
        font-size: 18px;
        font-weight: bold;
        color: white;
        position: relative;
        padding: 0 16px;

        font-family: MiSans;
        font-weight: 700;
        font-size: 36px;
        line-height: 100%;
        letter-spacing: 0px;
        text-align: center;
        text-transform: capitalize;
        color: #fff;

        &::before,
        &::after {
            content: "";
            position: absolute;
            top: 50%;
            width: 76px; // 横线宽度
            height: 2px; // 横线高度
            background-color: rgba(255, 255, 255, 0.2);
        }

        &::before {
            left: -100px;
        }

        &::after {
            right: -100px;
        }
    }
}

.process-grid {
    margin: 20px 62px 0 61px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-bottom: 64px;

    .process-card {
        background-color: #1f2937;
        padding: 82px 43px 90px 43px;
        border-radius: 8px;
        transition: all 0.5s ease-in-out;
        cursor: pointer;
        // width: 323px;
        // height: 418px;
        border-radius: 16px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
        opacity: 0;
        transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out, background 0.5s ease-in-out;
        transform: translateY(-50px);

        // transition: ; // 平滑过渡
        &.fade-in {
            opacity: 1;
            transform: translateY(0); // 滑入到正常位置
            // transform: translateY(50px);
        }


        &:hover {
            transform: translateY(-20px);
            // transform: scale(1.05);
            background: #EF88A3;
            backdrop-filter: blur(58.81349182128906px);
            background: 0.4s ease;

            box-shadow: 0 10px 20px rgba(255, 108, 135, 0.3);

            .icon {
                transform: scale(1.1);
            }

            &::before {
                bottom: 0; // 使背景从下往上出现
            }
        }

        // &:not(:hover) {
        //     transform: translateY(0);

        //     &::before {
        //         bottom: -100%; // 使背景从上往下消失
        //     }
        // }

        .process-header {
            display: flex;
            align-items: center;
            flex-direction: column;

            div {
                display: flex;
                align-items: center;
                flex-direction: column;

                span {
                    &:nth-of-type(1) {
                        margin: 46px 0 8px 0;
                        font-family: MiSans;
                        font-weight: 600;
                        font-size: 24px;
                        line-height: 150%;
                        letter-spacing: 0.5%;
                        text-align: center;
                        color: #FFFFFF;
                    }

                    &:nth-of-type(2) {
                        font-family: MiSans;
                        font-weight: 500;
                        font-size: 16px;
                        line-height: 150%;
                        letter-spacing: 0.5%;
                        text-align: center;
                        color: rgba(255, 255, 255, .5);
                    }

                }
            }
        }
    }
}

.business-scope {
    // height: 702px;
    // background-color: #141414;
    // background: red;
    display: flex;
    // justify-content: space-between;
    grid-template-columns: repeat(2, 1fr);
    // gap: 16px;
    // width: 100vw;
    margin: auto;
}

.card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: 20px;
    position: relative;
    overflow: hidden;
    transition: all .8s ease-in-out;
    border: 1px solid #FFFFFF1A;
    background: #202020;

    // width: 881px;
    height: 210px;
    border-radius: 16px;
    cursor: pointer;

    .card-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
        color: white;
        transition: color 0.4s ease-in-out;

        span {

            font-family: MiSans;
            font-weight: 700;
            font-size: 38px;
            line-height: 100%;
            letter-spacing: 0px;
            text-transform: capitalize;

        }

        p {
            font-size: 14px;
            opacity: 0.5;
            font-family: MiSans;
            font-weight: 500;
            font-size: 16px;
            line-height: 150%;
            letter-spacing: 0.5%;

        }
    }

    .icon {
        width: 147px;
        height: 117px;
        transition: transform 0.4s ease-in-out;
    }

    // &:nth-of-type(1) {
    //     width: 881px;

    //     .active {
    //         width: 426px;
    //     }
    // }

    // &:nth-of-type(2) {
    //     width: 426px;

    //     .active {
    //         width: 881px;
    //     }
    // }





    &:hover {
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(239, 136, 163, 0.1) 100%);
        box-shadow: 0px 16px 31.2px 0px #EF88A300;
        border: 1px solid #EF88A3;

        .card-content>span {
            color: #ff6c87;
        }

    }

    &.active {
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(239, 136, 163, 0.1) 100%);
        box-shadow: 0px 16px 31.2px 0px #EF88A300;
        border: 1px solid #EF88A3;

        &:nth-child(1) {
            width: 426px;
        }

        &:nth-child(2) {
            width: 881px;
        }

        .card-content {
            color: #ff6c87;
        }

        .icon {
            transform: scale(1.1);
        }
    }



    // &:nth-child(2).active,
    // &:nth-child(3).active {
    //     width: 60%;
    // }

    // &:nth-child(4).active {
    //     width: 60%;
    // }
}

.card2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.8s ease-in-out;
    border: 1px solid #FFFFFF1A;
    // width: 881px;
    height: 210px;
    border-radius: 16px;

    &:nth-of-type(1) {
        width: 426px;
    }



    .card-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
        color: white;
        transition: color 0.4s ease-in-out;
    }

    h3 {
        font-size: 18px;
        font-weight: bold;
    }

    p {
        font-size: 14px;
        opacity: 0.7;
    }

    .icon {
        width: 50px;
        height: 50px;
        transition: transform 0.4s ease-in-out;
    }

    &.active {
        background: rgba(255, 108, 135, 0.2);
        border-color: #ff6c87;

        .card-content {
            color: #ff6c87;
        }

        .icon {
            transform: scale(1.1);
        }
    }

    &:nth-child(1).active {
        width: 60%;
    }

    &:nth-child(2).active,
    &:nth-child(3).active {
        width: 60%;
    }

    &:nth-child(4).active {
        width: 60%;
    }
}

.feature-section {
    text-align: center;
    background: #111;
    padding: 40px 0;
    overflow: hidden;

    .feature-title {
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        position: relative;
        margin-bottom: 20px;

        &::before,
        &::after {
            content: "";
            width: 60px;
            height: 2px;
            background: #444;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
        }

        &::before {
            left: 20px;
        }

        &::after {
            right: 20px;
        }
    }
}

.feature-list {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding: 10px 20px;
    scroll-behavior: smooth;
    scrollbar-width: none;
    white-space: nowrap;
    position: relative;

    &::-webkit-scrollbar {
        display: none;
    }
}

// .feature-card {
//     flex: 0 0 240px;
//     background: #1a1a1a;
//     border-radius: 12px;
//     padding: 20px;
//     text-align: center;
//     border: 2px solid transparent;
//     transition: all 0.4s ease-in-out;
//     cursor: pointer;

//     .feature-icon {
//         width: 50px;
//         height: 50px;
//         transition: transform 0.3s ease-in-out;
//     }

//     .feature-name {
//         font-size: 16px;
//         font-weight: bold;
//         color: white;
//         margin: 10px 0;
//         transition: color 0.3s ease-in-out;
//     }

//     .feature-desc {
//         font-size: 12px;
//         color: #999;
//         transition: color 0.3s ease-in-out;
//     }

//     &:hover,
//     &.is-active {
//         background: rgba(255, 108, 135, 0.2);
//         border-color: #ff6c87;
//         transform: scale(1.05);

//         .feature-name {
//             color: #ff6c87;
//         }

//         .feature-desc {
//             color: white;
//         }

//         .feature-icon {
//             transform: scale(1.2);
//         }
//     }
// }

.crypto-container {
    text-align: center;
    background: linear-gradient(180deg, #141414 75.37%, #EF88A3 142.21%);
    // padding: 40px;
    position: relative;
    overflow: hidden;
    color: white;
    height: 680px;

    .animated-image {
        bottom: -164px;
        position: absolute;
        width: 530px;
        height: 530px;
        left: 50%;
        background-size: 100% 100%;
        transform: translateX(-50%);
        animation: playFrames 5s steps(150, end) infinite;
    }

    .subtitles {
        font-family: MiSans-thin;
        font-weight: 400;
        font-size: 16px;
        line-height: 100%;
        letter-spacing: 0px;
        text-align: center;
        text-transform: capitalize;
        color: rgba(255, 255, 255, .4);
        padding-bottom: 45px;
    }

    .coinall {
        margin-top: 120px;
        // height: fit-content;
        opacity: 0;
        transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;

        &.coin-in {
            transform: translateY(-120px); // 进入动画
            opacity: 1; // 透明度变为 100%
        }

        /* 滚动行 */
        .scroll-container {
            width: 100%;
            margin: 20px 0;

            &:nth-child(1) {
                opacity: 0.5;
            }

            &:nth-child(2) {
                opacity: 0.7;
            }

            &:nth-child(3) {
                opacity: 1;
            }

            .scroll-row {
                display: flex;
                gap: 24px;
                width: max-content;
                animation: scroll-left 20s linear infinite;

                /* 货币卡片 */
                .crypto-item {
                    display: flex;
                    align-items: center;
                    padding: 10px 20px;
                    background: rgba(255, 255, 255, 0.1);
                    // transform: translateY(120px);
                    transition: opacity 0.5s, transform 1s;
                    gap: 17px;
                    width: 245px;
                    height: 75px;
                    border-radius: 100px;
                    font-family: MiSans;
                    font-weight: 400;
                    font-size: 20px;
                    line-height: 100%;
                    letter-spacing: 0px;
                    text-align: center;
                    text-transform: capitalize;
                    color: #fff;
                    cursor: pointer;

                    img {
                        width: 60px;
                        height: 60px;
                    }

                    &:hover {
                        opacity: 1;
                        animation-play-state: paused;
                        transform: translateY(0);
                    }
                }

                &:nth-of-type(3) {
                    opacity: 1 !important;
                }
            }

            .scroll-row.reverse {
                animation: scroll-right 20s linear infinite;
            }
        }
    }

    .earth {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 530px;
        height: 530px;
        background: url("https://pro-oss.pinkwallet.com/image/20250324/330806b1c394ff6bb8ba148d55f5793d_2138x1472.png") no-repeat center/cover;
        animation: rotateGlobe 15s linear infinite;

        @keyframes rotateGlobe {
            from {
                transform: translateX(-50%) rotate(0deg);
            }

            to {
                transform: translateX(-50%) rotate(360deg);
            }
        }
    }

    /* 滚动动画 */
    @keyframes scroll-left {
        from {
            transform: translateX(0%);
        }

        to {
            transform: translateX(-50%);
        }
    }

    @keyframes scroll-right {
        from {
            transform: translateX(-50%);
        }

        to {
            transform: translateX(0%);
        }
    }

}

.container-card {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    // padding: 20px;
    padding: 0 60px 0 70px;
    margin: 47px 0 140px 0;

    .card {
        position: relative;
        background: rgba(30, 30, 30, 1);
        color: #fff;
        padding: 32px 74px 44px 63px;
        border-radius: 18px;
        overflow: hidden;
        transform: translateY(60px);
        opacity: 0;
        transition: transform 0.8s ease-out, opacity 0.8s ease-out;
        display: flex;
        flex-direction: column;
        justify-content: center;
        border: 1.13px solid rgba(75, 75, 75, 1);

        img {
            width: 49px;
            height: 53px;
        }
    }

    .card.loaded {
        transform: translateY(0);
        opacity: 1;
    }

    .card:hover {
        transform: scale(1.05);
        // box-shadow: 0px 8px 20p  x rgba(255, 192, 203, 0.5);
        // background: linear-gradient(180deg, rgba(30, 30, 30, 0) 37.36%, #EF88A3 167.71%);
        background-image: url("https://pro-oss.pinkwallet.com/image/20250325/9fb69368f39e19d2fcb1454cf0668610_680x376.png");
        background-size: 100% 100%;
    }

    .card .hover-border {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        width: 0;
        // background: pink;

        transition: width 0.8s ease-in-out;
    }

    .card:hover .hover-border {
        width: 100%;
    }

    .hover-bg {
        position: absolute;
        bottom: -80px;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 182, 193, 0.2);
        opacity: 0;
        transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
    }

    .card:hover .hover-bg {
        transform: translateY(-80px);
        opacity: 1;
    }

    .title {
        font-family: MiSans;
        font-weight: 600;
        font-size: 26px;
        line-height: 39px;
        letter-spacing: 0.5%;
        text-align: center;
        margin: 8px 0 10px 0;
    }

    .desc {
        font-family: MiSans;
        font-weight: 400;
        font-size: 18px;
        line-height: 27px;
        letter-spacing: 0.5%;
        text-align: center;
        color: rgba(255, 255, 255, .5);
    }

}







// /* 第一行 */
// .scroll-row:nth-child(1) {
//     // animation-duration: 12s;
//     opacity: 0.5;
// }

// /* 第二行 */

// .scroll-row:nth-child(2) {
//     animation-duration: 10s;
// }
// /* 第三行 */

// .scroll-row:nth-child(3) {
//     animation-duration: 8s;
// }







.crypto-box {
    overflow: hidden;
    background: #FF95B2;
    // padding: 20px;
    position: relative;

    .shadows {
        z-index: 1;
        width: 183px;
        height: 452px;
        background: linear-gradient(90deg, #FF95B2 0%, rgba(255, 149, 178, 0) 100%);
        position: absolute;
        top: 180px;
        left: 0;
    }

    .shadows2 {
        z-index: 1;
        width: 183px;
        height: 452px;
        background: linear-gradient(90deg, #FF95B2 0%, rgba(255, 149, 178, 0) 100%);
        position: absolute;
        top: 180px;
        right: 0;
        rotate: 180deg;
    }

    .title-container2 {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 100%;
        margin-top: 90px;
        .title2 {
            color: #000;
            position: relative;
            // padding: 0 16px;
            font-family: MiSans-bold;
            font-weight: bold;
            font-size: 36px;
            line-height: 48px;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;

            &::before,
            &::after {
                content: "";
                position: absolute;
                top: 50%;
                width: 76px; // 横线宽度
                height: 2px; // 横线高度
                background-color: rgba(94, 9, 32, .2);
                border-radius: 10px;
            }

            &::before {
                left: -100px;
            }

            &::after {
                right: -100px;
            }
        }
    }

    .subtitles {
        margin-top: 14px;
        font-family: MiSans-normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 21px;
        letter-spacing: 0px;
        text-align: center;
        text-transform: capitalize;

        color: #5E0920;
        padding-bottom: 61px;
    }

    .scroll-wrapper2 {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 141px;

        }
    }

    .scroll-track {
        display: flex;
        align-items: center;
        gap: 16px;
        animation: scroll-left2 20s linear infinite;

        &.reverse {
            animation: scroll-right2 20s linear infinite;
        }
    }

    .crypto-card {
        display: flex;
        align-items: center;
        background: #FFFFFF8C;
        cursor: pointer;
        transition: opacity 0.3s;
        opacity: 0.6;
        border: 1px solid #FFFFFF8C;
        // width: 379px;
        // height: 123px;
        border-radius: 16px;
        padding: 30px 105px 30px 32px;

        .name {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            font-family: MiSans;
            font-weight: 600;
            font-size: 24px;
            line-height: 100%;
            letter-spacing: 0px;
            text-align: center;
            text-transform: capitalize;
            color: rgba(0, 0, 0, 1);

            .bom {
                margin-top: 4px;

                span {
                    &:nth-of-type(1) {
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 20px;
                        line-height: 100%;
                        letter-spacing: 0px;
                        text-align: center;
                        text-transform: capitalize;
                        color: rgba(0, 0, 0, .6);
                    }

                    &:nth-of-type(2) {
                        margin-left: 14px;
                        font-family: MiSans;
                        font-weight: 400;
                        font-size: 20px;
                        line-height: 100%;
                        letter-spacing: 0px;
                        text-align: center;
                        text-transform: capitalize;

                        // 单数红色 双数绿色
                        &:nth-of-type(odd) {
                            color: rgba(223, 55, 95, 1);
                        }

                        &:nth-of-type(even) {
                            color: rgba(44, 168, 96, 1);
                        }
                    }

                }
            }
        }
        img {
            width: 40px;
            margin-right: 8px;
        }
        
        &:hover {
            opacity: 1;
        }

     

        span {
            font-weight: bold;
        }
    }

    // 滚动动画
    @keyframes scroll-left2 {
        from {
            transform: translateX(0);
        }

        to {
            transform: translateX(-50%);
        }
    }

    @keyframes scroll-right2 {
        from {
            transform: translateX(-50%);
        }

        to {
            transform: translateX(0);
        }
    }
}




// @keyframes rotate-globe {
//     from {
//         transform: translateX(-50%) rotate(0deg);
//     }

//     to {
//         transform: translateX(-50%) rotate(360deg);
//     }
// }

// .crypto-wrapper {
//     display: flex;
//     flex-direction: column;
//     gap: 16px;
//     align-items: center;
//     // width: 100vw;s

// }

// .scroll-row {
//     display: flex;
//     gap: 24px;
//     white-space: nowrap;
//     position: relative;
//     // opacity: 0;
//     transform: translateY(120px);

//     animation: fade-in 3s ease-out forwards, slide-up 1s ease-out forwards;

//     &:hover {
//         animation-play-state: paused;
//     }
// }

// // 动画
// @keyframes marquee-left {
//     from {
//         transform: translateX(100%);
//     }

//     to {
//         transform: translateX(-100%);
//     }
// }

// @keyframes marquee-right {
//     from {
//         transform: translateX(-100%);
//     }

//     to {
//         transform: translateX(100%);
//     }
// }

// /* 应用滚动动画 */
// .marquee-left {
//     animation: marquee-left 10s linear infinite;
// }

// .marquee-right {
//     animation: marquee-right 10s linear infinite;
// }

// .crypto-item {
//     display: flex;
//     align-items: center;
//     gap: 17px;
//     background: rgba(255, 255, 255, 0.1);
//     // padding: 10px 20px;
//     // border-radius: 50px;
//     transition: opacity 0.3s ease-in-out;

//     width: 245px;
//     height: 75px;
//     border-radius: 100px;

//     padding-left: 8px;
//     font-family: MiSans;
//     font-weight: 400;
//     font-size: 20px;
//     line-height: 100%;
//     letter-spacing: 0px;
//     text-align: center;
//     text-transform: capitalize;
//     color: #fff;

//     img {
//         width: 60px;
//         height: 60px;
//     }

//     &:hover {
//         opacity: 1 !important;
//     }
// }

// .earth {
//     width: 150px;
//     height: 150px;
//     background: url("https://pro-oss.pinkwallet.com/image/20250324/330806b1c394ff6bb8ba148d55f5793d_2138x1472.png") no-repeat center/cover;
//     border-radius: 50%;
//     position: absolute;
//     bottom: 0;
//     left: 50%;
//     transform: translateX(-50%);
//     animation: rotate 10s linear infinite;
// }



// @keyframes fade-in {
//     to {
//         opacity: 1;
//     }
// }

// @keyframes slide-up {
//     to {
//         transform: translateY(0);
//     }
// }

// @keyframes rotate {
//     from {
//         transform: rotate(0deg);
//     }

//     to {
//         transform: rotate(360deg);
//     }
// }


.applink {
    max-width: 100vw;
    background: #0A0A0A;
    padding: 140px 208px 151px 177px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .leftapp {
        height: 575px;
        width: 373px;
        position: relative;
        // margin-left: -450px;
        transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
        // opacity: 0;

        &:hover {
            img {

                &:nth-of-type(1) {
                    transform: translateX(-40px); // 进入动画
                }

                &:nth-of-type(2) {
                    transform: translateX(40px); // 进入动画

                }
            }
        }

        &.animate-in-link {
            transform: translateX(450px); // 进入动画
            opacity: 1; // 透明度变为 100%
        }

        // .animate-in-up {
        //     transform: translateY(160px); // 进入动画
        //     opacity: 1; // 透明度变为 100%
        // }

        // .animate-in-down {
        //     transform: translateY(-160px); // 进入动画
        //     opacity: 1; // 透明度变为 100%
        // }

        img {
            position: absolute;
            transition: transform 0.5s ease-in-out;

            &:nth-of-type(1) {
                // margin-top: -160px;
                left: 0;
                top: 0;
                width: 217px;
                height: 491px;
            }

            &:nth-of-type(2) {
                right: 0;
                // margin-top: 160px;
                bottom: 0;
                width: 217px;
                height: 510px;
            }
        }
    }

    .rightapp {
        display: flex;
        // justify-content: flex-start;
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
        // margin-right: -450px;
        transition: transform 0.8s ease-in-out, opacity 0.8s ease-in-out;
        // opacity: 0;

        &.animate-in-down {
            transform: translateX(-450px); // 进入动画

            opacity: 1; // 透明度变为 100%
        }

        .store-buttons {
            display: flex;
            gap: 16px;
            margin-top: 45px;

            .store-button {
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.8s;
                border: 1px solid rgba(255, 255, 255, 1);
                width: 213px;
                height: 76px;
                overflow: hidden;
                border-radius: 12px;


                &:hover {
                    background: #fff;

                    div {
                        span {
                            &:nth-of-type(1) {
                                color: rgba(0, 0, 0, .7);
                            }

                            &:nth-of-type(2) {
                                color: #000;
                            }

                        }
                    }
                }

                img {
                    width: 31px;
                    height: 32px;
                    transition: opacity 0.3s ease-in-out;
                }

                div {
                    margin-left: 12px;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;

                    span {
                        &:nth-of-type(1) {
                            font-family: MiSans;
                            font-weight: 400;
                            font-size: 12px;
                            line-height: 16px;
                            letter-spacing: 0px;
                            text-transform: capitalize;
                            color: rgba(255, 255, 255, .7);
                        }

                        &:nth-of-type(2) {
                            font-family: MiSans;
                            font-weight: 500;
                            font-size: 18px;
                            line-height: 24px;
                            letter-spacing: 0px;
                            text-transform: capitalize;
                            color: #fff;
                        }

                    }
                }
            }
        }

        .subtitle-link {
            font-family: MiSans;
            font-weight: 700;
            font-size: 52px;
            line-height: 69px;
            letter-spacing: 0px;
            text-transform: capitalize;
            color: #FF95B2;
            width: 502px;
            white-space: wrap;

        }

        .title-link {
            margin-bottom: 5px;
            font-family: MiSans;
            font-weight: 500;
            font-size: 20px;
            line-height: 32px;
            letter-spacing: 0px;
            text-transform: capitalize;
            color: rgba(255, 255, 255, .6);
        }
    }
}

.scroll-wrapper {
    margin: 0 62px 0 61px;
    overflow: hidden;
    white-space: nowrap;
    // width: 100%;
    padding: 20px 0;
    position: relative;

    .shadow {
        width: 191px;
        rotate: 180deg;
        height: 237px;
        background: linear-gradient(90deg, rgba(10, 10, 10, 0) 0%, #0A0A0A 71.91%);
        position: absolute;
        left: -1px;
        top: 0;
        z-index: 1;
    }

    .shadow2 {
        width: 191px;
        height: 237px;
        background: linear-gradient(90deg, rgba(10, 10, 10, 0) 0%, #0A0A0A 71.91%);
        position: absolute;
        right: 0;
        top: 0;
    }
}

.scroll-content {
    display: flex;
    gap: 20px;
    transform: translateX(0);
    transition: transform 0.1s linear;
}

.feature-cardscoll {
    flex: 0 0 auto;
    padding: 28px 60px;
    text-align: center;
    transition: transform 0.4s ease, border-color 0.4s ease, background 0.4s ease;

    width: 379px;
    // height: 215px;
    border-radius: 16px;
    background: #1E1E1E;
    border: 1px solid #4B4B4B;
    display: flex;
    flex-direction: column;
    align-items: center;

    span {
        &:nth-of-type(1) {
            font-family: MiSans;
            font-weight: 500;
            font-size: 24px;
            line-height: 150%;
            letter-spacing: 0.5%;
            text-align: center;
            color: #fff;
            line-height: 36px;
            margin: 16px 0 8px 0;
        }

        &:nth-of-type(1) {
            font-family: MiSans;
            font-weight: 400;
            font-size: 16px;
            line-height: 150%;
            letter-spacing: 0.5%;
            text-align: center;
            color: rgba(255, 255, 255, .5);
        }

    }

    &:hover {
        transform: scale(1.1);
        background: linear-gradient(180deg, #1E1E1E 60%, #EF88A3 234.88%);
        border-bottom: 3px solid #E2819B;
    }


}

.iconscroll {
    width: 43px;
    height: 47px;
    // margin-bottom: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-bottom: 64px;
}

.stat-card {
    background-color: #1f2937;
    padding: 24px;
    border-radius: 8px;
    text-align: center;
}

.stat-value {
    font-size: 30px;
    font-weight: bold;
    color: #ec4899;
    margin-bottom: 8px;
}

.stat-label {
    color: #9ca3af;
}

@media (max-width: 768px) {
    .download-section {
        flex-direction: column;
    }

    .download-content,
    .preview-image {
        width: 100%;
        margin-bottom: 32px;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .process-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
