<template>
  <div class="currency_select_mask" @click.self="close">
    <div class="currency_select_popup" :style="popupStyle" ref="popupRef">
      <div v-for="(item, idx) in currencyList" :key="item.code" class="currency_select_item"
        :class="{ selected: item.code === modelValue }" @click="select(item.code)">
        <img :src="item.flag" class="currency_flag" />
        <span class="currency_code">{{ item.code }}</span>
        <span class="currency_name">{{ item.name }}</span>
        <span v-if="item.code === modelValue" class="currency_selected_icon">✔</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { defineProps, defineEmits, computed, ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
  const props = defineProps({
    modelValue: String,
    anchor: Object // { top, left, width, height }
  })
  const emit = defineEmits(['update:modelValue', 'close'])

  const currencyList = [
    { code: 'USD', name: '美元', flag: 'https://pro-oss.pinkwallet.com/image/1374776145693204480.png' },
    { code: 'CAD', name: '加拿大', flag: 'https://pro-oss.pinkwallet.com/image/1374799011251838976.png' },
    { code: 'GBP', name: '英镑', flag: 'https://pro-oss.pinkwallet.com/image/1374776180739641344.png' }
  ]

  function select(code) {
    emit('update:modelValue', code)
    emit('close')
  }
  function close() {
    emit('close')
  }

  const popupRef = ref(null)
  const popupStyle = computed(() => {
    if (!props.anchor) return {}
    return {
      position: 'absolute',
      top: `${props.anchor.top + props.anchor.height + 6}px`,
      left: `${props.anchor.left}px`,
      minWidth: `${props.anchor.width}px`,
      zIndex: 1001
    }
  })

  const currency = ref('CAD')
  const showCurrencySelect = ref(false)
  const anchor = ref(null)
  const leftNameRef = ref(null)

  function updateAnchor() {
    if (leftNameRef.value) {
      const rect = leftNameRef.value.getBoundingClientRect()
      anchor.value = {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      }
    }
  }

  function openCurrencySelect() {
    nextTick(() => {
      const rect = leftNameRef.value.getBoundingClientRect()
      anchor.value = {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      }
      showCurrencySelect.value = true
    })
  }

  // 监听滚动和resize
  function handleScrollOrResize() {
    if (showCurrencySelect.value) {
      updateAnchor()
    }
  }

  onMounted(() => {
    window.addEventListener('scroll', handleScrollOrResize, true)
    window.addEventListener('resize', handleScrollOrResize)
  })
  onBeforeUnmount(() => {
    window.removeEventListener('scroll', handleScrollOrResize, true)
    window.removeEventListener('resize', handleScrollOrResize)
  })
</script>

<style lang="scss" scoped>
  .currency_select_mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: 1000;
  }

  .currency_select_popup {
    background: #232323;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
    min-width: 180px;
    padding: 0;
    overflow: hidden;
    border: 1px solid #333;
  }

  .currency_select_item {
    display: flex;
    align-items: center;
    padding: 12px 18px;
    cursor: pointer;
    color: #fff;
    font-size: 17px;
    position: relative;
    background: transparent;
    transition: background 0.2s;
    border-bottom: 1px solid #2d2d2d;

    &:last-child {
      border-bottom: none;
    }

    &:hover,
    &.selected {
      background: #313131;
    }

    .currency_flag {
      width: 28px;
      height: 28px;
      margin-right: 12px;
      border-radius: 50%;
      background: #fff;
      object-fit: cover;
    }

    .currency_code {
      font-weight: bold;
      margin-right: 8px;
    }

    .currency_name {
      color: #ccc;
    }

    .currency_selected_icon {
      margin-left: auto;
      color: #FF95B2;
      font-size: 18px;
      font-weight: bold;
    }
  }
</style>