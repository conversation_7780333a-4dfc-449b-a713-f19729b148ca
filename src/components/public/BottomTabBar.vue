<template>
    <view class="tabbar" id="tabbar-root">
        <view class="active-indicator" :style="indicatorStyle" />
        <view v-for="(item, index) in tabs" :key="index" :id="'tab-' + index"
            :class="['tab-item', { active: currentIndex === index }]" @click="changeTab(index, item.path)">
            <image class="icon" :src="currentIndex === index ? item.activeIcon : item.icon" mode="aspectFit" />
            <text class="label" v-show="currentIndex === index">{{ item.label }}</text>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        value: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            currentIndex: this.value,
            indicatorStyle: {
                width: '0px',
                transform: 'translateX(0px)'
            },
            tabs: [
                {
                    label: 'Home',
                    icon: "https://pro-oss.pinkwallet.com/image/1380608343981776896.png",
                    activeIcon: "https://pro-oss.pinkwallet.com/image/1380608267859353600.png",
                    path: 'index'
                },
                {
                    label: 'Markets',
                    icon: "https://pro-oss.pinkwallet.com/image/1380609152823943168.png",
                    activeIcon: "https://pro-oss.pinkwallet.com/image/1380609297800060928.png",
                    path: 'markets'
                },
                {
                    label: 'Futures',
                    icon: "https://pro-oss.pinkwallet.com/image/1380609451013791744.png",
                    activeIcon: "https://pro-oss.pinkwallet.com/image/1380609631444361216.png",
                    path: 'trade'

                },
                {
                    label: 'Stocks',
                    icon: "https://pro-oss.pinkwallet.com/image/1380610122156957696.png",
                    activeIcon: "https://pro-oss.pinkwallet.com/image/1380610177899257856.png",
                    path: 'us'

                },
                {
                    label: 'Assets',
                    icon: "https://pro-oss.pinkwallet.com/image/1380610241942085632.png",
                    activeIcon: "https://pro-oss.pinkwallet.com/image/1380610295499153408.png",
                    path: 'assets'

                }
            ]

        };
    },
    mounted() {
        this.$nextTick(() => {
            this.updateIndicator();
        });
    },
    watch: {
        value(val) {
            console.log(val);
            this.currentIndex = val;
            this.$nextTick(() => {
                this.updateIndicator();
            });
        }
    },
    methods: {
        changeTab(index, path) {
            // this.$Router.push({
            //     name: path
            // });
            this.currentIndex = index;
            this.$emit('input', index);
            this.$emit('change', index);
            this.$nextTick(() => {
                this.updateIndicator();
            });
        },
        updateIndicator() {
            const tabId = `#tab-${this.currentIndex}`;
            const barId = `#tabbar-root`;

            uni.createSelectorQuery()
                .in(this)
                .select(tabId)
                .boundingClientRect(tabRect => {
                    uni.createSelectorQuery()
                        .in(this)
                        .select(barId)
                        .boundingClientRect(barRect => {
                            if (tabRect && barRect) {
                                // const offset = tabRect.left - barRect.left;
                                let offset
                                console.log(this.currentIndex);
                                if (this.currentIndex == 0) {
                                    offset = -145
                                } else if (this.currentIndex == 1) {
                                    offset = -70
                                } else if (this.currentIndex == 2) {
                                    offset = 5
                                } else if (this.currentIndex == 3) {
                                    offset = 75
                                } else {
                                    offset = 150
                                }

                                this.indicatorStyle = {
                                    width: `${tabRect.width}px`,
                                    transform: `translateX(${offset}px)`,
                                    transition: 'all 0.3s ease'
                                };
                            }
                        })
                        .exec();
                })
                .exec();
        }

    }
};
</script>

<style lang="scss" scoped>
.tabbar {
    position: fixed;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 20rpx 0;
    // margin-top: -20rpx;

    // margin: 20rpx 0;
    background: #fff;
    box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
    z-index: 999;
    // position: relative;
    padding-bottom: constant(safe-area-inset-bottom);
    /* iOS <=11 */
    padding-bottom: env(safe-area-inset-bottom);
    /* 标准写法 */


    .active-indicator {
        position: absolute;
        bottom: constant(safe-area-inset-bottom);
        height: 80rpx;
        background-color: #ff82a3;
        border-radius: 999rpx;
        z-index: 0;
        transition: all 0.3s ease;
    }

    .tab-item {
        display: flex;
        align-items: center;
        padding: 30rpx 36rpx;
        border-radius: 999rpx;
        z-index: 1;
        transition: all 0.3s ease;
        gap: 20rpx;

        .icon {
            width: 40rpx;
            height: 40rpx;
            transition: all 0.3s ease;
            // opacity: 0.6;
            transform: translateX(0);
        }

        .label {
            font-size: 26rpx;
            // margin-left: 10rpx;
            color: #fff;
            transition: opacity 0.3s ease;
        }

        &.active .icon {
            transform: translateX(10rpx);
            opacity: 1;
        }
    }
}
</style>