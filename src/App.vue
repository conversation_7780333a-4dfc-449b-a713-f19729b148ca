<script>
const {
	VUE_APP_PAY_BACK,
	VUE_APP_COLLECTION_PATH,
	VUE_APP_UPLOAD_URL,
	VUE_APP_JAVA_UPLOADIMAGE,
	VUE_APP_JAVA_UPLOADVIDEO
} = process.env;
// const {
// 	origin,
// 	pathname
// } = document.location;
import router from "./router/index.js"
import { startSocket, onSocketOpen } from "./utils/websockets.js"
import { startSocketus, onSocketOpenus } from "./pagesA/project/us/websockets"
export default {
	data() {
		return {
			isFaceIDModalVisible: true, // 控制蒙版层显示
			isIOS: false, // 判断是否是iOS设备
			attemptsLeft: 1, // 限制验证次数
			lang: uni.getStorageSync("__language__") || "en", // 默认语言		
		}
	},
	globalData: {
		// url: 'http://prodaimzhan.hunanyanjie.com/h5/#/', //测试 
		url: 'https://appmzhan.yanjie.art/h5/#/', //线上
		socketApi: "ws://api-test.pinkwallet.xyz:81/kline-api/ws",
		socket_wallet_Api: "wss://test-wsapi.pinkwallet.xyz/pink-wallet/ws",
		apiUrl: "https://api-test.pinkwallet.xyz/blockchain/",
		active_url: "https://test-www.pinkwallet.xyz/active/#/broker",

		// apiUrl: "https://api-pro.pinkwallet.xyz/blockchain/",
		// isIPX: false, //是否为iphone X
		// isIphone: false,
		// url: origin + pathname, //支 付回掉地址
		// urlcollectionUrl: VUE_APP_COLLECTION_PATH, //测试 藏品室地址 
		// uploadUrl: VUE_APP_UPLOAD_URL, // 图片上传
		// java_uploadImage: VUE_APP_JAVA_UPLOADIMAGE, //java  图片上传
		// java_uploadVideo: VUE_APP_JAVA_UPLOADVIDEO, //java  视 频上传
		// // java_uploadVideoAudio:'http://127.0.0.1:8085/nms/dubbo/osscenter/appApi/uploadVideoAudio',//java  音频上传
		// loadingUrl: "https://cdn.yanjie.art/photo/loading_icon.gif", //加载动画
	},
	onLaunch: function () {
		// let RouterList = router.routers.routes;
		// for (let i = 0; i < RouterList.length; i++) { 
		// 	const e = RouterList[i];
		// }
		// 获取系统信息，判断是否是iOS设备
		// #ifdef APP-PLUS
		const systemInfo = uni.getSystemInfoSync();
		this.isIOS = systemInfo.platform === 'ios';
		console.log(this.isIOS);
		// 只有在 App + iOS 才为 true
		// this.isIOSApp = isApp && isIOS;

		if (this.isIOS) {
			console.log('到这来了');
			// this.startFaceIDAuthentication()
			// this.$Router.push({
			// 	name: 'face'
			// })
			// setTimeout(() => {
			uni.navigateTo({
				url: './pagesA/project/personal/face'
			});
			// }, 100);
		}
		// #endif
		// const whiteList = ["login", "register", "forget"];
		// // 假设当前页面路由信息在 this.$Route（或其他地方），我们取出当前页面的 name
		// const currentPageName = this.$Route; // 或者你用别的方式获取当前页面名称
		// console.log(currentPageName);

		// // 获取当前页面栈
		// const pages = getCurrentPages();
		// console.log(pages);

		// 如果当前页面不在白名单内，再判断 token
		// if (!whiteList.includes(currentPageName)) {
		// 	if (!this.$check.checkLogin()) {
		// 		this.$Router.push({
		// 			name: "login",
		// 		});
		// 	}
		// }

		startSocket()
		// startSocketus()
		uni.hideTabBar();
		// uni.setStorageSync("__language__", "en")
		// const langStorage = uni.getStorageSync("__language__")
		// if (!langStorage) {
		// 	this.$i18n.locale = "en"
		// } else {
		// 	this.$i18n.locale = langStorage
		// }
		this.loadLanguageStyles()
	},
	onShow: function () {
		// this.init()
		this.loadLanguageStyles()
		console.log('App Show');
		// #ifdef APP
		let appType = uni.getSystemInfoSync().platform
		const UmPush = uni.requireNativePlugin("spx-umpush");
		UmPush.initUmPush((res) => {
			console.log(res);
			uni.setStorageSync('deviceToken', res.deviceToken) // init
			// this.BindDevices()
		})
		console.log(UmPush)
		if (appType == 'android') {
			UmPush.setCustomPushChannel({
				soundName: "nftcnsound",//铃声名称，无需文件后缀
				channelId: "1",
				channelName: '测试',
				channelDesc: "渠道描述",
				enableLights: true,
				enableVibration: true,
				importance: 3,
				lockscreenVisibility: 0
			}, (errorCB) => {
				console.log(errorCB)
			});
			UmPush.onPushMessage((res) => {
				console.log(res); // message
			});
		}
		uni.hideTabBar();
		// #endif

		// #ifdef H5
		uni.hideTabBar({
			animation: false // 是否需要动画效果，默认为false
		});
		// #endif
	},
	onHide: function () {
		console.log('App Hide');
	},
	// 	const langStorage = uni.getStorageSync("__language__");
	// if (langStorage === "en") {
	//   require("./styles/en.css");
	// } else if (langStorage === "zh") {
	//   require("./styles/zh.css");
	// }
	mounted() {
	},
	methods: {
		startFaceIDAuthentication() {
			uni.startSoterAuthentication({
				requestAuthModes: ['facial'],
				challenge: '123456',
				authContent: '请用Face ID验证身份',
				success: (res) => {
					console.log('Face ID 验证成功:', res);
				},
				fail: (err) => {
					this.$Router.push({
						name: 'face'
					})
					console.log('Face ID 验证失败:', err);

					// setTimeout(() => {
					// 	this.startFaceIDAuthentication();
					// }, 100); // 稍微延迟再调起

				},
				complete: (res) => {
					console.log('Face ID 完成:', res);
				}
			});
		},
		init() {
			this.$nextTick(() => {
				// #ifdef APP-PLUS

				// const _url = plus.webview.all()[0].__uniapp_route
				// console.log(_url);

				setTimeout(() => {
					const pages = getCurrentPages();
					const currentPage = pages[pages.length - 1];
					const _url = currentPage.route;
				}, 0);

				// #endif
				// #ifndef APP-PLUS
				const _url = location.href
				// #endif


				// _url = http://localhost:8081/#/pages/project/login/login
				if (_url) {
					const whiteList = ["login", "register", "forget", "face	"];
					const isWhitePage = whiteList.some((name) => _url.includes(`/` + name));

					if (!isWhitePage && !this.$check.checkLogin()) {
						this.$Router.push({ name: "login" });
					}
				}
			})
		},
		loadLanguageStyles() {
			const lang = uni.getStorageSync("__language__") // 默认中文
			if (lang === "en") {
				require("@/styles/en.css");
			} else {
				require("@/styles/zh.css");
			}
			// 设置语言
		},
		changeLanguage(lang) {

			// 延迟一点，保证样式加载完成
			this.loadLanguageStyles();


		}
	}
};
</script>
<style lang="scss">
@import "@/uni_modules/uview-ui/index.scss";
@import "@/styles/public.css";
@import "@/styles/en.css";

.mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}


uni-app.inapp uni-page-head {
	display: none;
}

::v-deep .u-navbar .u-navbar-fixed {
	background: transparent !important;
}

::v-deep .u-hairline-border::after {
	border: none !important;
}

// 公共样式
/*每个页面公共css */
/* #ifdef H5 */
@font-face {
	font-family: 'HarmonyOS';
	src: url(https://cdn.yanjie.art/h5/ttf/HarmonyOS.ttf);
}

// html,
// body {
// 	// font-family: 'HarmonyOS';
// 	font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
// }

/* #endif */


uni-page-body,
page,
uni-app {
	// border-top:1rpx solid rgba($color: #D4DCE1, $alpha: 0.5);
	background: var(--main-bg-color);
	box-sizing: border-box;
	color: $uni-text-color;
	font-size: 32rpx;
}

::v-deep .u-checkbox__icon-wrap--square {
	border-radius: 8rpx !important;
}

::v-deep .u-input__input {
	color: #121212 !important;
}


::v-deep .u-select__header {
	background-color: #121212;
}

// ::v-deep .u-select__body__picker-view__item {
// 	color: var(--message-box-point-color);
// }

::v-deep .uni-picker-view-mask {
	height: 0;
}

::v-deep .u-select__body {
	background-color: #121212;
}

::v-deep .u-model {
	background-color: #241E15 !important;
	border-radius: 0rpx !important;

	.text_msg {
		color: #FFFFFF !important;
	}
}

.rotated {
	transform: rotate(180deg);
}

.search-box2 {
	margin-top: 10rpx;
	margin-right: 12rpx;
	background: #F1F1F1;
	border-radius: 50%;
	width: 100rpx;
	height: 100rpx;
	// height: fit-content;
	display: flex;
	align-items: center;
	justify-content: center;

	image {
		// padding: 25rpx;
		width: 40rpx;
		height: 40rpx;
	}
}

.search-box {
	margin-top: 10rpx;
	margin-right: 32rpx;
	background: #F1F1F1;
	border-radius: 50%;
	width: 100rpx;
	height: 100rpx;
	// height: fit-content;
	display: flex;
	align-items: center;
	justify-content: center;

	image {
		// padding: 25rpx;
		width: 40rpx;
		height: 40rpx;
	}
}


.uni-page-head__title {
	// color: #D8B662 !important;
	font-family: Gilroy-SemiBold;
	font-weight: 400;
}

.uni-page-head .uni-page-head-hd .uni-page-head-btn {
	// background-image: url(https://cdn.yanjie.art/image/20241025/563e60262c16b4a0b952a2c8cbc362d7_26x50.png) !important;
	background-size: contain !important;
	background-repeat: no-repeat !important;
	background-position: center !important;
	width: 50rpx !important;
	height: 50rpx !important;

	.uni-btn-icon {
		// color: transparent !important;
	}
}

// 等分布局
.flex_divide {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.item_divide {
	flex: 1
}

.flex_start {
	display: flex;
	display: -webkit-flex;
	justify-content: flex-start;
	-webkit-justify-content: flex-start;
	align-items: center;
	-webkit-align-items: center;
}

.flex-column {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.flex-column-end {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.flex-column-all {
	display: flex;
	justify-content: center;
	flex-direction: column;
	align-items: center;
}

/*弹性布局：水平垂直居中*/
.flex_all {
	display: flex;
	display: -webkit-flex;
	justify-content: center;
	-webkit-justify-content: center;
	align-items: center;
	-webkit-align-items: center;
}

//弹性布局：从末尾开始
.flex_end {
	display: flex;
	display: -webkit-flex;
	justify-content: flex-end;
	-webkit-justify-content: flex-end;
	align-items: center;
	-webkit-align-items: center;
}

/*水平居中弹性布局*/
.flex_x {
	display: flex;
	display: -webkit-flex;
	justify-content: center;
	-webkit-justify-content: center;
}

/* 垂直居中 */
.flex_y {
	display: flex;
	display: -webkit-flex;
	align-items: center;
	-webkit-align-items: center;
}

/* 水平分散对齐 */
.flex_between_x {
	display: flex;
	display: -webkit-flex;
	justify-content: space-between;
	-webkit-justify-content: space-between;
}

/* 上下分散对齐 */
.flex_between_y {
	display: flex;
	display: -webkit-flex;
	align-content: space-between;
	-webkit-align-content: space-between;
}

.iphoneX_bottom {
	height: 68rpx;
	width: 100%;
}

/* 清除浮动 */
.clear {
	zoom: 1;
}

.clear:after {
	content: '';
	display: block;
	clear: both;
}

/* 超出一行省略 */
.oneOver {
	display: inline-block;
	width: 86rpx;
	/*超出部分隐藏*/
	white-space: nowrap;
	overflow: hidden;
	/*不换行*/
	text-overflow: ellipsis;
	/*超出部分文字以...显示*/
}

/* 超出2行省略 */
.twoOver {
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
}

/* 超出3行省略 */
.threeOver {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}

.sk-wave {
	width: 80px;
	height: 50px;
	margin: 40rpx auto;
	text-align: center;
	font-size: 20px;
}

.sk-wave .sk-rect {
	background-color: #D8B662;
	height: 100%;
	width: 5px;
	margin-right: 4px;
	display: inline-block;
	-webkit-animation: sk-wave-stretch-delay 1.2s infinite ease-in-out;
	animation: sk-wave-stretch-delay 1.2s infinite ease-in-out;
}

.sk-wave .sk-rect-1 {
	-webkit-animation-delay: -1.2s;
	animation-delay: -1.2s;
}

.sk-wave .sk-rect-2 {
	-webkit-animation-delay: -1.1s;
	animation-delay: -1.1s;
}

.sk-wave .sk-rect-3 {
	-webkit-animation-delay: -1s;
	animation-delay: -1s;
}

.sk-wave .sk-rect-4 {
	-webkit-animation-delay: -0.9s;
	animation-delay: -0.9s;
}

.sk-wave .sk-rect-5 {
	-webkit-animation-delay: -0.8s;
	animation-delay: -0.8s;
}

@-webkit-keyframes sk-wave-stretch-delay {

	0%,
	40%,
	100% {
		-webkit-transform: scaleY(0.4);
		transform: scaleY(0.4);
	}

	20% {
		-webkit-transform: scaleY(1);
		transform: scaleY(1);
	}
}

@keyframes sk-wave-stretch-delay {

	0%,
	40%,
	100% {
		-webkit-transform: scaleY(0.4);
		transform: scaleY(0.4);
	}

	20% {
		-webkit-transform: scaleY(1);
		transform: scaleY(1);
	}
}

.loading_list {
	width: 100%;
	height: 45vh;
	display: flex;
	justify-content: center;
	align-items: center;

	.flex {
		display: flex;
		justify-content: center;
		align-items: center;

		.balls {
			width: 3em;
			display: flex;
			flex-flow: row nowrap;
			align-items: center;
			justify-content: space-between;

			view {
				width: 0.6em;
				height: 0.6em;
				border-radius: 50%;
				background-color: #D8B662;
				transform: translateY(-100%);
				animation: wave 0.8s ease-in-out alternate infinite;
			}

			view:nth-of-type(1) {
				animation-delay: -0.4s;
			}

			view:nth-of-type(2) {
				animation-delay: -0.2s;
			}
		}

		@keyframes wave {
			from {
				transform: translateY(-100%);
			}

			to {
				transform: translateY(100%);
			}
		}

	}

	.text {
		color: #A6A6A6;
		font-size: 22rpx;
		text-align: center;
		margin-top: 46rpx;
	}
}

.barHeight {
	height: var(--status-bar-height);
}

@keyframes spin-stretch {
	50% {
		transform: rotate(360deg) scale(0.4, 0.33);
		border-width: 16rpx;
	}

	100% {
		transform: rotate(720deg) scale(1, 1);
		border-width: 6rpx;
	}
}

/* 解决头条小程序组件内引入字体不生效的问题 */
/* #ifdef MP-TOUTIAO */
@font-face {
	font-family: uniicons;
	src: url('/static/uni.ttf');
}

/* #endif */
.loadding {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 120rpx;
	width: 100%;

	.gif {
		image {
			width: 100rpx;
		}
	}

	.dots {
		width: 2.9em;
		display: flex;
		flex-flow: row nowrap;
		align-items: center;
		justify-content: space-between;
	}

	.dots view {
		width: 0.6em;
		height: 0.6em;
		border-radius: 50%;
		background-color: #D8B662;
		animation: fade 0.8s ease-in-out alternate infinite;
	}

	.dots view:nth-of-type(1) {
		animation-delay: -0.4s;
	}

	.dots view:nth-of-type(2) {
		animation-delay: -0.2s;
	}

	@keyframes fade {
		from {
			opacity: 1;
		}

		to {
			opacity: 0;
		}
	}
}

/* 动画效果 */
.expand-slide-enter-active,
.expand-slide-leave-active {
	transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 打开时的初始状态，缩小并从右上角开始 */
.expand-slide-enter {
	opacity: 0;
	// transform: scale(0.8) translate(50%, -50%);
	transform: scaleY(1) translateY(0%);

}

/* 关闭时的最终状态，收缩回到右上角 */
.expand-slide-leave-to {

	opacity: 0;
	// transform: scale(0.5) translate(50%, -50%);
	transform: scaleY(0) translateY(-100%);

}
</style>