<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import util from '@/libs/util'

export default {
  name: 'app',
  watch: {
    '$i18n.locale': 'i18nHandle'
  },
  created() {
    this.i18nHandle(this.$i18n.locale)
    this.getCoinPair()
    this.getNetwork()
    this.gettoken()
    // this.getSimpleConfigs()
  },
  methods: {
    gettoken() {
      const token = localStorage.getItem("token");
      if (!token) return false;

      const payloadBase64 = token.split(".")[1];
      const payload = JSON.parse(
        decodeURIComponent(
          atob(payloadBase64)
            .split("")
            .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
            .join("")
        )
      );
      console.log(payload);
      if (payload.uid) {
        localStorage.setItem("uids", payload.uid);
      }
    },
    // async getSimpleConfigs() {
    //   let res = await this.$api.getSimpleConfig({
    //     pageNum: 1,
    //     pageSize: 1000
    //   })
    //   if (res.code == 200) {
    //     localStorage.setItem('simpleConfig', JSON.stringify(res.result))
    //   }
    // },
    async getCoinPair() {
      let res = await this.$api.getCoinPairEnum()
      if (res.code == 200) {
        localStorage.setItem('coinPair', JSON.stringify(this.toLabelValueOptions(res.result)))
      }
    },
    async getNetwork() {
      let res = await this.$api.getNetworkEnum()
      if (res.code == 200) {
        localStorage.setItem('Networks', JSON.stringify(this.toLabelValueOptions(res.result)))
      }
    },
    toLabelValueOptions(array) {
      return array.map(item => ({
        label: item,
        value: item
      }))
    },
    i18nHandle(val, oldVal) {
      util.cookies.set('lang', val)
      document.querySelector('html').setAttribute('lang', val)
    }
  }
}
</script>

<style lang="scss">
@import '~@/assets/style/public-class.scss';

.el-table th.el-table__cell {
  user-select: inherit !important;
}

.el-message-box {
  & .el-message-box__message p {
    word-break: break-all;
  }
}
</style>
