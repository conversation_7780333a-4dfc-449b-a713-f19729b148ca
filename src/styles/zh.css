@font-face {
	font-family: '<PERSON><PERSON>-SemiBold';
	src: local('PingFang SC');
	/* font-family: '<PERSON>roy-SemiBold'; */
	/* src: url(https://res.pinkwallet.com/font/HarmonyOS_Sans_SC_Bold.ttf);  */
}
/* https://res.pinkwallet.com/font/<PERSON><PERSON>-Black.ttf */

@font-face {
	/* font-family: 'Gilroy-Medium';
	src: url(https://res.pinkwallet.com/font/HarmonyOS_Sans_SC_Regular.ttf); */
	font-family: '<PERSON><PERSON>-Medium';
	src: local('PingFang SC');
}


@font-face {
	font-family: '<PERSON><PERSON>-Bold';
	/* font-family: 'PingFang SC'; */
	src: local('PingFang SC');
	/* font-family: 'Gilroy-Bold'; */
	/* src: url(https://res.pinkwallet.com/font/HarmonyOS_Sans_SC_Bold.ttf);  */

}

@font-face {
	font-family: '<PERSON><PERSON>-ExtraBold';
	src: local('PingFang SC');
	/* src: url(https://res.pinkwallet.com/font/HarmonyOS_Sans_SC_Black.ttf); */
	/* font-family: 'PingFang SC'; */
}