/**
 * 通用uni-app网络请求
 * 基于 Promise 对象实现更简单的 request 使用方式，支持请求和响应拦截
 */
//json对象转换成json字符

import function_tools from "@/common/public.js";
import jsrsasign from "jsrsasign";
import md5 from "js-md5";

var { VUE_APP_JAVA_API_URL } = process.env;
const spxRsa = uni.requireNativePlugin("spx-rsa");
const that = this; // 先保存 this

let navigatingToLogin = false;
let loginTimer = null;

function goToLoginOnce() {
  if (navigatingToLogin) return;

  navigatingToLogin = true;

  // 清掉可能的已有定时器
  if (loginTimer) clearTimeout(loginTimer);

  // 设置一个短暂锁定（防并发）
  loginTimer = setTimeout(() => {
    navigatingToLogin = false;
    loginTimer = null;
  }, 3000); // 可根据需要调整锁定时间

  uni.navigateTo({
    url: "/pages/project/login/login",
  });
}

const lang = uni.getStorageSync("__language__");
const languageCode =
  lang === "en" ? "en-US" : lang === "zh" ? "zh-CN" : "zh-HK";
console.log(languageCode, "lang"); // 例如 "en-US" 或 "zh-CN"

export default {
  // 配置初始化
  config: {
    //发版APP必改
    // #ifdef APP
    baseUrl_Java: "https://api-test.pinkwallet.xyz/blockchain/", //正式
    // baseUrl_Java:'http://api-test.nftcn.com.cn/nms/dubbo/', //测试
    // #endif
    // #ifdef H5
    baseUrl_Java: VUE_APP_JAVA_API_URL, //java接口正式 配置读取
    // #endif
    header: {
      Authorization: " Bearer " + uni.getStorageSync("token"), //自定义请求头信息
      // 'Authorization': "", //自定义请求头信息
      //这里增加语言的请求头
    },
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  },

  //拦截器初始化
  interceptor: {
    request(config) {
      console.log("发起请求中");
      uni.showLoading({
        title: "加载中",
      });
    },
    response(res) {
      uni.hideLoading();
      let isNavigatingToLogin = false;

      switch (res.statusCode) {
        case 200:
          // console.log('200状态', res.data)
          return res.data;
          break;
        case 400:
          uni.showToast({
            title: "抱歉，网络开小差啦~",
            icon: "none",
            duration: 2000,
          });
          break;
        case 401:
          //#ifdef H5
          goToLoginOnce();
          //#endif
          break;
        case 403:
          uni.showToast({
            title: "拒绝访问",
            icon: "none",
            duration: 2000,
          });
          break;
        case 404:
          uni.showToast({
            title: "拒绝访问",
            icon: "none",
            duration: 2000,
          });
          break;
        case 405:
          uni.showToast({
            title: "拒绝访问",
            icon: "none",
            duration: 2000,
          });
          break;
        case 408:
          uni.showToast({
            title: "拒绝访问",
            icon: "none",
            duration: 2000,
          });
          break;
        case 500:
          uni.showToast({
            title: "系统异常",
            icon: "none",
            duration: 2000,
          });
          break;
        case 501:
          uni.showToast({
            title: "抱歉，网络开小差啦~",
            icon: "none",
            duration: 2000,
          });
          break;
        case 502:
          uni.showToast({
            title: "抱歉，网络开小差啦~",
            icon: "none",
            duration: 2000,
          });
          break;
        case 503:
          uni.showToast({
            title: "抱歉，网络开小差啦~",
            icon: "none",
            duration: 2000,
          });
          break;
        case 504:
          uni.showToast({
            title: "网络超时，请稍后重试",
            icon: "none",
            duration: 2000,
          });
          break;
        case 505:
          uni.showToast({
            title: "http版本不支持该请求",
            icon: "none",
            duration: 2000,
          });
          break;
        default: //跳转程序员错误也并重试
          uni.showToast({
            title: "当前过于火爆，请稍后重试",
            icon: "none",
            duration: 2000,
          });
          break;
      }
    },
  },
  async apiRequest_java(method, url, data) {
    let _this = this;
    var nftcnApiAppId,
      nftcnApiNonce,
      nftcnApiSignature,
      nftcnApiAppSecret,
      nftcnApiTimestamp;
    nftcnApiAppId = "nftcn-web-h5";
    nftcnApiTimestamp = new Date().getTime();
    nftcnApiNonce = isRandom();
    nftcnApiAppSecret = md5(nftcnApiAppId + nftcnApiNonce).toUpperCase();
    var map = {
      nftcnApiAppId: nftcnApiAppId,
      nftcnApiAppSecret: nftcnApiAppSecret,
      nftcnApiNonce: nftcnApiNonce,
      nftcnApiTimestamp: nftcnApiTimestamp,
    };
    Object.assign(map, JSON.parse(JSON.stringify(data || null)));
    deleteEmptyProperty(map);
    let body = changeDataType(function_tools.objKeySort(map));
    nftcnApiSignature = getNftcnApiSignature(body);
    this.config.header["Authorization"] = uni.getStorageSync("token"); //自定义请求头信息
    // this.config.header["Authorization"] = "" //自定义请求头信息
    this.config.header["lang"] =
      uni.getStorageSync("__language__") === "en"
        ? "en-US"
        : uni.getStorageSync("__language__") === "zh"
        ? "zh-CN"
        : "zh-HK";
    // lang: uni.getStorageSync("__language__") === "en" ? "en-US" : uni.getStorageSync("__language__") === "zh" ? "zh-CN" : "zh-HK";
    //
    this.config.header["nftcnApiTimestamp"] = nftcnApiTimestamp;
    this.config.header["nftcnApiNonce"] = nftcnApiNonce;
    this.config.header["nftcnApiSignature"] = nftcnApiSignature;
    this.config.header["nftcnApiAppId"] = nftcnApiAppId;
    this.config.header["nftcnApiAppSecret"] = nftcnApiAppSecret;
    let [err, res] = await uni.request({
      method: method,
      header: this.config.header,
      url: this.config.baseUrl_Java + url,
      data,
      // timeout:60000, // 超时时间，单位为毫秒，这里设置为30秒
    });
    if (err) {
      uni.showToast({
        title: "当前过于火爆，请稍后重试",
        icon: "none",
        duration: 2000,
      });
    } else {
      let res1 = this.interceptor.response(res);
      reslog(url, data, res1);
      return res1;
    }
  },
  async apiRequest_java_ios(method, url, data) {
    let _this = this;
    var nftcnApiAppId,
      nftcnApiNonce,
      nftcnApiSignature,
      nftcnApiAppSecret,
      nftcnApiTimestamp;
    nftcnApiAppId = "nftcn-web-h5";
    nftcnApiTimestamp = new Date().getTime();
    nftcnApiNonce = isRandom();
    nftcnApiAppSecret = md5(nftcnApiAppId + nftcnApiNonce).toUpperCase();
    var map = {
      nftcnApiAppId: nftcnApiAppId,
      nftcnApiAppSecret: nftcnApiAppSecret,
      nftcnApiNonce: nftcnApiNonce,
      nftcnApiTimestamp: nftcnApiTimestamp,
    };
    Object.assign(map, JSON.parse(JSON.stringify(data || null)));
    deleteEmptyProperty(map);
    let body = changeDataType(function_tools.objKeySort(map));
    nftcnApiSignature = spxRsa.SHA256withRSASign({
      privateKey: this.config.privateKey,
      data: body,
    });
    this.config.header["Authorization"] = uni.getStorageSync("token"); //自定义请求头信息
    this.config.header["nftcnApiTimestamp"] = nftcnApiTimestamp;
    this.config.header["lang"] =
      uni.getStorageSync("__language__") === "en"
        ? "en-US"
        : uni.getStorageSync("__language__") === "zh"
        ? "zh-CN"
        : "zh-HK";
    this.config.header["nftcnApiNonce"] = nftcnApiNonce;
    this.config.header["nftcnApiSignature"] = nftcnApiSignature;
    this.config.header["nftcnApiAppId"] = nftcnApiAppId;
    this.config.header["nftcnApiAppSecret"] = nftcnApiAppSecret;
    let [err, res] = await uni.request({
      method: method,
      header: this.config.header,
      url: this.config.baseUrl_Java + url,
      data,
      // timeout:60000, // 超时时间，单位为毫秒，这里设置为30秒
    });
    if (err) {
      uni.showToast({
        title: "当前过于火爆，请稍后重试",
        icon: "none",
        duration: 2000,
      });
    } else {
      let res1 = this.interceptor.response(res);
      reslog(url, data, res1);
      return res1;
    }
    return res1;
  },
  get(url, data) {
    return this.apiRequest("get", url, data);
  },
  //发送数据
  post(url, data) {
    return this.apiRequest("POST", url, data);
  },
  //java后台接口
  get_java(url, data) {
    return this.apiRequest_java("get", url, data);
  },
  //发送数据
  post_java(url, data) {
    // #ifdef APP
    if (uni.getSystemInfoSync().platform == "android") {
      return this.apiRequest_java("POST", url, data);
    } else {
      return this.apiRequest_java_ios("POST", url, data);
    }
    // #endif
    // #ifdef H5
    return this.apiRequest_java("POST", url, data);
    // #endif
  },
};
function hexToBase64(hexString) {
  const bytes = new Uint8Array(
    hexString.match(/.{1,2}/g).map((byte) => parseInt(byte, 16))
  );
  return btoa(String.fromCharCode.apply(null, bytes));
}
/**
 * 请求相应日志记录
 */
function reslog(url, data, res) {
  // console.log("<<请求接口>>：" + url)
  // console.log("<<请求参数>>：" + JSON.stringify(data))
  // console.log("<<响应结果>>：" + JSON.stringify(res))
}

function changeDataType(obj) {
  let str = "";
  if (typeof obj == "object") {
    for (let i in obj) {
      if (typeof obj[i] != "function" && typeof obj[i] != "object") {
        str += i + "=" + obj[i] + "&";
      } else if (typeof obj[i] == "object") {
        // nextStr = '';
        str += changeDataType(i, obj[i]);
      }
    }
  }
  return str.replace(/&$/g, "");
}

function deleteEmptyProperty(object) {
  for (var i in object) {
    var value = object[i];
    if (typeof value === "object") {
      if (Array.isArray(value)) {
        if (value.length == 0) {
          delete object[i];
          console.log("delete Array", i);
          continue;
        }
      }
      deleteEmptyProperty(value);
      if (isEmpty(value)) {
        delete object[i];
      }
    } else {
      if (value === "" || value === null || value === undefined) {
        delete object[i];
        // console.log('delete ', i);
      } else {
        // console.log('check ', i, value);
      }
    }
  }
}

function isEmpty(object) {
  for (var name in object) {
    return false;
  }
  return true;
}

function isRandom() {
  let str = "0123456789";
  let num = "";
  for (let i = 0; i < 21; i++) {
    num += str.charAt(Math.floor(Math.random() * str.length));
  }
  return num;
}
function getNftcnApiSignature(str) {
  //此处操作与后端约定参数
  // 创建RSAKey对象
  var rsa = new jsrsasign.RSAKey();
  //因为后端提供的是pck#8的密钥对，所以这里使用 KEYUTIL.getKey来解析密钥
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  // 将密钥转码
  rsa = jsrsasign.KEYUTIL.getKey(k);
  // 创建Signature对象，设置签名编码算法
  var sig = new jsrsasign.KJUR.crypto.Signature({
    alg: "SHA256withRSA",
  });
  // 初始化
  sig.init(rsa);
  // 传入待加密字符串
  sig.updateString(str);
  // 生成密文
  var sign = jsrsasign.hextob64(sig.sign());
  // var timestamp = new Date().getTime();
  // console.log("RSA时间戳："+timestamp)
  // var sign = hexToBase64(sig.sign())
  // var timestamp1 = new Date().getTime();
  // console.log("RSA时间戳结束："+timestamp1)
  // console.log("耗时：",timestamp1-timestamp)
  // 对加密后内容进行URI编码
  //把参数与密文拼接好，返回
  var params = sign;

  return params;
}
