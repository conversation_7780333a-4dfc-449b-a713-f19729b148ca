/**
 * 将业务所有接口统一起来便于维护
 * requestPath:请求接口路径，
 * data：请求参数对象
 */
import http from "./interface";

export default {
  //路由请求地址
  requestPath: {
    sendPhoneVerifyCode: "pinkwallet/appApi/captcha/sendPhoneCaptcha", //发送手机验证码接口
    sendMailCaptcha: "pinkwallet/appApi/captcha/sendMailCaptcha",
    getAccToken: "pinkwallet/appApi/accToken/getAccToken",
    getWebHookResult: "pinkwallet/appApi/accToken/getWebHookResult",
    getApplicantInfo: "pinkwallet/appApi/accToken/getApplicantInfo",
    register: "pinkwallet/appApi/userRegister/doRegister",
    login: "pinkwallet/appApi/userLogin/doLogin",
    instrumentsAllcoin: "pinkwallet/appApi/b2c2/instruments", //获取所有交易对
    requestForQuote: "pinkwallet/appApi/b2c2/requestForQuote", //获取询价单
    orderInfo: "pinkwallet/appApi/b2c2/orderInfo", //获取订单信息
    flashExchange: "pinkwallet/appApi/b2c2/flashExchange",
    withdraw: "pinkwallet/appApi/coin/transfer/withdraw",
    getNetwork: "pinkwallet/appApi/coin/walletAddress/getNetwork",
    getWalletAddress: "pinkwallet/appApi/coin/walletAddress/getWalletAddress",
    querycoinWallet: "pinkwallet/appApi/b2c2/coinWallet", // app-查询自己币对和可用余额信息
    coinPair: "pinkwallet/appApi/b2c2/coinPair", // app-查询币对的组合
    Askrfq: "pinkwallet/appApi/b2c2/rfq", // app-闪兑询价
    addOrder: "pinkwallet/appApi/b2c2/addOrder", // app-闪兑下单
    bannerList: "pinkwallet/appApi/banner/bannerList", // banner
    signUpAndSignIn: "pinkwallet/appApi/sign/signUpAndSignIn", // 谷歌登录
    symbolListPaged: "pinkwallet/appApi/coinSymbolConfig/symbolListPaged", // 分页查询币对
    userCoinList: "pinkwallet/appApi/coinSymbolConfig/userCoinList", // 用户币对列表
    getTransactionPaged:
      "pinkwallet/appApi/coin/transaction/getTransactionPaged",
    userAssetsFiat: "pinkwallet/appApi/coinSymbolConfig/userAssets", // 用户资产查询-最终汇算成一种法币
    priceLine: "pinkwallet/appApi/b2c2/priceLine", // app-查询币对涨幅
    searchUserWithdrawAccountPaged:
      "pinkwallet/appApi/userWithdrawAccount/searchUserWithdrawAccountPaged", // 查询提现账户
    deleteUserWithdrawAccount:
      "pinkwallet/appApi/userWithdrawAccount/deleteUserWithdrawAccount", // 删除账户
    addOrUpdateUserWithdrawAccount:
      "pinkwallet/appApi/userWithdrawAccount/addOrUpdateUserWithdrawAccount", // 添加修改账户
    getWithdrawFee: "pinkwallet/appApi/coin/transfer/getWithdrawFee", // 获取手续费
    userAvailableCoinList:
      "pinkwallet/appApi/coinSymbolConfig/userAvailableCoinList", // 用户可用余额
    getWithdrawLimit: "pinkwallet/appApi/coin/transfer/getWithdrawLimit", // 查询提现金额限制
    getUserInfo: "pinkwallet/appApi/user/getUserInfo", // 获取用户信息
    forgotPassByEmail: "pinkwallet/appApi/user/forgotPassByEmail", // 忘记密码，通过邮箱修改密码
    changePasswordByEmail: "pinkwallet/appApi/user/changePasswordByEmail", // 通过邮箱修改密码
    changeEmail: "pinkwallet/appApi/user/changeEmail", // 变更邮箱
    BindDevice: "pinkwallet/appApi/userDevice/bindDevice",
    unBindDevice: "pinkwallet/appApi/userDevice/unBindDevice",
    fineGrainedCoinPrice: "pinkwallet/appApi/fineGrainedCoinPrice/price", // swap获取汇率
    priceGn: "pinkwallet/appApi/fineGrainedCoinPrice/priceGn", // 相比昨日
    userWalletSpotLine: "pinkwallet/appApi/coinSymbolConfig/userWalletSpotLine", // 用户资产折现
    innerMsg: "pinkwallet/appApi/innerMsg/list", // 站内信
    innerMsgreadAll: "pinkwallet/appApi/innerMsg/readAll",
    innerMsgread: "pinkwallet/appApi/innerMsg/read",
    signOut: "pinkwallet/appApi/sign/signOut", // 退出登录
    existCoinPair: "pinkwallet/appApi/b2c2/existCoinPair", // app-判断币对是否存在
    amountPriceGn: "pinkwallet/appApi/b2c2/amountPriceGn", // app-余额相比昨天涨幅
    setLg: "pinkwallet/appApi/user/setLg", // 设置用户语言
    getWithDrawPrecision:
      "pinkwallet/appApi/coin/transfer/getWithDrawPrecision", // 获取精度
    paymentMethods: "pinkwallet/appApi/quickBuyCoin/paymentMethods", // 付款方式
    getfiats: "pinkwallet/appApi/quickBuyCoin/fiats", // 法定货币
    cryptocurrencies: "pinkwallet/appApi/quickBuyCoin/cryptocurrencies", // 加密
    retrieveQuotes: "pinkwallet/appApi/quickBuyCoin/retrieveQuotes", // 报价
    createBuyOrder: "pinkwallet/appApi/quickBuyCoin/createBuyOrder", // 创建buy的订单
    setupMfaSecret: "pinkwallet/appApi/userMfa/setupMfaSecret", // 生成 secret
    verifyCode: "pinkwallet/appApi/userMfa/verifyCode", // 校验 code
    bindingSecret: "pinkwallet/appApi/userMfa/bindingSecret", // 绑定 secret
    c2cuserInfo: "pinkwallet/appApi/c2c/baseInfo/userInfo", // c2c用户信息
    c2csupportCoin: "pinkwallet/appApi/c2c/baseInfo/supportCoin", // c2c支持的币
    shopOrderList: "pinkwallet/appApi/c2c/shopOrder/list", // 订单列表
    c2corderlist: "pinkwallet/appApi/c2c/order/list", // 我的订单列表
    c2cOrderCreate: "pinkwallet/appApi/c2c/order/create", //创建ctc用户订单
    c2cShopCancel: "pinkwallet/appApi/c2c/shop/cancel", // 解除商家
    c2cShopApply: "pinkwallet/appApi/c2c/shop/apply", // 申请成为商家
    c2cAddUpdate: "pinkwallet/appApi/c2c/pay/addUpdate", // 添加修改收款方式
    c2cPayList: "pinkwallet/appApi/c2c/pay/lis", // 收款方式列表
    c2cShopOrderAdd: "pinkwallet/appApi/c2c/shopOrder/add", // 创建订单
    c2cPayRemove: "pinkwallet/appApi/c2c/pay/remove", // 删除收款方式
    c2cOrderConfirmReceipt: "pinkwallet/appApi/c2c/order/confirmReceipt", // 确认收货
    c2cOrderCancel: "pinkwallet/appApi/c2c/order/cancel", // 取消订单
    c2cShopIceInfo: "pinkwallet/appApi/c2c/shop/iceInfo", // 查询冻结信息
    c2cShopOrderCancel: "pinkwallet/appApi/c2c/shopOrder/cancel", // 卖家取消订单
    c2cBaseInfoOnLine: "pinkwallet/appApi/c2c/baseInfo/onLine", // 获取在线客服信息
    c2cShopOrderMyList: "pinkwallet/appApi/c2c/shopOrder/myList", // 获取我的订单列表
    c2cSupportPayType: "pinkwallet/appApi/c2c/pay/supportPayType", // 查询支持的支付方式
    C2CuploadImage: "pinkwallet/appApi/oss/uploadImage", // 上传图片
    c2corderalreadyPaid: "pinkwallet/appApi/c2c/order/alreadyPaid", // 我已付款
    c2cshopiceback: "pinkwallet/appApi/c2c/shop/ice/back", // 订单退回
    contractSymbolList: "pinkexchange/appApi/contract/contractSymbolList",
    createOrder: "pinkexchange/appApi/contract/order/createOrder",
    symbolAvailableBalance:
      "pinkwallet/appApi/asset/account/symbolAvailableBalance", // 查询可用余额
    currentPosition: "pinkexchange/appApi/contract/position/currentPosition", // 查询当前仓位
    delegatingOrder: "pinkexchange/appApi/contract/order/delegatingOrder", // 查询委托订单
    orderStatus: "pinkexchange/appApi/contract/order/orderStatus", // 查询订单状态
    cancelOrder: "pinkexchange/appApi/contract/order/cancelOrder", // 撤单
    closePosition: "pinkexchange/appApi/contract/position/closePosition", // 订单平仓
    otoPosition: "pinkexchange/appApi/contract/position/otoPosition", // 止盈止损
    resetOtoPosition: "pinkexchange/appApi/contract/position/resetOtoPosition", // 重置止盈止损
    historyOrder: "pinkexchange/appApi/contract/order/historyOrder", // 订单历史
    getUserInfos: "pinkexchange/appApi/user/getUserInfo", // 获取用户信息
    changeMergeStatus: "pinkexchange/appApi/contract/changeMergeStatus", // 合并订单
    getContractSelfList: "pinkexchange/appApi/contract/getContractSelfList", // 自选
    getContractSymbol: "pinkexchange/appApi/contract/getContractSymbol", // 获取合约详情
    addContractSelf: "pinkexchange/appApi/contract/addContractSelf", // 添加自选
    deleteContractSelf: "pinkexchange/appApi/contract/delContractSelf", // 删除自选
    isSelect: "pinkexchange/appApi/contract/isSelect", // 是否自选
    assettransfer: "pinkwallet/appApi/asset/transfer/transfer", // 资产转账
    setLoginPassword: "pinkwallet/appApi/user/setLoginPassword", // 设置登录密码
    getContractHighRank: "pinkexchange/appApi/contract/getContractHighRank", // 获取涨排行榜
    getContractLowRank: "pinkexchange/appApi/contract/getContractLowRank", // 获取跌排行榜
    getContractAmountRank: "pinkexchange/appApi/contract/getContractAmountRank", // 获取24交易额排行榜
    transferRecordList: "pinkwallet/appApi/asset/transfer/transferRecordList", // 获取转账记录
    stockmarkettimeline: "pinkwalletstock/appApi/stock/market/timeline", // 分时图，首页涨跌stock
    selfStockList: "pinkwalletstock/appApi/stock/ranking/selfStockList", // 自选股
    getHotStock: "pinkwalletstock/appApi/stock/base/getHotStock", // 热门股票
    dealAmountRank: "pinkwalletstock/appApi/stock/ranking/dealAmountRank", // 24小时交易额us
    kycSelete: "pinkwallet/appApi/kyc/apply/query", // 初级认证查询
    kycAdd: "pinkwallet/appApi/kyc/apply/add", // 初级认证申请
    authInfo: "pinkwallet/appApi/auth/info", // 获取认证信息
    changePassword: "pinkwallet/appApi/userPassword/changePassword", // 修改密码
    sendEmailCode: "pinkwallet/appApi/userEmail/sendEmailCode", // 验证邮箱验证码
    updateEmail: "pinkwallet/appApi/userEmail/updateEmail", // 修改邮箱
    emailCodeCompare: "pinkwallet/appApi/userEmail/emailCodeCompare", // 校验邮箱验证码
    stockList: "pinkwalletstock/appApi/stock/ranking/list", // 股票列表
    tradeTick: "pinkwalletstock/appApi/stock/market/tradeTick", // 逐笔成交行情-10分钟更新一次-第一次进来查询 后续走ws推送
    quoteStockTrade: "pinkwalletstock/appApi/stock/order/quoteStockTrade", // 获取股票交易行情 进来调用
    realtimeQuote: "pinkwalletstock/appApi/stock/market/realtimeQuote", // 实时行情
    marketkline: "pinkwalletstock/appApi/stock/market/kline", // K线图
    addSelfStock: "pinkwalletstock/appApi/stock/ranking/addSelfStock", // 添加自选
    delSelfStock: "pinkwalletstock/appApi/stock/ranking/delSelfStock", // 删除自选
    usdepthItems: "pinkwalletstock/appApi/stock/market/depthItems", //   获取深度
    stockOrderCreate: "pinkwalletstock/appApi/stock/order/create", // 创建订单
    stockmarketstatus: "pinkwalletstock/appApi/stock/market/status", // 获取市场状态
    setUserName: "pinkwallet/appApi/userInfo/getUserName", // 获取用户名
    setUserPicture: "pinkwallet/appApi/userInfo/getUserPicture", // 修改头像
    getCoinList: "pinkwallet/appApi/coin/getCoinList", //查询币种列表
    getCoinLike: "pinkwallet/appApi/coin/getCoinLike", //获取喜好
    tradeLog: "pinkwalletstock/appApi/stock/order/tradeLog", // 成交纪录
    stockorder: "pinkwalletstock/appApi/stock/order/order", // 委托历史订单
    stockhold: "pinkwalletstock/appApi/stock/order/hold", // 持仓
    stockcancel: "pinkwalletstock/appApi/stock/order/cancel", // 撤单
    increaseRate:"pinkwallet/appApi/asset/increaseRate",
    assetRecordList:"pinkwallet/appApi/asset/assetRecordList",
    accountChart:"pinkwallet/appApi/asset/accountChart",
    amount :"pinkwallet/appApi/asset/amount",
    stockInfo: "pinkwalletstock/appApi/stock/market/stockInfo", // 获取股票详情
  },
  stockInfo(data) {
    return http.post_java(this.requestPath.stockInfo, data);
  },
  stockorder(data) {
    return http.post_java(this.requestPath.stockorder, data);
  },
  stockhold(data) {
    return http.post_java(this.requestPath.stockhold, data);
  },
  stockcancel(data) {
    return http.post_java(this.requestPath.stockcancel, data);
  },
  tradeLog(data) {
    return http.post_java(this.requestPath.tradeLog, data);
  },
  stockmarketstatus(data) {
    return http.post_java(this.requestPath.stockmarketstatus, data);
  },
  stockOrderCreate(data) {
    return http.post_java(this.requestPath.stockOrderCreate, data);
  },
  usdepthItems(data) {
    return http.post_java(this.requestPath.usdepthItems, data);
  },
  delSelfStock(data) {
    return http.post_java(this.requestPath.delSelfStock, data);
  },
  addSelfStock(data) {
    return http.post_java(this.requestPath.addSelfStock, data);
  },
  marketkline(data) {
    return http.post_java(this.requestPath.marketkline, data);
  },
  realtimeQuote(data) {
    return http.post_java(this.requestPath.realtimeQuote, data);
  },
  quoteStockTrade(data) {
    return http.post_java(this.requestPath.quoteStockTrade, data);
  },
  tradeTick(data) {
    return http.post_java(this.requestPath.tradeTick, data);
  },
  stockList(data) {
    return http.post_java(this.requestPath.stockList, data);
  },
  dealAmountRank(data) {
    return http.post_java(this.requestPath.dealAmountRank, data);
  },
  getHotStock(data) {
    return http.post_java(this.requestPath.getHotStock, data);
  },
  selfStockList(data) {
    return http.post_java(this.requestPath.selfStockList, data);
  },
  stockmarkettimeline(data) {
    return http.post_java(this.requestPath.stockmarkettimeline, data);
  },
  transferRecordList(data) {
    return http.post_java(this.requestPath.transferRecordList, data);
  },
  getContractAmountRank(data) {
    return http.post_java(this.requestPath.getContractAmountRank, data);
  },
  getContractLowRank(data) {
    return http.post_java(this.requestPath.getContractLowRank, data);
  },
  getContractHighRank(data) {
    return http.post_java(this.requestPath.getContractHighRank, data);
  },
  setLoginPassword(data) {
    return http.post_java(this.requestPath.setLoginPassword, data);
  },
  assettransfer(data) {
    return http.post_java(this.requestPath.assettransfer, data);
  },
  isSelect(data) {
    return http.post_java(this.requestPath.isSelect, data);
  },
  addContractSelf(data) {
    return http.post_java(this.requestPath.addContractSelf, data);
  },
  deleteContractSelf(data) {
    return http.post_java(this.requestPath.deleteContractSelf, data);
  },
  getContractSymbol(data) {
    return http.post_java(this.requestPath.getContractSymbol, data);
  },
  getContractSelfList(data) {
    return http.post_java(this.requestPath.getContractSelfList, data);
  },
  changeMergeStatus(data) {
    return http.post_java(this.requestPath.changeMergeStatus, data);
  },
  getUserInfos(data) {
    return http.post_java(this.requestPath.getUserInfos, data);
  },
  historyOrder(data) {
    return http.post_java(this.requestPath.historyOrder, data);
  },
  resetOtoPosition(data) {
    return http.post_java(this.requestPath.resetOtoPosition, data);
  },
  otoPosition(data) {
    return http.post_java(this.requestPath.otoPosition, data);
  },
  closePosition(data) {
    return http.post_java(this.requestPath.closePosition, data);
  },
  cancelOrder(data) {
    return http.post_java(this.requestPath.cancelOrder, data);
  },
  orderStatus(data) {
    return http.post_java(this.requestPath.orderStatus, data);
  },
  delegatingOrder(data) {
    return http.post_java(this.requestPath.delegatingOrder, data);
  },
  currentPosition(data) {
    return http.post_java(this.requestPath.currentPosition, data);
  },
  symbolAvailableBalance(data) {
    return http.post_java(this.requestPath.symbolAvailableBalance, data);
  },
  createOrder(data) {
    return http.post_java(this.requestPath.createOrder, data);
  },
  contractSymbolList(data) {
    return http.post_java(this.requestPath.contractSymbolList, data);
  },
  c2cshopiceback(data) {
    return http.post_java(this.requestPath.c2cshopiceback, data);
  },
  c2corderalreadyPaid(data) {
    return http.post_java(this.requestPath.c2corderalreadyPaid, data);
  },
  C2CuploadImage(data) {
    return http.post_java(this.requestPath.C2CuploadImage, data);
  },
  c2cSupportPayType(data) {
    return http.post_java(this.requestPath.c2cSupportPayType, data);
  },
  c2cShopOrderMyList(data) {
    return http.post_java(this.requestPath.c2cShopOrderMyList, data);
  },
  c2cBaseInfoOnLine(data) {
    return http.post_java(this.requestPath.c2cBaseInfoOnLine, data);
  },
  c2cShopOrderCancel(data) {
    return http.post_java(this.requestPath.c2cShopOrderCancel, data);
  },
  c2cShopIceInfo(data) {
    return http.post_java(this.requestPath.c2cShopIceInfo, data);
  },
  c2cOrderCancel(data) {
    return http.post_java(this.requestPath.c2cOrderCancel, data);
  },
  c2cOrderConfirmReceipt(data) {
    return http.post_java(this.requestPath.c2cOrderConfirmReceipt, data);
  },
  c2cPayRemove(data) {
    return http.post_java(this.requestPath.c2cPayRemove, data);
  },
  c2cShopOrderAdd(data) {
    return http.post_java(this.requestPath.c2cShopOrderAdd, data);
  },
  c2cPayList(data) {
    return http.post_java(this.requestPath.c2cPayList, data);
  },
  c2cAddUpdate(data) {
    return http.post_java(this.requestPath.c2cAddUpdate, data);
  },
  c2cShopApply(data) {
    return http.post_java(this.requestPath.c2cShopApply, data);
  },
  c2cShopCancel(data) {
    return http.post_java(this.requestPath.c2cShopCancel, data);
  },
  c2cOrderCreate(data) {
    return http.post_java(this.requestPath.c2cOrderCreate, data);
  },
  c2corderlist(data) {
    return http.post_java(this.requestPath.c2corderlist, data);
  },
  shopOrderList(data) {
    return http.post_java(this.requestPath.shopOrderList, data);
  },
  c2csupportCoin(data) {
    return http.post_java(this.requestPath.c2csupportCoin, data);
  },
  c2cuserInfo(data) {
    return http.post_java(this.requestPath.c2cuserInfo, data);
  },
  setupMfaSecret(data) {
    return http.post_java(this.requestPath.setupMfaSecret, data);
  },
  verifyCode(data) {
    return http.post_java(this.requestPath.verifyCode, data);
  },
  bindingSecret(data) {
    return http.post_java(this.requestPath.bindingSecret, data);
  },
  paymentMethods(data) {
    return http.post_java(this.requestPath.paymentMethods, data);
  },
  getfiats(data) {
    return http.post_java(this.requestPath.getfiats, data);
  },
  cryptocurrencies(data) {
    return http.post_java(this.requestPath.cryptocurrencies, data);
  },
  retrieveQuotes(data) {
    return http.post_java(this.requestPath.retrieveQuotes, data);
  },
  createBuyOrder(data) {
    return http.post_java(this.requestPath.createBuyOrder, data);
  },
  getWithDrawPrecision(data) {
    return http.post_java(this.requestPath.getWithDrawPrecision, data);
  },
  setLg(data) {
    return http.post_java(this.requestPath.setLg, data);
  },
  amountPriceGn(data) {
    return http.post_java(this.requestPath.amountPriceGn, data);
  },
  existCoinPair(data) {
    return http.post_java(this.requestPath.existCoinPair, data);
  },
  signOut(data) {
    return http.post_java(this.requestPath.signOut, data);
  },
  BindDevice(data) {
    return http.post_java(this.requestPath.BindDevice, data);
  },
  innerMsg(data) {
    return http.post_java(this.requestPath.innerMsg, data);
  },
  innerMsgreadAll(data) {
    return http.post_java(this.requestPath.innerMsgreadAll, data);
  },
  innerMsgread(data) {
    return http.post_java(this.requestPath.innerMsgread, data);
  },
  userWalletSpotLine(data) {
    return http.post_java(this.requestPath.userWalletSpotLine, data);
  },
  priceGn(data) {
    return http.post_java(this.requestPath.priceGn, data);
  },
  fineGrainedCoinPrice(data) {
    return http.post_java(this.requestPath.fineGrainedCoinPrice, data);
  },
  unBindDevice(data) {
    return http.post_java(this.requestPath.unBindDevice, data);
  },
  changeEmail(data) {
    return http.post_java(this.requestPath.changeEmail, data);
  },
  changePasswordByEmail(data) {
    return http.post_java(this.requestPath.changePasswordByEmail, data);
  },
  forgotPassByEmail(data) {
    return http.post_java(this.requestPath.forgotPassByEmail, data);
  },
  getUserInfo(data) {
    return http.post_java(this.requestPath.getUserInfo, data);
  },
  getWithdrawLimit(data) {
    return http.post_java(this.requestPath.getWithdrawLimit, data);
  },
  userAvailableCoinList(data) {
    return http.post_java(this.requestPath.userAvailableCoinList, data);
  },
  getWithdrawFee(data) {
    return http.post_java(this.requestPath.getWithdrawFee, data);
  },
  searchUserWithdrawAccountPaged(data) {
    return http.post_java(
      this.requestPath.searchUserWithdrawAccountPaged,
      data
    );
  },
  deleteUserWithdrawAccount(data) {
    return http.post_java(this.requestPath.deleteUserWithdrawAccount, data);
  },
  addOrUpdateUserWithdrawAccount(data) {
    return http.post_java(
      this.requestPath.addOrUpdateUserWithdrawAccount,
      data
    );
  },
  priceLine(data) {
    return http.post_java(this.requestPath.priceLine, data);
  },
  userAssetsFiat(data) {
    return http.post_java(this.requestPath.userAssetsFiat, data);
  },
  getTransactionPaged(data) {
    return http.post_java(this.requestPath.getTransactionPaged, data);
  },
  userCoinList(data) {
    return http.post_java(this.requestPath.userCoinList, data);
  },
  symbolListPaged(data) {
    return http.post_java(this.requestPath.symbolListPaged, data);
  },
  signUpAndSignIn(data) {
    return http.post_java(this.requestPath.signUpAndSignIn, data);
  },
  bannerList(data) {
    return http.post_java(this.requestPath.bannerList, data);
  },
  querycoinWallet(data) {
    return http.post_java(this.requestPath.querycoinWallet, data);
  },
  coinPair(data) {
    return http.post_java(this.requestPath.coinPair, data);
  },
  Askrfq(data) {
    return http.post_java(this.requestPath.Askrfq, data);
  },
  addOrder(data) {
    return http.post_java(this.requestPath.addOrder, data);
  },
  getWalletAddress(data) {
    return http.post_java(this.requestPath.getWalletAddress, data);
  },
  getNetwork(data) {
    return http.post_java(this.requestPath.getNetwork, data);
  },
  withdraw(data) {
    return http.post_java(this.requestPath.withdraw, data);
  },
  flashExchange(data) {
    return http.post_java(this.requestPath.orderInfo, data);
  },
  orderInfo(data) {
    return http.post_java(this.requestPath.orderInfo, data);
  },
  requestForQuote(data) {
    return http.post_java(this.requestPath.requestForQuote, data);
  },
  instrumentsAllcoin(data) {
    return http.post_java(this.requestPath.instrumentsAllcoin, data);
  },
  sendMailCaptcha(data) {
    return http.post_java(this.requestPath.sendMailCaptcha, data);
  },
  sendPhoneVerifyCode(data) {
    return http.post_java(this.requestPath.sendPhoneVerifyCode, data);
  },
  login(data) {
    return http.post_java(this.requestPath.login, data);
  },
  getApplicantInfo(data) {
    return http.post_java(this.requestPath.getApplicantInfo, data);
  },
  getWebHookResult(data) {
    return http.post_java(this.requestPath.getWebHookResult, data);
  },
  getAccToken(data) {
    return http.post_java(this.requestPath.getAccToken, data);
  },
  register(data) {
    return http.post_java(this.requestPath.register, data);
  },
  Apply(data) {
    return http.post_java(this.requestPath.Apply, data);
  },
  kycAdd(data) {
    return http.post_java(this.requestPath.kycAdd, data);
  },
  authInfo(data) {
    return http.post_java(this.requestPath.authInfo, data);
  },
  changePassword(data) {
    return http.post_java(this.requestPath.changePassword, data);
  },
  sendEmailCode(data) {
    return http.post_java(this.requestPath.sendEmailCode, data);
  },
  updateEmail(data) {
    return http.post_java(this.requestPath.updateEmail, data);
  },

  emailCodeCompare(data) {
    return http.post_java(this.requestPath.emailCodeCompare, data);
  },
  setUserName(data) {
    return http.post_java(this.requestPath.setUserName, data);
  },
  setUserPicture(data) {
    return http.post_java(this.requestPath.setUserPicture, data);
  },
  getCoinList(data) {
    return http.post_java(this.requestPath.getCoinList, data);
  },
  getCoinLike(data) {
    return http.post_java(this.requestPath.getCoinLike, data);
  },
  increaseRate(data) {
    return http.post_java(this.requestPath.increaseRate, data);
  },
  assetRecordList(data) {
    return http.post_java(this.requestPath.assetRecordList, data);
  },
  accountChart(data) {
    return http.post_java(this.requestPath.accountChart, data);
  },
  amount(data) {
    return http.post_java(this.requestPath.amount, data);
  },
};
